import helmet from 'helmet';
/**
 * PHASE 1 SECURITY HARDENING: Comprehensive Security Headers
 *
 * This middleware implements Helmet.js security headers to protect against
 * common web vulnerabilities including XSS, clickjacking, and MIME sniffing.
 */
export const securityHeaders = helmet({
    // Content Security Policy
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"], // For CSS frameworks like Tailwind
            imgSrc: ["'self'", 'data:', 'https:'],
            connectSrc: ["'self'", process.env.SUPABASE_URL || 'https://*.supabase.co'],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
            baseUri: ["'self'"],
            formAction: ["'self'"],
        },
    },
    // HTTP Strict Transport Security (HSTS)
    hsts: {
        maxAge: 31536000, // 1 year in seconds
        includeSubDomains: true,
        preload: true,
    },
    // X-Frame-Options: Prevent clickjacking
    frameguard: { action: 'deny' },
    // X-Content-Type-Options: Prevent MIME sniffing
    noSniff: true,
    // X-XSS-Protection: Enable XSS filtering
    xssFilter: true,
    // Referrer Policy: Control referrer information
    referrerPolicy: { policy: 'same-origin' },
    // Cross-Origin Embedder Policy: Disable if causing issues
    crossOriginEmbedderPolicy: false,
    // Cross-Origin Resource Policy: Allow cross-origin requests
    crossOriginResourcePolicy: { policy: 'cross-origin' },
    // Cross-Origin Opener Policy: Prevent window.opener access
    crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
    // Hide X-Powered-By header (redundant with app.disable but ensures coverage)
    hidePoweredBy: true,
});
/**
 * Additional security middleware for custom headers and enhanced protection
 */
export const additionalSecurity = (req, res, next) => {
    // Ensure X-Powered-By is removed (backup to app.disable)
    res.removeHeader('X-Powered-By');
    // Add custom security headers
    res.setHeader('X-API-Version', '1.0');
    res.setHeader('X-Security-Level', 'HIGH');
    res.setHeader('X-Security-Phase', 'PHASE-1-HARDENED');
    // Add security timestamp for monitoring
    res.setHeader('X-Security-Timestamp', new Date().toISOString());
    next();
};
/**
 * Development-specific security middleware
 * Relaxes some restrictions for development environment
 */
export const developmentSecurity = (req, res, next) => {
    if (process.env.NODE_ENV === 'development') {
        // Add development security headers
        res.setHeader('X-Development-Mode', 'true');
        res.setHeader('X-Security-Warning', 'Development environment - not for production');
    }
    next();
};
/**
 * Production-specific security enhancements
 */
export const productionSecurity = (req, res, next) => {
    if (process.env.NODE_ENV === 'production') {
        // Force HTTPS in production
        if (req.header('x-forwarded-proto') !== 'https') {
            return res.redirect(`https://${req.header('host')}${req.url}`);
        }
        // Add production security headers
        res.setHeader('X-Production-Mode', 'true');
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    }
    next();
};
//# sourceMappingURL=security.js.map