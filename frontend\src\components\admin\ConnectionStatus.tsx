
'use client';

import React, {useState} from 'react';
import {
	Card,
	CardContent,
	CardDescription,
	Card<PERSON>ooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import {CheckCircle, XCircle, AlertCircle, RefreshCw} from 'lucide-react';
import {ActionButton} from '@/components/ui/action-button';
import {
	HealthResponse,
	getHealthStatus,
	getMockHealthStatus,
} from '@/lib/adminService';
import {Skeleton} from '@/components/ui/skeleton';
import {useApi} from '@/hooks/useApi';
import {LoadingError} from '@/components/ui/loading-states';
import {ErrorBoundary} from '@/components/ui/error-boundary';

/**
 * Component that displays the connection status of database and Supabase
 */
export function ConnectionStatus() {
	const {
		data: health,
		isLoading,
		error,
		refetch,
	} = useApi<HealthResponse>(() => getHealthStatus(), {
		maxRetries: 3,
		initialDelay: 500,
		deps: [Math.floor(Date.now() / 30000)],
	});

	const [refreshing, setRefreshing] = useState(false);

	const handleRefresh = async () => {
		setRefreshing(true);
		await refetch();
		setRefreshing(false);
	};

	const getStatusIcon = (status: string) => {
		if (status === 'UP')
			return <CheckCircle className='h-5 w-5 text-green-500' />;
		if (status === 'DOWN') return <XCircle className='h-5 w-5 text-red-500' />;
		return <AlertCircle className='h-5 w-5 text-yellow-500' />;
	};

	const getStatusBadge = (status: string) => {
		if (status === 'UP')
			return (
				<Badge
					variant='outline'
					className='bg-green-500/20 text-green-700 border-green-500/30'>
					Connected
				</Badge>
			);
		if (status === 'DOWN')
			return (
				<Badge
					variant='outline'
					className='bg-red-500/20 text-red-700 border-red-500/30'>
					Disconnected
				</Badge>
			);
		return (
			<Badge
				variant='outline'
				className='bg-yellow-500/20 text-yellow-700 border-yellow-500/30'>
				Unknown
			</Badge>
		);
	};

	return (
		<ErrorBoundary>
			<Card className='shadow-md'>
				<CardHeader className='pb-2 p-5'>
					<CardTitle className='text-xl font-semibold text-primary'>
						Connection Status
					</CardTitle>
					<CardDescription>
						Current status of database connections
					</CardDescription>
				</CardHeader>
				<CardContent className='p-5'>
					{isLoading || refreshing ? (
						<div className='space-y-3'>
							<Skeleton className='h-6 w-full' />
							<Skeleton className='h-6 w-full' />
							<Skeleton className='h-6 w-full' />
						</div>
					) : error ? (
						<LoadingError message={error} onRetry={refetch} />
					) : health ? (
						<div className='space-y-4'>
							<div className='flex items-center justify-between'>
								<div className='flex items-center space-x-2'>
									<span className='font-medium'>Overall Status:</span>
								</div>
								<div className='flex items-center space-x-2'>
									{getStatusIcon(health.status)}
									{getStatusBadge(health.status)}
								</div>
							</div>

							<div className='flex items-center justify-between'>
								<div className='flex items-center space-x-2'>
									<span className='font-medium'>Database:</span>
									<span className='text-sm text-muted-foreground'>
										{health.components.database.type}
									</span>
								</div>
								<div className='flex items-center space-x-2'>
									{getStatusIcon(health.components.database.status)}
									{getStatusBadge(health.components.database.status)}
								</div>
							</div>

							{health.components.database.error && (
								<div className='ml-6 p-2 text-sm border-l-2 border-red-300 bg-red-500/10 text-red-700 dark:text-red-400'>
									Error: {health.components.database.error.message || String(health.components.database.error)}
								</div>
							)}

							{health.components.supabase && (
								<>
									<div className='flex items-center justify-between'>
										<div className='flex items-center space-x-2'>
											<span className='font-medium'>Supabase:</span>
											<span className='text-sm text-muted-foreground'>
												{health.components.supabase.url}
											</span>
										</div>
										<div className='flex items-center space-x-2'>
											{getStatusIcon(health.components.supabase.status)}
											{getStatusBadge(health.components.supabase.status)}
										</div>
									</div>

									{health.components.supabase.error && (
										<div className='ml-6 p-2 text-sm border-l-2 border-red-300 bg-red-500/10 text-red-700 dark:text-red-400'>
											Error: {health.components.supabase.error.message || String(health.components.supabase.error)}
										</div>
									)}
								</>
							)}

							{health.version && (
								<div className='flex items-center justify-between'>
									<span className='font-medium'>Version:</span>
									<span>{health.version}</span>
								</div>
							)}

							{health.uptime !== undefined && (
								<div className='flex items-center justify-between'>
									<span className='font-medium'>Uptime:</span>
									<span>
										{Math.floor(health.uptime / 3600)}h{' '}
										{Math.floor((health.uptime % 3600) / 60)}m
									</span>
								</div>
							)}

							<div className='text-xs text-muted-foreground'>
								Last updated: {new Date(health.timestamp).toLocaleString()}
							</div>
						</div>
					) : null}
				</CardContent>
				<CardFooter className='p-5'>
					<ActionButton
						actionType='tertiary'
						size='sm'
						className='w-full'
						onClick={handleRefresh}
						isLoading={refreshing || isLoading}
						loadingText='Refreshing...'
						icon={<RefreshCw className='h-4 w-4' />}>
						Refresh Status
					</ActionButton>
				</CardFooter>
			</Card>
		</ErrorBoundary>
	);
}
