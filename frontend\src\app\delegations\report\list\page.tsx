'use client';

import {useEffect, useState, useCallback, useMemo, Suspense} from 'react';
import {useSearchParams} from 'next/navigation';
import type {Delegation, DelegationStatus} from '@/lib/types';
import {getDelegations} from '@/lib/store';
import {
	CalendarDays,
	MapPin,
	Users as UsersIcon,
	Briefcase,
	Search,
	X,
	ChevronUp,
	ChevronDown,
} from 'lucide-react';
import {ReportActions} from '@/components/reports/ReportActions';
import {format, parseISO} from 'date-fns';
import {Badge} from '@/components/ui/badge';
import {cn} from '@/lib/utils';
import {formatDelegationStatusForDisplay} from '@/lib/utils/formattingUtils';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {Card, CardContent} from '@/components/ui/card';
import {SkeletonLoader} from '@/components/ui/loading';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {DelegationSummary} from '@/components/reports/DelegationSummary';
import {PaginationControls} from '@/components/ui/pagination';

// Status options for filter
const STATUS_OPTIONS: DelegationStatus[] = [
	'Planned',
	'Confirmed',
	'In_Progress',
	'Completed',
	'Cancelled',
	'No_details',
];

// Status color function (unchanged)
const getStatusColor = (status: Delegation['status']) => {
	switch (status) {
		case 'Planned':
			return 'bg-blue-100 text-blue-800 border-blue-300';
		case 'Confirmed':
			return 'bg-green-100 text-green-800 border-green-300';
		case 'In_Progress':
			return 'bg-yellow-100 text-yellow-800 border-yellow-300';
		case 'Completed':
			return 'bg-purple-100 text-purple-800 border-purple-300';
		case 'Cancelled':
			return 'bg-red-100 text-red-800 border-red-300';
		case 'No_details':
		default:
			return 'bg-gray-100 text-gray-800 border-gray-300';
	}
};

// Date formatting function (unchanged)
const formatDate = (dateString: string | undefined): string => {
	if (!dateString) return 'N/A';
	try {
		return format(parseISO(dateString), 'MMM d, yyyy');
	} catch (e) {
		return 'Invalid Date';
	}
};

function DelegationListReportContent() {
	const searchParams = useSearchParams();
	const [allDelegations, setAllDelegations] = useState<Delegation[]>([]);
	const [filteredDelegations, setFilteredDelegations] = useState<Delegation[]>(
		[]
	);
	const [isLoading, setIsLoading] = useState(true);

	// Filter state
	const [searchTerm, setSearchTerm] = useState('');
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
	const [selectedStatus, setSelectedStatus] = useState<string>('all');
	const [dateRange, setDateRange] = useState<{from?: Date; to?: Date}>({});

	// Pagination state
	const [currentPage, setCurrentPage] = useState(1);
	const [itemsPerPage] = useState(10);

	// Sorting state
	const [sortField, setSortField] = useState<string>('durationFrom');
	const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

	// Initialize from URL params
	useEffect(() => {
		const urlSearchTerm = searchParams.get('searchTerm') || '';
		const urlStatus = searchParams.get('status') || 'all';

		setSearchTerm(urlSearchTerm);
		setDebouncedSearchTerm(urlSearchTerm);
		setSelectedStatus(urlStatus);
	}, [searchParams]);

	// Debounce search term
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	const fetchDelegations = async () => {
		setIsLoading(true);
		try {
			document.title = 'Delegation List Report';
			const delegations = await getDelegations();
			setAllDelegations(delegations);
			applyFilters(delegations);
		} catch (error) {
			console.error('Error fetching delegations for report:', error);
		} finally {
			setIsLoading(false);
		}
	};

	// Apply filters to delegations
	const applyFilters = useCallback(
		(delegations: Delegation[]) => {
			let tempDelegations = [...delegations];

			// Apply search filter
			if (debouncedSearchTerm) {
				const lowercasedSearch = debouncedSearchTerm.toLowerCase();
				tempDelegations = tempDelegations.filter((delegation) => {
					return (
						delegation.eventName.toLowerCase().includes(lowercasedSearch) ||
						delegation.location.toLowerCase().includes(lowercasedSearch) ||
						delegation.delegates.some((d) =>
							d.name.toLowerCase().includes(lowercasedSearch)
						) ||
						(delegation.notes &&
							delegation.notes.toLowerCase().includes(lowercasedSearch)) ||
						delegation.status.toLowerCase().includes(lowercasedSearch)
					);
				});
			}

			// Apply status filter
			if (selectedStatus !== 'all') {
				tempDelegations = tempDelegations.filter(
					(delegation) => delegation.status === selectedStatus
				);
			}

			// Apply date range filter
			if (dateRange.from) {
				tempDelegations = tempDelegations.filter((delegation) => {
					const delegationDate = new Date(delegation.durationFrom);
					return delegationDate >= dateRange.from!;
				});
			}

			if (dateRange.to) {
				tempDelegations = tempDelegations.filter((delegation) => {
					const delegationDate = new Date(delegation.durationFrom);
					return delegationDate <= dateRange.to!;
				});
			}

			// Apply sorting
			tempDelegations = sortDelegations(
				tempDelegations,
				sortField,
				sortDirection
			);

			setFilteredDelegations(tempDelegations);
			setCurrentPage(1); // Reset to first page when filters change
		},
		[debouncedSearchTerm, selectedStatus, dateRange, sortField, sortDirection]
	);

	// Sort delegations
	const sortDelegations = useCallback(
		(delegations: Delegation[], field: string, direction: 'asc' | 'desc') => {
			return [...delegations].sort((a, b) => {
				let valueA, valueB;

				// Handle different field types
				switch (field) {
					case 'durationFrom':
						valueA = new Date(a.durationFrom).getTime();
						valueB = new Date(b.durationFrom).getTime();
						break;
					case 'status':
						valueA = a.status;
						valueB = b.status;
						break;
					case 'eventName':
						valueA = a.eventName.toLowerCase();
						valueB = b.eventName.toLowerCase();
						break;
					case 'location':
						valueA = a.location.toLowerCase();
						valueB = b.location.toLowerCase();
						break;
					case 'delegates':
						valueA = a.delegates.length;
						valueB = b.delegates.length;
						break;
					default:
						valueA = a[field as keyof Delegation];
						valueB = b[field as keyof Delegation];
				}

				// Compare values based on direction
				if (
					valueA === null ||
					valueA === undefined ||
					valueB === null ||
					valueB === undefined
				)
					return 0;
				if (valueA < valueB) return direction === 'asc' ? -1 : 1;
				if (valueA > valueB) return direction === 'asc' ? 1 : -1;
				return 0;
			});
		},
		[]
	);

	// Handle sort
	const handleSort = useCallback(
		(field: string) => {
			if (sortField === field) {
				setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
			} else {
				setSortField(field);
				setSortDirection('asc');
			}
		},
		[sortField, sortDirection]
	);

	// Convert sort direction to aria-sort value
	const getAriaSortValue = useCallback(
		(field: string): 'ascending' | 'descending' | 'none' | undefined => {
			if (sortField !== field) return 'none';
			return sortDirection === 'asc' ? 'ascending' : 'descending';
		},
		[sortField, sortDirection]
	);

	// Reset filters
	const resetFilters = useCallback(() => {
		setSearchTerm('');
		setDebouncedSearchTerm('');
		setSelectedStatus('all');
		setDateRange({});
		setCurrentPage(1);
		applyFilters(allDelegations);
	}, [allDelegations, applyFilters]);

	// Effect to apply filters when they change
	useEffect(() => {
		if (allDelegations.length > 0) {
			applyFilters(allDelegations);
		}
	}, [
		debouncedSearchTerm,
		selectedStatus,
		dateRange,
		sortField,
		sortDirection,
		applyFilters,
		allDelegations,
	]);

	// Initial data fetch
	useEffect(() => {
		fetchDelegations();
	}, []);

	// Pagination logic
	const indexOfLastItem = currentPage * itemsPerPage;
	const indexOfFirstItem = indexOfLastItem - itemsPerPage;
	const paginatedDelegations = useMemo(
		() => filteredDelegations.slice(indexOfFirstItem, indexOfLastItem),
		[filteredDelegations, indexOfFirstItem, indexOfLastItem]
	);

	const totalPages = useMemo(
		() => Math.ceil(filteredDelegations.length / itemsPerPage),
		[filteredDelegations.length, itemsPerPage]
	);

	// Handle page change
	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
	}, []);

	// Render sort indicator
	const renderSortIndicator = useCallback(
		(field: string) => {
			if (sortField !== field) return null;
			return sortDirection === 'asc' ? (
				<ChevronUp className='inline-block h-4 w-4 ml-1' />
			) : (
				<ChevronDown className='inline-block h-4 w-4 ml-1' />
			);
		},
		[sortField, sortDirection]
	);

	if (isLoading) {
		return (
			<div className='max-w-5xl mx-auto p-4'>
				<SkeletonLoader variant='table' count={5} />
			</div>
		);
	}

	return (
		<div className='max-w-7xl mx-auto bg-white p-4 sm:p-6 lg:p-8 text-gray-800 min-h-screen'>
			<div className='text-right mb-6 no-print'>
				<ReportActions
					reportType='delegations'
					reportContentId='#delegations-list-report-content'
					tableId='#delegations-table'
					fileName={`delegations-list-report-${
						new Date().toISOString().split('T')[0]
					}`}
					enableCsv={filteredDelegations.length > 0}
				/>
			</div>

			<div id='delegations-list-report-content' className='report-content'>
				{/* Enhanced header for both screen and print */}
				<header className='text-center mb-10 pb-6 border-b border-gray-200 report-header'>
					<div className='mb-6'>
						<h1 className='text-4xl font-bold text-gray-900 mb-3 report-header-title'>
							Delegation List Report
						</h1>
						<div className='w-24 h-1 bg-gradient-to-r from-blue-500 to-indigo-600 mx-auto rounded-full'></div>
					</div>
					<p className='text-lg text-gray-600 mb-2 report-header-subtitle'>
						{searchTerm || selectedStatus !== 'all'
							? `Filtered by: ${
									selectedStatus !== 'all'
										? `Status - ${formatDelegationStatusForDisplay(
												selectedStatus as DelegationStatus
										  )}`
										: ''
							  }${
									searchTerm
										? (selectedStatus !== 'all' ? ' | ' : '') +
										  `Search - "${searchTerm}"`
										: ''
							  }`
							: 'All Delegations'}
					</p>
					<p className='text-sm text-gray-500 report-header-date'>
						Generated: {new Date().toLocaleDateString()}{' '}
						{new Date().toLocaleTimeString([], {
							hour: '2-digit',
							minute: '2-digit',
						})}
					</p>

					{/* Screen-only Summary Statistics */}
					<div className='no-print'>
						<DelegationSummary delegations={filteredDelegations} />
					</div>

					{/* Print-only linear summary statistics - A4 optimized */}
					<div className='print-only print-summary-stats'>
						<div className='delegation-print-summary-item'>
							<span className='delegation-print-summary-label'>
								Total Delegations:
							</span>{' '}
							<span className='delegation-print-summary-value'>
								{filteredDelegations.length}
							</span>
						</div>

						<div className='delegation-print-summary-item'>
							<span className='delegation-print-summary-label'>
								Total Delegates:
							</span>{' '}
							<span className='delegation-print-summary-value'>
								{filteredDelegations.reduce(
									(acc, delegation) => acc + delegation.delegates.length,
									0
								)}
							</span>
						</div>

						{/* Only show top 3 statuses with counts to save space */}
						{STATUS_OPTIONS.map((status) => ({
							status,
							count: filteredDelegations.filter((d) => d.status === status)
								.length,
						}))
							.filter((item) => item.count > 0)
							.sort((a, b) => b.count - a.count)
							.slice(0, 3)
							.map(({status, count}) => (
								<div key={status} className='delegation-print-summary-item'>
									<span className='delegation-print-summary-label'>
										{formatDelegationStatusForDisplay(status)}:
									</span>{' '}
									<span className='delegation-print-summary-value'>
										{count}
									</span>
								</div>
							))}

						{/* Show filter information if applied */}
						{selectedStatus !== 'all' && (
							<div className='delegation-print-summary-item'>
								<span className='delegation-print-summary-label'>
									Filtered by Status:
								</span>{' '}
								<span className='delegation-print-summary-value'>
									{formatDelegationStatusForDisplay(
										selectedStatus as DelegationStatus
									)}
								</span>
							</div>
						)}

						{searchTerm && (
							<div className='delegation-print-summary-item'>
								<span className='delegation-print-summary-label'>
									Search Term:
								</span>{' '}
								<span className='delegation-print-summary-value'>
									"{searchTerm}"
								</span>
							</div>
						)}
					</div>
				</header>

				{/* Enhanced Filter Section */}
				<div className='mb-8 no-print'>
					<Card className='shadow-lg border-0 bg-gradient-to-r from-slate-50 to-gray-50'>
						<CardContent className='p-6'>
							<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 filter-grid'>
								{/* Status Filter */}
								<div>
									<label
										htmlFor='status-filter'
										className='block text-sm font-semibold text-gray-700 mb-2'>
										Filter by Status
									</label>
									<Select
										value={selectedStatus}
										onValueChange={setSelectedStatus}
										aria-label='Filter by status'>
										<SelectTrigger className='w-full bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500'>
											<SelectValue placeholder='All Statuses' />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value='all'>All Statuses</SelectItem>
											{STATUS_OPTIONS.map((status) => (
												<SelectItem key={status} value={status}>
													{formatDelegationStatusForDisplay(status)}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>

								{/* Search Input */}
								<div className='relative'>
									<label
										htmlFor='search-input'
										className='block text-sm font-semibold text-gray-700 mb-2'>
										Search Delegations
									</label>
									<div className='relative'>
										<Input
											id='search-input'
											type='text'
											placeholder='Search by event, location, or delegate...'
											value={searchTerm}
											onChange={(e) => setSearchTerm(e.target.value)}
											className='pl-10 pr-10 bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500'
											aria-label='Search delegations'
										/>
										<Search
											className='absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400'
											aria-hidden='true'
										/>
										{searchTerm && (
											<Button
												variant='ghost'
												size='icon'
												className='absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7'
												onClick={() => setSearchTerm('')}
												aria-label='Clear search'>
												<X className='h-4 w-4' />
												<span className='sr-only'>Clear search</span>
											</Button>
										)}
									</div>
								</div>

								{/* Date Range Filter - Placeholder for future implementation */}
								<div>
									<label
										htmlFor='date-range'
										className='block text-sm font-semibold text-gray-700 mb-2'>
										Date Range
									</label>
									<Input
										id='date-range'
										type='text'
										placeholder='Date range filter coming soon'
										disabled
										className='opacity-50 bg-gray-100'
										aria-label='Date range filter (coming soon)'
									/>
								</div>
							</div>

							{/* Filter Summary & Reset */}
							{(searchTerm || selectedStatus !== 'all') && (
								<div className='mt-6 flex items-center justify-between bg-blue-50 p-4 rounded-lg border border-blue-200'>
									<div className='text-sm'>
										<span className='font-semibold text-blue-800'>
											Active Filters:
										</span>
										{searchTerm && (
											<span className='ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs'>
												Search: "{searchTerm}"
											</span>
										)}
										{selectedStatus !== 'all' && (
											<span className='ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-xs'>
												Status:{' '}
												{formatDelegationStatusForDisplay(
													selectedStatus as DelegationStatus
												)}
											</span>
										)}
									</div>
									<Button
										variant='outline'
										size='sm'
										onClick={resetFilters}
										className='border-blue-300 text-blue-700 hover:bg-blue-100'
										aria-label='Reset all filters'>
										Reset Filters
									</Button>
								</div>
							)}
						</CardContent>
					</Card>
				</div>

				{/* Empty State */}
				{filteredDelegations.length === 0 ? (
					<div className='text-center py-16 bg-gradient-to-br from-gray-50 to-slate-100 rounded-xl border border-gray-200'>
						<div className='max-w-md mx-auto'>
							<div className='mb-4 text-gray-400'>
								<CalendarDays className='h-16 w-16 mx-auto mb-4' />
							</div>
							<h3 className='text-lg font-semibold text-gray-700 mb-2'>
								No delegations found
							</h3>
							<p className='text-gray-500 mb-4'>
								No delegations match the current filter criteria.
							</p>
							<Button
								variant='outline'
								size='lg'
								onClick={resetFilters}
								className='mt-2'
								aria-label='Reset filters to show all delegations'>
								Reset Filters
							</Button>
						</div>
					</div>
				) : (
					<>
						{/* Enhanced Table with Sorting */}
						<Card className='card-print shadow-lg border-0 overflow-hidden'>
							<CardContent className='p-0'>
								<div className='delegations-table-container overflow-x-auto'>
									<Table id='delegations-table'>
										<TableHeader>
											<TableRow className='bg-gradient-to-r from-slate-100 to-gray-100 border-b border-gray-200'>
												<TableHead
													onClick={() => handleSort('eventName')}
													className='cursor-pointer hover:bg-slate-200 transition-colors font-semibold text-gray-700'
													aria-sort={getAriaSortValue('eventName')}
													role='columnheader'
													tabIndex={0}
													onKeyDown={(e) => {
														if (e.key === 'Enter' || e.key === ' ') {
															e.preventDefault();
															handleSort('eventName');
														}
													}}
													aria-label='Sort by event name'
													style={{width: '25%'}}>
													Event Name {renderSortIndicator('eventName')}
												</TableHead>
												<TableHead
													onClick={() => handleSort('location')}
													className='cursor-pointer hover:bg-slate-200 transition-colors font-semibold text-gray-700'
													aria-sort={getAriaSortValue('location')}
													role='columnheader'
													tabIndex={0}
													onKeyDown={(e) => {
														if (e.key === 'Enter' || e.key === ' ') {
															e.preventDefault();
															handleSort('location');
														}
													}}
													aria-label='Sort by location'
													style={{width: '20%'}}>
													Location {renderSortIndicator('location')}
												</TableHead>
												<TableHead
													onClick={() => handleSort('durationFrom')}
													className='cursor-pointer hover:bg-slate-200 transition-colors font-semibold text-gray-700'
													aria-sort={getAriaSortValue('durationFrom')}
													role='columnheader'
													tabIndex={0}
													onKeyDown={(e) => {
														if (e.key === 'Enter' || e.key === ' ') {
															e.preventDefault();
															handleSort('durationFrom');
														}
													}}
													aria-label='Sort by duration'
													style={{width: '15%'}}>
													Duration {renderSortIndicator('durationFrom')}
												</TableHead>
												<TableHead
													onClick={() => handleSort('status')}
													className='cursor-pointer hover:bg-slate-200 transition-colors font-semibold text-gray-700'
													aria-sort={getAriaSortValue('status')}
													role='columnheader'
													tabIndex={0}
													onKeyDown={(e) => {
														if (e.key === 'Enter' || e.key === ' ') {
															e.preventDefault();
															handleSort('status');
														}
													}}
													aria-label='Sort by status'
													style={{width: '10%'}}>
													Status {renderSortIndicator('status')}
												</TableHead>
												<TableHead
													onClick={() => handleSort('delegates')}
													className='cursor-pointer hover:bg-slate-200 transition-colors font-semibold text-gray-700'
													aria-sort={getAriaSortValue('delegates')}
													role='columnheader'
													tabIndex={0}
													onKeyDown={(e) => {
														if (e.key === 'Enter' || e.key === ' ') {
															e.preventDefault();
															handleSort('delegates');
														}
													}}
													aria-label='Sort by number of delegates'
													style={{width: '30%'}}>
													Delegates {renderSortIndicator('delegates')}
												</TableHead>
											</TableRow>
										</TableHeader>
										<TableBody>
											{paginatedDelegations.map((delegation, index) => (
												<TableRow
													key={delegation.id}
													className={cn(
														'page-break-inside-avoid hover:bg-slate-50 transition-colors border-b border-gray-100',
														index % 2 === 0 ? 'bg-white' : 'bg-slate-25'
													)}>
													<TableCell
														className='font-medium print-text-wrap py-4 px-4'
														title={delegation.eventName}>
														<div className='font-semibold text-gray-800'>
															{delegation.eventName}
														</div>
													</TableCell>
													<TableCell
														className='print-text-wrap print-location-col py-4 px-4'
														title={delegation.location}>
														<div className='flex items-center text-gray-600'>
															<MapPin className='h-4 w-4 mr-2 text-gray-400' />
															{delegation.location}
														</div>
													</TableCell>
													<TableCell className='whitespace-nowrap py-4 px-4'>
														<div className='flex items-center text-gray-600'>
															<CalendarDays className='h-4 w-4 mr-2 text-gray-400' />
															<div className='text-sm'>
																<div>{formatDate(delegation.durationFrom)}</div>
																<div className='text-xs text-gray-500'>
																	to {formatDate(delegation.durationTo)}
																</div>
															</div>
														</div>
													</TableCell>
													<TableCell className='py-4 px-4'>
														<Badge
															className={cn(
																'text-xs py-1 px-2 font-medium',
																getStatusColor(delegation.status)
															)}>
															{formatDelegationStatusForDisplay(
																delegation.status
															)}
														</Badge>
													</TableCell>
													<TableCell
														className='max-w-xs print-text-wrap py-4 px-4'
														title={delegation.delegates
															.map((d) => d.name)
															.join(', ')}>
														{delegation.delegates.length > 0 ? (
															<div className='flex items-start'>
																<UsersIcon className='h-4 w-4 mr-2 text-gray-400 mt-0.5 flex-shrink-0' />
																<div>
																	{/* Screen view - truncate long lists */}
																	<span className='no-print'>
																		{delegation.delegates.length <= 3 ? (
																			<div className='space-y-1'>
																				{delegation.delegates.map((d, i) => (
																					<div
																						key={i}
																						className='text-sm text-gray-700'>
																						{d.name}
																					</div>
																				))}
																			</div>
																		) : (
																			<div>
																				<div className='space-y-1 mb-1'>
																					{delegation.delegates
																						.slice(0, 2)
																						.map((d, i) => (
																							<div
																								key={i}
																								className='text-sm text-gray-700'>
																								{d.name}
																							</div>
																						))}
																				</div>
																				<span className='text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded'>
																					+{delegation.delegates.length - 2}{' '}
																					more
																				</span>
																			</div>
																		)}
																	</span>

																	{/* Print view - show all delegates */}
																	<span className='print-only'>
																		<div className='space-y-1'>
																			{delegation.delegates.map((d, i) => (
																				<div key={i} className='text-sm'>
																					{d.name}
																				</div>
																			))}
																		</div>
																	</span>
																</div>
															</div>
														) : (
															<span className='text-gray-400 text-sm'>
																No delegates
															</span>
														)}
													</TableCell>
												</TableRow>
											))}
										</TableBody>
									</Table>
								</div>
							</CardContent>
						</Card>

						{/* Enhanced Pagination */}
						{filteredDelegations.length > itemsPerPage && (
							<div className='flex justify-center mt-8 no-print'>
								<PaginationControls
									currentPage={currentPage}
									totalPages={totalPages}
									onPageChange={handlePageChange}
								/>
							</div>
						)}
					</>
				)}

				<footer className='mt-12 pt-6 border-t border-gray-200 text-center text-sm text-gray-500 report-footer'>
					<div className='space-y-2'>
						<p className='font-medium'>
							Report generated on: {new Date().toLocaleDateString()}{' '}
							{new Date().toLocaleTimeString([], {
								hour: '2-digit',
								minute: '2-digit',
							})}
						</p>
						<p className='text-gray-400'>WorkHub - Delegation Management</p>
						<p className='report-page-number'>Page </p>
						<p className='print-only text-xs'>
							Confidential - For internal use only
						</p>
					</div>
				</footer>
			</div>
			<style jsx global>{`
				.print-text-wrap {
					word-break: break-word;
					white-space: normal !important;
				}

				@media (max-width: 640px) {
					.delegations-table-container {
						overflow-x: auto;
					}

					.filter-grid {
						grid-template-columns: 1fr;
					}

					.summary-grid {
						grid-template-columns: 1fr 1fr;
					}
				}

				/* Component-specific print styles that complement globals.css */
				@media print {
					/* Ensure all delegations are printed */
					#delegations-list-report-content {
						width: 100% !important;
						max-width: 100% !important;
					}

					/* Add subtle alternating row colors */
					#delegations-table tr:nth-child(even) {
						background-color: #f9f9f9 !important;
					}

					/* Ensure proper text wrapping in specific columns */
					#delegations-table .print-location-col {
						max-width: 50mm !important;
						word-break: break-word !important;
					}

					/* Optimize column widths for A4 paper */
					#delegations-table th:first-child,
					#delegations-table td:first-child {
						width: 25% !important;
					}

					#delegations-table th:nth-child(2),
					#delegations-table td:nth-child(2) {
						width: 20% !important;
					}

					#delegations-table th:nth-child(3),
					#delegations-table td:nth-child(3) {
						width: 20% !important;
						white-space: nowrap !important;
					}

					#delegations-table th:nth-child(4),
					#delegations-table td:nth-child(4) {
						width: 10% !important;
					}

					#delegations-table th:nth-child(5),
					#delegations-table td:nth-child(5) {
						width: 25% !important;
					}
				}
			`}</style>
		</div>
	);
}

export default function DelegationListReportPage() {
	return (
		<Suspense
			fallback={<div className='text-center py-10'>Loading report...</div>}>
			<DelegationListReportContent />
		</Suspense>
	);
}
