'use client';

import {useEffect, useState} from 'react';
import {useParams, useRouter} from 'next/navigation';
import TaskForm from '@/components/tasks/TaskForm';
import {getTaskById, updateTask as storeUpdateTask} from '@/lib/store';
import type {TaskFormData} from '@/lib/schemas/taskSchemas';
import type {Task} from '@/lib/types';
import {PageHeader} from '@/components/ui/PageHeader';
import {FileEdit} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';
import {Skeleton} from '@/components/ui/skeleton';

export default function EditTaskPage() {
	const router = useRouter();
	const params = useParams();
	const {toast} = useToast();
	const [task, setTask] = useState<Task | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	const taskId = params.id as string;

	useEffect(() => {
		const fetchTask = async () => {
			if (taskId) {
				try {
					const fetchedTask = await getTaskById(taskId);
					if (fetchedTask) {
						setTask(fetchedTask);
					} else {
						toast({
							title: 'Error',
							description: 'Task not found.',
							variant: 'destructive',
						});
						router.push('/tasks');
					}
				} catch (error) {
					console.error('Error fetching task:', error);
					toast({
						title: 'Error',
						description: 'Failed to load task data.',
						variant: 'destructive',
					});
					router.push('/tasks');
				} finally {
					setIsLoading(false);
				}
			}
		};

		fetchTask();
	}, [taskId, router, toast]);

	const handleSubmit = async (data: TaskFormData) => {
		if (!taskId || !task) return;

		try {
			// storeUpdateTask now handles the assignment logic internally
			// based on assignedDriverId or assignedEmployeeId in data
			const updatedTask = await storeUpdateTask(taskId, data);

			if (updatedTask) {
				toast({
					title: 'Task Updated',
					description: `The task "${updatedTask.description.substring(
						0,
						30
					)}..." has been successfully updated.`,
					variant: 'default',
				});
				router.push(`/tasks/${taskId}`);
			} else {
				toast({
					title: 'Error',
					description: 'Failed to update task.',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Error updating task:', error);
			toast({
				title: 'Error',
				description: 'Failed to update task. Please try again.',
				variant: 'destructive',
			});
		}
	};

	if (isLoading) {
		return (
			<div className='space-y-6'>
				<PageHeader title='Loading...' icon={FileEdit} />
				<Skeleton className='h-[600px] w-full rounded-lg bg-card' />
			</div>
		);
	}

	if (!task) {
		return <p>Task not found.</p>;
	}

	return (
		<div className='space-y-6'>
			<PageHeader
				title={`Edit Task: ${task.description.substring(0, 50)}${
					task.description.length > 50 ? '...' : ''
				}`}
				description='Modify the details for this task.'
				icon={FileEdit}
			/>
			<TaskForm onSubmit={handleSubmit} initialData={task} isEditing={true} />
		</div>
	);
}
