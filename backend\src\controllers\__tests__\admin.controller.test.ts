/**
 * Admin Controller Tests
 *
 * This module tests the admin controller functionality.
 */

// Import the test setup to ensure Jest globals are available
import '../../tests/setup';
import {createExpressMocks} from '../../tests/testUtils';

import {Request, Response} from 'express';
import HttpError from '../../utils/HttpError';

// --- Mock definitions START ---
const mockGetHealthStatus = jest.fn();
const mockGetPerformanceMetrics = jest.fn();
const mockGetErrorLogs = jest.fn();

const mockLoggerInfo = jest.fn();
const mockLoggerError = jest.fn();

(jest as any).unstable_mockModule('src/services/admin.service.ts', () => ({
	getHealthStatus: mockGetHealthStatus,
	getPerformanceMetrics: mockGetPerformanceMetrics,
	getErrorLogs: mockGetErrorLogs,
}));

(jest as any).unstable_mockModule('src/utils/logger.ts', () => ({
	default: {
		info: mockLoggerInfo,
		error: mockLoggerError,
	},
	logger: {
		info: mockLoggerInfo,
		error: mockLoggerError,
	},
}));
// --- Mock definitions END ---

// Module to be tested, will be dynamically imported
let actualAdminController: typeof import('../admin.controller');

describe('Admin Controller', () => {
	const {mockRequest, mockResponse, responseJson, responseStatus} =
		createExpressMocks();

	beforeAll(async () => {
		// Prime mocks
		await import('src/services/admin.service.ts');
		await import('src/utils/logger.ts');
		// Dynamically import the controller
		actualAdminController = await import('../admin.controller');
	});

	beforeEach(() => {
		// Clear all individual mock instances
		mockGetHealthStatus.mockClear();
		mockGetPerformanceMetrics.mockClear();
		mockGetErrorLogs.mockClear();
		mockLoggerInfo.mockClear();
		mockLoggerError.mockClear();
		// also clear express mocks
		mockRequest.query = {}; // Reset query for each test
		(mockRequest as any).params = {}; // Reset params
		(mockRequest as any).body = {}; // Reset body
		responseJson.mockClear();
		responseStatus.mockClear();
	});

	describe('getHealthStatus', () => {
		it('should return 200 status when system is healthy', async () => {
			// Mock service response
			const mockHealthStatusData = {
				status: 'UP',
				message: 'Backend service is healthy',
				components: {
					database: {
						status: 'UP',
						type: 'PostgreSQL via Prisma',
						url: 'postgresql://user:****@localhost:5432/db',
						error: null,
					},
				},
				config: {
					useSupabase: false,
					supabaseConfigured: false,
					connectionMode: 'session',
				},
				timestamp: '2023-01-01T00:00:00.000Z',
				version: '1.0.0',
				uptime: 3600,
			};
			mockGetHealthStatus.mockResolvedValue(mockHealthStatusData);

			// Call the controller
			await actualAdminController.getHealthStatus(
				mockRequest as unknown as Request,
				mockResponse as unknown as Response
			);

			// Verify the response
			expect(responseStatus).toHaveBeenCalledWith(200);
			expect(responseJson).toHaveBeenCalledWith({
				status: 'success',
				data: mockHealthStatusData,
			});
			expect(mockLoggerInfo).toHaveBeenCalledWith(
				'Health status check requested'
			);
		});

		it('should return 503 status when system is unhealthy', async () => {
			// Mock service response
			const mockHealthStatusData = {
				status: 'DOWN',
				message: 'Backend service is unhealthy',
				components: {
					database: {
						status: 'DOWN',
						type: 'PostgreSQL via Prisma',
						url: 'postgresql://user:****@localhost:5432/db',
						error: 'Connection error',
					},
				},
				config: {
					useSupabase: false,
					supabaseConfigured: false,
					connectionMode: 'session',
				},
				timestamp: '2023-01-01T00:00:00.000Z',
				version: '1.0.0',
				uptime: 3600,
			};
			mockGetHealthStatus.mockResolvedValue(mockHealthStatusData);

			// Call the controller
			await actualAdminController.getHealthStatus(
				mockRequest as unknown as Request,
				mockResponse as unknown as Response
			);

			// Verify the response
			expect(responseStatus).toHaveBeenCalledWith(503);
			expect(responseJson).toHaveBeenCalledWith({
				status: 'success',
				data: mockHealthStatusData,
			});
		});

		it('should handle errors', async () => {
			// Mock service error
			const error = new Error('Test error');
			mockGetHealthStatus.mockRejectedValue(error);

			// Call the controller
			await actualAdminController.getHealthStatus(
				mockRequest as unknown as Request,
				mockResponse as unknown as Response
			);

			// Verify the response
			expect(responseStatus).toHaveBeenCalledWith(500);
			expect(responseJson).toHaveBeenCalledWith({
				status: 'error',
				message: 'Failed to retrieve health status',
				error: 'Test error',
			});
			expect(mockLoggerError).toHaveBeenCalledWith(
				'Error in health status endpoint:',
				error
			);
		});
	});

	describe('getPerformanceMetrics', () => {
		it('should return performance metrics', async () => {
			// Mock service response
			const mockMetrics = {
				cacheHitRate: {
					indexHitRate: 90,
					tableHitRate: 85,
				},
				connectionCount: 5,
				activeQueries: 2,
				avgQueryTime: 1.5,
				timestamp: '2023-01-01T00:00:00.000Z',
			};
			mockGetPerformanceMetrics.mockResolvedValue(mockMetrics);

			// Call the controller
			await actualAdminController.getPerformanceMetrics(
				mockRequest as unknown as Request,
				mockResponse as unknown as Response
			);

			// Verify the response
			expect(responseStatus).toHaveBeenCalledWith(200);
			expect(responseJson).toHaveBeenCalledWith({
				status: 'success',
				data: mockMetrics,
			});
			expect(mockLoggerInfo).toHaveBeenCalledWith(
				'Performance metrics requested'
			);
		});

		it('should handle errors', async () => {
			// Mock service error
			const error = new Error('Test error');
			mockGetPerformanceMetrics.mockRejectedValue(error);

			// Call the controller
			await actualAdminController.getPerformanceMetrics(
				mockRequest as unknown as Request,
				mockResponse as unknown as Response
			);

			// Verify the response
			expect(responseStatus).toHaveBeenCalledWith(500);
			expect(responseJson).toHaveBeenCalledWith({
				status: 'error',
				message: 'Failed to retrieve performance metrics',
				error: 'Test error',
			});
			expect(mockLoggerError).toHaveBeenCalledWith(
				'Error in performance metrics endpoint:',
				error
			);
		});
	});

	describe('getErrorLogs', () => {
		it('should return error logs with pagination', async () => {
			const mockValidatedData = {page: 1, limit: 10, level: 'ERROR' as const};
			const mockReq = {
				validatedData: mockValidatedData,
			} as unknown as Request;

			// Mock service response
			const mockLogsResponse = {
				data: [],
				pagination: {
					page: 1,
					limit: 10,
					total: 0,
					totalPages: 0,
				},
			};
			mockGetErrorLogs.mockResolvedValue(mockLogsResponse);

			// Call the controller
			await actualAdminController.getErrorLogs(
				mockReq,
				mockResponse as unknown as Response
			);

			// Verify the response
			expect(responseStatus).toHaveBeenCalledWith(200);
			expect(responseJson).toHaveBeenCalledWith({
				status: 'success',
				data: mockLogsResponse.data,
				pagination: mockLogsResponse.pagination,
			});
		});
	});
});
