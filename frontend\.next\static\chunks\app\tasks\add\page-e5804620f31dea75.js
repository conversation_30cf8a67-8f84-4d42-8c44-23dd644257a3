(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1011],{15622:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(95155),r=s(79732),i=s(2730),d=s(35695),n=s(95647);let c=(0,s(40157).A)("ListPlus",[["path",{d:"M11 12H3",key:"51ecnj"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M16 18H3",key:"12xzn7"}],["path",{d:"M18 9v6",key:"1twb98"}],["path",{d:"M21 12h-6",key:"bt1uis"}]]);var o=s(87481);function l(){let e=(0,d.useRouter)(),{toast:t}=(0,o.dj)(),s=async s=>{try{await (0,i.addTask)(s),t({title:"Task Added",description:'The task "'.concat(s.description.substring(0,30),'..." has been successfully created.'),variant:"default"}),e.push("/tasks")}catch(e){console.error("Error adding task:",e),t({title:"Error",description:"Failed to add task. Please try again.",variant:"destructive"})}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(n.z,{title:"Add New Task",description:"Enter the details for the new task or job.",icon:c}),(0,a.jsx)(r.A,{onSubmit:s,isEditing:!1})]})}},33464:(e,t,s)=>{Promise.resolve().then(s.bind(s,15622))}},e=>{var t=t=>e(e.s=t);e.O(0,[5769,8360,832,2688,2512,1859,4066,8162,2730,6174,8441,1684,7358],()=>t(33464)),_N_E=e.O()}]);