{"/_not-found/page": "/_not-found", "/favicon.ico/route": "/favicon.ico", "/delegations/[id]/edit/page": "/delegations/[id]/edit", "/add-vehicle/page": "/add-vehicle", "/delegations/[id]/page": "/delegations/[id]", "/delegations/page": "/delegations", "/delegations/add/page": "/delegations/add", "/employees/[id]/page": "/employees/[id]", "/auth-test/page": "/auth-test", "/employees/[id]/edit/page": "/employees/[id]/edit", "/employees/edit/[id]/page": "/employees/edit/[id]", "/employees/add/page": "/employees/add", "/employees/page": "/employees", "/page": "/", "/employees/new/page": "/employees/new", "/tasks/[id]/page": "/tasks/[id]", "/service-history/page": "/service-history", "/tasks/[id]/edit/page": "/tasks/[id]/edit", "/tasks/add/page": "/tasks/add", "/tasks/page": "/tasks", "/vehicles/[id]/page": "/vehicles/[id]", "/vehicles/edit/[id]/page": "/vehicles/edit/[id]", "/vehicles/page": "/vehicles", "/vehicles/new/page": "/vehicles/new", "/delegations/[id]/report/page": "/delegations/[id]/report", "/delegations/report/list/page": "/delegations/report/list", "/admin/page": "/admin", "/vehicles/[id]/report/service-history/page": "/vehicles/[id]/report/service-history", "/vehicles/[id]/report/page": "/vehicles/[id]/report", "/tasks/report/page": "/tasks/report"}