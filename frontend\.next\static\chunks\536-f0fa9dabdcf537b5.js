"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[536],{6560:(e,s,a)=>{a.d(s,{r:()=>n});var r=a(95155),t=a(12115),l=a(30285),d=a(50172),c=a(59434);let n=t.forwardRef((e,s)=>{let{actionType:a="primary",icon:t,isLoading:n=!1,loadingText:i,className:m,children:o,disabled:x,asChild:h=!1,...f}=e,{variant:u,className:N}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[a];return(0,r.jsx)(l.$,{ref:s,variant:u,className:(0,c.cn)(N,m),disabled:n||x,asChild:h,...f,children:n?(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),i||o]}):(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",t&&(0,r.jsx)("span",{className:"mr-2",children:t}),o]})})});n.displayName="ActionButton"},15080:(e,s,a)=>{a.d(s,{A:()=>i});var r=a(95155),t=a(12115),l=a(55365),d=a(30285),c=a(67554);class n extends t.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e,errorInfo:null}}componentDidCatch(e,s){console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",s.componentStack),this.setState({errorInfo:s})}render(){if(this.state.hasError){var e,s;return this.props.fallback?this.props.fallback:(0,r.jsxs)(l.Fc,{variant:"destructive",className:"my-4",children:[(0,r.jsx)(l.XL,{className:"text-lg font-semibold",children:"Something went wrong"}),(0,r.jsxs)(l.TN,{className:"mt-2",children:[(0,r.jsx)("p",{className:"mb-2",children:(null==(e=this.state.error)?void 0:e.message)||"An unexpected error occurred"}),(null==(s=this.state.error)?void 0:s.stack)&&(0,r.jsxs)("details",{className:"mt-2 text-xs",children:[(0,r.jsx)("summary",{children:"Error details"}),(0,r.jsx)("pre",{className:"mt-2 whitespace-pre-wrap overflow-auto max-h-[200px] p-2 bg-slate-100 dark:bg-slate-900 rounded",children:this.state.error.stack})]}),(0,r.jsxs)(d.$,{variant:"outline",size:"sm",onClick:this.handleRetry,className:"mt-4",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}}let i=n},55365:(e,s,a)=>{a.d(s,{Fc:()=>n,TN:()=>m,XL:()=>i});var r=a(95155),t=a(12115),l=a(74466),d=a(59434);let c=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),n=t.forwardRef((e,s)=>{let{className:a,variant:t,...l}=e;return(0,r.jsx)("div",{ref:s,role:"alert",className:(0,d.cn)(c({variant:t}),a),...l})});n.displayName="Alert";let i=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h5",{ref:s,className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",a),...t})});i.displayName="AlertTitle";let m=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",a),...t})});m.displayName="AlertDescription"},66695:(e,s,a)=>{a.d(s,{BT:()=>i,Wu:()=>m,ZB:()=>n,Zp:()=>d,aR:()=>c,wL:()=>o});var r=a(95155),t=a(12115),l=a(59434);let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});d.displayName="Card";let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...t})});c.displayName="CardHeader";let n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})});n.displayName="CardTitle";let i=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",a),...t})});i.displayName="CardDescription";let m=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",a),...t})});m.displayName="CardContent";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",a),...t})});o.displayName="CardFooter"},68856:(e,s,a)=>{a.d(s,{E:()=>l});var r=a(95155),t=a(59434);function l(e){let{className:s,...a}=e;return(0,r.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-md bg-muted",s),...a})}},77023:(e,s,a)=>{a.d(s,{gO:()=>u,jt:()=>h});var r=a(95155);a(12115);var t=a(50172),l=a(11133),d=a(59434),c=a(68856),n=a(55365),i=a(6560);let m={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},o={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function x(e){let{size:s="md",className:a,text:l,fullPage:c=!1}=e;return(0,r.jsx)("div",{className:(0,d.cn)("flex items-center justify-center",c&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",a),children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(t.A,{className:(0,d.cn)("animate-spin text-primary",m[s])}),l&&(0,r.jsx)("span",{className:(0,d.cn)("mt-2 text-muted-foreground",o[s]),children:l})]})})}function h(e){let{variant:s="default",count:a=1,className:t,testId:l="loading-skeleton"}=e;return"card"===s?(0,r.jsx)("div",{className:(0,d.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",t),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,r.jsx)(c.E,{className:"aspect-[16/10] w-full"}),(0,r.jsxs)("div",{className:"p-5",children:[(0,r.jsx)(c.E,{className:"h-7 w-3/4 mb-1"}),(0,r.jsx)(c.E,{className:"h-4 w-1/2 mb-3"}),(0,r.jsx)(c.E,{className:"h-px w-full my-3"}),(0,r.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,r.jsx)(c.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===s?(0,r.jsxs)("div",{className:(0,d.cn)("space-y-3",t),"data-testid":l,children:[(0,r.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,r.jsx)(c.E,{className:"h-8 flex-1"},s))}),Array(a).fill(0).map((e,s)=>(0,r.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,r.jsx)(c.E,{className:"h-6 flex-1"},s))},s))]}):"list"===s?(0,r.jsx)("div",{className:(0,d.cn)("space-y-3",t),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(c.E,{className:"h-12 w-12 rounded-full"}),(0,r.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,r.jsx)(c.E,{className:"h-4 w-1/3"}),(0,r.jsx)(c.E,{className:"h-4 w-full"})]})]},s))}):"stats"===s?(0,r.jsx)("div",{className:(0,d.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",t),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(c.E,{className:"h-5 w-1/3"}),(0,r.jsx)(c.E,{className:"h-5 w-5 rounded-full"})]}),(0,r.jsx)(c.E,{className:"h-8 w-1/2 mt-3"}),(0,r.jsx)(c.E,{className:"h-4 w-2/3 mt-2"})]},s))}):(0,r.jsx)("div",{className:(0,d.cn)("space-y-2",t),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,r.jsx)(c.E,{className:"w-full h-5"},s))})}function f(e){let{message:s,onRetry:a,className:c}=e;return(0,r.jsxs)(n.Fc,{variant:"destructive",className:(0,d.cn)("my-4",c),children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)(n.XL,{children:"Error"}),(0,r.jsx)(n.TN,{children:(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:s}),a&&(0,r.jsx)(i.r,{actionType:"tertiary",size:"sm",onClick:a,icon:(0,r.jsx)(t.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function u(e){let{isLoading:s,error:a,data:t,onRetry:l,children:c,loadingComponent:n,errorComponent:i,emptyComponent:m,className:o}=e;return s?n||(0,r.jsx)(x,{className:o,text:"Loading..."}):a?i||(0,r.jsx)(f,{message:a,onRetry:l,className:o}):!t||Array.isArray(t)&&0===t.length?m||(0,r.jsx)("div",{className:(0,d.cn)("text-center py-8",o),children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,r.jsx)("div",{className:o,children:c(t)})}},95647:(e,s,a)=>{a.d(s,{z:()=>t});var r=a(95155);function t(e){let{title:s,description:a,icon:t,children:l}=e;return(0,r.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[t&&(0,r.jsx)(t,{className:"h-8 w-8 text-primary"}),(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:s})]}),a&&(0,r.jsx)("p",{className:"text-muted-foreground mt-1",children:a})]}),l&&(0,r.jsx)("div",{className:"flex items-center gap-2",children:l})]})}a(12115)}}]);