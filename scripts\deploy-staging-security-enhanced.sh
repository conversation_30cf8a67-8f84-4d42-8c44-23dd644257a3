#!/bin/bash

# WorkHub Staging Deployment Script - Security Enhanced
# Phase 1 Security Hardening Complete: Docker Security, Headers, Input Validation, Rate Limiting
# Version: 2.0 - Enhanced with Context7 Docker Best Practices

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Script header
echo -e "${BLUE}
╔══════════════════════════════════════════════════════════════════════════════╗
║                    WorkHub Staging Deployment Script                        ║
║                     Security Enhanced - Phase 1 Complete                   ║
║                                                                              ║
║  🔒 Security Features: Docker Hardening, Headers, Input Validation          ║
║  🛡️  Rate Limiting, Secrets Management, RBAC System                         ║
║  📊 Health Checks, Resource Limits, Security Options                        ║
╚══════════════════════════════════════════════════════════════════════════════╝
${NC}"

# Check prerequisites
log "🔍 Checking prerequisites..."

# Check Docker
if ! command -v docker &> /dev/null; then
    error "Docker is not installed. Please install Docker first."
    exit 1
fi
success "Docker is installed"

# Check Docker Compose
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi
success "Docker Compose is installed"

# Check if running from project root
if [[ ! -f "docker-compose.staging.yml" ]]; then
    error "docker-compose.staging.yml not found. Please run this script from the project root."
    exit 1
fi
success "Project structure verified"

# Environment validation
log "🔧 Validating environment configuration..."

# Check for required environment variables
REQUIRED_VARS=(
    "SUPABASE_URL"
    "SUPABASE_ANON_KEY" 
    "SUPABASE_SERVICE_ROLE_KEY"
    "DATABASE_URL"
    "JWT_SECRET"
)

# Load environment variables from backend/.env
if [[ -f "backend/.env" ]]; then
    set -a  # automatically export all variables
    source backend/.env
    set +a
    success "Environment variables loaded from backend/.env"
else
    warning "backend/.env not found, using system environment variables"
fi

# Validate required variables
MISSING_VARS=()
for var in "${REQUIRED_VARS[@]}"; do
    if [[ -z "${!var:-}" ]]; then
        MISSING_VARS+=("$var")
    fi
done

if [[ ${#MISSING_VARS[@]} -gt 0 ]]; then
    error "Missing required environment variables:"
    for var in "${MISSING_VARS[@]}"; do
        echo "  - $var"
    done
    echo ""
    echo "Please set these variables in backend/.env or your environment."
    exit 1
fi
success "All required environment variables are set"

# Security validation
log "🛡️  Validating security configuration..."

# Check JWT secret strength
if [[ ${#JWT_SECRET} -lt 32 ]]; then
    warning "JWT_SECRET is shorter than 32 characters. Consider using a stronger secret."
else
    success "JWT_SECRET has adequate length"
fi

# Check Supabase URL format
if [[ ! $SUPABASE_URL =~ ^https://.*\.supabase\.co$ ]]; then
    warning "SUPABASE_URL format may be incorrect. Expected format: https://project.supabase.co"
else
    success "SUPABASE_URL format is correct"
fi

# Pre-deployment cleanup
log "🧹 Cleaning up previous deployment..."

# Stop and remove existing containers
if docker-compose -f docker-compose.staging.yml ps -q | grep -q .; then
    log "Stopping existing staging containers..."
    docker-compose -f docker-compose.staging.yml down --remove-orphans
    success "Previous containers stopped and removed"
else
    log "No existing containers to stop"
fi

# Remove old images (optional - uncomment if you want to force rebuild)
# log "Removing old staging images..."
# docker rmi workhub-backend:staging-security-hardened workhub-frontend:staging-security-hardened 2>/dev/null || true

# Build and deploy
log "🏗️  Building security-hardened Docker images..."

# Build with no cache to ensure latest security updates
docker-compose -f docker-compose.staging.yml build --no-cache --parallel

success "Docker images built successfully"

# Start services
log "🚀 Starting staging services..."

# Start services in detached mode
docker-compose -f docker-compose.staging.yml up -d

success "Services started in detached mode"

# Wait for services to be healthy
log "⏳ Waiting for services to become healthy..."

# Function to check service health
check_service_health() {
    local service=$1
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose -f docker-compose.staging.yml ps "$service" | grep -q "healthy"; then
            return 0
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            return 1
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
}

# Check backend health
echo -n "Checking backend health"
if check_service_health "backend"; then
    echo ""
    success "Backend is healthy"
else
    echo ""
    error "Backend failed to become healthy"
    log "Backend logs:"
    docker-compose -f docker-compose.staging.yml logs backend
    exit 1
fi

# Check frontend health
echo -n "Checking frontend health"
if check_service_health "frontend"; then
    echo ""
    success "Frontend is healthy"
else
    echo ""
    error "Frontend failed to become healthy"
    log "Frontend logs:"
    docker-compose -f docker-compose.staging.yml logs frontend
    exit 1
fi

# Deployment verification
log "🔍 Verifying deployment..."

# Test backend API
BACKEND_URL="http://localhost:3001"
if curl -f -s "$BACKEND_URL/api/health" > /dev/null; then
    success "Backend API is responding"
else
    error "Backend API is not responding"
    exit 1
fi

# Test frontend
FRONTEND_URL="http://localhost:3000"
if curl -f -s "$FRONTEND_URL" > /dev/null; then
    success "Frontend is responding"
else
    error "Frontend is not responding"
    exit 1
fi

# Security verification
log "🔐 Running basic security verification..."

# Test that protected endpoints require authentication
if curl -s "$BACKEND_URL/api/employees" | grep -q "401\|Unauthorized"; then
    success "Protected endpoints require authentication"
else
    warning "Protected endpoints may not be properly secured"
fi

# Display deployment summary
echo -e "${GREEN}
╔══════════════════════════════════════════════════════════════════════════════╗
║                        🎉 DEPLOYMENT SUCCESSFUL! 🎉                         ║
╚══════════════════════════════════════════════════════════════════════════════╝
${NC}"

echo -e "${BLUE}📊 Deployment Summary:${NC}"
echo "  🌐 Frontend URL: http://localhost:3000"
echo "  🔧 Backend API: http://localhost:3001"
echo "  📋 API Health: http://localhost:3001/api/health"
echo "  🔒 Security: Phase 1 Hardening Active"
echo ""

echo -e "${BLUE}🛡️  Security Features Active:${NC}"
echo "  ✅ Docker Security Hardening (non-root users, dumb-init)"
echo "  ✅ Security Headers (Helmet.js)"
echo "  ✅ Enhanced Input Validation (Zod + DOMPurify)"
echo "  ✅ Rate Limiting Protection"
echo "  ✅ Secrets Management"
echo "  ✅ Resource Limits & Security Options"
echo "  ✅ RBAC System with JWT Custom Claims"
echo ""

echo -e "${BLUE}📋 Next Steps:${NC}"
echo "  1. Run security verification: ./scripts/verify-staging-security.sh"
echo "  2. Test authentication flow in browser"
echo "  3. Verify RBAC system functionality"
echo "  4. Monitor logs: docker-compose -f docker-compose.staging.yml logs -f"
echo "  5. Proceed to Phase 2 security enhancements"
echo ""

echo -e "${YELLOW}⚠️  Important Notes:${NC}"
echo "  • This is a staging environment - not for production use"
echo "  • Monitor resource usage and adjust limits if needed"
echo "  • Run comprehensive security tests before production deployment"
echo "  • Keep environment variables secure and rotate secrets regularly"
echo ""

success "Staging deployment completed successfully!"
