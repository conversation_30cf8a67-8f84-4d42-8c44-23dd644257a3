'use client';

import {useEffect, useState, useCallback, useMemo, Suspense} from 'react';
import {useSearchParams} from 'next/navigation';
import type {Task, Employee} from '@/lib/types';
import {getTasks, getEmployees, getEmployeeById} from '@/lib/store';
import {
	ClipboardList,
	Search,
	Filter,
	X,
	Calendar,
	AlertTriangle,
	RefreshCw,
} from 'lucide-react';
import {ReportActions} from '@/components/reports/ReportActions';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {format, parseISO, isPast} from 'date-fns';
import {Badge} from '@/components/ui/badge';
import {cn} from '@/lib/utils';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {Card, CardContent} from '@/components/ui/card';
import {<PERSON><PERSON>, AlertTitle, AlertDescription} from '@/components/ui/alert';
import {SkeletonLoader} from '@/components/ui/loading';
import {EnhancedTasksContainer} from '@/components/tasks/EnhancedTasksContainer';
import {PageHeader} from '@/components/ui/PageHeader';

const getStatusColor = (status: Task['status']) => {
	// (Same as TaskCard)
	switch (status) {
		case 'Pending':
			return 'bg-yellow-100 text-yellow-800 border-yellow-300';
		case 'Assigned':
			return 'bg-blue-100 text-blue-800 border-blue-300';
		case 'In Progress':
			return 'bg-indigo-100 text-indigo-800 border-indigo-300';
		case 'Completed':
			return 'bg-green-100 text-green-800 border-green-300';
		case 'Cancelled':
			return 'bg-red-100 text-red-800 border-red-300';
		default:
			return 'bg-gray-100 text-gray-800 border-gray-300';
	}
};

const getPriorityColor = (priority: Task['priority']) => {
	// (Same as TaskCard)
	switch (priority) {
		case 'Low':
			return 'bg-green-100 text-green-800 border-green-300';
		case 'Medium':
			return 'bg-yellow-100 text-yellow-800 border-yellow-300';
		case 'High':
			return 'bg-red-100 text-red-800 border-red-300';
		default:
			return 'bg-gray-100 text-gray-800 border-gray-300';
	}
};

const formatDate = (
	dateString: string | undefined,
	includeTime = false
): string => {
	if (!dateString) return 'N/A';
	try {
		return format(
			parseISO(dateString),
			includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy'
		);
	} catch (e) {
		return 'Invalid Date';
	}
};

function TaskReportContent() {
	const searchParams = useSearchParams();
	const [allTasks, setAllTasks] = useState<Task[]>([]);
	const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [isLoadingData, setIsLoadingData] = useState(true); // Separate loading state for initial data fetch
	const [error, setError] = useState<string | null>(null);
	const [fetchError, setFetchError] = useState<string | null>(null); // Separate error state for data fetching
	const [employeesList, setEmployeesList] = useState<Employee[]>([]);

	// Filter state
	const [searchTerm, setSearchTerm] = useState('');
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
	const [statusFilter, setStatusFilter] = useState<string>('all');
	const [priorityFilter, setPriorityFilter] = useState<string>('all');
	const [employeeFilter, setEmployeeFilter] = useState<string>('all');
	const [retryCount, setRetryCount] = useState(0);

	// Initialize filters from URL params
	useEffect(() => {
		const urlSearchTerm = searchParams.get('searchTerm') || '';
		const urlStatusFilter = searchParams.get('status') || 'all';
		const urlPriorityFilter = searchParams.get('priority') || 'all';
		const urlEmployeeFilter = searchParams.get('employee') || 'all';

		setSearchTerm(urlSearchTerm);
		setDebouncedSearchTerm(urlSearchTerm);
		setStatusFilter(urlStatusFilter);
		setPriorityFilter(urlPriorityFilter);
		setEmployeeFilter(urlEmployeeFilter);
	}, [searchParams]);

	// Debounce search term
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Fetch data
	const fetchData = useCallback(async () => {
		setIsLoadingData(true);
		setFetchError(null);

		try {
			document.title = 'Task Report';

			// Use Promise.all to fetch data in parallel
			const [tasks, employees] = await Promise.all([
				getTasks(),
				getEmployees(),
			]);

			// Ensure employees is an array before setting state
			setAllTasks(Array.isArray(tasks) ? tasks : []);
			setEmployeesList(Array.isArray(employees) ? employees : []);
		} catch (err) {
			console.error('Failed to fetch tasks:', err);
			setFetchError('Failed to load tasks data. Please try again.');
		} finally {
			setIsLoadingData(false);
			setIsLoading(false); // Keep the original loading state for backward compatibility
		}
	}, []);

	// Initial data fetch
	useEffect(() => {
		fetchData();
	}, [fetchData, retryCount]);

	// Handle retry
	const handleRetry = useCallback(() => {
		setRetryCount((prev) => prev + 1);
	}, []);

	// Filter tasks
	useEffect(() => {
		let tempTasks = [...allTasks];
		const lowercasedSearch = debouncedSearchTerm.toLowerCase();

		// Filter by status
		if (statusFilter !== 'all') {
			tempTasks = tempTasks.filter((task) => task.status === statusFilter);
		}

		// Filter by priority
		if (priorityFilter !== 'all') {
			tempTasks = tempTasks.filter((task) => task.priority === priorityFilter);
		}

		// Filter by employee
		if (employeeFilter !== 'all') {
			tempTasks = tempTasks.filter(
				(task) =>
					task.assignedEmployeeId === employeeFilter ||
					(employeeFilter === 'unassigned' && !task.assignedEmployeeId)
			);
		}

		// Filter by search term
		if (lowercasedSearch) {
			tempTasks = tempTasks.filter((task) => {
				const employee = task.assignedEmployeeId
					? employeesList.find((e) => e.id === task.assignedEmployeeId)
					: null;
				const employeeName = employee ? employee.fullName.toLowerCase() : '';

				return (
					task.description.toLowerCase().includes(lowercasedSearch) ||
					task.location.toLowerCase().includes(lowercasedSearch) ||
					(task.notes && task.notes.toLowerCase().includes(lowercasedSearch)) ||
					employeeName.includes(lowercasedSearch) ||
					task.status.toLowerCase().includes(lowercasedSearch) ||
					task.priority.toLowerCase().includes(lowercasedSearch)
				);
			});
		}

		setFilteredTasks(tempTasks);
	}, [
		allTasks,
		debouncedSearchTerm,
		statusFilter,
		priorityFilter,
		employeeFilter,
		employeesList,
	]);

	// Handle retry for data fetching errors
	const handleDataRetry = useCallback(() => {
		fetchData();
	}, [fetchData]);

	// If we're loading the initial data, show a loading skeleton
	if (isLoadingData) {
		return (
			<div className='space-y-6'>
				<PageHeader
					title='Tasks Report'
					description='Loading task data...'
					icon={ClipboardList}
				/>
				<div className='space-y-6'>
					<SkeletonLoader variant='card' count={1} />
					<SkeletonLoader variant='table' count={5} className='mt-6' />
				</div>
			</div>
		);
	}

	// If there was an error fetching data, show an error message with retry button
	if (fetchError) {
		return (
			<div className='space-y-6'>
				<PageHeader
					title='Tasks Report'
					description='Error loading task data.'
					icon={ClipboardList}
				/>
				<Alert variant='destructive' className='mb-6'>
					<AlertTriangle className='h-4 w-4' />
					<AlertTitle>Error Loading Data</AlertTitle>
					<AlertDescription>{fetchError}</AlertDescription>
					<Button
						variant='outline'
						size='sm'
						onClick={handleDataRetry}
						className='mt-2'>
						<RefreshCw className='h-4 w-4 mr-2' />
						Try Again
					</Button>
				</Alert>
			</div>
		);
	}

	return (
		<div className='space-y-6'>
			<PageHeader
				title='Tasks Report'
				description='View and manage all tasks and assignments.'
				icon={ClipboardList}>
				<div className='flex gap-2 items-center no-print'>
					<ReportActions
						reportContentId='#task-report-content'
						tableId='#tasks-table'
						fileName={`tasks-report-${new Date().toISOString().split('T')[0]}`}
						enableCsv={filteredTasks.length > 0}
					/>
				</div>
			</PageHeader>

			{/* Filters */}
			<Card className='shadow-md no-print'>
				<CardContent className='pt-6'>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 items-end filter-grid'>
						<div>
							<Label
								htmlFor='status-filter'
								className='block text-sm font-medium text-muted-foreground mb-1'>
								Filter by Status
							</Label>
							<Select
								value={statusFilter}
								onValueChange={setStatusFilter}
								aria-label='Filter by status'>
								<SelectTrigger id='status-filter' className='w-full'>
									<SelectValue placeholder='All Statuses' />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value='all'>All Statuses</SelectItem>
									<SelectItem value='Pending'>Pending</SelectItem>
									<SelectItem value='Assigned'>Assigned</SelectItem>
									<SelectItem value='In_Progress'>In Progress</SelectItem>
									<SelectItem value='Completed'>Completed</SelectItem>
									<SelectItem value='Cancelled'>Cancelled</SelectItem>
								</SelectContent>
							</Select>
						</div>

						<div>
							<Label
								htmlFor='priority-filter'
								className='block text-sm font-medium text-muted-foreground mb-1'>
								Filter by Priority
							</Label>
							<Select
								value={priorityFilter}
								onValueChange={setPriorityFilter}
								aria-label='Filter by priority'>
								<SelectTrigger id='priority-filter' className='w-full'>
									<SelectValue placeholder='All Priorities' />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value='all'>All Priorities</SelectItem>
									<SelectItem value='High'>High</SelectItem>
									<SelectItem value='Medium'>Medium</SelectItem>
									<SelectItem value='Low'>Low</SelectItem>
								</SelectContent>
							</Select>
						</div>

						<div>
							<Label
								htmlFor='employee-filter'
								className='block text-sm font-medium text-muted-foreground mb-1'>
								Filter by Assignee
							</Label>
							<Select
								value={employeeFilter}
								onValueChange={setEmployeeFilter}
								aria-label='Filter by assignee'>
								<SelectTrigger id='employee-filter' className='w-full'>
									<SelectValue placeholder='All Assignees' />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value='all'>All Assignees</SelectItem>
									<SelectItem value='unassigned'>Unassigned</SelectItem>
									{Array.isArray(employeesList) &&
										employeesList.map((employee) => (
											<SelectItem key={employee.id} value={employee.id}>
												{employee.fullName ||
													employee.name ||
													`Employee ${employee.id}`}
											</SelectItem>
										))}
								</SelectContent>
							</Select>
						</div>

						<div className='relative'>
							<Label
								htmlFor='search-tasks'
								className='block text-sm font-medium text-muted-foreground mb-1'>
								Search Tasks
							</Label>
							<div className='relative'>
								<Input
									id='search-tasks'
									type='text'
									placeholder='Search by description, location, notes...'
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className='pl-10 pr-10'
									aria-label='Search tasks'
								/>
								<Search
									className='absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground'
									aria-hidden='true'
								/>
								{searchTerm && (
									<Button
										variant='ghost'
										size='icon'
										className='absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7'
										onClick={() => setSearchTerm('')}
										aria-label='Clear search'>
										<X className='h-4 w-4' />
										<span className='sr-only'>Clear search</span>
									</Button>
								)}
							</div>
						</div>
					</div>

					{/* Filter Summary & Reset */}
					{(statusFilter !== 'all' ||
						priorityFilter !== 'all' ||
						employeeFilter !== 'all' ||
						searchTerm) && (
						<div className='mt-4 flex items-center justify-between bg-gray-50 p-2 rounded'>
							<div className='text-sm'>
								<span className='font-medium'>Active Filters:</span>
								{statusFilter !== 'all' && (
									<span className='ml-2'>
										Status: {statusFilter.replace('_', ' ')}
									</span>
								)}
								{priorityFilter !== 'all' && (
									<span className='ml-2'>Priority: {priorityFilter}</span>
								)}
								{employeeFilter !== 'all' && (
									<span className='ml-2'>
										Assignee:{' '}
										{employeeFilter === 'unassigned'
											? 'Unassigned'
											: employeesList.find((e) => e.id === employeeFilter)
													?.fullName || 'Unknown'}
									</span>
								)}
								{searchTerm && (
									<span className='ml-2'>Search: "{searchTerm}"</span>
								)}
							</div>
							<Button
								variant='ghost'
								size='sm'
								onClick={() => {
									setStatusFilter('all');
									setPriorityFilter('all');
									setEmployeeFilter('all');
									setSearchTerm('');
								}}
								aria-label='Reset all filters'>
								Reset Filters
							</Button>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Tasks Report Content */}
			<div id='task-report-content' className='report-content'>
				<header className='text-center mb-8 pb-4 border-b-2 border-gray-300 print-only'>
					<h1 className='text-3xl font-bold text-gray-800'>Tasks Report</h1>
					<p className='text-md text-gray-600'>
						{statusFilter !== 'all' &&
							`Status: ${statusFilter.replace('_', ' ')}`}
						{priorityFilter !== 'all' &&
							(statusFilter !== 'all' ? ' | ' : '') +
								`Priority: ${priorityFilter}`}
						{employeeFilter !== 'all' &&
							(statusFilter !== 'all' || priorityFilter !== 'all'
								? ' | '
								: '') +
								`Assignee: ${
									employeeFilter === 'unassigned'
										? 'Unassigned'
										: Array.isArray(employeesList) &&
										  employeesList.find((e) => e.id === employeeFilter)
										? employeesList.find((e) => e.id === employeeFilter)
												?.fullName ||
										  employeesList.find((e) => e.id === employeeFilter)
												?.name ||
										  'Employee ' + employeeFilter
										: 'Unknown'
								}`}
					</p>
				</header>

				<EnhancedTasksContainer
					tasks={filteredTasks}
					isLoading={isLoading}
					error={error}
					onRetry={handleRetry}
				/>

				<footer className='mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500'>
					<p>Report generated on: {new Date().toLocaleDateString()}</p>
					<p>WorkHub - Task Management</p>
				</footer>
			</div>

			{/* Print Styles */}
			<style jsx global>{`
				.print-only {
					display: none;
				}
				@media print {
					.no-print {
						display: none !important;
					}
					.print-only {
						display: block;
					}
					.print-container {
						padding: 1rem;
					}
					.card-print {
						box-shadow: none !important;
						border: none !important;
					}
					.print-description,
					.print-location,
					.print-service-col,
					.print-notes-col {
						max-width: 200px;
						white-space: normal !important;
						word-break: break-word;
					}
					.print-text-wrap {
						word-break: break-word;
						white-space: normal !important;
					}
				}

				@media (max-width: 640px) {
					.overflow-x-auto {
						overflow-x: auto;
					}

					.filter-grid {
						grid-template-columns: 1fr !important;
					}

					.summary-grid {
						grid-template-columns: 1fr 1fr !important;
					}
				}
			`}</style>
		</div>
	);
}

export default function TaskReportPage() {
	return (
		<Suspense
			fallback={<div className='text-center py-10'>Loading report...</div>}>
			<TaskReportContent />
		</Suspense>
	);
}
