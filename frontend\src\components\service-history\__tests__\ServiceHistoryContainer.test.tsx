/**
 * @deprecated These tests are for the deprecated ServiceHistoryContainer component.
 * This file should be removed once the component is removed.
 * See the ticket in docs/tickets/remove-deprecated-service-history-container.md
 */

import {describe, it, expect, vi, beforeEach} from 'vitest';
import {render, screen, waitFor} from '@testing-library/react';
import ServiceHistoryContainer from '../ServiceHistoryContainer';
import * as apiService from '@/lib/apiService';
import type {EnrichedServiceRecord, Vehicle} from '@/lib/types';

// Mock the API service
vi.mock('@/lib/apiService', () => ({
	getAllEnrichedServiceRecords: vi.fn(),
}));

describe('ServiceHistoryContainer', () => {
	const mockVehicles: Vehicle[] = [
		{
			id: 1,
			make: 'Toyota',
			model: 'Camry',
			year: 2020,
			vin: 'ABC123',
			licensePlate: 'XYZ-123',
			ownerName: '<PERSON>',
			ownerContact: '123-456-7890',
			initialOdometer: 0,
			serviceHistory: [],
			createdAt: '2023-01-01',
			updatedAt: '2023-01-01',
		},
		{
			id: 2,
			make: 'Honda',
			model: 'Civic',
			year: 2019,
			vin: 'DEF456',
			licensePlate: 'ABC-789',
			ownerName: 'Jane Smith',
			ownerContact: '987-654-3210',
			initialOdometer: 0,
			serviceHistory: [],
			createdAt: '2023-01-01',
			updatedAt: '2023-01-01',
		},
	];

	const mockEnrichedServiceRecords: EnrichedServiceRecord[] = [
		{
			id: '1',
			vehicleId: '1',
			date: '2023-01-01',
			odometer: 10000,
			servicePerformed: ['Oil Change'],
			notes: 'Regular maintenance',
			cost: 50,
			vehicleMake: 'Toyota',
			vehicleModel: 'Camry',
			vehicleYear: 2020,
			vehiclePlateNumber: 'XYZ-123',
		},
		{
			id: '2',
			vehicleId: '2',
			date: '2023-02-01',
			odometer: 20000,
			servicePerformed: ['Tire Rotation'],
			notes: 'Seasonal maintenance',
			cost: 30,
			vehicleMake: 'Honda',
			vehicleModel: 'Civic',
			vehicleYear: 2019,
			vehiclePlateNumber: 'ABC-789',
		},
	];

	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('should render loading state initially', () => {
		// Setup
		vi.mocked(apiService.getAllEnrichedServiceRecords).mockResolvedValue([]);

		// Execute
		render(<ServiceHistoryContainer />);

		// Verify
		expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
	});

	it('should render service records when loaded', async () => {
		// Setup
		vi.mocked(apiService.getAllEnrichedServiceRecords).mockResolvedValue(
			mockEnrichedServiceRecords
		);

		// Execute
		render(<ServiceHistoryContainer />);

		// Verify
		await waitFor(() => {
			expect(screen.getByText('Toyota Camry (2020)')).toBeInTheDocument();
			expect(screen.getByText('Honda Civic (2019)')).toBeInTheDocument();
			expect(screen.getByText('Oil Change')).toBeInTheDocument();
			expect(screen.getByText('Tire Rotation')).toBeInTheDocument();
		});
	});

	it('should filter records by vehicle ID', async () => {
		// Setup
		vi.mocked(apiService.getAllEnrichedServiceRecords).mockResolvedValue(
			mockEnrichedServiceRecords
		);

		// Execute
		render(<ServiceHistoryContainer selectedVehicleId='1' />);

		// Verify
		await waitFor(() => {
			expect(screen.getByText('Toyota Camry (2020)')).toBeInTheDocument();
			expect(screen.queryByText('Honda Civic (2019)')).not.toBeInTheDocument();
		});
	});

	it('should filter records by service type', async () => {
		// Setup
		vi.mocked(apiService.getAllEnrichedServiceRecords).mockResolvedValue(
			mockEnrichedServiceRecords
		);

		// Execute
		render(<ServiceHistoryContainer selectedServiceType='Oil Change' />);

		// Verify
		await waitFor(() => {
			expect(screen.getByText('Toyota Camry (2020)')).toBeInTheDocument();
			expect(screen.queryByText('Honda Civic (2019)')).not.toBeInTheDocument();
		});
	});

	it('should filter records by search term', async () => {
		// Setup
		vi.mocked(apiService.getAllEnrichedServiceRecords).mockResolvedValue(
			mockEnrichedServiceRecords
		);

		// Execute
		render(<ServiceHistoryContainer searchTerm='Toyota' />);

		// Verify
		await waitFor(() => {
			expect(screen.getByText('Toyota Camry (2020)')).toBeInTheDocument();
			expect(screen.queryByText('Honda Civic (2019)')).not.toBeInTheDocument();
		});
	});

	it('should show error message when API fails', async () => {
		// Setup
		vi.mocked(apiService.getAllEnrichedServiceRecords).mockRejectedValue(
			new Error('API error')
		);

		// Execute
		render(<ServiceHistoryContainer />);

		// Verify
		await waitFor(() => {
			expect(screen.getByText('Error')).toBeInTheDocument();
			expect(
				screen.getByText(
					'Failed to load service records. Please try again later.'
				)
			).toBeInTheDocument();
		});
	});

	it('should show empty state when no records match filters', async () => {
		// Setup
		vi.mocked(apiService.getAllEnrichedServiceRecords).mockResolvedValue([]);

		// Execute
		render(<ServiceHistoryContainer />);

		// Verify
		await waitFor(() => {
			expect(screen.getByText('No Service Records Yet')).toBeInTheDocument();
		});
	});
});
