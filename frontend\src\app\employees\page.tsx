'use client';

import React, {useEffect, useState} from 'react';
import Link from 'next/link';
import {Button} from '@/components/ui/button';
import {
	PlusCircle,
	UsersRound,
	Search,
	RefreshCw,
	ClipboardCheck, // Added from tasks page, might be useful
} from 'lucide-react';
import type {Employee} from '@/lib/types';
import EmployeeListContainer from '@/components/employees/EmployeeListContainer';
import {PageHeader} from '@/components/ui/PageHeader';
import {Input} from '@/components/ui/input';
import {Skeleton} from '@/components/ui/skeleton';
import EmployeeCard from '@/components/employees/EmployeeCard';
import {Card, CardContent} from '@/components/ui/card';
import {Label} from '@/components/ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	EmployeeStatusSchema,
	EmployeeRoleSchema,
} from '@/lib/schemas/employeeSchemas';
import {useToast} from '@/hooks/use-toast';
import {ActionButton} from '@/components/ui/action-button'; // Added
import {DataLoader, SkeletonLoader} from '@/components/ui/loading'; // Added
import ErrorBoundary from '@/components/ErrorBoundary'; // Import ErrorBoundary

// Define the type for the EmployeeListContainer render prop arguments
interface EmployeeListData {
	employees: Employee[];
	loading: boolean;
	error: string | null;
	handleDelete: (id: number) => Promise<void>; // Not used in this component, but part of the interface
	fetchEmployees: () => Promise<void>; // Not used in this component, but part of the interface
	isRefreshing: boolean;
	isConnected: boolean;
	socketTriggered: boolean;
}

function EmployeeCardSkeleton() {
	return (
		<div className='overflow-hidden shadow-lg flex flex-col h-full bg-card border-border/60 rounded-lg'>
			<div className='p-5 flex-grow flex flex-col'>
				<div className='flex justify-between items-start mb-2'>
					<Skeleton className='h-8 w-3/5 bg-muted/50' />
					<Skeleton className='h-5 w-1/4 bg-muted/50 rounded-full' />
				</div>
				<Skeleton className='h-4 w-1/2 mb-1 bg-muted/50' />
				<Skeleton className='h-4 w-1/3 mb-3 bg-muted/50' />
				<Skeleton className='h-px w-full my-3 bg-border/50' />
				<div className='space-y-2.5 flex-grow'>
					{[...Array(3)].map((_, i) => (
						<div key={i} className='flex items-center'>
							<Skeleton className='mr-2.5 h-5 w-5 rounded-full bg-muted/50' />
							<Skeleton className='h-5 w-2/3 bg-muted/50' />
						</div>
					))}
				</div>
			</div>
			<div className='p-4 border-t border-border/60 bg-muted/20'>
				<Skeleton className='h-10 w-full bg-muted/50' />
			</div>
		</div>
	);
}

const EmployeesPageContent = () => {
	const [allEmployeesState, setAllEmployeesState] = useState<Employee[]>([]);
	const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
	const [searchTerm, setSearchTerm] = useState('');
	const [statusFilter, setStatusFilter] = useState<string>('all');
	const [departmentFilter, setDepartmentFilter] = useState<string>('all');
	const [roleFilter, setRoleFilter] = useState<string>('all');
	const {toast} = useToast();

	const uniqueDepartments = Array.from(
		new Set(allEmployeesState.map((e) => e.department))
	).sort();

	useEffect(() => {
		let tempEmployees = [...allEmployeesState];
		const lowercasedSearch = searchTerm.toLowerCase();

		if (statusFilter !== 'all') {
			tempEmployees = tempEmployees.filter(
				(emp) => emp.status === statusFilter
			);
		}
		if (departmentFilter !== 'all') {
			tempEmployees = tempEmployees.filter(
				(emp) => emp.department === departmentFilter
			);
		}
		if (roleFilter !== 'all') {
			tempEmployees = tempEmployees.filter((emp) => emp.role === roleFilter);
		}

		if (lowercasedSearch) {
			tempEmployees = tempEmployees.filter((employee) => {
				return (
					(employee.name || employee.fullName || '')
						.toLowerCase()
						.includes(lowercasedSearch) ||
					(employee.position || '').toLowerCase().includes(lowercasedSearch) ||
					(employee.department || '')
						.toLowerCase()
						.includes(lowercasedSearch) ||
					(employee.role || '').toLowerCase().includes(lowercasedSearch) ||
					(employee.contactEmail || '')
						.toLowerCase()
						.includes(lowercasedSearch) ||
					(employee.skills &&
						employee.skills.some((skill) =>
							(skill || '').toLowerCase().includes(lowercasedSearch)
						)) ||
					(employee.role === 'driver' &&
						employee.availability &&
						(employee.availability || '')
							.toLowerCase()
							.includes(lowercasedSearch)) ||
					(employee.role === 'driver' &&
						employee.currentLocation &&
						(employee.currentLocation || '')
							.toLowerCase()
							.includes(lowercasedSearch))
				);
			});
		}
		setFilteredEmployees(tempEmployees);
	}, [
		searchTerm,
		allEmployeesState,
		statusFilter,
		departmentFilter,
		roleFilter,
	]);

	return (
		<EmployeeListContainer>
			{({
				employees: fetchedEmployees,
				loading,
				error,
				fetchEmployees,
				isRefreshing,
				isConnected,
				socketTriggered,
			}: EmployeeListData) => {
				useEffect(() => {
					if (!loading && !error) {
						const sortedEmployees = [...fetchedEmployees].sort((a, b) =>
							(a.name || a.fullName || '').localeCompare(
								b.name || b.fullName || ''
							)
						);
						setAllEmployeesState(sortedEmployees);
						// setFilteredEmployees(sortedEmployees); // Filter effect will handle this
					}
				}, [fetchedEmployees, loading, error]);

				const handleRefresh = async () => {
					try {
						await fetchEmployees();
						toast({
							title: 'Refresh Complete',
							description: 'Employee list has been updated.',
						});
					} catch (err) {
						console.error('Error refreshing employees:', err);
						toast({
							title: 'Refresh Failed',
							description: 'Could not update employee list. Please try again.',
							variant: 'destructive',
						});
					}
				};

				const activeFilters =
					searchTerm ||
					statusFilter !== 'all' ||
					departmentFilter !== 'all' ||
					roleFilter !== 'all';

				return (
					<div className='space-y-8'>
						<PageHeader
							title='Manage Employees'
							description='Oversee employee profiles, roles, status, and assignments.'
							icon={UsersRound}>
							<div className='flex gap-2'>
								<ActionButton
									actionType='tertiary'
									onClick={handleRefresh}
									isLoading={loading || isRefreshing}
									icon={
										<RefreshCw
											className={`h-4 w-4 ${
												isRefreshing || socketTriggered ? 'animate-spin' : ''
											}`}
										/>
									}
									loadingText={isRefreshing ? 'Updating...' : 'Refresh'}>
									{!(isRefreshing || socketTriggered) && 'Refresh'}
								</ActionButton>
								<ActionButton
									actionType='primary'
									icon={<PlusCircle className='h-4 w-4' />}
									asChild>
									<Link href='/employees/new'>Add New Employee</Link>
								</ActionButton>
							</div>
						</PageHeader>

						<Card className='mb-6 p-4 shadow relative'>
							{(isRefreshing || socketTriggered) && !loading && (
								<div className='absolute right-4 top-4 text-xs flex items-center text-muted-foreground'>
									<RefreshCw className='h-3 w-3 mr-1 animate-spin' />
									{socketTriggered ? 'Real-time update...' : 'Refreshing...'}
								</div>
							)}
							{!loading && (
								<div className='absolute right-24 top-4 text-xs inline-flex items-center sm:right-24'>
									<div
										className={`h-2 w-2 rounded-full mr-2 ${
											isConnected ? 'bg-green-500' : 'bg-gray-400'
										}`}></div>
									<span className='text-muted-foreground text-xs'>
										{isConnected ? 'Real-time active' : 'Real-time inactive'}
									</span>
								</div>
							)}
							<CardContent className='pt-4'>
								<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end'>
									<div className='relative lg:col-span-1'>
										<Label
											htmlFor='search-employees'
											className='block text-sm font-medium text-muted-foreground mb-1'>
											Search Employees
										</Label>
										<Search className='absolute left-3 top-[calc(50%_-_0.5rem_+_12px)] h-5 w-5 text-muted-foreground' />
										<Input
											id='search-employees'
											type='text'
											placeholder='Name, Role, Dept, Skill...'
											value={searchTerm}
											onChange={(e) => setSearchTerm(e.target.value)}
											className='pl-10 w-full'
										/>
									</div>
									<div>
										<Label
											htmlFor='status-filter'
											className='block text-sm font-medium text-muted-foreground mb-1'>
											Filter by Status
										</Label>
										<Select
											value={statusFilter}
											onValueChange={setStatusFilter}>
											<SelectTrigger id='status-filter'>
												<SelectValue placeholder='All Statuses' />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value='all'>All Statuses</SelectItem>
												{EmployeeStatusSchema.options.map((s) => (
													<SelectItem key={s} value={s}>
														{s}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
									<div>
										<Label
											htmlFor='department-filter'
											className='block text-sm font-medium text-muted-foreground mb-1'>
											Filter by Department
										</Label>
										<Select
											value={departmentFilter}
											onValueChange={setDepartmentFilter}>
											<SelectTrigger id='department-filter'>
												<SelectValue placeholder='All Departments' />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value='all'>All Departments</SelectItem>
												{uniqueDepartments.map((dep) => (
													<SelectItem key={dep} value={dep}>
														{dep}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
									<div>
										<Label
											htmlFor='role-filter'
											className='block text-sm font-medium text-muted-foreground mb-1'>
											Filter by Role
										</Label>
										<Select value={roleFilter} onValueChange={setRoleFilter}>
											<SelectTrigger id='role-filter'>
												<SelectValue placeholder='All Roles' />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value='all'>All Roles</SelectItem>
												{EmployeeRoleSchema.options.map((role) => (
													<SelectItem key={role} value={role}>
														{role.charAt(0).toUpperCase() +
															role.slice(1).replace('_', ' ')}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
								</div>
							</CardContent>
						</Card>
						<DataLoader
							isLoading={loading}
							error={error}
							data={filteredEmployees}
							onRetry={fetchEmployees}
							loadingComponent={
								<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
									{[...Array(3)].map((_, i) => (
										<EmployeeCardSkeleton key={i} />
									))}
								</div>
							}
							emptyComponent={
								<div className='text-center py-12 bg-card rounded-lg shadow-md'>
									<UsersRound className='mx-auto h-16 w-16 text-muted-foreground mb-6' />
									<h3 className='text-2xl font-semibold text-foreground mb-2'>
										{activeFilters
											? 'No Employees Match Your Filters'
											: 'No Employees Registered Yet'}
									</h3>
									<p className='text-muted-foreground mt-2 mb-6 max-w-md mx-auto'>
										{activeFilters
											? 'Try adjusting your search or filter criteria.'
											: 'Get started by adding an employee.'}
									</p>
									{!activeFilters && (
										<ActionButton
											actionType='primary'
											size='lg'
											icon={<PlusCircle className='h-4 w-4' />}
											asChild>
											<Link href='/employees/add'>Add Your First Employee</Link>
										</ActionButton>
									)}
								</div>
							}>
							{(employeeData) => (
								<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
									{employeeData.map((employee) => (
										<EmployeeCard key={employee.id} employee={employee} />
									))}
								</div>
							)}
						</DataLoader>
					</div>
				);
			}}
		</EmployeeListContainer>
	);
};

export default function EmployeesPage() {
	return (
		<ErrorBoundary>
			<EmployeesPageContent />
		</ErrorBoundary>
	);
}
