"use strict";exports.id=3860,exports.ids=[3860],exports.modules={1814:(e,l,a)=>{a.d(l,{O2:()=>i,Q:()=>n,gT:()=>t});var s=a(45880),r=a(74880);let i=s.k5(["Active","On Leave","Terminated","Inactive"]),n=s.k5(["driver","mechanic","administrator","office_staff","manager","service_advisor","technician","other"]),t=s.Ik({name:s.Yj().min(1,"Name is required"),fullName:s.Yj().min(1,"Full name is required").optional(),employeeId:s.Yj().min(1,"Employee ID (unique business ID) is required"),position:s.Yj().min(1,"Position/Title is required"),department:s.Yj().min(1,"Department is required"),contactInfo:s.Yj().min(1,"Primary contact (email or phone) is required").refine(e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)||/^[\d\s()+-]{7,20}$/.test(e),"Must be a valid email or phone number."),contactEmail:s.Yj().email("Invalid email address").optional().nullable().or(s.eu("")),contactPhone:s.Yj().optional().nullable().or(s.eu("")),contactMobile:s.Yj().optional().nullable().or(s.eu("")),hireDate:s.Yj().refine(e=>e&&!isNaN(Date.parse(e)),{message:"Invalid hire date"}),status:i.default("Active"),role:n.default("other"),availability:r.X.optional().nullable(),currentLocation:s.Yj().optional().or(s.eu("")),workingHours:s.Yj().optional().or(s.eu("")),assignedVehicleId:s.ai().int().positive().nullable().optional(),skills:s.YO(s.Yj()).optional().default([]),shiftSchedule:s.Yj().optional().or(s.eu("")),generalAssignments:s.YO(s.Yj()).optional().default([]),notes:s.Yj().optional().or(s.eu("")),profileImageUrl:s.Yj().url("Invalid URL for profile image").optional().or(s.eu("")),statusChangeReason:s.Yj().optional().nullable()})},15079:(e,l,a)=>{a.d(l,{bq:()=>h,eb:()=>j,gC:()=>u,l6:()=>c,yv:()=>m});var s=a(60687),r=a(43210),i=a(22670),n=a(61662),t=a(89743),o=a(58450),d=a(4780);let c=i.bL;i.YJ;let m=i.WT,h=r.forwardRef(({className:e,children:l,...a},r)=>(0,s.jsxs)(i.l9,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[l,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));h.displayName=i.l9.displayName;let x=r.forwardRef(({className:e,...l},a)=>(0,s.jsx)(i.PP,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...l,children:(0,s.jsx)(t.A,{className:"h-4 w-4"})}));x.displayName=i.PP.displayName;let p=r.forwardRef(({className:e,...l},a)=>(0,s.jsx)(i.wn,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...l,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));p.displayName=i.wn.displayName;let u=r.forwardRef(({className:e,children:l,position:a="popper",...r},n)=>(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:n,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,s.jsx)(x,{}),(0,s.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,s.jsx)(p,{})]})}));u.displayName=i.UC.displayName,r.forwardRef(({className:e,...l},a)=>(0,s.jsx)(i.JU,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...l})).displayName=i.JU.displayName;let j=r.forwardRef(({className:e,children:l,...a},r)=>(0,s.jsxs)(i.q7,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:l})]}));j.displayName=i.q7.displayName,r.forwardRef(({className:e,...l},a)=>(0,s.jsx)(i.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...l})).displayName=i.wv.displayName},34729:(e,l,a)=>{a.d(l,{T:()=>n});var s=a(60687),r=a(43210),i=a(4780);let n=r.forwardRef(({className:e,...l},a)=>(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...l}));n.displayName="Textarea"},35950:(e,l,a)=>{a.d(l,{w:()=>t});var s=a(60687),r=a(43210),i=a(62369),n=a(4780);let t=r.forwardRef(({className:e,orientation:l="horizontal",decorative:a=!0,...r},t)=>(0,s.jsx)(i.b,{ref:t,decorative:a,orientation:l,className:(0,n.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",e),...r}));t.displayName=i.b.displayName},37716:(e,l,a)=>{a.d(l,{A:()=>v});var s=a(60687),r=a(43210),i=a(16189),n=a(27605),t=a(63442),o=a(1814),d=a(74880),c=a(28840),m=a(29523),h=a(89667),x=a(34729),p=a(44493),u=a(15079),j=a(35950),f=a(71669),g=a(29867);let v=({onSubmit:e,initialData:l={},isEditing:a=!1,submitButtonText:v=a?"Save Changes":"Create Employee",isLoading:y=!1})=>{let b=(0,i.useRouter)(),{toast:N}=(0,g.dj)(),[I,C]=(0,r.useState)([]),w=(0,n.mN)({resolver:(0,t.u)(o.gT),defaultValues:{name:l?.name||l?.fullName||"",fullName:l?.fullName||l?.name||"",employeeId:l?.employeeId||"",position:l?.position||"",department:l?.department||"",contactInfo:l?.contactInfo||l?.contactEmail||"",contactEmail:l?.contactEmail||"",contactPhone:l?.contactPhone||"",contactMobile:l?.contactMobile||"",hireDate:l?.hireDate?new Date(l.hireDate).toISOString().split("T")[0]:"",status:l?.status||"Active",role:l?.role||"other",availability:l?.availability||null,currentLocation:l?.currentLocation||"",workingHours:l?.workingHours||"",assignedVehicleId:l?.assignedVehicleId?Number(l.assignedVehicleId):l?.vehicleId?Number(l.vehicleId):null,skills:l?.skills||[],shiftSchedule:l?.shiftSchedule||"",generalAssignments:l?.generalAssignments||[],notes:l?.notes||"",profileImageUrl:l?.profileImageUrl||""}}),{reset:R,control:S,handleSubmit:M,watch:k,formState:{errors:A,isSubmitting:B}}=w,z=JSON.stringify(l);(0,r.useEffect)(()=>{a&&l&&Object.keys(l).length>0&&R({name:l.name||l.fullName||"",fullName:l.fullName||l.name||"",employeeId:l.employeeId||"",position:l.position||"",department:l.department||"",contactInfo:l.contactInfo||l.contactEmail||"",contactEmail:l.contactEmail||"",contactPhone:l.contactPhone||"",contactMobile:l.contactMobile||"",hireDate:l.hireDate?new Date(l.hireDate).toISOString().split("T")[0]:"",status:l.status||"Active",role:l.role||"other",availability:l.availability||null,currentLocation:l.currentLocation||"",workingHours:l.workingHours||"",assignedVehicleId:l.assignedVehicleId?Number(l.assignedVehicleId):l?.vehicleId?Number(l.vehicleId):null,skills:l.skills||[],shiftSchedule:l.shiftSchedule||"",generalAssignments:l.generalAssignments||[],notes:l.notes||"",profileImageUrl:l.profileImageUrl||""})},[z,a,R]),(0,r.useEffect)(()=>{(async()=>{try{let e=await (0,c.getVehicles)();C(e||[])}catch(e){console.error("Failed to fetch vehicles for employee form:",e),N({title:"Warning",description:"Could not load vehicles for assignment.",variant:"default"})}})()},[N]);let D=k("role"),J=async l=>{let a={...l,assignedVehicleId:l.assignedVehicleId?Number(l.assignedVehicleId):null,skills:Array.isArray(l.skills)?l.skills.map(String):"string"==typeof l.skills?l.skills.split(",").map(e=>e.trim()).filter(Boolean):[],generalAssignments:Array.isArray(l.generalAssignments)?l.generalAssignments.map(String):"string"==typeof l.generalAssignments?l.generalAssignments.split(",").map(e=>e.trim()).filter(Boolean):[]};await e(a)};return(0,s.jsx)(f.lV,{...w,children:(0,s.jsx)("form",{onSubmit:M(J),children:(0,s.jsxs)(p.Zp,{className:"max-w-3xl mx-auto",children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{children:a?"Edit Employee":"Add New Employee"}),(0,s.jsx)(p.BT,{children:a?"Update the details of the employee.":"Enter the details for the new employee."})]}),(0,s.jsxs)(p.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(f.zB,{control:S,name:"name",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Display Name"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., Jane D.",...e})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S,name:"fullName",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Full Name (Optional)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., Jane Marie Doe",...e})}),(0,s.jsx)(f.C5,{})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(f.zB,{control:S,name:"employeeId",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Employee ID (Business Key)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., EMP12345",...e})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S,name:"position",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Position/Title"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., Senior Mechanic",...e})}),(0,s.jsx)(f.C5,{})]})})]}),(0,s.jsx)(f.zB,{control:S,name:"department",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Department"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., Maintenance",...e})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(j.w,{className:"my-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Contact Information"}),(0,s.jsx)(f.zB,{control:S,name:"contactInfo",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Primary Contact (Email/Phone)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., <EMAIL> or 555-0101",...e})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(f.zB,{control:S,name:"contactEmail",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Contact Email (Optional)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{type:"email",placeholder:"e.g., <EMAIL>",...e,value:e.value||""})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S,name:"contactPhone",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Contact Phone (Optional)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{type:"tel",placeholder:"e.g., 555-0102",...e,value:e.value||""})}),(0,s.jsx)(f.C5,{})]})})]}),(0,s.jsx)(f.zB,{control:S,name:"contactMobile",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Contact Mobile (Optional)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{type:"tel",placeholder:"e.g., 555-0103",...e,value:e.value||""})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(j.w,{className:"my-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Employment Details"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(f.zB,{control:S,name:"hireDate",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Hire Date"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{type:"date",...e})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S,name:"status",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Status"}),(0,s.jsxs)(u.l6,{onValueChange:e.onChange,value:e.value??void 0,children:[(0,s.jsx)(f.MJ,{children:(0,s.jsx)(u.bq,{id:"status",children:(0,s.jsx)(u.yv,{placeholder:"Select status"})})}),(0,s.jsx)(u.gC,{children:o.O2.options.map(e=>(0,s.jsx)(u.eb,{value:e,children:e},e))})]}),(0,s.jsx)(f.C5,{})]})})]}),(0,s.jsx)(j.w,{className:"my-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Role & Availability"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(f.zB,{control:S,name:"role",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Role"}),(0,s.jsxs)(u.l6,{onValueChange:e.onChange,value:e.value??void 0,children:[(0,s.jsx)(f.MJ,{children:(0,s.jsx)(u.bq,{id:"role",children:(0,s.jsx)(u.yv,{placeholder:"Select role"})})}),(0,s.jsx)(u.gC,{children:o.Q.options.map(e=>(0,s.jsx)(u.eb,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1).replace("_"," ")},e))})]}),(0,s.jsx)(f.C5,{})]})}),"driver"===D&&(0,s.jsx)(f.zB,{control:S,name:"availability",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Availability (for Drivers)"}),(0,s.jsxs)(u.l6,{onValueChange:e.onChange,value:e.value??void 0,children:[(0,s.jsx)(f.MJ,{children:(0,s.jsx)(u.bq,{id:"availability",children:(0,s.jsx)(u.yv,{placeholder:"Select availability"})})}),(0,s.jsx)(u.gC,{children:d.X.options.map(e=>(0,s.jsx)(u.eb,{value:e,children:e.replace("_"," ")},e))})]}),(0,s.jsx)(f.C5,{})]})})]}),"driver"===D&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(f.zB,{control:S,name:"currentLocation",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Current Location (Optional, for Drivers)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., City, State or GPS link",...e,value:e.value||""})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S,name:"workingHours",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Working Hours (Optional, for Drivers)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., Mon-Fri 9am-5pm",...e,value:e.value||""})}),(0,s.jsx)(f.C5,{})]})})]}),(0,s.jsx)(f.zB,{control:S,name:"assignedVehicleId",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Assigned Vehicle (Optional, for Drivers)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsxs)(u.l6,{onValueChange:l=>e.onChange("null"===l?null:parseInt(l,10)),value:null===e.value?"null":String(e.value??""),children:[(0,s.jsx)(f.MJ,{children:(0,s.jsx)(u.bq,{id:"assignedVehicleId",children:(0,s.jsx)(u.yv,{placeholder:"Select vehicle (optional)"})})}),(0,s.jsxs)(u.gC,{children:[(0,s.jsx)(u.eb,{value:"null",children:"No Vehicle"}),I.map(e=>(0,s.jsxs)(u.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,") - ",e.licensePlate]},e.id))]})]})}),(0,s.jsx)(f.C5,{})]})})]}),(0,s.jsx)(j.w,{className:"my-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Other Details"}),(0,s.jsx)(f.zB,{control:S,name:"skills",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Skills (comma-separated)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., Diesel Engine Repair, HVAC Systems, Welding",value:Array.isArray(e.value)?e.value.join(", "):"string"==typeof e.value?e.value:"",onChange:l=>{let a=l.target.value.split(",").map(e=>e.trim()).filter(Boolean);e.onChange(a)}})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S,name:"shiftSchedule",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Shift Schedule (Optional)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., Mon-Wed 8am-4pm, Thu-Fri 10am-6pm",...e,value:e.value||""})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S,name:"generalAssignments",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"General Assignments (comma-separated, Optional)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"e.g., Workshop Cleanup, Inventory Check",value:Array.isArray(e.value)?e.value.join(", "):"string"==typeof e.value?e.value:"",onChange:l=>{let a=l.target.value.split(",").map(e=>e.trim()).filter(Boolean);e.onChange(a)}})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S,name:"profileImageUrl",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Profile Image URL (Optional)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"https://example.com/profile.png",...e,value:e.value||""})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S,name:"notes",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{children:"Notes (Optional)"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(x.T,{placeholder:"Any additional notes about the employee...",...e,value:e.value||""})}),(0,s.jsx)(f.C5,{})]})})]}),(0,s.jsxs)(p.wL,{className:"flex justify-end space-x-3 pt-6",children:[(0,s.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>b.back(),disabled:w.formState.isSubmitting||y,children:"Cancel"}),(0,s.jsx)(m.$,{type:"submit",disabled:w.formState.isSubmitting||y,children:w.formState.isSubmitting||y?"Processing...":v})]})]})})})}},48041:(e,l,a)=>{a.d(l,{z:()=>r});var s=a(60687);function r({title:e,description:l,icon:a,children:r}){return(0,s.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[a&&(0,s.jsx)(a,{className:"h-8 w-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),l&&(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:l})]}),r&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:r})]})}a(43210)},62369:(e,l,a)=>{a.d(l,{b:()=>d});var s=a(43210),r=a(14163),i=a(60687),n="horizontal",t=["horizontal","vertical"],o=s.forwardRef((e,l)=>{var a;let{decorative:s,orientation:o=n,...d}=e,c=(a=o,t.includes(a))?o:n;return(0,i.jsx)(r.sG.div,{"data-orientation":c,...s?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:l})});o.displayName="Separator";var d=o},71669:(e,l,a)=>{a.d(l,{C5:()=>f,MJ:()=>j,eI:()=>p,lR:()=>u,lV:()=>d,zB:()=>m});var s=a(60687),r=a(43210),i=a(8730),n=a(27605),t=a(4780),o=a(80013);let d=n.Op,c=r.createContext({}),m=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(n.xI,{...e})}),h=()=>{let e=r.useContext(c),l=r.useContext(x),{getFieldState:a,formState:s}=(0,n.xW)(),i=a(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:t}=l;return{id:t,name:e.name,formItemId:`${t}-form-item`,formDescriptionId:`${t}-form-item-description`,formMessageId:`${t}-form-item-message`,...i}},x=r.createContext({}),p=r.forwardRef(({className:e,...l},a)=>{let i=r.useId();return(0,s.jsx)(x.Provider,{value:{id:i},children:(0,s.jsx)("div",{ref:a,className:(0,t.cn)("space-y-2",e),...l})})});p.displayName="FormItem";let u=r.forwardRef(({className:e,...l},a)=>{let{error:r,formItemId:i}=h();return(0,s.jsx)(o.J,{ref:a,className:(0,t.cn)(r&&"text-destructive",e),htmlFor:i,...l})});u.displayName="FormLabel";let j=r.forwardRef(({...e},l)=>{let{error:a,formItemId:r,formDescriptionId:n,formMessageId:t}=h();return(0,s.jsx)(i.DX,{ref:l,id:r,"aria-describedby":a?`${n} ${t}`:`${n}`,"aria-invalid":!!a,...e})});j.displayName="FormControl",r.forwardRef(({className:e,...l},a)=>{let{formDescriptionId:r}=h();return(0,s.jsx)("p",{ref:a,id:r,className:(0,t.cn)("text-sm text-muted-foreground",e),...l})}).displayName="FormDescription";let f=r.forwardRef(({className:e,children:l,...a},r)=>{let{error:i,formMessageId:n}=h(),o=i?String(i?.message??""):l;return o?(0,s.jsx)("p",{ref:r,id:n,className:(0,t.cn)("text-sm font-medium text-destructive",e),...a,children:o}):null});f.displayName="FormMessage"},74880:(e,l,a)=>{a.d(l,{X:()=>s});let s=a(45880).k5(["On_Shift","Off_Shift","On_Break","Busy"])}};