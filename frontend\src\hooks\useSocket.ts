import {useEffect, useState, useRef, useCallback} from 'react';
import {io, Socket} from 'socket.io-client';

// Socket.io event names - must match backend
export const SOCKET_EVENTS = {
	VEHICLE_UPDATED: 'vehicle:updated',
	VEHICLE_CREATED: 'vehicle:created',
	VEHICLE_DELETED: 'vehicle:deleted',
	EMPLOYEE_UPDATED: 'employee:updated',
	EMPLOYEE_CREATED: 'employee:created',
	EMPLOYEE_DELETED: 'employee:deleted',
	REFRESH_VEHICLES: 'refresh:vehicles',
	REFRESH_EMPLOYEES: 'refresh:employees',
};

interface UseSocketOptions {
	url?: string;
	autoConnect?: boolean;
}

/**
 * Hook to manage Socket.io connection and events
 */
export function useSocket(options: UseSocketOptions = {}) {
	const {
		url = process.env.NEXT_PUBLIC_API_BASE_URL?.replace('/api', '') ||
			'http://localhost:4000',
		autoConnect = true,
	} = options;

	const [isConnected, setIsConnected] = useState(false);
	const [error, setError] = useState<Error | null>(null);
	const socketRef = useRef<Socket | null>(null);

	// Initialize the socket connection
	useEffect(() => {
		if (!socketRef.current && autoConnect) {
			try {
				socketRef.current = io(url, {
					reconnectionAttempts: 5,
					reconnectionDelay: 1000,
					autoConnect: true,
				});

				socketRef.current.on('connect', () => {
					setIsConnected(true);
					setError(null);
					console.log('Socket connected:', socketRef.current?.id);
				});

				socketRef.current.on('disconnect', (reason) => {
					setIsConnected(false);
					console.log('Socket disconnected:', reason);
				});

				socketRef.current.on('connect_error', (err) => {
					setError(err);
					console.error('Socket connection error:', err);
				});
			} catch (err) {
				setError(
					err instanceof Error ? err : new Error('Unknown socket error')
				);
				console.error('Error initializing socket:', err);
			}
		}

		// Cleanup on unmount
		return () => {
			if (socketRef.current) {
				socketRef.current.disconnect();
				socketRef.current = null;
				setIsConnected(false);
			}
		};
	}, [url, autoConnect]);

	// Subscribe to an event
	const subscribe = useCallback(
		(event: string, callback: (...args: any[]) => void) => {
			if (!socketRef.current) return () => {};

			socketRef.current.on(event, callback);
			return () => {
				socketRef.current?.off(event, callback);
			};
		},
		[]
	);

	// Manually connect
	const connect = useCallback(() => {
		if (!socketRef.current) {
			try {
				socketRef.current = io(url);
				socketRef.current.connect();
			} catch (err) {
				setError(err instanceof Error ? err : new Error('Failed to connect'));
			}
		} else if (!socketRef.current.connected) {
			socketRef.current.connect();
		}
	}, [url]);

	// Manually disconnect
	const disconnect = useCallback(() => {
		if (socketRef.current) {
			socketRef.current.disconnect();
		}
	}, []);

	return {
		socket: socketRef.current,
		isConnected,
		error,
		subscribe,
		connect,
		disconnect,
	};
}
