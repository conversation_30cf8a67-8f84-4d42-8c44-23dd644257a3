(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8880],{15300:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},17649:(e,t,a)=>{"use strict";a.d(t,{UC:()=>F,VY:()=>B,ZD:()=>q,ZL:()=>E,bL:()=>R,hE:()=>I,hJ:()=>Z,l9:()=>L,rc:()=>H});var r=a(12115),s=a(46081),l=a(6101),i=a(15452),n=a(85185),d=a(99708),o=a(95155),c="AlertDialog",[u,h]=(0,s.A)(c,[i.Hs]),m=(0,i.Hs)(),p=e=>{let{__scopeAlertDialog:t,...a}=e,r=m(t);return(0,o.jsx)(i.bL,{...r,...a,modal:!0})};p.displayName=c;var x=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,s=m(a);return(0,o.jsx)(i.l9,{...s,...r,ref:t})});x.displayName="AlertDialogTrigger";var g=e=>{let{__scopeAlertDialog:t,...a}=e,r=m(t);return(0,o.jsx)(i.ZL,{...r,...a})};g.displayName="AlertDialogPortal";var f=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,s=m(a);return(0,o.jsx)(i.hJ,{...s,...r,ref:t})});f.displayName="AlertDialogOverlay";var y="AlertDialogContent",[v,j]=u(y),b=(0,d.Dc)("AlertDialogContent"),N=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,children:s,...d}=e,c=m(a),u=r.useRef(null),h=(0,l.s)(t,u),p=r.useRef(null);return(0,o.jsx)(i.G$,{contentName:y,titleName:A,docsSlug:"alert-dialog",children:(0,o.jsx)(v,{scope:a,cancelRef:p,children:(0,o.jsxs)(i.UC,{role:"alertdialog",...c,...d,ref:h,onOpenAutoFocus:(0,n.m)(d.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=p.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,o.jsx)(b,{children:s}),(0,o.jsx)(P,{contentRef:u})]})})})});N.displayName=y;var A="AlertDialogTitle",k=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,s=m(a);return(0,o.jsx)(i.hE,{...s,...r,ref:t})});k.displayName=A;var D="AlertDialogDescription",w=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,s=m(a);return(0,o.jsx)(i.VY,{...s,...r,ref:t})});w.displayName=D;var M=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,s=m(a);return(0,o.jsx)(i.bm,{...s,...r,ref:t})});M.displayName="AlertDialogAction";var C="AlertDialogCancel",T=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,{cancelRef:s}=j(C,a),n=m(a),d=(0,l.s)(t,s);return(0,o.jsx)(i.bm,{...n,...r,ref:d})});T.displayName=C;var P=e=>{let{contentRef:t}=e,a="`".concat(y,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(y,"` by passing a `").concat(D,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(y,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(a)},[a,t]),null},R=p,L=x,E=g,Z=f,F=N,H=M,q=T,I=k,B=w},18763:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},24865:(e,t,a)=>{"use strict";a.d(t,{M:()=>o});var r=a(95155);a(12115);var s=a(6874),l=a.n(s),i=a(15300),n=a(61840),d=a(6560);function o(e){let{href:t,getReportUrl:a,isList:s=!1,className:o}=e;if(!t&&!a)return console.error("ViewReportButton requires either href or getReportUrl prop"),null;let c=s?"View List Report":"View Report";return t?(0,r.jsx)(d.r,{actionType:"secondary",asChild:!0,icon:(0,r.jsx)(i.A,{className:"h-4 w-4"}),className:o,children:(0,r.jsxs)(l(),{href:t,target:"_blank",rel:"noopener noreferrer",children:[c,(0,r.jsx)(n.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"}),(0,r.jsx)("span",{className:"sr-only",children:"(opens in new tab)"})]})}):(0,r.jsxs)(d.r,{actionType:"secondary",onClick:()=>{if(a){let e=a();window.open(e,"_blank","noopener,noreferrer")}},icon:(0,r.jsx)(i.A,{className:"h-4 w-4"}),className:o,children:[c,(0,r.jsx)(n.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"})]})}},28987:(e,t,a)=>{Promise.resolve().then(a.bind(a,94722))},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},37648:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},50594:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},57082:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58260:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},61840:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},74465:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("PlaneTakeoff",[["path",{d:"M2 22h20",key:"272qi7"}],["path",{d:"M6.36 17.4 4 17l-2-4 1.1-.55a2 2 0 0 1 1.8 0l.17.1a2 2 0 0 0 1.8 0L8 12 5 6l.9-.45a2 2 0 0 1 2.09.2l4.02 3a2 2 0 0 0 2.1.2l4.19-2.06a2.41 2.41 0 0 1 1.73-.17L21 7a1.4 1.4 0 0 1 .87 1.99l-.38.76c-.23.46-.6.84-1.07 1.08L7.58 17.2a2 2 0 0 1-1.22.18Z",key:"fkigj9"}]])},83082:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("PlaneLanding",[["path",{d:"M2 22h20",key:"272qi7"}],["path",{d:"M3.77 10.77 2 9l2-4.5 1.1.55c.55.28.9.84.9 1.45s.35 1.17.9 1.45L8 8.5l3-6 1.05.53a2 2 0 0 1 1.09 1.52l.72 5.4a2 2 0 0 0 1.09 1.52l4.4 2.2c.42.22.78.55 1.01.96l.6 1.03c.49.88-.06 1.98-1.06 2.1l-1.18.15c-.47.06-.95-.02-1.37-.24L4.29 11.15a2 2 0 0 1-.52-.38Z",key:"1ma21e"}]])},83662:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},94722:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>q});var r=a(95155),s=a(12115),l=a(35695),i=a(6874),n=a.n(i),d=a(66766),o=a(6560),c=a(66695),u=a(57082),h=a(12543),m=a(18763),p=a(77223),x=a(83662),g=a(98328),f=a(50286),y=a(50594),v=a(58260),j=a(83082),b=a(37648),N=a(74465),A=a(2730),k=a(95647),D=a(87481),w=a(26126),M=a(59434),C=a(73168),T=a(83343),P=a(99673),R=a(90010),L=a(77023),E=a(24865);let Z=e=>{switch(e){case"Planned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";case"Confirmed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Completed":return"bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20";case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},F=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return(0,C.GP)((0,T.H)(e),t?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return"Invalid Date"}};function H(e){let{icon:t,label:a,value:s,children:l,valueClassName:i}=e;return s||l?(0,r.jsxs)("div",{className:"flex items-start py-2",children:[(0,r.jsx)(t,{className:"h-5 w-5 text-accent mr-3 mt-0.5 shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:a}),s&&(0,r.jsx)("p",{className:(0,M.cn)("text-base font-semibold text-foreground",i),children:s}),l]})]}):null}function q(){let e=(0,l.useParams)(),t=(0,l.useRouter)(),{toast:a}=(0,D.dj)(),[i,C]=(0,s.useState)(null),[T,q]=(0,s.useState)(!0),[I,B]=(0,s.useState)(null),_=e.id,S=(0,s.useCallback)(async()=>{q(!0),B(null);try{if(_){let e=await (0,A.getDelegationById)(_);e?(e.statusHistory.sort((e,t)=>new Date(t.changedAt).getTime()-new Date(e.changedAt).getTime()),C(e)):(B("Delegation not found."),a({title:"Error",description:"Delegation not found.",variant:"destructive"}))}}catch(t){console.error("Error fetching delegation:",t);let e=t instanceof Error?t.message:"Failed to load delegation details.";B(e),a({title:"Error",description:e,variant:"destructive"})}finally{q(!1)}},[_,a]);(0,s.useEffect)(()=>{S()},[S]);let z=async()=>{if(i)try{await (0,A.deleteDelegation)(i.id),a({title:"Delegation Deleted",description:'Delegation "'.concat(i.eventName,'" has been deleted.')}),t.push("/delegations")}catch(e){console.error("Error deleting delegation:",e),a({title:"Error",description:"Failed to delete delegation. Please try again.",variant:"destructive"})}};return(0,r.jsx)("div",{className:"space-y-8",children:(0,r.jsx)(L.gO,{isLoading:T,error:I,data:i,onRetry:S,loadingComponent:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(k.z,{title:"Loading Delegation...",icon:u.A}),(0,r.jsx)(L.jt,{variant:"card",count:1}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 items-start",children:[(0,r.jsx)(L.jt,{variant:"card",count:1,className:"md:col-span-2"}),(0,r.jsx)(L.jt,{variant:"card",count:1})]})]}),emptyComponent:(0,r.jsxs)("div",{className:"text-center py-10",children:[(0,r.jsx)(k.z,{title:"Delegation Not Found",icon:u.A}),(0,r.jsx)("p",{className:"mb-4",children:"The requested delegation could not be found."}),(0,r.jsx)(o.r,{actionType:"primary",onClick:()=>t.push("/delegations"),icon:(0,r.jsx)(h.A,{className:"h-4 w-4"}),children:"Back to List"})]}),children:e=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(k.z,{title:e.eventName,icon:u.A,children:(0,r.jsxs)("div",{className:"flex gap-2 items-center flex-wrap",children:[(0,r.jsx)(o.r,{actionType:"tertiary",onClick:()=>t.push("/delegations"),icon:(0,r.jsx)(h.A,{className:"h-4 w-4"}),children:"Back to List"}),(0,r.jsx)(o.r,{actionType:"secondary",asChild:!0,icon:(0,r.jsx)(m.A,{className:"h-4 w-4"}),children:(0,r.jsx)(n(),{href:"/delegations/".concat(e.id,"/edit"),children:"Edit"})}),(0,r.jsx)(E.M,{href:"/delegations/".concat(e.id,"/report")}),(0,r.jsxs)(R.Lt,{children:[(0,r.jsx)(R.tv,{asChild:!0,children:(0,r.jsx)(o.r,{actionType:"danger",icon:(0,r.jsx)(p.A,{className:"h-4 w-4"}),children:"Delete Delegation"})}),(0,r.jsxs)(R.EO,{children:[(0,r.jsxs)(R.wd,{children:[(0,r.jsx)(R.r7,{children:"Are you sure?"}),(0,r.jsx)(R.$v,{children:"This action cannot be undone. This will permanently delete the delegation and all its related information."})]}),(0,r.jsxs)(R.ck,{children:[(0,r.jsx)(R.Zr,{children:"Cancel"}),(0,r.jsx)(R.Rx,{onClick:z,className:"bg-destructive hover:bg-destructive/90",children:"Delete"})]})]})]})]})})," ",(0,r.jsxs)(c.Zp,{className:"shadow-lg bg-card p-0",children:[(0,r.jsx)(c.aR,{className:"border-b p-6",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.ZB,{className:"text-2xl font-bold text-primary mb-1",children:e.eventName}),(0,r.jsx)(c.BT,{className:"text-muted-foreground",children:"Detailed overview of the delegation."})]}),(0,r.jsx)(w.E,{className:(0,M.cn)("text-sm py-1 px-3 font-semibold",Z(e.status)),children:(0,P.fZ)(e.status)})]})}),(0,r.jsxs)(c.Wu,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4",children:[(0,r.jsxs)("div",{className:"lg:col-span-1 space-y-1",children:[(0,r.jsx)(H,{icon:x.A,label:"Location",value:e.location}),(0,r.jsx)(H,{icon:g.A,label:"Duration",value:"".concat(F(e.durationFrom)," - ").concat(F(e.durationTo))}),e.invitationFrom&&(0,r.jsx)(H,{icon:f.A,label:"Invitation From",value:e.invitationFrom}),e.invitationTo&&(0,r.jsx)(H,{icon:f.A,label:"Invitation To",value:e.invitationTo}),e.notes&&(0,r.jsx)(H,{icon:y.A,label:"General Notes",value:e.notes,valueClassName:"whitespace-pre-wrap"})]}),(0,r.jsx)("div",{className:"relative aspect-[16/10] w-full md:col-span-2 lg:col-span-2 rounded-lg overflow-hidden mb-4 lg:mb-0 shadow-sm",children:(0,r.jsx)(d.default,{src:e.imageUrl||"https://picsum.photos/seed/".concat(e.id,"/600/375"),alt:e.eventName,layout:"fill",objectFit:"cover",className:"bg-muted","data-ai-hint":"event venue",priority:!0})})]})]}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6 items-start",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[" ",(0,r.jsxs)(c.Zp,{className:"shadow-md bg-card p-0",children:[(0,r.jsx)(c.aR,{className:"p-6",children:(0,r.jsxs)(c.ZB,{className:"text-xl font-semibold text-primary flex items-center",children:[(0,r.jsx)(f.A,{className:"mr-2 h-5 w-5 text-accent"})," Delegates (",e.delegates.length,")"]})}),(0,r.jsx)(c.Wu,{children:e.delegates.length>0?(0,r.jsx)("div",{className:"space-y-3",children:e.delegates.map(e=>(0,r.jsxs)("div",{className:"p-3 border rounded-md bg-background",children:[(0,r.jsx)("p",{className:"font-semibold text-foreground",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.title}),e.notes&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1 italic",children:e.notes})]},e.id))}):(0,r.jsx)("p",{className:"text-muted-foreground",children:"No delegates assigned to this delegation."})})]}),(e.flightArrivalDetails||e.flightDepartureDetails)&&(0,r.jsxs)(c.Zp,{className:"shadow-md bg-card p-0",children:[(0,r.jsx)(c.aR,{className:"p-6",children:(0,r.jsxs)(c.ZB,{className:"text-xl font-semibold text-primary flex items-center",children:[(0,r.jsx)(v.A,{className:"mr-2 h-5 w-5 text-accent"})," Flight Information"]})}),(0,r.jsxs)(c.Wu,{className:"grid md:grid-cols-2 gap-6",children:[e.flightArrivalDetails&&(0,r.jsxs)("div",{className:"space-y-2 p-3 border rounded-md bg-background",children:[(0,r.jsx)("h4",{className:"font-semibold text-foreground",children:"Arrival Details"}),(0,r.jsx)(H,{icon:j.A,label:"Flight No.",value:e.flightArrivalDetails.flightNumber}),(0,r.jsx)(H,{icon:b.A,label:"Date & Time",value:F(e.flightArrivalDetails.dateTime,!0)}),(0,r.jsx)(H,{icon:x.A,label:"Airport",value:e.flightArrivalDetails.airport}),e.flightArrivalDetails.terminal&&(0,r.jsx)(H,{icon:x.A,label:"Terminal",value:e.flightArrivalDetails.terminal}),e.flightArrivalDetails.notes&&(0,r.jsx)(H,{icon:y.A,label:"Notes",value:e.flightArrivalDetails.notes,valueClassName:"whitespace-pre-wrap"})]}),e.flightDepartureDetails&&(0,r.jsxs)("div",{className:"space-y-2 p-3 border rounded-md bg-background",children:[(0,r.jsx)("h4",{className:"font-semibold text-foreground",children:"Departure Details"}),(0,r.jsx)(H,{icon:N.A,label:"Flight No.",value:e.flightDepartureDetails.flightNumber}),(0,r.jsx)(H,{icon:b.A,label:"Date & Time",value:F(e.flightDepartureDetails.dateTime,!0)}),(0,r.jsx)(H,{icon:x.A,label:"Airport",value:e.flightDepartureDetails.airport}),e.flightDepartureDetails.terminal&&(0,r.jsx)(H,{icon:x.A,label:"Terminal",value:e.flightDepartureDetails.terminal}),e.flightDepartureDetails.notes&&(0,r.jsx)(H,{icon:y.A,label:"Notes",value:e.flightDepartureDetails.notes,valueClassName:"whitespace-pre-wrap"})]}),!e.flightArrivalDetails&&!e.flightDepartureDetails&&(0,r.jsx)("p",{className:"text-muted-foreground md:col-span-2",children:"No flight details logged."})]})]})]})," ",(0,r.jsxs)("div",{className:"lg:col-span-1",children:[" ",(0,r.jsxs)(c.Zp,{className:"shadow-md bg-card p-0",children:[(0,r.jsx)(c.aR,{className:"p-6",children:(0,r.jsxs)(c.ZB,{className:"text-xl font-semibold text-primary flex items-center",children:[(0,r.jsx)(b.A,{className:"mr-2 h-5 w-5 text-accent"})," Status History"]})}),(0,r.jsx)(c.Wu,{children:e.statusHistory.length>0?(0,r.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:e.statusHistory.map(e=>(0,r.jsxs)("div",{className:"p-3 border rounded-md bg-background text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)(w.E,{className:(0,M.cn)("text-xs py-0.5 px-1.5",Z(e.status)),children:(0,P.fZ)(e.status)}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:F(e.changedAt,!0)})]}),e.reason&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1 italic",children:["Reason: ",e.reason]})]},e.id))}):(0,r.jsx)("p",{className:"text-muted-foreground",children:"No status history available."})})]})]})]})]})})})}},98328:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(40157).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},99673:(e,t,a)=>{"use strict";function r(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}a.d(t,{fZ:()=>r})}},e=>{var t=t=>e(e.s=t);e.O(0,[5769,8360,832,2688,7529,6766,8241,8162,2730,4765,8441,1684,7358],()=>t(28987)),_N_E=e.O()}]);