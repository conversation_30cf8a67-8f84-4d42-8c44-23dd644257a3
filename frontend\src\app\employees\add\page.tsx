'use client';

import EmployeeForm from '@/components/employees/EmployeeForm';
import {addEmployee as storeAddEmployee} from '@/lib/store';
import {useRouter} from 'next/navigation';
import type {EmployeeFormData} from '@/lib/schemas/employeeSchemas';
import {PageHeader} from '@/components/ui/PageHeader';
import {UserPlus2} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';
import {useState} from 'react'; // Added useState for loading/error

export default function AddEmployeePage() {
	const router = useRouter();
	const {toast} = useToast();
	const [isSubmittingForm, setIsSubmittingForm] = useState(false);
	const [submissionError, setSubmissionError] = useState<string | null>(null);

	const handleSubmit = async (data: EmployeeFormData) => {
		// Made async
		setIsSubmittingForm(true);
		setSubmissionError(null);
		try {
			await storeAddEmployee(data); // Await the async operation
			toast({
				title: 'Employee Added',
				description: `The employee "${
					data.fullName || data.name
				}" has been successfully created.`, // Use name as fallback
				variant: 'default',
			});
			router.push('/employees');
		} catch (error: any) {
			console.error('Error adding employee:', error);

			// Check for validation errors
			if (error.validationErrors && Array.isArray(error.validationErrors)) {
				// Format validation errors for display
				const validationMessages = error.validationErrors
					.map((err: any) => `${err.path}: ${err.message}`)
					.join('\n');

				console.log('Validation errors details:', validationMessages);

				setSubmissionError(`Validation failed: ${validationMessages}`);
				toast({
					title: 'Validation Error',
					description: 'Please check the form for errors',
					variant: 'destructive',
				});
			} else {
				// Handle other errors
				const errorMessage =
					error.message || 'Failed to add employee. Please try again.';
				setSubmissionError(errorMessage);
				toast({
					title: 'Error Adding Employee',
					description: errorMessage,
					variant: 'destructive',
				});
			}
		} finally {
			setIsSubmittingForm(false);
		}
	};

	return (
		<div className='space-y-6'>
			<PageHeader
				title='Add New Employee'
				description='Enter the details for the new employee.'
				icon={UserPlus2}
			/>
			{submissionError && (
				<div className='bg-destructive/20 p-3 rounded-md text-destructive text-sm'>
					{submissionError}
				</div>
			)}
			<EmployeeForm
				onSubmit={handleSubmit}
				isEditing={false}
				isLoading={isSubmittingForm} // Pass loading state to the form
			/>
		</div>
	);
}
