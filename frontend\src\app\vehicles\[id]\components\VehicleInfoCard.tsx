'use client';

import Image from 'next/image';
import {Card, Card<PERSON>ontent, CardHeader, CardTitle} from '@/components/ui/card';
import type {Vehicle} from '@/lib/types';
import {CalendarDays, Palette, Tag, Gauge, Wrench, Edit} from 'lucide-react';
// Button and Link imports are not used directly if the edit button is commented out or handled by parent
// import { Button } from '@/components/ui/button';
// import Link from 'next/link';

interface VehicleInfoCardProps {
	vehicle: Vehicle;
}

export default function VehicleInfoCard({vehicle}: VehicleInfoCardProps) {
	const hasServiceHistory = vehicle.serviceHistory.length > 0;
	const hasInitialOdometer = vehicle.initialOdometer !== null;

	const latestOdometer = hasServiceHistory
		? Math.max(
				...vehicle.serviceHistory.map((s) => s.odometer),
				vehicle.initialOdometer || 0
		  )
		: vehicle.initialOdometer || 0;

	const formatInitialOdometer = (): string => {
		return hasInitialOdometer
			? `${vehicle.initialOdometer!.toLocaleString()} miles`
			: 'Not recorded';
	};

	const formatLatestOdometer = (): string => {
		if (hasServiceHistory) {
			return `${latestOdometer.toLocaleString()} miles`;
		} else if (hasInitialOdometer) {
			return `${vehicle.initialOdometer!.toLocaleString()} miles`;
		} else {
			return 'No odometer data';
		}
	};

	return (
		<Card className='shadow-lg bg-card'>
			<CardHeader className='flex flex-row items-start justify-between gap-4 border-b p-5'>
				<div>
					<CardTitle className='text-2xl font-bold text-primary mb-1'>
						{vehicle.make} {vehicle.model}
					</CardTitle>
					<p className='text-sm text-muted-foreground'>Vehicle Overview</p>
				</div>
				{/* Placeholder for Edit Button - functionality to be fully implemented if needed */}
				{/* <ActionButton actionType="secondary" size="sm" asChild icon={<Edit className="mr-2 h-3 w-3" />}>
          <Link href={`/vehicles/${vehicle.id}/edit`}> Edit </Link>
        </ActionButton> */}
			</CardHeader>
			<CardContent className='p-5 grid md:grid-cols-2 gap-x-8 gap-y-4'>
				<div className='relative aspect-[16/10] w-full md:col-span-2 lg:col-span-1 rounded-lg overflow-hidden mb-4 lg:mb-0 shadow-sm'>
					<Image
						src={
							vehicle.imageUrl ||
							`https://picsum.photos/seed/${vehicle.id}/600/375`
						}
						alt={`${vehicle.make} ${vehicle.model}`}
						layout='fill'
						objectFit='cover'
						className='bg-muted'
						data-ai-hint='car side'
						priority
					/>
				</div>
				<div className='space-y-3 text-foreground lg:col-span-1'>
					<InfoItem
						icon={CalendarDays}
						label='Year'
						value={vehicle.year.toString()}
					/>
					{vehicle.licensePlate && (
						<InfoItem
							icon={Tag}
							label='License Plate'
							value={vehicle.licensePlate}
						/>
					)}
					{vehicle.color && (
						<InfoItem icon={Palette} label='Color' value={vehicle.color} />
					)}
					<InfoItem
						icon={Gauge}
						label='Initial Odometer'
						value={formatInitialOdometer()}
					/>
					<InfoItem
						icon={Gauge}
						label='Latest Odometer'
						value={formatLatestOdometer()}
					/>
					<InfoItem
						icon={Wrench}
						label='Services Logged'
						value={vehicle.serviceHistory.length.toString()}
					/>
				</div>
			</CardContent>
		</Card>
	);
}

interface InfoItemProps {
	icon: React.ElementType;
	label: string;
	value: string;
}

function InfoItem({icon: Icon, label, value}: InfoItemProps) {
	return (
		<div className='flex items-center'>
			<Icon className='h-5 w-5 text-accent mr-3 shrink-0' />
			<div>
				<p className='text-sm font-medium text-muted-foreground'>{label}</p>
				<p className='text-md font-semibold'>{value}</p>
			</div>
		</div>
	);
}
