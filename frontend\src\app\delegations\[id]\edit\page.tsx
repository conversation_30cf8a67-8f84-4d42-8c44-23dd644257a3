'use client';

import {useEffect, useState, useCallback} from 'react'; // Added useCallback
import {useParams, useRouter} from 'next/navigation';
import DelegationForm from '@/components/delegations/DelegationForm';
import {
	getDelegationById,
	updateDelegation as storeUpdateDelegation,
} from '@/lib/store';
import type {DelegationFormData} from '@/lib/schemas/delegationSchemas';
import type {Delegation} from '@/lib/types';
import {PageHeader} from '@/components/ui/PageHeader';
import {
	Briefcase,
	AlertTriangle,
	Loader2,
	RefreshCw,
	ArrowLeft,
} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';
import {
	SkeletonLoader,
	DataLoader,
	ErrorDisplay,
} from '@/components/ui/loading'; // Changed Skeleton to SkeletonLoader, Added DataLoader and ErrorDisplay
import ErrorBoundary from '@/components/ErrorBoundary'; // Kept, good practice
import {ActionButton} from '@/components/ui/action-button'; // Added

export default function EditDelegationPage() {
	const router = useRouter();
	const params = useParams();
	const {toast} = useToast();
	const [delegation, setDelegation] = useState<Delegation | null>(null);
	const [isLoading, setIsLoading] = useState(true); // For initial data fetch
	const [error, setError] = useState<string | null>(null);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const delegationId = params.id as string;

	const fetchDelegation = useCallback(async () => {
		// useCallback for fetchDelegation
		setIsLoading(true);
		setError(null);
		try {
			if (delegationId) {
				const fetchedDelegation = await getDelegationById(delegationId);
				if (fetchedDelegation) {
					setDelegation(fetchedDelegation);
				} else {
					setError('Delegation not found');
					toast({
						title: 'Error',
						description: 'Delegation not found.',
						variant: 'destructive',
					});
				}
			}
		} catch (err: any) {
			console.error('Error fetching delegation:', err);
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to load delegation';
			setError(errorMessage);
			toast({
				title: 'Error',
				description: errorMessage,
				variant: 'destructive',
			});
		} finally {
			setIsLoading(false);
		}
	}, [delegationId, toast]); // Removed router from deps as it's stable

	useEffect(() => {
		fetchDelegation();
	}, [fetchDelegation]); // Correct dependency

	const handleSubmit = async (data: DelegationFormData) => {
		if (!delegationId) return;

		setIsSubmitting(true);
		setError(null);
		try {
			await storeUpdateDelegation(delegationId, data);
			toast({
				title: 'Delegation Updated',
				description: `The delegation "${data.eventName}" has been successfully updated.`,
				variant: 'default',
			});
			router.push(`/delegations/${delegationId}`);
		} catch (err: any) {
			console.error('Error updating delegation:', err);
			let errorMessage = 'Failed to update delegation';
			if (err.validationErrors && Array.isArray(err.validationErrors)) {
				const validationMessages = err.validationErrors
					.map((e: any) => `${e.path}: ${e.message}`)
					.join(', ');
				errorMessage = `Validation failed: ${validationMessages}`;
			} else if (err.message) {
				errorMessage = err.message;
			}
			setError(errorMessage);
			toast({
				title: 'Error',
				description: errorMessage,
				variant: 'destructive',
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<ErrorBoundary>
			<DataLoader
				isLoading={isLoading}
				error={error}
				data={delegation}
				onRetry={fetchDelegation}
				loadingComponent={
					<div className='space-y-6'>
						<PageHeader title='Loading Delegation...' icon={Briefcase} />
						<SkeletonLoader variant='card' count={1} />{' '}
						{/* Using standardized SkeletonLoader */}
					</div>
				}
				emptyComponent={
					<div className='space-y-6 text-center'>
						<PageHeader title='Delegation Not Found' icon={AlertTriangle} />
						<p>The requested delegation could not be found.</p>
						<ActionButton
							actionType='primary'
							onClick={() => router.push('/delegations')}
							icon={<ArrowLeft className='h-4 w-4' />}>
							Back to Delegations
						</ActionButton>
					</div>
				}>
				{(loadedDelegation) => (
					<div className='space-y-6'>
						<PageHeader
							title={`Edit Delegation: ${loadedDelegation.eventName}`}
							description='Modify the details for this delegation or event.'
							icon={Briefcase}
						/>
						<DelegationForm
							onSubmit={handleSubmit}
							initialData={loadedDelegation}
							isEditing={true}
							isSubmitting={isSubmitting}
						/>
					</div>
				)}
			</DataLoader>
		</ErrorBoundary>
	);
}
