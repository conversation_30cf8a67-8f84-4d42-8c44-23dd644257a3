// @ts-nocheck
'use client';

import {useEffect, useState} from 'react';
import {useParams} from 'next/navigation';
import type {Delegation, FlightDetails, Delegate} from '@/lib/types';
import {getDelegationById} from '@/lib/store';
import Image from 'next/image';
import {
	Users,
	MapPin,
	CalendarDays,
	Plane,
	Clock,
	Info as InfoIcon,
} from 'lucide-react';
import {ReportActions} from '@/components/reports/ReportActions';
import {format, parseISO} from 'date-fns';
import {Badge} from '@/components/ui/badge';
import {cn} from '@/lib/utils';
import {formatDelegationStatusForDisplay} from '@/lib/utils/formattingUtils';
import {SkeletonLoader} from '@/components/ui/loading';

const formatDate = (
	dateString: string | undefined,
	includeTime = false
): string => {
	if (!dateString) return 'N/A';
	try {
		return format(
			parseISO(dateString),
			includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy'
		);
	} catch (e) {
		return 'Invalid Date';
	}
};

const getStatusColor = (status: Delegation['status']) => {
	switch (status) {
		case 'Planned':
			return 'bg-blue-100 text-blue-800 border-blue-300';
		case 'Confirmed':
			return 'bg-green-100 text-green-800 border-green-300';
		case 'In_Progress':
			return 'bg-yellow-100 text-yellow-800 border-yellow-300';
		case 'Completed':
			return 'bg-purple-100 text-purple-800 border-purple-300';
		case 'Cancelled':
			return 'bg-red-100 text-red-800 border-red-300';
		case 'No_details':
		default:
			return 'bg-gray-100 text-gray-800 border-gray-300';
	}
};

export default function DelegationReportPage() {
	const params = useParams();
	const [delegation, setDelegation] = useState<Delegation | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const delegationId = params.id as string;

	const fetchDelegation = async () => {
		setIsLoading(true);
		try {
			if (delegationId) {
				const foundDelegation = await getDelegationById(delegationId);
				if (foundDelegation) {
					setDelegation(foundDelegation);
					document.title = `${foundDelegation.eventName} - Delegation Report`;
				}
			}
		} catch (error) {
			console.error('Error fetching delegation for report:', error);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchDelegation();
	}, [delegationId]);

	if (isLoading) {
		return (
			<div className='max-w-4xl mx-auto p-4'>
				<SkeletonLoader variant='card' count={1} />
				<SkeletonLoader variant='table' count={3} className='mt-6' />
				<SkeletonLoader variant='table' count={2} className='mt-6' />
			</div>
		);
	}

	if (!delegation) {
		return <div className='text-center py-10'>Delegation not found.</div>;
	}

	return (
		<div className='max-w-4xl mx-auto bg-white p-2 sm:p-4 text-gray-800'>
			<div className='text-right mb-4 no-print'>
				<ReportActions
					reportContentId='#delegation-report-content'
					tableId='#delegates-table'
					fileName={`delegation-report-${delegation.eventName.replace(
						/\s+/g,
						'-'
					)}`}
					enableCsv={
						delegation.delegates.length > 0 ||
						(delegation.statusHistory && delegation.statusHistory.length > 0)
					}
				/>
			</div>

			<div id='delegation-report-content' className='report-content'>
				<header className='text-center mb-8 pb-4 border-b-2 border-gray-300'>
					<h1 className='text-3xl font-bold text-gray-800'>
						Delegation Report
					</h1>
					<p className='text-xl text-gray-600'>{delegation.eventName}</p>
					<Badge
						className={cn(
							'mt-2 text-sm py-1 px-3 font-semibold',
							getStatusColor(delegation.status)
						)}>
						Status: {formatDelegationStatusForDisplay(delegation.status)}
					</Badge>
				</header>

				<section className='mb-6 card-print p-4 border border-gray-200 rounded'>
					<h2 className='text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200'>
						Delegation Summary
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 text-sm'>
						<div>
							<strong>Event Name:</strong> {delegation.eventName}
						</div>
						<div>
							<strong>Location:</strong> {delegation.location}
						</div>
						<div>
							<strong>Duration:</strong> {formatDate(delegation.durationFrom)}{' '}
							to {formatDate(delegation.durationTo)}
						</div>
						{delegation.invitationFrom && (
							<div>
								<strong>Invitation From:</strong> {delegation.invitationFrom}
							</div>
						)}
						{delegation.invitationTo && (
							<div>
								<strong>Invitation To:</strong> {delegation.invitationTo}
							</div>
						)}
					</div>
					{delegation.imageUrl && (
						<div className='mt-4 relative aspect-[16/9] w-full max-w-md mx-auto overflow-hidden rounded no-print'>
							<Image
								src={delegation.imageUrl}
								alt={delegation.eventName}
								layout='fill'
								objectFit='contain'
								data-ai-hint='event placeholder'
							/>
						</div>
					)}
					{delegation.notes && (
						<div className='mt-3 text-sm'>
							<strong>Notes:</strong>{' '}
							<span className='italic'>{delegation.notes}</span>
						</div>
					)}
				</section>

				<section className='mb-6 card-print p-4 border border-gray-200 rounded'>
					<h2 className='text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200'>
						Delegates ({delegation.delegates.length})
					</h2>
					{delegation.delegates.length > 0 ? (
						<table
							id='delegates-table'
							className='w-full text-sm text-left text-gray-600'>
							<thead className='text-xs text-gray-700 uppercase bg-gray-50'>
								<tr>
									<th scope='col' className='px-3 py-2'>
										Name
									</th>
									<th scope='col' className='px-3 py-2'>
										Title
									</th>
									<th scope='col' className='px-3 py-2'>
										Notes
									</th>
								</tr>
							</thead>
							<tbody>
								{delegation.delegates.map((delegate: Delegate) => (
									<tr
										key={delegate.id}
										className='bg-white border-b hover:bg-gray-50'>
										<td className='px-3 py-2 font-medium'>{delegate.name}</td>
										<td className='px-3 py-2'>{delegate.title}</td>
										<td className='px-3 py-2'>{delegate.notes || '-'}</td>
									</tr>
								))}
							</tbody>
						</table>
					) : (
						<p className='text-gray-500 text-sm'>No delegates listed.</p>
					)}
				</section>

				{(delegation.flightArrivalDetails ||
					delegation.flightDepartureDetails) && (
					<section className='mb-6 card-print p-4 border border-gray-200 rounded'>
						<h2 className='text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200'>
							Flight Information
						</h2>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-6 text-sm'>
							{delegation.flightArrivalDetails && (
								<div>
									<h3 className='font-semibold text-md mb-1'>
										Arrival Details
									</h3>
									<p>
										<Plane className='inline h-4 w-4 mr-1' />
										<strong>Flight:</strong>{' '}
										{delegation.flightArrivalDetails.flightNumber}
									</p>
									<p>
										<Clock className='inline h-4 w-4 mr-1' />
										<strong>Time:</strong>{' '}
										{formatDate(delegation.flightArrivalDetails.dateTime, true)}
									</p>
									<p>
										<MapPin className='inline h-4 w-4 mr-1' />
										<strong>Airport:</strong>{' '}
										{delegation.flightArrivalDetails.airport}{' '}
										{delegation.flightArrivalDetails.terminal &&
											`(Terminal ${delegation.flightArrivalDetails.terminal})`}
									</p>
									{delegation.flightArrivalDetails.notes && (
										<p className='mt-1 text-xs italic'>
											<strong>Notes:</strong>{' '}
											{delegation.flightArrivalDetails.notes}
										</p>
									)}
								</div>
							)}
							{delegation.flightDepartureDetails && (
								<div>
									<h3 className='font-semibold text-md mb-1'>
										Departure Details
									</h3>
									<p>
										<Plane className='inline h-4 w-4 mr-1' />
										<strong>Flight:</strong>{' '}
										{delegation.flightDepartureDetails.flightNumber}
									</p>
									<p>
										<Clock className='inline h-4 w-4 mr-1' />
										<strong>Time:</strong>{' '}
										{formatDate(
											delegation.flightDepartureDetails.dateTime,
											true
										)}
									</p>
									<p>
										<MapPin className='inline h-4 w-4 mr-1' />
										<strong>Airport:</strong>{' '}
										{delegation.flightDepartureDetails.airport}{' '}
										{delegation.flightDepartureDetails.terminal &&
											`(Terminal ${delegation.flightDepartureDetails.terminal})`}
									</p>
									{delegation.flightDepartureDetails.notes && (
										<p className='mt-1 text-xs italic'>
											<strong>Notes:</strong>{' '}
											{delegation.flightDepartureDetails.notes}
										</p>
									)}
								</div>
							)}
						</div>
						{!delegation.flightArrivalDetails &&
							!delegation.flightDepartureDetails && (
								<p className='text-gray-500 text-sm'>
									No flight details logged.
								</p>
							)}
					</section>
				)}

				<section className='card-print p-4 border border-gray-200 rounded'>
					<h2 className='text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200'>
						Status History
					</h2>
					{delegation.statusHistory && delegation.statusHistory.length > 0 ? (
						<table
							id='status-history-table'
							className='w-full text-sm text-left text-gray-600'>
							<thead className='text-xs text-gray-700 uppercase bg-gray-50'>
								<tr>
									<th scope='col' className='px-3 py-2'>
										Status
									</th>
									<th scope='col' className='px-3 py-2'>
										Changed At
									</th>
									<th scope='col' className='px-3 py-2'>
										Reason
									</th>
								</tr>
							</thead>
							<tbody>
								{delegation.statusHistory
									.slice()
									.sort(
										(a, b) =>
											new Date(b.changedAt).getTime() -
											new Date(a.changedAt).getTime()
									)
									.map((entry) => (
										<tr
											key={entry.id}
											className='bg-white border-b hover:bg-gray-50'>
											<td className='px-3 py-2'>
												<Badge
													className={cn(
														'text-xs py-0.5 px-1.5',
														getStatusColor(entry.status)
													)}>
													{formatDelegationStatusForDisplay(
														entry.status as Delegation['status']
													)}
												</Badge>
											</td>
											<td className='px-3 py-2'>
												{formatDate(entry.changedAt, true)}
											</td>
											<td className='px-3 py-2'>{entry.reason || '-'}</td>
										</tr>
									))}
							</tbody>
						</table>
					) : (
						<p className='text-gray-500 text-sm'>
							No status history available.
						</p>
					)}
				</section>

				<footer className='mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500'>
					<p>Report generated on: {new Date().toLocaleDateString()}</p>
					<p>WorkHub - Delegation Management</p>
				</footer>
			</div>
		</div>
	);
}
