import * as z from 'zod';
import {isValidDateString} from '@/lib/utils/dateUtils';

export const TaskStatusSchema = z.enum([
	'Pending',
	'Assigned',
	'In Progress',
	'Completed',
	'Cancelled',
]);

export const TaskPrioritySchema = z.enum(['Low', 'Medium', 'High']);

// Define SubTask schema for use in TaskSchema
export const SubTaskSchema = z.object({
	id: z.string().uuid().optional(),
	title: z.string().min(1, 'Subtask title cannot be empty'),
	completed: z.boolean().default(false),
});

export const TaskSchema = z
	.object({
		id: z.string().uuid().optional(), // Optional for new tasks, will be generated
		description: z.string().min(1, 'Task description is required'),
		location: z.string().min(1, 'Location is required'),
		dateTime: z
			.string()
			.min(1, 'Start date & time is required')
			.refine((val) => isValidDateString(val), {
				message:
					'Please enter a valid date and time in YYYY-MM-DD HH:MM format',
			}),
		estimatedDuration: z.coerce
			.number()
			.int() // Ensure it's an integer
			.min(1, 'Estimated duration must be at least 1 minute'),
		requiredSkills: z.array(z.string()).optional().default([]),
		priority: TaskPrioritySchema.default('Medium'),
		deadline: z
			.string()
			.refine((val) => val === '' || isValidDateString(val), {
				// Allow empty string for optional or valid date
				message:
					'Please enter a valid deadline date and time in YYYY-MM-DD HH:MM format',
			})
			.optional()
			.transform((val) => (val === '' ? undefined : val)), // Transform empty string to undefined
		status: TaskStatusSchema.default('Pending'),
		// Changed from assignedTo: z.array(z.string().uuid())
		// Assuming a task is assigned to at most one employee directly via a foreign key
		// If tasks can be assigned to multiple employees, Prisma schema would need a many-to-many relation table
		// The error messages pointed towards `assignedEmployees` on Prisma types, which implies a relation.
		// If the frontend form sends a single ID, this works. If multiple, the TaskFormData would need to be `assignedEmployeeIds: z.array(z.number())`
		// and the controller would use `connect: ids.map(id => ({id}))`.
		// For now, assuming a single assignee via a direct FK or a single connect.
		// If task.model.ts has `assignedEmployeeIds: [String(driverEmployee.id)]` then the backend controller
		// expects `assignedEmployeeIds` as an array of numbers/strings.
		// Let's match the controller's expectation which is an array of numbers for connect.
		assignedEmployeeIds: z
			.array(
				z.number().int().positive('Employee ID must be a positive integer.')
			)
			.optional()
			.default([]),
		subTasks: z.array(SubTaskSchema).optional().default([]),
		notes: z.string().optional().or(z.literal('')),
		// vehicleId should be a number if it's a foreign key to Vehicle.id (Int)
		vehicleId: z
			.number()
			.int()
			.positive('Vehicle ID must be a positive integer.')
			.nullable()
			.optional(),
		statusChangeReason: z.string().optional(),
	})
	// Add a superRefine to check if deadline is after dateTime when both are provided
	.superRefine((data, ctx) => {
		if (data.dateTime && data.deadline) {
			const startDate = new Date(data.dateTime);
			const deadlineDate = new Date(data.deadline);

			if (deadlineDate < startDate) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Deadline cannot be earlier than the start date & time',
					path: ['deadline'],
				});
			}
		}
	});

export type TaskFormData = z.infer<typeof TaskSchema>;
