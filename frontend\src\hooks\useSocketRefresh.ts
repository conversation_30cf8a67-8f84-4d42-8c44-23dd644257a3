import {useEffect, useState, useCallback} from 'react';
import {useSocket, SOCKET_EVENTS} from './useSocket';
import {useAutoRefresh} from './useAutoRefresh';

interface UseSocketRefreshOptions {
	interval?: number;
	enabled?: boolean;
	immediate?: boolean;
	socketUrl?: string;
	enableSocket?: boolean;
	enablePolling?: boolean;
	onRefresh?: () => void;
	onError?: (error: unknown) => void;
}

/**
 * Hook that combines WebSocket real-time updates with fallback polling
 *
 * @param fetchFn - The function to call for refreshing data
 * @param eventTypes - Array of socket events that should trigger a refresh
 * @param options - Configuration options
 */
export function useSocketRefresh<T = void>(
	fetchFn: () => Promise<T>,
	eventTypes: string[] = [],
	options: UseSocketRefreshOptions = {}
) {
	const {
		interval = 60000, // Default polling interval is longer with sockets (60s)
		enabled = true,
		immediate = true,
		socketUrl,
		enableSocket = true,
		enablePolling = true, // Fallback polling is enabled by default
		onRefresh,
		onError,
	} = options;

	const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
	const [socketTriggered, setSocketTriggered] = useState(false);

	// Set up socket connection
	const {isConnected, subscribe} = useSocket({
		url: socketUrl,
		autoConnect: enableSocket && enabled,
	});

	// Fetch function wrapper that updates lastUpdated
	const wrappedFetchFn = useCallback(async () => {
		try {
			const result = await fetchFn();
			setLastUpdated(new Date());
			onRefresh?.();
			return result;
		} catch (error) {
			onError?.(error);
			throw error;
		}
	}, [fetchFn, onRefresh, onError]);

	// Set up auto-refresh with a longer interval as backup
	const {isRefreshing, refresh: refreshData} = useAutoRefresh(
		async () => {
			await wrappedFetchFn();
		},
		{
			interval: interval,
			// Only enable polling if socket is disabled or as a backup
			enabled: enablePolling && enabled && (!isConnected || !enableSocket),
			immediate:
				immediate && (!isConnected || !enableSocket || !eventTypes.length),
			onError,
		}
	);

	// Subscribe to socket events for real-time updates
	useEffect(() => {
		if (!enableSocket || !enabled || !isConnected || eventTypes.length === 0) {
			return;
		}

		const unsubscribers = eventTypes.map((event) =>
			subscribe(event, (data) => {
				console.log(`Socket event received: ${event}`, data);
				setSocketTriggered(true);
				refreshData().catch(console.error);
				setTimeout(() => setSocketTriggered(false), 1000);
			})
		);

		return () => {
			unsubscribers.forEach((unsubscribe) => unsubscribe());
		};
	}, [enableSocket, enabled, isConnected, eventTypes, subscribe, refreshData]);

	// Manually trigger refresh
	const refresh = useCallback(async () => {
		return await refreshData();
	}, [refreshData]);

	return {
		isRefreshing,
		socketTriggered,
		isConnected,
		lastUpdated,
		refresh,
	};
}
