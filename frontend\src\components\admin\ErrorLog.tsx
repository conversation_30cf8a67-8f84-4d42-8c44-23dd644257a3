'use client';

import React, {useState} from 'react';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import {RefreshCw, AlertTriangle, XCircle, Info, Filter} from 'lucide-react';
import {ActionButton} from '@/components/ui/action-button';
import {
	ErrorLogEntry,
	PaginatedResponse,
	LogLevel,
	getRecentErrors,
	getMockRecentErrors,
} from '@/lib/adminService';
import {Skeleton} from '@/components/ui/skeleton';
import {ScrollArea} from '@/components/ui/scroll-area';
import {usePaginatedApi} from '@/hooks/useApi';
import {LoadingError} from '@/components/ui/loading-states';
import {ErrorBoundary} from '@/components/ui/error-boundary';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';

/**
 * Component that displays recent system error logs
 */
export function ErrorLog() {
	const [level, setLevel] = useState<LogLevel | undefined>(undefined);
	const [refreshing, setRefreshing] = useState(false);

	const {
		data: errors,
		isLoading,
		error,
		refetch,
		page,
		totalPages,
		nextPage,
		prevPage,
		hasNextPage,
		hasPrevPage,
	} = usePaginatedApi<ErrorLogEntry>(
		(page, limit) => getRecentErrors({page, limit, level}),
		{
			maxRetries: 3,
			initialDelay: 500,
			pageSize: 10,
			deps: [level],
		}
	);

	const handleRefresh = async () => {
		setRefreshing(true);
		await refetch();
		setRefreshing(false);
	};

	const handleLevelChange = (value: string) => {
		setLevel(value === 'all' ? undefined : (value as LogLevel));
	};

	const getLevelIcon = (level: string) => {
		if (level === 'ERROR') return <XCircle className='h-4 w-4 text-red-500' />;
		if (level === 'WARNING')
			return <AlertTriangle className='h-4 w-4 text-yellow-500' />;
		return <Info className='h-4 w-4 text-blue-500' />;
	};

	const getLevelBadge = (level: string) => {
		if (level === 'ERROR')
			return (
				<Badge
					variant='outline'
					className='bg-red-500/20 text-red-700 border-red-500/30'>
					Error
				</Badge>
			);
		if (level === 'WARNING')
			return (
				<Badge
					variant='outline'
					className='bg-yellow-500/20 text-yellow-700 border-yellow-500/30'>
					Warning
				</Badge>
			);
		return (
			<Badge
				variant='outline'
				className='bg-blue-500/20 text-blue-700 border-blue-500/30'>
				Info
			</Badge>
		);
	};

	return (
		<ErrorBoundary>
			<Card className='shadow-md'>
				<CardHeader className='pb-2 p-5'>
					<CardTitle className='text-xl font-semibold text-primary'>
						Recent Errors & Warnings
					</CardTitle>
					<CardDescription>Latest system errors and warnings</CardDescription>
				</CardHeader>

				<div className='px-5 pb-2'>
					<div className='flex items-center space-x-2'>
						<Filter className='h-4 w-4 text-muted-foreground' />
						<span className='text-sm font-medium'>Filter by level:</span>
						<Select value={level || 'all'} onValueChange={handleLevelChange}>
							<SelectTrigger className='w-[140px]'>
								<SelectValue placeholder='All levels' />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value='all'>All levels</SelectItem>
								<SelectItem value='ERROR'>Errors only</SelectItem>
								<SelectItem value='WARNING'>Warnings only</SelectItem>
								<SelectItem value='INFO'>Info only</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>

				<CardContent className='p-5'>
					{isLoading || refreshing ? (
						<div className='space-y-3'>
							<Skeleton className='h-6 w-full' />
							<Skeleton className='h-6 w-full' />
							<Skeleton className='h-6 w-full' />
							<Skeleton className='h-6 w-full' />
							<Skeleton className='h-6 w-full' />
						</div>
					) : error ? (
						<LoadingError message={error} onRetry={refetch} />
					) : errors && errors.length > 0 ? (
						<>
							<ScrollArea className='h-[300px] pr-4'>
								<div className='space-y-3'>
									{errors.map((entry) => (
										<div
											key={entry.id}
											className='p-3 border rounded-md hover:bg-accent/50 transition-colors'>
											<div className='flex items-center justify-between mb-1'>
												<div className='flex items-center space-x-2'>
													{getLevelIcon(entry.level)}
													<span className='font-medium'>{entry.message}</span>
												</div>
												{getLevelBadge(entry.level)}
											</div>
											<div className='flex items-center justify-between'>
												<div className='text-xs text-muted-foreground'>
													{new Date(entry.timestamp).toLocaleString()}
												</div>
												{entry.source && (
													<div className='text-xs text-muted-foreground'>
														Source: {entry.source}
													</div>
												)}
											</div>
											{entry.details &&
												Object.keys(entry.details).length > 0 && (
													<div className='mt-2 text-xs p-2 bg-muted rounded'>
														<pre className='whitespace-pre-wrap'>
															{JSON.stringify(entry.details, null, 2)}
														</pre>
													</div>
												)}
										</div>
									))}
								</div>
							</ScrollArea>

							{totalPages > 1 && (
								<div className='flex items-center justify-between mt-4'>
									<ActionButton
										actionType='tertiary'
										size='sm'
										onClick={prevPage}
										disabled={!hasPrevPage || isLoading || refreshing}>
										Previous
									</ActionButton>
									<span className='text-sm text-muted-foreground'>
										Page {page} of {totalPages}
									</span>
									<ActionButton
										actionType='tertiary'
										size='sm'
										onClick={nextPage}
										disabled={!hasNextPage || isLoading || refreshing}>
										Next
									</ActionButton>
								</div>
							)}
						</>
					) : (
						<div className='p-8 text-center text-muted-foreground'>
							<AlertTriangle className='mx-auto h-8 w-8 mb-2 text-muted-foreground/50' />
							<p>No errors or warnings found for the selected filter.</p>
							{level && (
								<p className='text-sm mt-2'>
									Try changing the filter to see more results.
								</p>
							)}
						</div>
					)}
				</CardContent>
				<CardFooter className='p-5'>
					<ActionButton
						actionType='tertiary'
						size='sm'
						className='w-full'
						onClick={handleRefresh}
						isLoading={refreshing || isLoading}
						loadingText='Refreshing...'
						icon={<RefreshCw className='h-4 w-4' />}>
						Refresh Logs
					</ActionButton>
				</CardFooter>
			</Card>
		</ErrorBoundary>
	);
}
