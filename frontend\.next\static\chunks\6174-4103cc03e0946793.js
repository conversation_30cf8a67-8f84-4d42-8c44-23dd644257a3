"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6174],{12543:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},17759:(e,t,a)=>{a.d(t,{C5:()=>j,MJ:()=>f,eI:()=>x,lR:()=>h,lV:()=>o,zB:()=>m});var r=a(95155),s=a(12115),l=a(99708),i=a(62177),n=a(59434),d=a(85057);let o=i.Op,c=s.createContext({}),m=e=>{let{...t}=e;return(0,r.jsx)(c.Provider,{value:{name:t.name},children:(0,r.jsx)(i.xI,{...t})})},u=()=>{let e=s.useContext(c),t=s.useContext(p),{getFieldState:a,formState:r}=(0,i.xW)(),l=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...l}},p=s.createContext({}),x=s.forwardRef((e,t)=>{let{className:a,...l}=e,i=s.useId();return(0,r.jsx)(p.Provider,{value:{id:i},children:(0,r.jsx)("div",{ref:t,className:(0,n.cn)("space-y-2",a),...l})})});x.displayName="FormItem";let h=s.forwardRef((e,t)=>{let{className:a,...s}=e,{error:l,formItemId:i}=u();return(0,r.jsx)(d.J,{ref:t,className:(0,n.cn)(l&&"text-destructive",a),htmlFor:i,...s})});h.displayName="FormLabel";let f=s.forwardRef((e,t)=>{let{...a}=e,{error:s,formItemId:i,formDescriptionId:n,formMessageId:d}=u();return(0,r.jsx)(l.DX,{ref:t,id:i,"aria-describedby":s?"".concat(n," ").concat(d):"".concat(n),"aria-invalid":!!s,...a})});f.displayName="FormControl",s.forwardRef((e,t)=>{let{className:a,...s}=e,{formDescriptionId:l}=u();return(0,r.jsx)("p",{ref:t,id:l,className:(0,n.cn)("text-sm text-muted-foreground",a),...s})}).displayName="FormDescription";let j=s.forwardRef((e,t)=>{var a;let{className:s,children:l,...i}=e,{error:d,formMessageId:o}=u(),c=d?String(null!=(a=null==d?void 0:d.message)?a:""):l;return c?(0,r.jsx)("p",{ref:t,id:o,className:(0,n.cn)("text-sm font-medium text-destructive",s),...i,children:c}):null});j.displayName="FormMessage"},31949:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},35079:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("ClipboardList",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},37648:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},59119:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},59409:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>f,gC:()=>h,l6:()=>c,yv:()=>m});var r=a(95155),s=a(12115),l=a(31992),i=a(79556),n=a(77381),d=a(10518),o=a(59434);let c=l.bL;l.YJ;let m=l.WT,u=s.forwardRef((e,t)=>{let{className:a,children:s,...n}=e;return(0,r.jsxs)(l.l9,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...n,children:[s,(0,r.jsx)(l.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let p=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});p.displayName=l.PP.displayName;let x=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})});x.displayName=l.wn.displayName;let h=s.forwardRef((e,t)=>{let{className:a,children:s,position:i="popper",...n}=e;return(0,r.jsx)(l.ZL,{children:(0,r.jsxs)(l.UC,{ref:t,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...n,children:[(0,r.jsx)(p,{}),(0,r.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(x,{})]})})});h.displayName=l.UC.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...s})}).displayName=l.JU.displayName;let f=s.forwardRef((e,t)=>{let{className:a,children:s,...i}=e;return(0,r.jsxs)(l.q7,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.VF,{children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}),(0,r.jsx)(l.p4,{children:s})]})});f.displayName=l.q7.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...s})}).displayName=l.wv.displayName},62523:(e,t,a)=>{a.d(t,{p:()=>i});var r=a(95155),s=a(12115),l=a(59434);let i=s.forwardRef((e,t)=>{let{className:a,type:s,...i}=e;return(0,r.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...i})});i.displayName="Input"},66695:(e,t,a)=>{a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>m});var r=a(95155),s=a(12115),l=a(59434);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});i.displayName="Card";let n=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...s})});n.displayName="CardHeader";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...s})});d.displayName="CardTitle";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...s})});o.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",a),...s})});c.displayName="CardContent";let m=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",a),...s})});m.displayName="CardFooter"},79732:(e,t,a)=>{a.d(t,{A:()=>A});var r=a(95155),s=a(62177),l=a(90221),i=a(82603),n=a(30285),d=a(62523),o=a(88539),c=a(66695),m=a(17759),u=a(59409),p=a(35079),x=a(37648),h=a(31949),f=a(12543),j=a(59119),g=a(35695),v=a(87481),y=a(2730),b=a(12115),N=a(21876),w=a(73168);let C="__UNASSIGNED__",I="__NO_VEHICLE__";function A(e){var t;let{onSubmit:a,initialData:A,isEditing:S=!1}=e,k=(0,g.useRouter)(),{toast:M}=(0,v.dj)(),[T,R]=(0,b.useState)([]),[D,E]=(0,b.useState)([]),_=T.filter(e=>"Active"===e.status);(0,b.useEffect)(()=>{(async()=>{try{let e=await (0,y.getEmployees)();R(e||[]);let t=await (0,y.getVehicles)();E(t||[])}catch(e){console.error("Failed to fetch employees or vehicles for form:",e),M({title:"Error",description:"Could not load employees/vehicles for form.",variant:"destructive"})}})()},[M]);let O=(0,s.mN)({resolver:(0,l.u)(i.pj),defaultValues:{description:(null==A?void 0:A.description)||"",location:(null==A?void 0:A.location)||"",dateTime:A?(0,N.formatDateForInput)(A.dateTime,"datetime-local"):(0,w.GP)(new Date,"yyyy-MM-dd'T'HH:mm"),estimatedDuration:(null==A?void 0:A.estimatedDuration)||60,requiredSkills:(null==A?void 0:A.requiredSkills)||[],priority:(null==A?void 0:A.priority)||"Medium",deadline:(null==A?void 0:A.deadline)?(0,N.formatDateForInput)(A.deadline,"datetime-local"):"",status:(null==A?void 0:A.status)||"Pending",assignedEmployeeIds:(null==A||null==(t=A.assignedTo)?void 0:t.map(e=>Number(e)).filter(e=>!isNaN(e)))||[],notes:(null==A?void 0:A.notes)||"",vehicleId:(null==A?void 0:A.vehicleId)?Number(A.vehicleId):null}});return(0,r.jsx)(m.lV,{...O,children:(0,r.jsx)("form",{onSubmit:O.handleSubmit(e=>{a({...e,dateTime:(0,N.formatDateForApi)(e.dateTime),deadline:e.deadline?(0,N.formatDateForApi)(e.deadline):void 0,vehicleId:e.vehicleId?Number(e.vehicleId):null,assignedEmployeeIds:Array.isArray(e.assignedEmployeeIds)?e.assignedEmployeeIds.map(e=>Number(e)):[]})}),children:(0,r.jsxs)(c.Zp,{className:"shadow-lg",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{className:"text-2xl text-primary",children:S?"Edit Task":"Add New Task"}),(0,r.jsx)(c.BT,{children:"Enter the details for the task."})]}),(0,r.jsxs)(c.Wu,{className:"space-y-6",children:[(0,r.jsxs)("section",{className:"space-y-4 p-4 border rounded-lg bg-card",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-2 h-5 w-5 text-accent"}),"Task Details"]}),(0,r.jsx)(m.zB,{control:O.control,name:"description",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Description"}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(o.T,{placeholder:"e.g., Pick up package from Warehouse A",...t})}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsx)(m.zB,{control:O.control,name:"location",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Location"}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(d.p,{placeholder:"e.g., 123 Main St, City Center",...t})}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(m.zB,{control:O.control,name:"dateTime",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Start Date & Time"}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(d.p,{type:"datetime-local",...t})}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsx)(m.zB,{control:O.control,name:"estimatedDuration",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Estimated Duration (minutes)"}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(d.p,{type:"number",placeholder:"e.g., 60",...t})}),(0,r.jsx)(m.C5,{})]})}})]})]}),(0,r.jsxs)("section",{className:"space-y-4 p-4 border rounded-lg bg-card",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,r.jsx)(x.A,{className:"mr-2 h-5 w-5 text-accent"}),"Scheduling & Assignment"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(m.zB,{control:O.control,name:"priority",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Priority"}),(0,r.jsxs)(u.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(m.MJ,{children:(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Select priority"})})}),(0,r.jsx)(u.gC,{children:i.xb.options.map(e=>(0,r.jsx)(u.eb,{value:e,children:e},e))})]}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsx)(m.zB,{control:O.control,name:"deadline",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Deadline (Optional)"}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(d.p,{type:"datetime-local",...t,value:t.value||""})}),(0,r.jsx)(m.C5,{})]})}})]}),(0,r.jsx)(m.zB,{control:O.control,name:"status",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Status"}),(0,r.jsxs)(u.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(m.MJ,{children:(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Select status"})})}),(0,r.jsx)(u.gC,{children:i.l8.options.map(e=>(0,r.jsx)(u.eb,{value:e,children:e},e))})]}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsx)(m.zB,{control:O.control,name:"assignedEmployeeIds",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Assign to Employees (Optional)"}),(0,r.jsxs)(u.l6,{onValueChange:e=>{e===C?t.onChange([]):e?t.onChange([Number(e)]):t.onChange([])},value:t.value&&t.value.length>0?String(t.value[0]):C,children:[(0,r.jsx)(m.MJ,{children:(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Select an employee (optional)"})})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:C,children:"Unassigned"}),_.map(e=>{let t="driver"===e.role&&e.vehicleId?D.find(t=>t.id===Number(e.vehicleId)):null;return(0,r.jsxs)(u.eb,{value:String(e.id),children:[e.fullName," (",e.role.charAt(0).toUpperCase()+e.role.slice(1).replace("_"," "),", ",e.status,"driver"===e.role&&e.availability&&(0,r.jsxs)("span",{children:[", ",e.availability.replace("_"," ")]}),"driver"===e.role&&e.currentLocation&&(0,r.jsxs)("span",{children:[", @ ",e.currentLocation]}),"driver"===e.role&&(0,r.jsxs)("span",{children:[", Vehicle:"," ",t?"".concat(t.make," ").concat(t.model):"None"]}),")"]},e.id)})]})]}),(0,r.jsx)(m.C5,{})]})}})]}),(0,r.jsxs)("section",{className:"space-y-4 p-4 border rounded-lg bg-card",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,r.jsx)(h.A,{className:"mr-2 h-5 w-5 text-accent"}),"Additional Information"]}),(0,r.jsx)(m.zB,{control:O.control,name:"requiredSkills",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Required Skills (Optional, comma-separated)"}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(d.p,{placeholder:"e.g., Forklift License, Customer Service",value:Array.isArray(t.value)?t.value.join(", "):"",onChange:e=>t.onChange(e.target.value.split(",").map(e=>e.trim()).filter(e=>e))})}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsx)(m.zB,{control:O.control,name:"vehicleId",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Associated Vehicle (Optional)"}),(0,r.jsxs)(u.l6,{onValueChange:e=>{t.onChange(e===I?null:Number(e))},value:null===t.value?I:t.value?String(t.value):void 0,children:[(0,r.jsx)(m.MJ,{children:(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Select a vehicle"})})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:I,children:"No Specific Vehicle"}),D.map(e=>(0,r.jsxs)(u.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,") -"," ",e.licensePlate||"N/A"]},e.id))]})]}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsx)(m.zB,{control:O.control,name:"notes",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Notes (Optional)"}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(o.T,{placeholder:"e.g., Gate code is 1234, contact person: Jane Smith",...t})}),(0,r.jsx)(m.C5,{})]})}})]})]}),(0,r.jsxs)(c.wL,{className:"flex justify-between gap-2 border-t pt-6",children:[(0,r.jsxs)(n.$,{type:"button",variant:"outline",onClick:()=>k.back(),children:[(0,r.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),(0,r.jsxs)(n.$,{type:"submit",className:"bg-accent text-accent-foreground hover:bg-accent/90",children:[(0,r.jsx)(j.A,{className:"mr-2 h-4 w-4"}),S?"Save Changes":"Create Task"]})]})]})})})}},82603:(e,t,a)=>{a.d(t,{l8:()=>l,pj:()=>d,xb:()=>i});var r=a(55594),s=a(21876);let l=r.k5(["Pending","Assigned","In Progress","Completed","Cancelled"]),i=r.k5(["Low","Medium","High"]),n=r.Ik({id:r.Yj().uuid().optional(),title:r.Yj().min(1,"Subtask title cannot be empty"),completed:r.zM().default(!1)}),d=r.Ik({id:r.Yj().uuid().optional(),description:r.Yj().min(1,"Task description is required"),location:r.Yj().min(1,"Location is required"),dateTime:r.Yj().min(1,"Start date & time is required").refine(e=>(0,s.isValidDateString)(e),{message:"Please enter a valid date and time in YYYY-MM-DD HH:MM format"}),estimatedDuration:r.au.number().int().min(1,"Estimated duration must be at least 1 minute"),requiredSkills:r.YO(r.Yj()).optional().default([]),priority:i.default("Medium"),deadline:r.Yj().refine(e=>""===e||(0,s.isValidDateString)(e),{message:"Please enter a valid deadline date and time in YYYY-MM-DD HH:MM format"}).optional().transform(e=>""===e?void 0:e),status:l.default("Pending"),assignedEmployeeIds:r.YO(r.ai().int().positive("Employee ID must be a positive integer.")).optional().default([]),subTasks:r.YO(n).optional().default([]),notes:r.Yj().optional().or(r.eu("")),vehicleId:r.ai().int().positive("Vehicle ID must be a positive integer.").nullable().optional(),statusChangeReason:r.Yj().optional()}).superRefine((e,t)=>{if(e.dateTime&&e.deadline){let a=new Date(e.dateTime);new Date(e.deadline)<a&&t.addIssue({code:r.eq.custom,message:"Deadline cannot be earlier than the start date & time",path:["deadline"]})}})},85057:(e,t,a)=>{a.d(t,{J:()=>o});var r=a(95155),s=a(12115),l=a(40968),i=a(74466),n=a(59434);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.b,{ref:t,className:(0,n.cn)(d(),a),...s})});o.displayName=l.b.displayName},87481:(e,t,a)=>{a.d(t,{dj:()=>u});var r=a(12115);let s=0,l=new Map,i=e=>{if(l.has(e))return;let t=setTimeout(()=>{l.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?i(a):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],o={toasts:[]};function c(e){o=n(o,e),d.forEach(e=>{e(o)})}function m(e){let{...t}=e,a=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||r()}}}),{id:a,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function u(){let[e,t]=r.useState(o);return r.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},88539:(e,t,a)=>{a.d(t,{T:()=>i});var r=a(95155),s=a(12115),l=a(59434);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...s})});i.displayName="Textarea"},95647:(e,t,a)=>{a.d(t,{z:()=>s});var r=a(95155);function s(e){let{title:t,description:a,icon:s,children:l}=e;return(0,r.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[s&&(0,r.jsx)(s,{className:"h-8 w-8 text-primary"}),(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:t})]}),a&&(0,r.jsx)("p",{className:"text-muted-foreground mt-1",children:a})]}),l&&(0,r.jsx)("div",{className:"flex items-center gap-2",children:l})]})}a(12115)}}]);