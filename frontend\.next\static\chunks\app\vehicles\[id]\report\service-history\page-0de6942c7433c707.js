(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5298],{3638:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},16125:(e,a,s)=>{Promise.resolve().then(s.bind(s,31364))},28328:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},31364:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>k});var t=s(95155),r=s(11518),i=s.n(r),c=s(12115),l=s(35695),n=s(2730),d=s(5611),o=s(66766),m=s(39798),h=s(95647),x=s(66695),p=s(77023),b=s(58824),u=s(3638),j=s(28328),v=s(6874),y=s.n(v),N=s(30285),f=s(6560),g=s(15080);function k(){let e=(0,l.useParams)(),[a,s]=(0,c.useState)(null),[r,v]=(0,c.useState)([]),[k,w]=(0,c.useState)(!0),[A,M]=(0,c.useState)(null),[S,C]=(0,c.useState)(0),P=e.id,V=(0,c.useCallback)(async()=>{if(w(!0),M(null),!P){M("No Vehicle ID provided."),w(!1);return}try{let e=await (0,n.getVehicleById)(Number(P));if(!e){M("Vehicle not found."),w(!1);return}s(e),document.title="".concat(e.make," ").concat(e.model," - Service History Report");let a=await (0,d._X)(Number(P));v(a)}catch(e){console.error("Error fetching vehicle service history:",e),M(e instanceof Error?e.message:"Failed to load vehicle data.")}finally{w(!1)}},[P]);(0,c.useEffect)(()=>{V()},[V,S]);let L=(0,c.useCallback)(()=>{C(e=>e+1)},[]);return k?(0,t.jsx)(g.A,{children:(0,t.jsx)("div",{className:"max-w-5xl mx-auto p-4",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(p.jt,{variant:"card",count:1}),(0,t.jsx)(p.jt,{variant:"table",count:5})]})})}):A||!a?(0,t.jsx)(g.A,{children:(0,t.jsx)("div",{className:"max-w-5xl mx-auto p-4",children:(0,t.jsx)(x.Zp,{className:"shadow-md",children:(0,t.jsxs)(x.Wu,{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-red-600 mb-2",children:"Error"}),(0,t.jsx)("p",{className:"text-gray-700 mb-4",children:A||"Vehicle not found"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(N.$,{onClick:L,children:"Try Again"}),(0,t.jsx)(N.$,{variant:"outline",asChild:!0,children:(0,t.jsx)(y(),{href:"/vehicles",children:"Back to Vehicles"})})]})]})})})}):(0,t.jsx)(g.A,{children:(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 max-w-5xl mx-auto p-4 space-y-6 print-container",children:[(0,t.jsx)(h.z,{title:"Vehicle Service History",description:"".concat(a.make," ").concat(a.model," (").concat(a.year,")"),icon:u.A,children:(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 flex gap-2 no-print",children:[(0,t.jsx)(f.r,{actionType:"tertiary",asChild:!0,icon:(0,t.jsx)(j.A,{className:"h-4 w-4"}),children:(0,t.jsx)(y(),{href:"/vehicles/".concat(P),children:"View Vehicle"})}),(0,t.jsx)(m.k,{reportContentId:"#vehicle-service-history-content",reportType:"vehicle-service-history",entityId:P,tableId:"#service-history-table",fileName:"vehicle-service-history-".concat(a.make,"-").concat(a.model),enableCsv:r.length>0})]})}),(0,t.jsxs)("div",{id:"vehicle-service-history-content",className:"jsx-1952bdba77817484 report-content",children:[(0,t.jsx)(x.Zp,{className:"shadow-md mb-6 card-print",children:(0,t.jsx)(x.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 col-span-1 md:col-span-2",children:[(0,t.jsx)("h2",{className:"jsx-1952bdba77817484 text-xl font-semibold text-gray-800 mb-4",children:"Vehicle Details"}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 grid grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Make:"})," ",a.make]}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Model:"})," ",a.model]}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Year:"})," ",a.year]}),a.licensePlate&&(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Plate Number:"})," ",a.licensePlate]}),a.color&&(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Color:"})," ",a.color]}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("strong",{className:"jsx-1952bdba77817484",children:"Initial Odometer:"})," ",null!==a.initialOdometer?"".concat(a.initialOdometer.toLocaleString()," miles"):"Not recorded"]}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Current Odometer:"})," ",r.length>0?"".concat(Math.max(...r.map(e=>e.odometer)).toLocaleString()," miles"):null!==a.initialOdometer?"".concat(a.initialOdometer.toLocaleString()," miles"):"No odometer data available"]}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 col-span-2",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Last Updated:"})," ",r.length>0?new Date(Math.max(...r.map(e=>new Date(e.date).getTime()))).toLocaleDateString():"No service records"]})]})]}),a.imageUrl&&(0,t.jsx)("div",{className:"jsx-1952bdba77817484 col-span-1 no-print",children:(0,t.jsx)("div",{className:"jsx-1952bdba77817484 relative aspect-[4/3] w-full overflow-hidden rounded",children:(0,t.jsx)(o.default,{src:a.imageUrl,alt:"".concat(a.make," ").concat(a.model),fill:!0,sizes:"(max-width: 768px) 100vw, 300px",style:{objectFit:"cover"}})})})]})})}),(0,t.jsxs)("header",{className:"jsx-1952bdba77817484 text-center mb-8 pb-4 border-b-2 border-gray-300 print-only",children:[(0,t.jsx)("h1",{className:"jsx-1952bdba77817484 text-3xl font-bold text-gray-800",children:"Vehicle Service History Report"}),(0,t.jsxs)("p",{className:"jsx-1952bdba77817484 text-md text-gray-600",children:[a.make," ",a.model," (",a.year,")",a.licensePlate&&" - ".concat(a.licensePlate)]})]}),(0,t.jsx)(b.R,{records:r,isLoading:!1,error:null,onRetry:L,showVehicleInfo:!1,vehicleSpecific:!0}),(0,t.jsxs)("footer",{className:"jsx-1952bdba77817484 mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500",children:[(0,t.jsxs)("p",{className:"jsx-1952bdba77817484",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,t.jsx)("p",{className:"jsx-1952bdba77817484",children:"WorkHub - Vehicle Service Management"})]})]}),(0,t.jsx)(i(),{id:"1952bdba77817484",children:".print-only{display:none}@media print{.no-print{display:none!important}.print-only{display:block}.print-container{padding:1rem}.card-print{-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;border:none!important}.print-service-col{max-width:200px;white-space:normal!important}.print-notes-col{max-width:200px;white-space:normal!important}.print-text-wrap{word-break:break-word;white-space:normal!important}}@media(max-width:640px){.overflow-x-auto{overflow-x:auto}.summary-grid{grid-template-columns:1fr 1fr!important}}"})]})})}},31949:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},35695:(e,a,s)=>{"use strict";var t=s(18999);s.o(t,"useParams")&&s.d(a,{useParams:function(){return t.useParams}}),s.o(t,"usePathname")&&s.d(a,{usePathname:function(){return t.usePathname}}),s.o(t,"useRouter")&&s.d(a,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(a,{useSearchParams:function(){return t.useSearchParams}})},50594:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},67554:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},77381:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},79556:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[5769,8360,832,2688,2512,7529,6766,5267,6045,8738,8162,2730,536,9798,8610,8441,1684,7358],()=>a(16125)),_N_E=e.O()}]);