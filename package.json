{"name": "work-hub-services-system", "version": "1.0.0", "description": "Work Hub is a comprehensive platform for service teams to efficiently manage projects, track progress, and receive AI-powered insights for optimized workflow.", "scripts": {"docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "frontend:build": "cd frontend && npm run docker:build", "backend:build": "cd backend && npm run docker:build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "test:watch:frontend": "cd frontend && npm run test:watch", "test:watch:backend": "cd backend && npm run test:watch", "test:coverage": "npm run test:coverage:frontend && npm run test:coverage:backend", "test:coverage:frontend": "cd frontend && npm run test:coverage", "test:coverage:backend": "cd backend && npm run test:coverage"}, "dependencies": {"@types/jsdom": "^21.1.7", "jsdom": "^26.1.0", "morgan": "^1.10.0", "zod": "^3.25.28"}, "devDependencies": {"@types/morgan": "^1.9.9", "jest": "^29.7.0"}}