// @ts-nocheck
'use client';

import {useEffect, useState, useCallback} from 'react';
import {use<PERSON><PERSON><PERSON>, useRouter} from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {ActionButton} from '@/components/ui/action-button';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
	CardFooter,
} from '@/components/ui/card';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {Separator} from '@/components/ui/separator';
import {
	ArrowLeft,
	Printer,
	Edit,
	Trash2,
	Briefcase,
	Users,
	MapPin,
	CalendarDays,
	Plane,
	Clock,
	Info,
	PlaneLanding,
	PlaneTakeoff,
	AlertTriangle,
	RefreshCw,
} from 'lucide-react';
import type {
	Delegation,
	Delegate,
	FlightDetails,
	StatusHistoryEntry,
} from '@/lib/types';
import {getDelegationById, deleteDelegation} from '@/lib/store';
import {PageHeader} from '@/components/ui/PageHeader';
import {useToast} from '@/hooks/use-toast';
import {Badge} from '@/components/ui/badge';
import {cn} from '@/lib/utils';
import {format, parseISO} from 'date-fns';
import {formatDelegationStatusForDisplay} from '@/lib/utils/formattingUtils';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
	DataLoader,
	SkeletonLoader,
	ErrorDisplay,
} from '@/components/ui/loading';
import {ViewReportButton} from '@/components/reports/ViewReportButton';

const getStatusColor = (status: Delegation['status']) => {
	switch (status) {
		case 'Planned':
			return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';
		case 'Confirmed':
			return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
		case 'In_Progress':
			return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
		case 'Completed':
			return 'bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20';
		case 'Cancelled':
			return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
		case 'No_details':
		default:
			return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
	}
};

const formatDate = (dateString: string, includeTime = false) => {
	try {
		return format(
			parseISO(dateString),
			includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy'
		);
	} catch (e) {
		return 'Invalid Date';
	}
};

interface DetailItemProps {
	icon: React.ElementType;
	label: string;
	value?: string | number | null;
	children?: React.ReactNode;
	valueClassName?: string;
}

function DetailItem({
	icon: Icon,
	label,
	value,
	children,
	valueClassName,
}: DetailItemProps) {
	if (!value && !children) return null;
	return (
		<div className='flex items-start py-2'>
			<Icon className='h-5 w-5 text-accent mr-3 mt-0.5 shrink-0' />
			<div>
				<p className='text-sm text-muted-foreground'>{label}</p>
				{value && (
					<p
						className={cn(
							'text-base font-semibold text-foreground',
							valueClassName
						)}>
						{value}
					</p>
				)}
				{children}
			</div>
		</div>
	);
}

export default function DelegationDetailPage() {
	const params = useParams();
	const router = useRouter();
	const {toast} = useToast();
	const [delegation, setDelegation] = useState<Delegation | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const delegationId = params.id as string;

	const fetchDelegation = useCallback(async () => {
		setIsLoading(true);
		setError(null);
		try {
			if (delegationId) {
				const foundDelegation = await getDelegationById(delegationId);
				if (foundDelegation) {
					foundDelegation.statusHistory.sort(
						(a, b) =>
							new Date(b.changedAt).getTime() - new Date(a.changedAt).getTime()
					);
					setDelegation(foundDelegation);
				} else {
					setError('Delegation not found.');
					toast({
						title: 'Error',
						description: 'Delegation not found.',
						variant: 'destructive',
					});
				}
			}
		} catch (err) {
			console.error('Error fetching delegation:', err);
			const errorMessage =
				err instanceof Error
					? err.message
					: 'Failed to load delegation details.';
			setError(errorMessage);
			toast({
				title: 'Error',
				description: errorMessage,
				variant: 'destructive',
			});
		} finally {
			setIsLoading(false);
		}
	}, [delegationId, toast]);

	useEffect(() => {
		fetchDelegation();
	}, [fetchDelegation]);

	const handleDeleteDelegation = async () => {
		if (delegation) {
			try {
				await deleteDelegation(delegation.id);
				toast({
					title: 'Delegation Deleted',
					description: `Delegation "${delegation.eventName}" has been deleted.`,
				});
				router.push('/delegations');
			} catch (error) {
				console.error('Error deleting delegation:', error);
				toast({
					title: 'Error',
					description: 'Failed to delete delegation. Please try again.',
					variant: 'destructive',
				});
			}
		}
	};

	return (
		<div className='space-y-8'>
			<DataLoader
				isLoading={isLoading}
				error={error}
				data={delegation}
				onRetry={fetchDelegation}
				loadingComponent={
					<div className='space-y-6'>
						<PageHeader title='Loading Delegation...' icon={Briefcase} />
						<SkeletonLoader variant='card' count={1} />
						<div className='grid md:grid-cols-3 gap-6 items-start'>
							<SkeletonLoader
								variant='card'
								count={1}
								className='md:col-span-2'
							/>
							<SkeletonLoader variant='card' count={1} />
						</div>
					</div>
				}
				emptyComponent={
					<div className='text-center py-10'>
						<PageHeader title='Delegation Not Found' icon={Briefcase} />
						<p className='mb-4'>The requested delegation could not be found.</p>
						<ActionButton
							actionType='primary'
							onClick={() => router.push('/delegations')}
							icon={<ArrowLeft className='h-4 w-4' />}>
							Back to List
						</ActionButton>
					</div>
				}>
				{(loadedDelegation) => (
					<>
						<PageHeader title={loadedDelegation.eventName} icon={Briefcase}>
							<div className='flex gap-2 items-center flex-wrap'>
								<ActionButton
									actionType='tertiary'
									onClick={() => router.push('/delegations')}
									icon={<ArrowLeft className='h-4 w-4' />}>
									Back to List
								</ActionButton>
								<ActionButton
									actionType='secondary'
									asChild
									icon={<Edit className='h-4 w-4' />}>
									<Link href={`/delegations/${loadedDelegation.id}/edit`}>
										Edit
									</Link>
								</ActionButton>
								<ViewReportButton
									href={`/delegations/${loadedDelegation.id}/report`}
								/>
								<AlertDialog>
									<AlertDialogTrigger asChild>
										<ActionButton
											actionType='danger'
											icon={<Trash2 className='h-4 w-4' />}>
											Delete Delegation
										</ActionButton>
									</AlertDialogTrigger>
									<AlertDialogContent>
										<AlertDialogHeader>
											<AlertDialogTitle>Are you sure?</AlertDialogTitle>
											<AlertDialogDescription>
												This action cannot be undone. This will permanently
												delete the delegation and all its related information.
											</AlertDialogDescription>
										</AlertDialogHeader>
										<AlertDialogFooter>
											<AlertDialogCancel>Cancel</AlertDialogCancel>
											<AlertDialogAction
												onClick={handleDeleteDelegation}
												className='bg-destructive hover:bg-destructive/90'>
												Delete
											</AlertDialogAction>
										</AlertDialogFooter>
									</AlertDialogContent>
								</AlertDialog>
							</div>
						</PageHeader>{' '}
						<Card className='shadow-lg bg-card p-0'>
							<CardHeader className='border-b p-6'>
								<div className='flex justify-between items-start'>
									<div>
										<CardTitle className='text-2xl font-bold text-primary mb-1'>
											{loadedDelegation.eventName}
										</CardTitle>
										<CardDescription className='text-muted-foreground'>
											Detailed overview of the delegation.
										</CardDescription>
									</div>
									<Badge
										className={cn(
											'text-sm py-1 px-3 font-semibold',
											getStatusColor(loadedDelegation.status)
										)}>
										{formatDelegationStatusForDisplay(loadedDelegation.status)}
									</Badge>
								</div>
							</CardHeader>
							<CardContent className='grid md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4'>
								<div className='lg:col-span-1 space-y-1'>
									<DetailItem
										icon={MapPin}
										label='Location'
										value={loadedDelegation.location}
									/>
									<DetailItem
										icon={CalendarDays}
										label='Duration'
										value={`${formatDate(
											loadedDelegation.durationFrom
										)} - ${formatDate(loadedDelegation.durationTo)}`}
									/>
									{loadedDelegation.invitationFrom && (
										<DetailItem
											icon={Users}
											label='Invitation From'
											value={loadedDelegation.invitationFrom}
										/>
									)}
									{loadedDelegation.invitationTo && (
										<DetailItem
											icon={Users}
											label='Invitation To'
											value={loadedDelegation.invitationTo}
										/>
									)}
									{loadedDelegation.notes && (
										<DetailItem
											icon={Info}
											label='General Notes'
											value={loadedDelegation.notes}
											valueClassName='whitespace-pre-wrap'
										/>
									)}
								</div>
								<div className='relative aspect-[16/10] w-full md:col-span-2 lg:col-span-2 rounded-lg overflow-hidden mb-4 lg:mb-0 shadow-sm'>
									<Image
										src={
											loadedDelegation.imageUrl ||
											`https://picsum.photos/seed/${loadedDelegation.id}/600/375`
										}
										alt={loadedDelegation.eventName}
										layout='fill'
										objectFit='cover'
										className='bg-muted'
										data-ai-hint='event venue'
										priority
									/>
								</div>
							</CardContent>
						</Card>
						<div className='grid lg:grid-cols-3 gap-6 items-start'>
							<div className='lg:col-span-2 space-y-6'>
								{' '}
								<Card className='shadow-md bg-card p-0'>
									<CardHeader className='p-6'>
										<CardTitle className='text-xl font-semibold text-primary flex items-center'>
											<Users className='mr-2 h-5 w-5 text-accent' /> Delegates (
											{loadedDelegation.delegates.length})
										</CardTitle>
									</CardHeader>
									<CardContent>
										{loadedDelegation.delegates.length > 0 ? (
											<div className='space-y-3'>
												{loadedDelegation.delegates.map((delegate) => (
													<div
														key={delegate.id}
														className='p-3 border rounded-md bg-background'>
														<p className='font-semibold text-foreground'>
															{delegate.name}
														</p>
														<p className='text-sm text-muted-foreground'>
															{delegate.title}
														</p>
														{delegate.notes && (
															<p className='text-xs text-muted-foreground mt-1 italic'>
																{delegate.notes}
															</p>
														)}
													</div>
												))}
											</div>
										) : (
											<p className='text-muted-foreground'>
												No delegates assigned to this delegation.
											</p>
										)}
									</CardContent>
								</Card>
								{(loadedDelegation.flightArrivalDetails ||
									loadedDelegation.flightDepartureDetails) && (
									<Card className='shadow-md bg-card p-0'>
										<CardHeader className='p-6'>
											<CardTitle className='text-xl font-semibold text-primary flex items-center'>
												<Plane className='mr-2 h-5 w-5 text-accent' /> Flight
												Information
											</CardTitle>
										</CardHeader>
										<CardContent className='grid md:grid-cols-2 gap-6'>
											{loadedDelegation.flightArrivalDetails && (
												<div className='space-y-2 p-3 border rounded-md bg-background'>
													<h4 className='font-semibold text-foreground'>
														Arrival Details
													</h4>
													<DetailItem
														icon={PlaneLanding}
														label='Flight No.'
														value={
															loadedDelegation.flightArrivalDetails.flightNumber
														}
													/>
													<DetailItem
														icon={Clock}
														label='Date & Time'
														value={formatDate(
															loadedDelegation.flightArrivalDetails.dateTime,
															true
														)}
													/>
													<DetailItem
														icon={MapPin}
														label='Airport'
														value={
															loadedDelegation.flightArrivalDetails.airport
														}
													/>
													{loadedDelegation.flightArrivalDetails.terminal && (
														<DetailItem
															icon={MapPin}
															label='Terminal'
															value={
																loadedDelegation.flightArrivalDetails.terminal
															}
														/>
													)}
													{loadedDelegation.flightArrivalDetails.notes && (
														<DetailItem
															icon={Info}
															label='Notes'
															value={
																loadedDelegation.flightArrivalDetails.notes
															}
															valueClassName='whitespace-pre-wrap'
														/>
													)}
												</div>
											)}
											{loadedDelegation.flightDepartureDetails && (
												<div className='space-y-2 p-3 border rounded-md bg-background'>
													<h4 className='font-semibold text-foreground'>
														Departure Details
													</h4>
													<DetailItem
														icon={PlaneTakeoff}
														label='Flight No.'
														value={
															loadedDelegation.flightDepartureDetails
																.flightNumber
														}
													/>
													<DetailItem
														icon={Clock}
														label='Date & Time'
														value={formatDate(
															loadedDelegation.flightDepartureDetails.dateTime,
															true
														)}
													/>
													<DetailItem
														icon={MapPin}
														label='Airport'
														value={
															loadedDelegation.flightDepartureDetails.airport
														}
													/>
													{loadedDelegation.flightDepartureDetails.terminal && (
														<DetailItem
															icon={MapPin}
															label='Terminal'
															value={
																loadedDelegation.flightDepartureDetails.terminal
															}
														/>
													)}
													{loadedDelegation.flightDepartureDetails.notes && (
														<DetailItem
															icon={Info}
															label='Notes'
															value={
																loadedDelegation.flightDepartureDetails.notes
															}
															valueClassName='whitespace-pre-wrap'
														/>
													)}
												</div>
											)}
											{!loadedDelegation.flightArrivalDetails &&
												!loadedDelegation.flightDepartureDetails && (
													<p className='text-muted-foreground md:col-span-2'>
														No flight details logged.
													</p>
												)}
										</CardContent>
									</Card>
								)}
							</div>{' '}
							<div className='lg:col-span-1'>
								{' '}
								<Card className='shadow-md bg-card p-0'>
									<CardHeader className='p-6'>
										<CardTitle className='text-xl font-semibold text-primary flex items-center'>
											<Clock className='mr-2 h-5 w-5 text-accent' /> Status
											History
										</CardTitle>
									</CardHeader>
									<CardContent>
										{loadedDelegation.statusHistory.length > 0 ? (
											<div className='space-y-3 max-h-96 overflow-y-auto'>
												{loadedDelegation.statusHistory.map((entry) => (
													<div
														key={entry.id}
														className='p-3 border rounded-md bg-background text-sm'>
														<div className='flex justify-between items-center'>
															<Badge
																className={cn(
																	'text-xs py-0.5 px-1.5',
																	getStatusColor(entry.status)
																)}>
																{formatDelegationStatusForDisplay(
																	entry.status as Delegation['status']
																)}
															</Badge>
															<p className='text-xs text-muted-foreground'>
																{formatDate(entry.changedAt, true)}
															</p>
														</div>
														{entry.reason && (
															<p className='text-xs text-muted-foreground mt-1 italic'>
																Reason: {entry.reason}
															</p>
														)}
													</div>
												))}
											</div>
										) : (
											<p className='text-muted-foreground'>
												No status history available.
											</p>
										)}
									</CardContent>
								</Card>
							</div>
						</div>
					</>
				)}
			</DataLoader>
		</div>
	);
}
