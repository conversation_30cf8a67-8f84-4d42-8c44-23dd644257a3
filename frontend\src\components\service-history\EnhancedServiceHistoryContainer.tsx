'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import type { EnrichedServiceRecord } from '@/lib/types';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Info, AlertTriangle } from 'lucide-react';
import { ServiceHistoryTable } from './ServiceHistoryTable';
import { ServiceHistorySummary } from '@/components/reports/ServiceHistorySummary';
import { PaginationControls } from '@/components/ui/pagination';
import { SkeletonLoader } from '@/components/ui/loading';

/**
 * Props for the EnhancedServiceHistoryContainer component
 */
interface EnhancedServiceHistoryContainerProps {
  /** Array of service records to display */
  records: EnrichedServiceRecord[];
  /** Whether data is currently loading */
  isLoading: boolean;
  /** Error message, if any */
  error: string | null;
  /** Function to retry loading data */
  onRetry: () => void;
  /** Whether to show vehicle information column */
  showVehicleInfo?: boolean;
  /** Whether this is a vehicle-specific view */
  vehicleSpecific?: boolean;
  /** Additional CSS class names */
  className?: string;
}

/**
 * A container component that handles sorting, pagination, and display of service history records
 * 
 * @example
 * ```tsx
 * <EnhancedServiceHistoryContainer
 *   records={filteredRecords}
 *   isLoading={isLoading}
 *   error={error}
 *   onRetry={handleRetry}
 *   showVehicleInfo={true}
 *   vehicleSpecific={false}
 * />
 * ```
 */
export function EnhancedServiceHistoryContainer({
  records,
  isLoading,
  error,
  onRetry,
  showVehicleInfo = true,
  vehicleSpecific = false,
  className,
}: EnhancedServiceHistoryContainerProps) {
  // Sorting state
  const [sortField, setSortField] = useState<string>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Sort records
  const sortedRecords = useMemo(() => {
    return [...records].sort((a, b) => {
      let valueA, valueB;
      
      // Handle different field types
      switch (sortField) {
        case 'date':
          valueA = new Date(a.date).getTime();
          valueB = new Date(b.date).getTime();
          break;
        case 'cost':
          valueA = Number(a.cost) || 0;
          valueB = Number(b.cost) || 0;
          break;
        case 'odometer':
          valueA = a.odometer;
          valueB = b.odometer;
          break;
        case 'servicePerformed':
          valueA = a.servicePerformed.join(', ').toLowerCase();
          valueB = b.servicePerformed.join(', ').toLowerCase();
          break;
        case 'vehicleMake':
          valueA = `${a.vehicleMake} ${a.vehicleModel}`.toLowerCase();
          valueB = `${b.vehicleMake} ${b.vehicleModel}`.toLowerCase();
          break;
        default:
          valueA = a[sortField as keyof EnrichedServiceRecord];
          valueB = b[sortField as keyof EnrichedServiceRecord];
      }
      
      // Compare values based on direction
      if (valueA < valueB) return sortDirection === 'asc' ? -1 : 1;
      if (valueA > valueB) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [records, sortField, sortDirection]);
  
  // Handle sort
  const handleSort = useCallback((field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField, sortDirection]);
  
  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedRecords = useMemo(() => 
    sortedRecords.slice(indexOfFirstItem, indexOfLastItem),
  [sortedRecords, indexOfFirstItem, indexOfLastItem]);
  
  const totalPages = useMemo(() => 
    Math.ceil(sortedRecords.length / itemsPerPage),
  [sortedRecords.length, itemsPerPage]);
  
  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);
  
  // Reset pagination when records change
  useEffect(() => {
    setCurrentPage(1);
  }, [records]);
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4" data-testid="loading-skeleton">
        <SkeletonLoader variant="table" count={5} />
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
        <Button
          variant="outline"
          size="sm"
          onClick={onRetry}
          className="mt-2"
          aria-label="Try loading service records again"
        >
          Try Again
        </Button>
      </Alert>
    );
  }
  
  // Render empty state
  if (records.length === 0) {
    return (
      <Alert variant="default" className="mb-6">
        <Info className="h-4 w-4" />
        <AlertTitle>No Service Records</AlertTitle>
        <AlertDescription>
          {vehicleSpecific
            ? "No service records available for this vehicle."
            : "No service records match your current filters. Try adjusting your search or filter criteria."}
        </AlertDescription>
      </Alert>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <ServiceHistorySummary 
        records={sortedRecords} 
        vehicleSpecific={vehicleSpecific} 
      />
      
      {/* Service Records Table */}
      <Card className="shadow-md card-print">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <ServiceHistoryTable
              records={paginatedRecords}
              showVehicleInfo={showVehicleInfo}
              sortField={sortField}
              sortDirection={sortDirection}
              onSort={handleSort}
            />
          </div>
        </CardContent>
      </Card>
      
      {/* Pagination */}
      {sortedRecords.length > itemsPerPage && (
        <div className="flex justify-center mt-4 no-print">
          <PaginationControls
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}
