(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8147],{15300:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},19968:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},22346:(e,t,r)=>{"use strict";r.d(t,{w:()=>i});var a=r(95155),s=r(12115),l=r(87489),d=r(59434);let i=s.forwardRef((e,t)=>{let{className:r,orientation:s="horizontal",decorative:i=!0,...n}=e;return(0,a.jsx)(l.b,{ref:t,decorative:i,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",r),...n})});i.displayName=l.b.displayName},24865:(e,t,r)=>{"use strict";r.d(t,{M:()=>o});var a=r(95155);r(12115);var s=r(6874),l=r.n(s),d=r(15300),i=r(61840),n=r(6560);function o(e){let{href:t,getReportUrl:r,isList:s=!1,className:o}=e;if(!t&&!r)return console.error("ViewReportButton requires either href or getReportUrl prop"),null;let c=s?"View List Report":"View Report";return t?(0,a.jsx)(n.r,{actionType:"secondary",asChild:!0,icon:(0,a.jsx)(d.A,{className:"h-4 w-4"}),className:o,children:(0,a.jsxs)(l(),{href:t,target:"_blank",rel:"noopener noreferrer",children:[c,(0,a.jsx)(i.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"}),(0,a.jsx)("span",{className:"sr-only",children:"(opens in new tab)"})]})}):(0,a.jsxs)(n.r,{actionType:"secondary",onClick:()=>{if(r){let e=r();window.open(e,"_blank","noopener,noreferrer")}},icon:(0,a.jsx)(d.A,{className:"h-4 w-4"}),className:o,children:[c,(0,a.jsx)(i.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"})]})}},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(95155);r(12115);var s=r(74466),l=r(59434);let d=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)(d({variant:r}),t),...s})}},34301:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},37648:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var a=r(12115),s=r(63655),l=r(95155),d=a.forwardRef((e,t)=>(0,l.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var i=d},50286:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},56193:(e,t,r)=>{Promise.resolve().then(r.bind(r,94249))},59409:(e,t,r)=>{"use strict";r.d(t,{bq:()=>m,eb:()=>f,gC:()=>p,l6:()=>c,yv:()=>u});var a=r(95155),s=r(12115),l=r(31992),d=r(79556),i=r(77381),n=r(10518),o=r(59434);let c=l.bL;l.YJ;let u=l.WT,m=s.forwardRef((e,t)=>{let{className:r,children:s,...i}=e;return(0,a.jsxs)(l.l9,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...i,children:[s,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(d.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=l.l9.displayName;let x=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let h=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let p=s.forwardRef((e,t)=>{let{className:r,children:s,position:d="popper",...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:t,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===d&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:d,...i,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===d&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(h,{})]})})});p.displayName=l.UC.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...s})}).displayName=l.JU.displayName;let f=s.forwardRef((e,t)=>{let{className:r,children:s,...d}=e;return(0,a.jsxs)(l.q7,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...d,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:s})]})});f.displayName=l.q7.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",r),...s})}).displayName=l.wv.displayName},61840:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>d});var a=r(95155),s=r(12115),l=r(59434);let d=s.forwardRef((e,t)=>{let{className:r,type:s,...d}=e;return(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...d})});d.displayName="Input"},67554:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},75074:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},82603:(e,t,r)=>{"use strict";r.d(t,{l8:()=>l,pj:()=>n,xb:()=>d});var a=r(55594),s=r(21876);let l=a.k5(["Pending","Assigned","In Progress","Completed","Cancelled"]),d=a.k5(["Low","Medium","High"]),i=a.Ik({id:a.Yj().uuid().optional(),title:a.Yj().min(1,"Subtask title cannot be empty"),completed:a.zM().default(!1)}),n=a.Ik({id:a.Yj().uuid().optional(),description:a.Yj().min(1,"Task description is required"),location:a.Yj().min(1,"Location is required"),dateTime:a.Yj().min(1,"Start date & time is required").refine(e=>(0,s.isValidDateString)(e),{message:"Please enter a valid date and time in YYYY-MM-DD HH:MM format"}),estimatedDuration:a.au.number().int().min(1,"Estimated duration must be at least 1 minute"),requiredSkills:a.YO(a.Yj()).optional().default([]),priority:d.default("Medium"),deadline:a.Yj().refine(e=>""===e||(0,s.isValidDateString)(e),{message:"Please enter a valid deadline date and time in YYYY-MM-DD HH:MM format"}).optional().transform(e=>""===e?void 0:e),status:l.default("Pending"),assignedEmployeeIds:a.YO(a.ai().int().positive("Employee ID must be a positive integer.")).optional().default([]),subTasks:a.YO(i).optional().default([]),notes:a.Yj().optional().or(a.eu("")),vehicleId:a.ai().int().positive("Vehicle ID must be a positive integer.").nullable().optional(),statusChangeReason:a.Yj().optional()}).superRefine((e,t)=>{if(e.dateTime&&e.deadline){let r=new Date(e.dateTime);new Date(e.deadline)<r&&t.addIssue({code:a.eq.custom,message:"Deadline cannot be earlier than the start date & time",path:["deadline"]})}})},83662:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var a=r(95155),s=r(12115),l=r(40968),d=r(74466),i=r(59434);let n=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,i.cn)(n(),r),...s})});o.displayName=l.b.displayName},87489:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var a=r(12115),s=r(63655),l=r(95155),d="horizontal",i=["horizontal","vertical"],n=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:n=d,...o}=e,c=(r=n,i.includes(r))?n:d;return(0,l.jsx)(s.sG.div,{"data-orientation":c,...a?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:t})});n.displayName="Separator";var o=n},91721:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},94249:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>z});var a=r(95155),s=r(12115),l=r(6874),d=r.n(l),i=r(35695);let n=(0,r(40157).A)("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);var o=r(34301),c=r(75074),u=r(2730),m=r(95647),x=r(62523),h=r(66695),p=r(6560),f=r(91721),g=r(50286),y=r(83662),b=r(98328),j=r(37648),v=r(19968),k=r(26126),w=r(22346),N=r(59434),A=r(73168),M=r(83343);let C=e=>{switch(e){case"Pending":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Assigned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";case"In Progress":return"bg-indigo-500/20 text-indigo-700 border-indigo-500/30 dark:text-indigo-400 dark:bg-indigo-500/10 dark:border-indigo-500/20";case"Completed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},P=e=>{switch(e){case"Low":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Medium":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"High":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},E=e=>{if(!e)return"N/A";try{return(0,A.GP)((0,M.H)(e),"MMM d, yyyy HH:mm")}catch(e){return"Invalid Date"}};function S(e){let{task:t}=e,[r,l]=(0,s.useState)(void 0);(0,s.useEffect)(()=>{t.assignedEmployeeId?l((0,u.getEmployeeById)(t.assignedEmployeeId)):l(null)},[t.assignedEmployeeId]);let i=null==r?void 0:r.fullName,n=(null==r?void 0:r.role)==="driver"?f.A:g.A;return(0,a.jsxs)(h.Zp,{className:"overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60",children:[(0,a.jsxs)(h.aR,{className:"p-5",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start gap-2",children:[(0,a.jsx)(h.ZB,{className:"text-lg font-semibold text-primary line-clamp-2",title:t.description,children:t.description}),(0,a.jsxs)("div",{className:"flex flex-col items-end gap-1 shrink-0",children:[(0,a.jsx)(k.E,{className:(0,N.cn)("text-xs py-1 px-2 font-semibold",C(t.status)),children:t.status}),(0,a.jsxs)(k.E,{className:(0,N.cn)("text-xs py-1 px-2 font-semibold",P(t.priority)),children:[t.priority," Priority"]})]})]}),(0,a.jsxs)(h.BT,{className:"text-sm text-muted-foreground flex items-center pt-1",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-1.5 flex-shrink-0 text-accent"}),t.location]})]}),(0,a.jsxs)(h.Wu,{className:"p-5 flex-grow flex flex-col",children:[(0,a.jsx)(w.w,{className:"my-3 bg-border/50"}),(0,a.jsxs)("div",{className:"space-y-2.5 text-sm text-foreground flex-grow",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Start: "}),(0,a.jsx)("strong",{className:"font-semibold",children:E(t.dateTime)})]})]}),t.deadline&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Deadline: "}),(0,a.jsx)("strong",{className:"font-semibold",children:E(t.deadline)})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Duration: "}),(0,a.jsxs)("strong",{className:"font-semibold",children:[t.estimatedDuration," mins"]})]})]}),i&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Assigned to: "}),(0,a.jsxs)("strong",{className:"font-semibold",children:[i," (",(null==r?void 0:r.role)?r.role.charAt(0).toUpperCase()+r.role.slice(1).replace("_"," "):"Employee",")"]})]})]}),!i&&"Completed"!==t.status&&"Cancelled"!==t.status&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"mr-2.5 h-4 w-4 text-destructive flex-shrink-0"}),(0,a.jsx)("strong",{className:"font-semibold text-destructive",children:"Unassigned"})]})]}),t.notes&&(0,a.jsx)("p",{className:"mt-3 text-xs text-muted-foreground line-clamp-2 pt-2 border-t border-dashed border-border/50",title:t.notes,children:t.notes})]}),(0,a.jsx)(h.wL,{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,a.jsx)(p.r,{actionType:"tertiary",className:"w-full",icon:(0,a.jsx)(v.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(d(),{href:"/tasks/".concat(t.id),children:"View Details"})})})]})}var R=r(59409),T=r(82603),Y=r(85057),q=r(77023),D=r(15080),I=r(24865);function L(){return(0,a.jsxs)("div",{className:"overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60 rounded-lg",children:[(0,a.jsxs)("div",{className:"p-5 flex-grow flex flex-col",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsx)(q.jt,{variant:"default",count:1,className:"h-7 w-3/5 mb-1 bg-muted/50"}),(0,a.jsx)(q.jt,{variant:"default",count:1,className:"h-5 w-1/4 mb-1 bg-muted/50 rounded-full"})]}),(0,a.jsx)(q.jt,{variant:"default",count:1,className:"h-4 w-1/2 mb-3 bg-muted/50"}),(0,a.jsx)(q.jt,{variant:"default",count:1,className:"h-px w-full my-3 bg-border/50"}),(0,a.jsx)("div",{className:"space-y-2.5 flex-grow",children:[void 0,void 0,void 0].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(q.jt,{variant:"default",count:1,className:"mr-2.5 h-5 w-5 rounded-full bg-muted/50"}),(0,a.jsx)(q.jt,{variant:"default",count:1,className:"h-5 w-2/3 bg-muted/50"})]},t))})]}),(0,a.jsx)("div",{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,a.jsx)(q.jt,{variant:"default",count:1,className:"h-10 w-full bg-muted/50"})})]})}let H=()=>{(0,i.useRouter)();let[e,t]=(0,s.useState)([]),[r,l]=(0,s.useState)([]),[f,g]=(0,s.useState)(!0),[y,b]=(0,s.useState)(null),[j,v]=(0,s.useState)(""),[k,w]=(0,s.useState)("all"),[N,A]=(0,s.useState)("all"),[M,C]=(0,s.useState)("all"),[P,E]=(0,s.useState)([]),D=(0,s.useCallback)(async()=>{g(!0),b(null);try{let[e,r]=await Promise.all([(0,u.getTasks)(),(0,u.getEmployees)()]);if(Array.isArray(e)?(e.sort((e,t)=>new Date(t.dateTime).getTime()-new Date(e.dateTime).getTime()),t(e)):(console.error("getTasks did not return an array:",e),t([])),Array.isArray(r)){let e=r.map(e=>({id:String(e.id),name:e.fullName||e.name,role:e.role}));E(e)}else console.error("getEmployees did not return an array:",r),E([])}catch(e){console.error("Error fetching data:",e),b(e instanceof Error?e.message:"Failed to load data"),t([]),E([])}finally{g(!1)}},[]);(0,s.useEffect)(()=>{D()},[D]),(0,s.useEffect)(()=>{let t=[...e],r=j.toLowerCase();"all"!==k&&(t=t.filter(e=>e.status===k)),"all"!==N&&(t=t.filter(e=>e.priority===N)),"all"!==M&&(t=t.filter(e=>e.assignedEmployeeId&&e.assignedEmployeeId===M||"unassigned"===M&&!e.assignedEmployeeId)),r&&(t=t.filter(e=>{let t=e.assignedEmployeeId?P.find(t=>t.id===String(e.assignedEmployeeId)):null;return e.description.toLowerCase().includes(r)||e.location.toLowerCase().includes(r)||e.notes&&e.notes.toLowerCase().includes(r)||t&&t.name.toLowerCase().includes(r)})),l(t)},[j,e,k,N,M,P]);let H=j||"all"!==k||"all"!==N||"all"!==M;return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)(m.z,{title:"Manage Tasks",description:"Oversee all tasks, assignments, and progress.",icon:n,children:(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(p.r,{actionType:"primary",icon:(0,a.jsx)(o.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(d(),{href:"/tasks/add",children:"Add New Task"})}),(0,a.jsx)(I.M,{getReportUrl:()=>{let e=new URLSearchParams({searchTerm:j,status:k,priority:N,employee:M}).toString();return"/tasks/report?".concat(e)},isList:!0})]})}),(0,a.jsx)(h.Zp,{className:"mb-6 p-4 shadow",children:(0,a.jsx)(h.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end",children:[(0,a.jsxs)("div",{className:"relative lg:col-span-1",children:[(0,a.jsx)(Y.J,{htmlFor:"search-tasks",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Search Tasks"}),(0,a.jsx)(c.A,{className:"absolute left-3 top-[calc(50%_-_0.5rem_+_12px)] h-5 w-5 text-muted-foreground"}),(0,a.jsx)(x.p,{id:"search-tasks",type:"text",placeholder:"Description, location, assignee...",value:j,onChange:e=>v(e.target.value),className:"pl-10 w-full"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(Y.J,{htmlFor:"status-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Status"}),(0,a.jsxs)(R.l6,{value:k,onValueChange:w,children:[(0,a.jsx)(R.bq,{id:"status-filter",children:(0,a.jsx)(R.yv,{placeholder:"All Statuses"})}),(0,a.jsxs)(R.gC,{children:[(0,a.jsx)(R.eb,{value:"all",children:"All Statuses"}),T.l8.options.map(e=>(0,a.jsx)(R.eb,{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(Y.J,{htmlFor:"priority-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Priority"}),(0,a.jsxs)(R.l6,{value:N,onValueChange:A,children:[(0,a.jsx)(R.bq,{id:"priority-filter",children:(0,a.jsx)(R.yv,{placeholder:"All Priorities"})}),(0,a.jsxs)(R.gC,{children:[(0,a.jsx)(R.eb,{value:"all",children:"All Priorities"}),T.xb.options.map(e=>(0,a.jsx)(R.eb,{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(Y.J,{htmlFor:"employee-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Employee"}),(0,a.jsxs)(R.l6,{value:M,onValueChange:C,children:[(0,a.jsx)(R.bq,{id:"employee-filter",children:(0,a.jsx)(R.yv,{placeholder:"All Employees"})}),(0,a.jsxs)(R.gC,{children:[(0,a.jsx)(R.eb,{value:"all",children:"All Employees"}),P.map(e=>(0,a.jsxs)(R.eb,{value:e.id,children:[e.name," (",e.role.charAt(0).toUpperCase()+e.role.slice(1).replace("_"," "),")"]},e.id)),(0,a.jsx)(R.eb,{value:"unassigned",children:"Unassigned"})]})]})]})]})})}),(0,a.jsx)(q.gO,{isLoading:f,error:y,data:r,onRetry:D,loadingComponent:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[void 0,void 0,void 0].map((e,t)=>(0,a.jsx)(L,{},t))}),emptyComponent:(0,a.jsxs)("div",{className:"text-center py-12 bg-card rounded-lg shadow-md",children:[(0,a.jsx)(n,{className:"mx-auto h-16 w-16 text-muted-foreground mb-6"}),(0,a.jsx)("h3",{className:"text-2xl font-semibold text-foreground mb-2",children:H?"No Tasks Match Your Filters":"No Tasks Created Yet"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2 mb-6 max-w-md mx-auto",children:H?"Try adjusting your search or filter criteria.":"It looks like you haven't created any tasks yet. Get started by adding one."}),!H&&(0,a.jsx)(p.r,{actionType:"primary",size:"lg",icon:(0,a.jsx)(o.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(d(),{href:"/tasks/add",children:"Create Your First Task"})})]}),children:e=>(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-8",children:e.map(e=>(0,a.jsx)(S,{task:e},e.id))})})]})};function z(){return(0,a.jsx)(D.A,{children:(0,a.jsx)(H,{})})}},98328:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(40157).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[5769,8360,832,2688,2512,1859,7529,8162,2730,536,8441,1684,7358],()=>t(56193)),_N_E=e.O()}]);