import crypto from 'crypto';

/**
 * PHASE 1 SECURITY HARDENING: Comprehensive Secrets Management
 * 
 * This module validates and manages all application secrets to ensure
 * strong security practices and prevent credential exposure.
 */

// Required secrets for application security
const REQUIRED_SECRETS = [
	'SUPABASE_URL',
	'SUPABASE_ANON_KEY',
	'SUPABASE_SERVICE_ROLE_KEY',
	'JWT_SECRET',
] as const;

// Optional but recommended secrets
const RECOMMENDED_SECRETS = [
	'API_SECRET',
	'SESSION_SECRET',
	'ENCRYPTION_KEY',
] as const;

// Secrets that must meet minimum length requirements
const SECRET_LENGTH_REQUIREMENTS = {
	JWT_SECRET: 32,
	API_SECRET: 32,
	SESSION_SECRET: 32,
	ENCRYPTION_KEY: 32,
} as const;

// Secrets that should not contain certain patterns (weak secrets)
const WEAK_SECRET_PATTERNS = [
	/^(test|demo|example|sample|default)/i,
	/^(123|password|secret|key)/i,
	/^(GENERATE|REPLACE|CHANGE|UPDATE)/i,
	/^[a-zA-Z0-9]{1,8}$/,  // Too short and simple
];

interface SecretValidationResult {
	isValid: boolean;
	missing: string[];
	weak: string[];
	insecure: string[];
	warnings: string[];
	recommendations: string[];
}

/**
 * Validates all application secrets for security compliance
 */
export const validateSecrets = (): SecretValidationResult => {
	const result: SecretValidationResult = {
		isValid: true,
		missing: [],
		weak: [],
		insecure: [],
		warnings: [],
		recommendations: [],
	};

	// Check required secrets
	for (const secret of REQUIRED_SECRETS) {
		const value = process.env[secret];
		
		if (!value) {
			result.missing.push(secret);
			result.isValid = false;
		} else {
			// Check for weak patterns
			if (isWeakSecret(value)) {
				result.weak.push(secret);
				result.isValid = false;
			}

			// Check length requirements
			const minLength = SECRET_LENGTH_REQUIREMENTS[secret as keyof typeof SECRET_LENGTH_REQUIREMENTS];
			if (minLength && value.length < minLength) {
				result.weak.push(`${secret} (minimum ${minLength} characters required)`);
				result.isValid = false;
			}

			// Check for insecure patterns
			if (isInsecureSecret(value)) {
				result.insecure.push(secret);
				result.isValid = false;
			}
		}
	}

	// Check recommended secrets
	for (const secret of RECOMMENDED_SECRETS) {
		const value = process.env[secret];
		
		if (!value) {
			result.warnings.push(`Recommended secret ${secret} is not set`);
			result.recommendations.push(`Generate ${secret} using: openssl rand -base64 32`);
		}
	}

	// Additional security checks
	performAdditionalSecurityChecks(result);

	return result;
};

/**
 * Checks if a secret value matches weak patterns
 */
const isWeakSecret = (value: string): boolean => {
	return WEAK_SECRET_PATTERNS.some(pattern => pattern.test(value));
};

/**
 * Checks if a secret value is insecure (e.g., contains obvious patterns)
 */
const isInsecureSecret = (value: string): boolean => {
	// Check for repeated characters
	if (/(.)\1{4,}/.test(value)) return true;
	
	// Check for sequential patterns
	if (/(?:abc|123|xyz|789|qwe|asd|zxc)/i.test(value)) return true;
	
	// Check for common words
	if (/(?:admin|user|pass|login|auth|token|secret|key|test)/i.test(value)) return true;
	
	return false;
};

/**
 * Performs additional security checks and recommendations
 */
const performAdditionalSecurityChecks = (result: SecretValidationResult): void => {
	// Check NODE_ENV
	const nodeEnv = process.env.NODE_ENV;
	if (nodeEnv === 'production') {
		// Production-specific checks
		if (process.env.JWT_SECRET === process.env.API_SECRET) {
			result.insecure.push('JWT_SECRET and API_SECRET should be different');
			result.isValid = false;
		}
	}

	// Check for development secrets in production
	if (nodeEnv === 'production') {
		const devPatterns = [/localhost/, /127\.0\.0\.1/, /dev/, /test/];
		for (const secret of REQUIRED_SECRETS) {
			const value = process.env[secret];
			if (value && devPatterns.some(pattern => pattern.test(value))) {
				result.insecure.push(`${secret} appears to contain development values in production`);
				result.isValid = false;
			}
		}
	}
};

/**
 * Generates a cryptographically secure secret
 */
export const generateSecureSecret = (length: number = 32): string => {
	return crypto.randomBytes(length).toString('base64');
};

/**
 * Validates secrets and throws error if validation fails
 */
export const validateSecretsOrThrow = (): void => {
	const validation = validateSecrets();
	
	if (!validation.isValid) {
		const errorMessages = [];
		
		if (validation.missing.length > 0) {
			errorMessages.push(`Missing required secrets: ${validation.missing.join(', ')}`);
		}
		
		if (validation.weak.length > 0) {
			errorMessages.push(`Weak secrets detected: ${validation.weak.join(', ')}`);
		}
		
		if (validation.insecure.length > 0) {
			errorMessages.push(`Insecure secrets detected: ${validation.insecure.join(', ')}`);
		}
		
		throw new Error(`🚨 SECURITY VALIDATION FAILED:\n${errorMessages.join('\n')}`);
	}
	
	// Log warnings
	if (validation.warnings.length > 0) {
		console.warn('⚠️  Security Warnings:');
		validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
	}
	
	// Log recommendations
	if (validation.recommendations.length > 0) {
		console.info('💡 Security Recommendations:');
		validation.recommendations.forEach(rec => console.info(`  - ${rec}`));
	}
	
	console.log('✅ All required secrets validated successfully');
};

/**
 * Generates all missing secrets and displays them
 */
export const generateMissingSecrets = (): void => {
	const validation = validateSecrets();
	
	console.log('🔐 PHASE 1 SECURITY: Generated Secrets');
	console.log('=====================================');
	
	for (const secret of [...REQUIRED_SECRETS, ...RECOMMENDED_SECRETS]) {
		const value = process.env[secret];
		if (!value || isWeakSecret(value)) {
			const generated = generateSecureSecret();
			console.log(`${secret}=${generated}`);
		}
	}
	
	console.log('=====================================');
	console.log('⚠️  Copy these to your .env file and restart the application');
};

export default {
	validateSecrets,
	validateSecretsOrThrow,
	generateSecureSecret,
	generateMissingSecrets,
};
