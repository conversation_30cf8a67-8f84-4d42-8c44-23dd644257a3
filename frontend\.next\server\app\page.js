(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28946:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29333:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},33873:e=>{"use strict";e.exports=require("path")},34047:(e,s,t)=>{Promise.resolve().then(t.bind(t,21204))},34631:e=>{"use strict";e.exports=require("tls")},35265:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},48041:(e,s,t)=>{"use strict";t.d(s,{z:()=>r});var a=t(60687);function r({title:e,description:s,icon:t,children:r}){return(0,a.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[t&&(0,a.jsx)(t,{className:"h-8 w-8 text-primary"}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),s&&(0,a.jsx)("p",{className:"text-muted-foreground mt-1",children:s})]}),r&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:r})]})}t(43210)},52027:(e,s,t)=>{"use strict";t.d(s,{gO:()=>u,jt:()=>p});var a=t(60687);t(43210);var r=t(11516),i=t(72963),n=t(4780),l=t(85726),c=t(91821),d=t(68752);let o={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},m={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function x({size:e="md",className:s,text:t,fullPage:i=!1}){return(0,a.jsx)("div",{className:(0,n.cn)("flex items-center justify-center",i&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(r.A,{className:(0,n.cn)("animate-spin text-primary",o[e])}),t&&(0,a.jsx)("span",{className:(0,n.cn)("mt-2 text-muted-foreground",m[e]),children:t})]})})}function p({variant:e="default",count:s=1,className:t,testId:r="loading-skeleton"}){return"card"===e?(0,a.jsx)("div",{className:(0,n.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",t),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,a.jsx)(l.E,{className:"aspect-[16/10] w-full"}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)(l.E,{className:"h-7 w-3/4 mb-1"}),(0,a.jsx)(l.E,{className:"h-4 w-1/2 mb-3"}),(0,a.jsx)(l.E,{className:"h-px w-full my-3"}),(0,a.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,a.jsx)(l.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===e?(0,a.jsxs)("div",{className:(0,n.cn)("space-y-3",t),"data-testid":r,children:[(0,a.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,a.jsx)(l.E,{className:"h-8 flex-1"},s))}),Array(s).fill(0).map((e,s)=>(0,a.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,a.jsx)(l.E,{className:"h-6 flex-1"},s))},s))]}):"list"===e?(0,a.jsx)("div",{className:(0,n.cn)("space-y-3",t),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(l.E,{className:"h-12 w-12 rounded-full"}),(0,a.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,a.jsx)(l.E,{className:"h-4 w-1/3"}),(0,a.jsx)(l.E,{className:"h-4 w-full"})]})]},s))}):"stats"===e?(0,a.jsx)("div",{className:(0,n.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",t),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(l.E,{className:"h-5 w-1/3"}),(0,a.jsx)(l.E,{className:"h-5 w-5 rounded-full"})]}),(0,a.jsx)(l.E,{className:"h-8 w-1/2 mt-3"}),(0,a.jsx)(l.E,{className:"h-4 w-2/3 mt-2"})]},s))}):(0,a.jsx)("div",{className:(0,n.cn)("space-y-2",t),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,a.jsx)(l.E,{className:"w-full h-5"},s))})}function h({message:e,onRetry:s,className:t}){return(0,a.jsxs)(c.Fc,{variant:"destructive",className:(0,n.cn)("my-4",t),children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)(c.XL,{children:"Error"}),(0,a.jsx)(c.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:e}),s&&(0,a.jsx)(d.r,{actionType:"tertiary",size:"sm",onClick:s,icon:(0,a.jsx)(r.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function u({isLoading:e,error:s,data:t,onRetry:r,children:i,loadingComponent:l,errorComponent:c,emptyComponent:d,className:o}){return e?l||(0,a.jsx)(x,{className:o,text:"Loading..."}):s?c||(0,a.jsx)(h,{message:s,onRetry:r,className:o}):!t||Array.isArray(t)&&0===t.length?d||(0,a.jsx)("div",{className:(0,n.cn)("text-center py-8",o),children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,a.jsx)("div",{className:o,children:i(t)})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68752:(e,s,t)=>{"use strict";t.d(s,{r:()=>d});var a=t(60687),r=t(43210),i=t.n(r),n=t(29523),l=t(11516),c=t(4780);let d=i().forwardRef(({actionType:e="primary",icon:s,isLoading:t=!1,loadingText:r,className:i,children:d,disabled:o,asChild:m=!1,...x},p)=>{let{variant:h,className:u}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[e];return(0,a.jsx)(n.$,{ref:p,variant:h,className:(0,c.cn)(u,i),disabled:t||o,asChild:m,...x,children:t?(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4 animate-spin"}),r||d]}):(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",s&&(0,a.jsx)("span",{className:"mr-2",children:s}),d]})})});d.displayName="ActionButton"},70070:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(60687),r=t(85814),i=t.n(r),n=t(43210),l=t(44493),c=t(48041),d=t(63213),o=t(68752),m=t(52027),x=t(59059),p=t(76311),h=t(35265),u=t(44610),g=t(33886),y=t(80489),j=t(28399),f=t(24920),v=t(29333),k=t(82614);let N=(0,k.A)("CarFront",[["path",{d:"m21 8-2 2-1.5-3.7A2 2 0 0 0 15.646 5H8.4a2 2 0 0 0-1.903 1.257L5 10 3 8",key:"1imjwt"}],["path",{d:"M7 14h.01",key:"1qa3f1"}],["path",{d:"M17 14h.01",key:"7oqj8z"}],["rect",{width:"18",height:"8",x:"3",y:"10",rx:"2",key:"a7itu8"}],["path",{d:"M5 18v2",key:"ppbyun"}],["path",{d:"M19 18v2",key:"gy7782"}]]),b=(0,k.A)("Route",[["circle",{cx:"6",cy:"19",r:"3",key:"1kj8tv"}],["path",{d:"M9 19h8.5a3.5 3.5 0 0 0 0-7h-11a3.5 3.5 0 0 1 0-7H15",key:"1d8sl"}],["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}]]);var w=t(76485),A=t(28946);let T=(0,k.A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),M=(0,k.A)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]);function C({title:e,value:s,icon:t,description:r,link:n,linkText:c,isFuture:d}){return(0,a.jsxs)(l.Zp,{className:"shadow-md bg-card",children:[(0,a.jsxs)(l.aR,{className:"p-5 flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ZB,{className:"text-sm font-medium text-primary",children:e}),(0,a.jsx)(t,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(l.Wu,{className:"p-5 pt-2",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-foreground",children:s}),r&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:r}),d&&(0,a.jsx)("p",{className:"text-xs text-amber-600 dark:text-amber-500",children:"(Future Feature)"}),n&&c&&(0,a.jsx)(o.r,{actionType:"tertiary",size:"sm",asChild:!0,className:"mt-2",children:(0,a.jsx)(i(),{href:n,children:c})})]})]})}function E({title:e,description:s,icon:t,link:r,actionText:n="Learn More",isFuture:c=!1}){return(0,a.jsxs)(l.Zp,{className:"shadow-md flex flex-col bg-card",children:[(0,a.jsxs)(l.aR,{className:"p-5 pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)(t,{className:"h-6 w-6 text-accent"}),(0,a.jsx)(l.ZB,{className:"text-lg font-semibold text-primary",children:e})]}),(0,a.jsx)(l.BT,{className:"text-sm leading-relaxed",children:s})]}),(0,a.jsx)(l.Wu,{className:"p-5 pt-0 flex-grow",children:c&&(0,a.jsx)("p",{className:"text-xs text-amber-600 dark:text-amber-500 mb-2",children:"(Coming Soon)"})}),(0,a.jsxs)(l.wL,{className:"p-5 pt-0",children:[r&&(0,a.jsx)(o.r,{actionType:"tertiary",asChild:!0,className:"w-full",children:(0,a.jsx)(i(),{href:r,children:n})}),!r&&c&&(0,a.jsx)(o.r,{actionType:"tertiary",className:"w-full",disabled:!0,children:n})]})]})}function P(){let{getVehicles:e,getDelegations:s,getTasks:t,getEmployees:r,isAuthenticated:l,isLoading:k}=function(){let{api:e,isAuthenticated:s,isLoading:t}=function(){let{session:e,user:s,loading:t}=(0,d.Z2)(),a=(0,n.useCallback)(async(t,a={})=>{if(!s||!e?.access_token)throw Error("User not authenticated or token not available");let r={"Content-Type":"application/json",Authorization:`Bearer ${e.access_token}`};a.headers&&Object.assign(r,a.headers);let i={...a,headers:r},n=t.startsWith("http")?t:`http://localhost:3001/api${t}`,l=await fetch(n,i);if(!l.ok){let e;try{e=await l.json()}catch(s){e={message:l.statusText||`HTTP error ${l.status}`}}console.error(`🔐 Authenticated API Error ${l.status} at ${n}:`,e);let s=e?.error?.message||e?.message||"Unknown server error";throw Error(`API request to ${n} failed with status ${l.status}: ${s}`)}return 204===l.status?null:await l.json()},[e?.access_token,s]);return{api:{get:(0,n.useCallback)(e=>a(e,{method:"GET"}),[a]),post:(0,n.useCallback)((e,s)=>a(e,{method:"POST",body:s?JSON.stringify(s):void 0}),[a]),put:(0,n.useCallback)((e,s)=>a(e,{method:"PUT",body:s?JSON.stringify(s):void 0}),[a]),delete:(0,n.useCallback)(e=>a(e,{method:"DELETE"}),[a])},isAuthenticated:!!s&&!!e?.access_token,isLoading:t,makeAuthenticatedRequest:a}}();return{...(0,n.useMemo)(()=>({getVehicles:()=>e.get("/vehicles"),getVehicleById:s=>e.get(`/vehicles/${s}`),createVehicle:s=>e.post("/vehicles",s),updateVehicle:(s,t)=>e.put(`/vehicles/${s}`,t),deleteVehicle:s=>e.delete(`/vehicles/${s}`),getEmployees:()=>e.get("/employees"),getEmployeeById:s=>e.get(`/employees/${s}`),createEmployee:s=>e.post("/employees",s),updateEmployee:(s,t)=>e.put(`/employees/${s}`,t),deleteEmployee:s=>e.delete(`/employees/${s}`),getDelegations:()=>e.get("/delegations"),getTasks:()=>e.get("/tasks"),getAdminDiagnostics:()=>e.get("/admin/diagnostics")}),[e]),isAuthenticated:s,isLoading:t}}(),[P,q]=(0,n.useState)(null),[S,z]=(0,n.useState)(!0),[_,$]=(0,n.useState)(null);return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)(c.z,{title:"Dashboard",description:"Welcome to your WorkHub dashboard. Manage everything from here.",icon:x.A}),(0,a.jsxs)("section",{"aria-labelledby":"quick-actions-title",children:[(0,a.jsx)("h2",{id:"quick-actions-title",className:"text-2xl font-semibold text-foreground mb-4",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4",children:[(0,a.jsx)(o.r,{actionType:"secondary",size:"lg",className:"w-full py-3 text-base md:text-lg",icon:(0,a.jsx)(p.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(i(),{href:"/vehicles",children:"Assets"})}),(0,a.jsx)(o.r,{actionType:"primary",size:"lg",className:"w-full py-3 text-base md:text-lg",icon:(0,a.jsx)(h.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(i(),{href:"/add-vehicle",children:"Add Asset"})}),(0,a.jsx)(o.r,{actionType:"tertiary",size:"lg",className:"w-full py-3 text-base md:text-lg",icon:(0,a.jsx)(u.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(i(),{href:"/service-history",children:"Maintenance"})}),(0,a.jsx)(o.r,{actionType:"tertiary",size:"lg",className:"w-full py-3 text-base md:text-lg",icon:(0,a.jsx)(g.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(i(),{href:"/delegations",children:"Projects"})}),(0,a.jsx)(o.r,{actionType:"tertiary",size:"lg",className:"w-full py-3 text-base md:text-lg",icon:(0,a.jsx)(y.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(i(),{href:"/tasks",children:"Tasks"})}),(0,a.jsx)(o.r,{actionType:"tertiary",size:"lg",className:"w-full py-3 text-base md:text-lg",icon:(0,a.jsx)(j.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(i(),{href:"/employees",children:"Team"})})]})]}),(0,a.jsxs)("section",{"aria-labelledby":"overview-title",children:[(0,a.jsx)("h2",{id:"overview-title",className:"text-2xl font-semibold text-foreground mb-4",children:"At a Glance"}),(0,a.jsx)(m.gO,{isLoading:S||k,error:_,data:P,onRetry:()=>window.location.reload(),loadingComponent:(0,a.jsx)(m.jt,{variant:"stats",count:5}),children:e=>(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",children:[(0,a.jsx)(C,{title:"Total Assets",value:e.vehicleCount,icon:f.A,description:"Assets tracked.",link:"/vehicles",linkText:"Manage Assets"}),(0,a.jsx)(C,{title:"Maintenance Logged",value:e.totalServices,icon:v.A,description:"Maintenance records.",link:"/service-history",linkText:"View History"}),(0,a.jsx)(C,{title:"Active Delegations",value:e.delegationCount,icon:g.A,description:"Delegations managed.",link:"/delegations",linkText:"View Delegations"}),(0,a.jsx)(C,{title:"Pending Tasks",value:e.taskCount,icon:y.A,description:"Tasks needing attention.",link:"/tasks",linkText:"View Tasks"}),(0,a.jsx)(C,{title:"Team Members",value:e.employeeCount,icon:j.A,description:"Team members in system.",link:"/employees",linkText:"Manage Team"})]})})]}),(0,a.jsxs)("section",{"aria-labelledby":"features-title",children:[(0,a.jsx)("h2",{id:"features-title",className:"text-2xl font-semibold text-foreground mb-6",children:"Features & Tools"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)(E,{title:"Asset Management",description:"Easily add, view, and manage details for all your assets. Keep everything organized in one central hub.",icon:N,link:"/vehicles",actionText:"Manage Assets"}),(0,a.jsx)(E,{title:"Maintenance History",description:"Access a comprehensive log of all maintenance for all assets. Filter by asset or service type, and print reports.",icon:u.A,link:"/service-history",actionText:"View Maintenance History"}),(0,a.jsx)(E,{title:"Delegation & Trip Management",description:"Track and manage events, trips, delegates, and related flight details efficiently.",icon:g.A,link:"/delegations",actionText:"Manage Delegations"}),(0,a.jsx)(E,{title:"Task & Dispatch Management",description:"Create, assign, and track tasks. Optimize workload and ensure timely completion.",icon:b,link:"/tasks",actionText:"Manage Tasks"}),(0,a.jsx)(E,{title:"Team Management",description:"Manage team member profiles, roles, contact information, and assignments.",icon:j.A,link:"/employees",actionText:"Manage Team"}),(0,a.jsx)(E,{title:"AI Maintenance Advisor",description:"Receive intelligent maintenance suggestions based on your asset's details and service history.",icon:w.A,actionText:"Get Suggestions",link:"/vehicles"}),(0,a.jsx)(E,{title:"Printable PDF Reports",description:"Generate professional PDF reports of asset details, maintenance histories, delegations and task lists.",icon:A.A,actionText:"Access Reporting Features",isFuture:!1}),(0,a.jsx)(E,{title:"Expense Tracking & Analysis",description:"Monitor all asset-related expenses, categorize spending, and visualize cost trends over time.",icon:T,isFuture:!0,actionText:"Track Expenses"}),(0,a.jsx)(E,{title:"Automated Reminders",description:"Set up and receive automated reminders for upcoming maintenance, inspections, and other important dates.",icon:M,isFuture:!0,actionText:"Setup Reminders"})]})]})]})}"undefined"!=typeof document&&(document.title="Dashboard - WorkHub")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70999:(e,s,t)=>{Promise.resolve().then(t.bind(t,70070))},74075:e=>{"use strict";e.exports=require("zlib")},76485:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84245:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21204)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85726:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var a=t(60687),r=t(4780);function i({className:e,...s}){return(0,a.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",e),...s})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,3744,1658,8141],()=>t(84245));module.exports=a})();