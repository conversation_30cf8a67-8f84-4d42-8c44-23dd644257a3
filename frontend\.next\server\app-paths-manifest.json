{"/_not-found/page": "app/_not-found/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/delegations/[id]/edit/page": "app/delegations/[id]/edit/page.js", "/add-vehicle/page": "app/add-vehicle/page.js", "/delegations/[id]/page": "app/delegations/[id]/page.js", "/delegations/page": "app/delegations/page.js", "/delegations/add/page": "app/delegations/add/page.js", "/employees/[id]/page": "app/employees/[id]/page.js", "/auth-test/page": "app/auth-test/page.js", "/employees/[id]/edit/page": "app/employees/[id]/edit/page.js", "/employees/edit/[id]/page": "app/employees/edit/[id]/page.js", "/employees/add/page": "app/employees/add/page.js", "/employees/page": "app/employees/page.js", "/page": "app/page.js", "/employees/new/page": "app/employees/new/page.js", "/tasks/[id]/page": "app/tasks/[id]/page.js", "/service-history/page": "app/service-history/page.js", "/tasks/[id]/edit/page": "app/tasks/[id]/edit/page.js", "/tasks/add/page": "app/tasks/add/page.js", "/tasks/page": "app/tasks/page.js", "/vehicles/[id]/page": "app/vehicles/[id]/page.js", "/vehicles/edit/[id]/page": "app/vehicles/edit/[id]/page.js", "/vehicles/page": "app/vehicles/page.js", "/vehicles/new/page": "app/vehicles/new/page.js", "/delegations/[id]/report/page": "app/delegations/[id]/report/page.js", "/delegations/report/list/page": "app/delegations/report/list/page.js", "/admin/page": "app/admin/page.js", "/vehicles/[id]/report/service-history/page": "app/vehicles/[id]/report/service-history/page.js", "/vehicles/[id]/report/page": "app/vehicles/[id]/report/page.js", "/tasks/report/page": "app/tasks/report/page.js"}