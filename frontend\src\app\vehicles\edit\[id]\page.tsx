'use client';

import React, {useEffect, useState, useCallback} from 'react';
import {useRouter, useParams} from 'next/navigation';
import VehicleForm from '@/components/vehicles/VehicleForm';
import {getVehicleById, updateVehicle} from '@/lib/store';
import {VehicleFormData} from '@/lib/schemas/vehicleSchemas';
import type {Vehicle} from '@/lib/types';
import {PageHeader} from '@/components/ui/PageHeader';
import {Car, Edit} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';
import {Skeleton} from '@/components/ui/skeleton';
import {Button} from '@/components/ui/button';

const EditVehiclePage = () => {
	const router = useRouter();
	const params = useParams();
	const {toast} = useToast();

	const vehicleId = params.id ? Number(params.id) : null;

	const [vehicle, setVehicle] = useState<Partial<Vehicle> | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [isFetching, setIsFetching] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const fetchVehicle = useCallback(async () => {
		if (!vehicleId) {
			setError('No vehicle ID provided.');
			setIsFetching(false);
			return;
		}
		setIsFetching(true);
		try {
			const data = await getVehicleById(vehicleId);
			if (data) {
				setVehicle(data);
			} else {
				setError('Vehicle not found.');
			}
		} catch (err: any) {
			console.error('Failed to fetch vehicle:', err);
			setError(err.message || 'Failed to load vehicle data.');
		} finally {
			setIsFetching(false);
		}
	}, [vehicleId]);

	useEffect(() => {
		fetchVehicle();
	}, [fetchVehicle]);

	const handleSubmit = async (data: VehicleFormData) => {
		if (!vehicleId) {
			setError('Cannot update vehicle without an ID.');
			return;
		}
		setIsLoading(true);
		setError(null);
		try {
			// Ensure initialOdometer is a number, as VehicleFormData might have it as optional
			const dataToUpdate = {
				...data,
				initialOdometer:
					data.initialOdometer === undefined
						? vehicle?.initialOdometer || 0
						: data.initialOdometer,
			};
			// updateVehicle in store expects Partial<Omit<Vehicle, 'id' | 'createdAt' | 'updatedAt' | 'serviceHistory'>>
			// VehicleFormData aligns with this after stripping id, createdAt, etc.
			await updateVehicle(vehicleId, dataToUpdate);
			toast({
				title: 'Vehicle Updated',
				description: `${data.make} ${data.model} has been successfully updated.`,
				variant: 'default',
			});
			router.push('/vehicles'); // Navigate to the list page
		} catch (err: any) {
			console.error('Failed to update vehicle:', err);
			setError(
				err.message || 'An unexpected error occurred. Please try again.'
			);
			toast({
				title: 'Error Updating Vehicle',
				description:
					err.message ||
					'Could not update the vehicle. Please check the details and try again.',
				variant: 'destructive',
			});
		} finally {
			setIsLoading(false);
		}
	};

	if (isFetching) {
		return (
			<div className='container mx-auto py-8 space-y-8'>
				<PageHeader
					title='Edit Vehicle'
					description='Loading vehicle details...'
					icon={Edit}
				/>
				{/* Basic skeleton for form area */}
				<div className='max-w-2xl mx-auto space-y-6'>
					<Skeleton className='h-10 w-1/3' />
					<Skeleton className='h-12 w-full' />
					<Skeleton className='h-12 w-full' />
					<Skeleton className='h-12 w-full' />
					<div className='flex justify-end space-x-3 pt-6'>
						<Skeleton className='h-10 w-24' />
						<Skeleton className='h-10 w-24' />
					</div>
				</div>
			</div>
		);
	}

	if (error && !vehicle) {
		// Show critical error if vehicle couldn't be loaded
		return (
			<div className='container mx-auto py-8 space-y-8 text-center'>
				<PageHeader title='Error' description={error} icon={Car} />
				<Button onClick={() => router.push('/vehicles')}>
					Back to Vehicles
				</Button>
			</div>
		);
	}

	return (
		<div className='container mx-auto py-8 space-y-8'>
			<PageHeader
				title='Edit Vehicle'
				description={`Update details for ${vehicle?.make || 'vehicle'} ${
					vehicle?.model || ''
				}`}
				icon={Edit}
			/>
			{/* Display non-critical errors, e.g., submission error, above the form */}
			{error && vehicle && (
				<p className='text-red-500 bg-red-100 p-3 rounded-md'>Error: {error}</p>
			)}
			{vehicle && (
				<VehicleForm
					onSubmit={handleSubmit}
					initialData={vehicle}
					isEditing={true}
					isLoading={isLoading}
				/>
			)}
		</div>
	);
};

export default EditVehiclePage;
