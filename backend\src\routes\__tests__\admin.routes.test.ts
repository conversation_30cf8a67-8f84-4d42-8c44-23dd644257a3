/**
 * Admin Routes Test
 */

// Import setup
import '../../tests/setup';

import express from 'express';
import request from 'supertest';
// Removed: import adminRoutes from '../admin.routes';
// Removed: import * as adminController from '../../controllers/admin.controller.ts';

// --- Mock Controller Functions START ---
const mockCtrlGetHealthStatus = jest.fn();
const mockCtrlGetPerformanceMetrics = jest.fn();
const mockCtrlGetErrorLogs = jest.fn();

(jest as any).unstable_mockModule(
	'src/controllers/admin.controller.ts',
	() => ({
		getHealthStatus: mockCtrlGetHealthStatus,
		getPerformanceMetrics: mockCtrlGetPerformanceMetrics,
		getErrorLogs: mockCtrlGetErrorLogs,
	})
);
// --- <PERSON>ck Controller Functions END ---

// Dynamically imported routes
let adminRoutes: express.Router;

describe('Admin Routes', () => {
	let app: express.Application;

	beforeAll(async () => {
		// Prime the mock for admin.controller
		await import('src/controllers/admin.controller.ts');
		// Dynamically import adminRoutes after mocks are set up
		const routesModule = await import('../admin.routes');
		adminRoutes = routesModule.default; // Assuming adminRoutes is the default export
	});

	beforeEach(() => {
		app = express();
		app.use(express.json());
		app.use('/api/admin', adminRoutes);

		// Reset all individual mock functions
		mockCtrlGetHealthStatus.mockClear();
		mockCtrlGetPerformanceMetrics.mockClear();
		mockCtrlGetErrorLogs.mockClear();
	});

	describe('GET /api/admin/health', () => {
		it('should call the health status controller', async () => {
			// Mock controller implementation
			mockCtrlGetHealthStatus.mockImplementation((req, res) => {
				res.status(200).json({
					status: 'success',
					data: {
						status: 'UP',
						message: 'Backend service is healthy',
						components: {
							database: {
								status: 'UP',
								type: 'PostgreSQL via Prisma',
							},
						},
					},
				});
			});

			// Make the request
			const response = await request(app).get('/api/admin/health');

			// Verify the response
			expect(response.status).toBe(200);
			expect(response.body.status).toBe('success');
			expect(response.body.data.status).toBe('UP');

			// Verify the controller was called
			expect(mockCtrlGetHealthStatus).toHaveBeenCalled();
		});
	});

	describe('GET /api/admin/performance', () => {
		it('should call the performance metrics controller', async () => {
			// Mock controller implementation
			mockCtrlGetPerformanceMetrics.mockImplementation((req, res) => {
				res.status(200).json({
					status: 'success',
					data: {
						cacheHitRate: {
							indexHitRate: 90,
							tableHitRate: 85,
						},
						connectionCount: 5,
					},
				});
			});

			// Make the request
			const response = await request(app).get('/api/admin/performance');

			// Verify the response
			expect(response.status).toBe(200);
			expect(response.body.status).toBe('success');
			expect(response.body.data).toHaveProperty('cacheHitRate');
			expect(response.body.data).toHaveProperty('connectionCount');

			// Verify the controller was called
			expect(mockCtrlGetPerformanceMetrics).toHaveBeenCalled();
		});
	});

	describe('GET /api/admin/errors', () => {
		it('should call the error logs controller with default parameters', async () => {
			// Mock controller implementation
			mockCtrlGetErrorLogs.mockImplementation((req, res) => {
				res.status(200).json({
					status: 'success',
					data: [
						{
							id: 'log-1',
							timestamp: '2023-01-01T00:00:00.000Z',
							level: 'ERROR',
							message: 'Test error',
						},
					],
					pagination: {
						page: 1,
						limit: 10,
						total: 1,
						totalPages: 1,
					},
				});
			});

			// Make the request
			const response = await request(app).get('/api/admin/errors');

			// Verify the response
			expect(response.status).toBe(200);
			expect(response.body.status).toBe('success');
			expect(Array.isArray(response.body.data)).toBe(true);
			expect(response.body.pagination).toBeDefined();

			// Verify the controller was called
			expect(mockCtrlGetErrorLogs).toHaveBeenCalled();
			const defaultArgs = mockCtrlGetErrorLogs.mock.calls[0];
			const defaultReq = defaultArgs[0];
			expect((defaultReq as any).validatedData).toEqual({
				page: 1,
				limit: 10,
			});
		});

		it('should call the error logs controller with custom parameters', async () => {
			// Mock controller implementation
			mockCtrlGetErrorLogs.mockImplementation((req, res) => {
				res.status(200).json({
					status: 'success',
					data: [
						{
							id: 'log-1',
							timestamp: '2023-01-01T00:00:00.000Z',
							level: 'ERROR',
							message: 'Test error',
						},
					],
					pagination: {
						page: 2,
						limit: 20,
						total: 25,
						totalPages: 2,
					},
				});
			});

			// Make the request with custom query params
			const response = await request(app)
				.get('/api/admin/errors')
				.query({page: '2', limit: '20', level: 'ERROR'});

			// Verify the response
			expect(response.status).toBe(200);

			// Verify the controller was called with custom params
			expect(mockCtrlGetErrorLogs).toHaveBeenCalled();
			const customArgs = mockCtrlGetErrorLogs.mock.calls[0];
			const customReq = customArgs[0];
			expect((customReq as any).validatedData).toEqual({
				page: 2,
				limit: 20,
				level: 'ERROR',
			});
		});
	});
});
