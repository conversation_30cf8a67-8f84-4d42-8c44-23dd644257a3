'use client';

import {useState, useEffect, useCallback} from 'react';
import {getVehicles} from '@/lib/store';
import {getAllEnrichedServiceRecords} from '@/lib/services/serviceRecordService';
import type {Vehicle, EnrichedServiceRecord} from '@/lib/types';
import {PageHeader} from '@/components/ui/PageHeader';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
} from '@/components/ui/card';
import {Label} from '@/components/ui/label';
import {Badge} from '@/components/ui/badge';
import {History, Search, PlusCircle, X, Car, Wrench} from 'lucide-react';
import Link from 'next/link';
import {Skeleton} from '@/components/ui/skeleton';
import {EnhancedServiceHistoryContainer} from '@/components/service-history/EnhancedServiceHistoryContainer';
import ServiceRecordsErrorBoundary from '@/components/error-boundaries/ServiceRecordsErrorBoundary';
import {ActionButton} from '@/components/ui/action-button';
import ErrorBoundary from '@/components/ErrorBoundary';
import {ReportActions} from '@/components/reports/ReportActions';

// Common service types for filtering
const commonServicesList = [
	'Oil Change',
	'Tire Rotation',
	'Brake Service',
	'Battery Replacement',
	'Air Filter Replacement',
	'Cabin Air Filter Replacement',
	'Wiper Blade Replacement',
	'Fluid Check/Top-up',
	'Spark Plug Replacement',
	'Coolant Flush',
	'Transmission Service',
	'Wheel Alignment',
	'State Inspection',
	'Other',
];

export default function ServiceHistoryPage() {
	const [allVehicles, setAllVehicles] = useState<Vehicle[]>([]);
	const [serviceRecords, setServiceRecords] = useState<EnrichedServiceRecord[]>(
		[]
	);
	const [filteredRecords, setFilteredRecords] = useState<
		EnrichedServiceRecord[]
	>([]);
	const [isLoadingVehicles, setIsLoadingVehicles] = useState(true);
	const [isLoadingRecords, setIsLoadingRecords] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selectedVehicleId, setSelectedVehicleId] = useState('all');
	const [selectedServiceType, setSelectedServiceType] = useState('all');
	const [searchTerm, setSearchTerm] = useState('');
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
	const [uniqueServiceTypes, setUniqueServiceTypes] = useState<string[]>([]);
	const [retryCount, setRetryCount] = useState(0);

	// Debounce search term
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Fetch vehicles
	const fetchVehicles = useCallback(async () => {
		setIsLoadingVehicles(true);
		try {
			const vehiclesData = await getVehicles();
			setAllVehicles(Array.isArray(vehiclesData) ? vehiclesData : []);
		} catch (error) {
			console.error('Failed to fetch vehicles:', error);
			setAllVehicles([]);
		} finally {
			setIsLoadingVehicles(false);
		}
	}, []);

	// Fetch service records
	const fetchServiceRecords = useCallback(async () => {
		setIsLoadingRecords(true);
		setError(null);
		try {
			const records = await getAllEnrichedServiceRecords();
			setServiceRecords(records);
		} catch (error) {
			console.error('Failed to fetch service records:', error);
			setError('Failed to load service records. Please try again.');
		} finally {
			setIsLoadingRecords(false);
		}
	}, []);

	// Initial data fetch
	useEffect(() => {
		fetchVehicles();
		fetchServiceRecords();
	}, [fetchVehicles, fetchServiceRecords, retryCount]);

	// Set unique service types
	useEffect(() => {
		// Get unique service types from records
		const types = new Set<string>();
		serviceRecords.forEach((record) => {
			record.servicePerformed.forEach((service) => types.add(service));
		});
		// Ensure common services are listed even if not used yet
		commonServicesList.forEach((service) => types.add(service));
		setUniqueServiceTypes(Array.from(types).sort());
	}, [serviceRecords]);

	// Filter records based on selected filters
	useEffect(() => {
		let tempRecords = [...serviceRecords];

		// Filter by vehicle
		if (selectedVehicleId !== 'all') {
			tempRecords = tempRecords.filter(
				(record) => record.vehicleId === selectedVehicleId
			);
		}

		// Filter by service type
		if (selectedServiceType !== 'all') {
			tempRecords = tempRecords.filter((record) =>
				record.servicePerformed.includes(selectedServiceType)
			);
		}

		// Filter by search term
		if (debouncedSearchTerm) {
			const lowerSearchTerm = debouncedSearchTerm.toLowerCase();
			tempRecords = tempRecords.filter(
				(record) =>
					`${record.vehicleMake} ${record.vehicleModel}`
						.toLowerCase()
						.includes(lowerSearchTerm) ||
					record.servicePerformed
						.join(' ')
						.toLowerCase()
						.includes(lowerSearchTerm) ||
					(record.notes &&
						record.notes.toLowerCase().includes(lowerSearchTerm)) ||
					(record.vehiclePlateNumber &&
						record.vehiclePlateNumber
							.toLowerCase()
							.includes(lowerSearchTerm)) ||
					record.odometer.toString().includes(lowerSearchTerm)
			);
		}

		setFilteredRecords(tempRecords);
	}, [
		serviceRecords,
		selectedVehicleId,
		selectedServiceType,
		debouncedSearchTerm,
	]);

	// Handle retry
	const handleRetry = useCallback(() => {
		setRetryCount((prev) => prev + 1);
	}, []);

	// Show loading state when vehicles are loading
	if (isLoadingVehicles) {
		return (
			<ErrorBoundary>
				<div className='space-y-6'>
					<PageHeader title='Service History' icon={History}>
						<div className='flex gap-2'>
							<Skeleton className='h-10 w-32' />
							<Skeleton className='h-10 w-32' />
						</div>
					</PageHeader>
					<Card>
						<CardContent className='pt-6'>
							<div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6'>
								<Skeleton className='h-10 w-full' />
								<Skeleton className='h-10 w-full' />
								<Skeleton className='h-10 w-full' />
							</div>
						</CardContent>
					</Card>
					<div className='space-y-4'>
						{[...Array(5)].map((_, i) => (
							<div key={i} className='flex items-center space-x-4 p-4 border-b'>
								<Skeleton className='h-6 w-1/6' />
								<Skeleton className='h-6 w-1/6' />
								<Skeleton className='h-6 w-2/6' />
								<Skeleton className='h-6 w-1/6' />
								<Skeleton className='h-6 w-1/6' />
							</div>
						))}
					</div>
				</div>
			</ErrorBoundary>
		);
	}

	return (
		<ErrorBoundary>
			<div className='space-y-6 print-container'>
				<PageHeader
					title='Service History Report'
					description='View and manage all service records for your vehicles.'
					icon={History}>
					<div className='flex gap-2 no-print'>
						<ActionButton
							actionType='tertiary'
							asChild
							icon={<PlusCircle className='h-4 w-4' />}>
							<Link href='/vehicles'>Log New Service</Link>
						</ActionButton>
						<ReportActions
							reportContentId='#service-history-report-content'
							reportType='service-history'
							tableId='#service-history-table'
							fileName={`service-history-report-${
								new Date().toISOString().split('T')[0]
							}`}
							enableCsv={filteredRecords.length > 0}
						/>
					</div>
				</PageHeader>

				{/* Filters */}
				<Card className='shadow-sm no-print'>
					<CardHeader className='pb-2'>
						<CardTitle className='text-lg'>Filter Options</CardTitle>
						<CardDescription>
							Filter service records by vehicle, service type, or search terms
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-end filter-grid'>
							<div>
								<Label htmlFor='vehicle-filter'>Filter by Vehicle</Label>
								<Select
									value={selectedVehicleId}
									onValueChange={setSelectedVehicleId}
									aria-label='Filter by vehicle'>
									<SelectTrigger id='vehicle-filter' className='w-full mt-1.5'>
										<SelectValue placeholder='All Vehicles' />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value='all'>All Vehicles</SelectItem>
										{allVehicles.map((vehicle) => (
											<SelectItem key={vehicle.id} value={String(vehicle.id)}>
												{vehicle.make} {vehicle.model} ({vehicle.year})
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							<div>
								<Label htmlFor='service-filter'>Filter by Service Type</Label>
								<Select
									value={selectedServiceType}
									onValueChange={setSelectedServiceType}
									aria-label='Filter by service type'>
									<SelectTrigger id='service-filter' className='w-full mt-1.5'>
										<SelectValue placeholder='All Services' />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value='all'>All Services</SelectItem>
										{uniqueServiceTypes.map((service) => (
											<SelectItem key={service} value={service}>
												{service}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							<div>
								<Label htmlFor='search-records'>Search Records</Label>
								<div className='relative mt-1.5'>
									<Input
										id='search-records'
										type='text'
										placeholder='Search by keyword, notes, plate...'
										value={searchTerm}
										onChange={(e) => setSearchTerm(e.target.value)}
										className='pl-10 pr-10'
										aria-label='Search service records'
									/>
									<Search
										className='absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground'
										aria-hidden='true'
									/>
									{searchTerm && (
										<Button
											variant='ghost'
											size='icon'
											className='absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7'
											onClick={() => setSearchTerm('')}
											aria-label='Clear search'>
											<X className='h-4 w-4' />
											<span className='sr-only'>Clear search</span>
										</Button>
									)}
								</div>
							</div>
						</div>

						{/* Filter Summary & Reset */}
						{(selectedVehicleId !== 'all' ||
							selectedServiceType !== 'all' ||
							searchTerm) && (
							<div className='mt-6 flex flex-wrap items-center justify-between border border-border p-3 rounded-md'>
								<div className='flex flex-wrap gap-2 items-center'>
									<span className='font-medium text-sm'>Active Filters:</span>
									{selectedVehicleId !== 'all' && (
										<Badge
											variant='outline'
											className='flex items-center gap-1'>
											<Car className='h-3 w-3' />
											{
												allVehicles.find(
													(v) => v.id === Number(selectedVehicleId)
												)?.make
											}{' '}
											{
												allVehicles.find(
													(v) => v.id === Number(selectedVehicleId)
												)?.model
											}
										</Badge>
									)}
									{selectedServiceType !== 'all' && (
										<Badge
											variant='outline'
											className='flex items-center gap-1'>
											<Wrench className='h-3 w-3' />
											{selectedServiceType}
										</Badge>
									)}
									{searchTerm && (
										<Badge
											variant='outline'
											className='flex items-center gap-1'>
											<Search className='h-3 w-3' />"{searchTerm}"
										</Badge>
									)}
								</div>
								<Button
									variant='outline'
									size='sm'
									onClick={() => {
										setSelectedVehicleId('all');
										setSelectedServiceType('all');
										setSearchTerm('');
									}}
									className='mt-2 sm:mt-0'
									aria-label='Reset all filters'>
									<X className='h-3 w-3 mr-1' />
									Reset Filters
								</Button>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Service History Content */}
				<div id='service-history-report-content' className='report-content'>
					<header className='text-center mb-8 pb-4 border-b-2 border-gray-300 print-only'>
						<h1 className='text-3xl font-bold text-gray-800'>
							Service History Report
						</h1>
						<p className='text-md text-gray-600'>
							{selectedVehicleId !== 'all'
								? `Vehicle: ${
										allVehicles.find((v) => v.id === Number(selectedVehicleId))
											?.make
								  } ${
										allVehicles.find((v) => v.id === Number(selectedVehicleId))
											?.model
								  }`
								: 'All Vehicles'}
						</p>
					</header>

					<ServiceRecordsErrorBoundary>
						<EnhancedServiceHistoryContainer
							records={filteredRecords}
							isLoading={isLoadingRecords}
							error={error}
							onRetry={handleRetry}
							showVehicleInfo={true}
							vehicleSpecific={false}
						/>
					</ServiceRecordsErrorBoundary>

					<footer className='mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500'>
						<p>Report generated on: {new Date().toLocaleDateString()}</p>
						<p>WorkHub - Vehicle Service Management</p>
					</footer>
				</div>

				{/* Print Styles */}
				<style jsx global>{`
					.print-only {
						display: none;
					}
					@media print {
						.no-print {
							display: none !important;
						}
						.print-only {
							display: block;
						}
						.print-container {
							padding: 1rem;
						}
						.card-print {
							box-shadow: none !important;
							border: none !important;
						}
						.print-service-col {
							max-width: 200px;
							white-space: normal !important;
						}
						.print-notes-col {
							max-width: 200px;
							white-space: normal !important;
						}
						.print-text-wrap {
							word-break: break-word;
							white-space: normal !important;
						}
					}

					@media (max-width: 640px) {
						.delegations-table-container,
						.overflow-x-auto {
							overflow-x: auto;
						}

						.filter-grid {
							grid-template-columns: 1fr !important;
						}

						.summary-grid {
							grid-template-columns: 1fr 1fr !important;
						}
					}
				`}</style>
			</div>
		</ErrorBoundary>
	);
}
