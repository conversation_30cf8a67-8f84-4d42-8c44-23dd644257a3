'use client';

import {useState, useEffect, useCallback} from 'react'; // Added useCallback
import Link from 'next/link';
import {useRouter} from 'next/navigation';
import {ActionButton} from '@/components/ui/action-button';
import {PlusCircle, Briefcase, Search} from 'lucide-react';
import {PageHeader} from '@/components/ui/PageHeader';
import {Input} from '@/components/ui/input';
import DelegationListContainer from '@/components/delegations/DelegationListContainer';
import {SkeletonLoader, DataLoader} from '@/components/ui/loading';
import DelegationCard from '@/components/delegations/DelegationCard'; // Corrected import for use inside DataLoader
import ErrorBoundary from '@/components/ErrorBoundary'; // Import ErrorBoundary
import {ViewReportButton} from '@/components/reports/ViewReportButton';

export default function DelegationsPage() {
	const router = useRouter();
	const [searchTerm, setSearchTerm] = useState('');

	const getDelegationsReportUrl = () => {
		const queryParams = new URLSearchParams({searchTerm}).toString();
		return `/delegations/report/list?${queryParams}`;
	};

	// This component now focuses on layout and passing props to DelegationListContainer
	// DataLoader will be used within DelegationListContainer or here, depending on where data fetching state is managed
	// For this structure, DataLoader is suitable here, and DelegationListContainer can just filter.
	// Let's assume DelegationListContainer handles its own loading/error states internally using DataLoader.

	return (
		<ErrorBoundary>
			<div className='space-y-8'>
				<PageHeader
					title='Manage Delegations'
					description='Track and manage all your events, trips, and delegate information.'
					icon={Briefcase}>
					<div className='flex gap-2 items-center'>
						<ActionButton
							actionType='primary'
							icon={<PlusCircle className='mr-2 h-4 w-4' />}
							asChild>
							<Link href='/delegations/add'>Add New Delegation</Link>
						</ActionButton>
						<ViewReportButton
							getReportUrl={getDelegationsReportUrl}
							isList={true}
						/>
					</div>
				</PageHeader>

				<div className='mb-6 p-4 bg-card rounded-lg shadow-md'>
					<div className='relative'>
						<Search className='absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground' />
						<Input
							type='text'
							placeholder='Search delegations (Event, Location, Delegate, Status...)'
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className='pl-10 w-full'
						/>
					</div>
				</div>

				<DelegationListContainer searchTerm={searchTerm}>
					{({delegations, loading, error, fetchDelegations}) => (
						<DataLoader
							isLoading={loading}
							error={error}
							data={delegations}
							onRetry={fetchDelegations}
							loadingComponent={<SkeletonLoader variant='card' count={3} />}
							emptyComponent={
								<div className='text-center py-12 bg-card rounded-lg shadow-md'>
									<Briefcase className='mx-auto h-16 w-16 text-muted-foreground mb-6' />
									<h3 className='text-2xl font-semibold text-foreground mb-2'>
										{searchTerm
											? 'No Delegations Match Your Search'
											: 'No Delegations Yet!'}
									</h3>
									<p className='text-muted-foreground mt-2 mb-6 max-w-md mx-auto'>
										{searchTerm
											? 'Try adjusting your search terms or add a new delegation.'
											: "It looks like you haven't added any delegations yet. Get started by adding one."}
									</p>
									{!searchTerm && (
										<ActionButton
											actionType='primary'
											size='lg'
											icon={<PlusCircle className='h-4 w-4' />}
											asChild>
											<Link href='/delegations/add'>
												Add Your First Delegation
											</Link>
										</ActionButton>
									)}
								</div>
							}>
							{(delegationsData) => (
								<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
									{delegationsData.map((delegation) => (
										<DelegationCard
											key={delegation.id}
											delegation={delegation}
										/>
									))}
								</div>
							)}
						</DataLoader>
					)}
				</DelegationListContainer>
			</div>
		</ErrorBoundary>
	);
}
