import {Request, Response} from 'express';
import * as delegationModel from '../models/delegation.model.js';
import {
	Prisma,
	DelegationStatus as PrismaDelegationStatus,
} from '../generated/prisma/index.js';
import {
	emitDelegationChange,
	SOCKET_EVENTS,
} from '../services/socketService.js';
import type {DelegationStatusEnumType} from '../schemas/delegation.schema.js';

// Define a more specific type for what the model's updateDelegation expects
// This type does NOT extend Prisma.DelegationUpdateInput directly,
// as it contains fields for manual processing by the model layer.
interface CustomDelegationUpdatePayload {
	// Fields that can be directly part of Prisma.DelegationUpdateInput
	eventName?: Prisma.StringFieldUpdateOperationsInput | string;
	location?: Prisma.StringFieldUpdateOperationsInput | string;
	durationFrom?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	durationTo?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
	invitationFrom?:
		| Prisma.NullableStringFieldUpdateOperationsInput
		| string
		| null;
	invitationTo?:
		| Prisma.NullableStringFieldUpdateOperationsInput
		| string
		| null;
	notes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null;
	imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null;
	status?:
		| Prisma.EnumDelegationStatusFieldUpdateOperationsInput
		| PrismaDelegationStatus; // Use imported Prisma enum

	// Fields for manual processing by the model layer
	flightArrivalDetails?: Prisma.FlightDetailsCreateInput | null; // Corrected to Prisma type
	flightDepartureDetails?: Prisma.FlightDetailsCreateInput | null; // Corrected to Prisma type
	delegates?: {name: string; title: string; notes?: string}[];
	statusChangeReason?: string; // For status history logic in model
}

const processDelegationData = (data: any): Prisma.DelegationCreateInput => {
	// Log the incoming data for debugging
	console.log('Processing delegation data:', JSON.stringify(data, null, 2));

	// Validate required fields
	if (!data.eventName) {
		throw new Error('Event name is required');
	}
	if (!data.location) {
		throw new Error('Location is required');
	}
	if (!data.durationFrom) {
		throw new Error('Start date is required');
	}
	if (!data.durationTo) {
		throw new Error('End date is required');
	}

	// Validate and parse dates
	let durationFrom, durationTo;
	try {
		durationFrom = new Date(data.durationFrom);
		if (isNaN(durationFrom.getTime())) {
			throw new Error(`Invalid start date format: ${data.durationFrom}`);
		}
	} catch (error) {
		throw new Error(`Invalid start date format: ${data.durationFrom}`);
	}

	try {
		durationTo = new Date(data.durationTo);
		if (isNaN(durationTo.getTime())) {
			throw new Error(`Invalid end date format: ${data.durationTo}`);
		}
	} catch (error) {
		throw new Error(`Invalid end date format: ${data.durationTo}`);
	}

	// Validate date range
	if (durationFrom > durationTo) {
		throw new Error('End date cannot be earlier than start date');
	}

	// Validate status if provided
	let status: PrismaDelegationStatus = PrismaDelegationStatus.Planned;
	if (data.status) {
		const validStatuses = Object.values(PrismaDelegationStatus);
		if (!validStatuses.includes(data.status)) {
			throw new Error(
				`Invalid status value: ${
					data.status
				}. Valid values are: ${validStatuses.join(', ')}`
			);
		}
		status = data.status as PrismaDelegationStatus;
	}

	// Process flight details with validation
	let flightArrivalDetails = undefined;
	if (data.flightArrivalDetails) {
		const {flightNumber, dateTime, airport} = data.flightArrivalDetails;

		// Check if all required fields are present
		const missingFields = [];
		if (!flightNumber) missingFields.push('flightNumber');
		if (!dateTime) missingFields.push('dateTime');
		if (!airport) missingFields.push('airport');

		if (missingFields.length > 0) {
			throw new Error(
				`Flight arrival details missing required fields: ${missingFields.join(
					', '
				)}`
			);
		}

		// Parse and validate the date
		let parsedDateTime;
		try {
			parsedDateTime = new Date(dateTime);
			if (isNaN(parsedDateTime.getTime())) {
				throw new Error(`Invalid date format: ${dateTime}`);
			}
		} catch (error) {
			throw new Error(`Invalid flight arrival date/time format: ${dateTime}`);
		}

		flightArrivalDetails = {
			create: {
				...data.flightArrivalDetails,
				dateTime: parsedDateTime,
			},
		};
	}

	// Process flight departure details with validation
	let flightDepartureDetails = undefined;
	if (data.flightDepartureDetails) {
		const {flightNumber, dateTime, airport} = data.flightDepartureDetails;

		// Check if all required fields are present
		const missingFields = [];
		if (!flightNumber) missingFields.push('flightNumber');
		if (!dateTime) missingFields.push('dateTime');
		if (!airport) missingFields.push('airport');

		if (missingFields.length > 0) {
			throw new Error(
				`Flight departure details missing required fields: ${missingFields.join(
					', '
				)}`
			);
		}

		// Parse and validate the date
		let parsedDateTime;
		try {
			parsedDateTime = new Date(dateTime);
			if (isNaN(parsedDateTime.getTime())) {
				throw new Error(`Invalid date format: ${dateTime}`);
			}
		} catch (error) {
			throw new Error(`Invalid flight departure date/time format: ${dateTime}`);
		}

		flightDepartureDetails = {
			create: {
				...data.flightDepartureDetails,
				dateTime: parsedDateTime,
			},
		};
	}

	// Process delegates
	let delegates = undefined;
	if (data.delegates && Array.isArray(data.delegates)) {
		delegates = {
			create: data.delegates.map((d: any) => {
				if (!d.name) {
					throw new Error('Delegate name is required');
				}
				if (!d.title) {
					throw new Error('Delegate title is required');
				}
				return {
					name: d.name,
					title: d.title,
					notes: d.notes,
				};
			}),
		};
	}

	// Create status history entry
	const statusHistory = {
		create: [
			{
				status,
				reason: 'Delegation created',
			},
		],
	};

	// Return the processed data
	return {
		...data,
		durationFrom,
		durationTo,
		status,
		delegates,
		flightArrivalDetails,
		flightDepartureDetails,
		statusHistory,
	};
};

const processDelegationUpdateData = (
	data: any
): CustomDelegationUpdatePayload => {
	// Log the incoming data for debugging
	console.log(
		'Processing delegation update data:',
		JSON.stringify(data, null, 2)
	);

	const updatePayload: CustomDelegationUpdatePayload = {};

	// Process basic fields
	if (data.eventName !== undefined) updatePayload.eventName = data.eventName;
	if (data.location !== undefined) updatePayload.location = data.location;
	if (data.invitationFrom !== undefined)
		updatePayload.invitationFrom = data.invitationFrom;
	if (data.invitationTo !== undefined)
		updatePayload.invitationTo = data.invitationTo;
	if (data.notes !== undefined) updatePayload.notes = data.notes;
	if (data.imageUrl !== undefined) updatePayload.imageUrl = data.imageUrl;

	// Process status with validation
	if (data.status !== undefined) {
		try {
			// Validate that the status is one of the allowed enum values
			const validStatuses = Object.values(PrismaDelegationStatus);
			if (!validStatuses.includes(data.status)) {
				console.warn(
					`Invalid status value received: ${
						data.status
					}. Valid values are: ${validStatuses.join(', ')}`
				);
				// Don't set the status if it's invalid
			} else {
				updatePayload.status = data.status as PrismaDelegationStatus;
			}
		} catch (error) {
			console.error('Error processing status:', error);
		}
	}

	// Process dates with validation
	if (data.durationFrom) {
		try {
			const parsedDate = new Date(data.durationFrom);
			if (isNaN(parsedDate.getTime())) {
				console.warn(`Invalid durationFrom date: ${data.durationFrom}`);
			} else {
				updatePayload.durationFrom = parsedDate;
			}
		} catch (error) {
			console.error('Error parsing durationFrom date:', error);
		}
	}

	if (data.durationTo) {
		try {
			const parsedDate = new Date(data.durationTo);
			if (isNaN(parsedDate.getTime())) {
				console.warn(`Invalid durationTo date: ${data.durationTo}`);
			} else {
				updatePayload.durationTo = parsedDate;
			}
		} catch (error) {
			console.error('Error parsing durationTo date:', error);
		}
	}

	// Process delegates
	if (data.delegates && Array.isArray(data.delegates)) {
		updatePayload.delegates = data.delegates;
	}

	// Process flight details with validation
	if (data.hasOwnProperty('flightArrivalDetails')) {
		if (data.flightArrivalDetails === null) {
			updatePayload.flightArrivalDetails = null;
		} else if (data.flightArrivalDetails) {
			try {
				// Validate required fields
				const {flightNumber, dateTime, airport} = data.flightArrivalDetails;

				// Check if all required fields are present
				const missingFields = [];
				if (!flightNumber) missingFields.push('flightNumber');
				if (!dateTime) missingFields.push('dateTime');
				if (!airport) missingFields.push('airport');

				if (missingFields.length > 0) {
					console.warn(
						`Incomplete flight arrival details - missing required fields: ${missingFields.join(
							', '
						)}`
					);
					// Return a more specific error in the response
					throw new Error(
						`Flight arrival details missing required fields: ${missingFields.join(
							', '
						)}`
					);
				} else {
					// Parse and validate the date
					let parsedDateTime;
					try {
						parsedDateTime = new Date(dateTime);
						if (isNaN(parsedDateTime.getTime())) {
							throw new Error(`Invalid date format: ${dateTime}`);
						}
					} catch (dateError) {
						console.warn(
							`Invalid flight arrival dateTime: ${dateTime}`,
							dateError
						);
						throw new Error(
							`Invalid flight arrival date/time format: ${dateTime}`
						);
					}

					// All validations passed, set the flight details
					updatePayload.flightArrivalDetails = {
						...data.flightArrivalDetails,
						dateTime: parsedDateTime,
					};
				}
			} catch (error) {
				console.error('Error processing flight arrival details:', error);
				// Re-throw the error to be caught by the controller
				throw error;
			}
		}
	}

	if (data.hasOwnProperty('flightDepartureDetails')) {
		if (data.flightDepartureDetails === null) {
			updatePayload.flightDepartureDetails = null;
		} else if (data.flightDepartureDetails) {
			try {
				// Validate required fields
				const {flightNumber, dateTime, airport} = data.flightDepartureDetails;

				// Check if all required fields are present
				const missingFields = [];
				if (!flightNumber) missingFields.push('flightNumber');
				if (!dateTime) missingFields.push('dateTime');
				if (!airport) missingFields.push('airport');

				if (missingFields.length > 0) {
					console.warn(
						`Incomplete flight departure details - missing required fields: ${missingFields.join(
							', '
						)}`
					);
					// Return a more specific error in the response
					throw new Error(
						`Flight departure details missing required fields: ${missingFields.join(
							', '
						)}`
					);
				} else {
					// Parse and validate the date
					let parsedDateTime;
					try {
						parsedDateTime = new Date(dateTime);
						if (isNaN(parsedDateTime.getTime())) {
							throw new Error(`Invalid date format: ${dateTime}`);
						}
					} catch (dateError) {
						console.warn(
							`Invalid flight departure dateTime: ${dateTime}`,
							dateError
						);
						throw new Error(
							`Invalid flight departure date/time format: ${dateTime}`
						);
					}

					// All validations passed, set the flight details
					updatePayload.flightDepartureDetails = {
						...data.flightDepartureDetails,
						dateTime: parsedDateTime,
					};
				}
			} catch (error) {
				console.error('Error processing flight departure details:', error);
				// Re-throw the error to be caught by the controller
				throw error;
			}
		}
	}

	// Process status change reason
	if (data.status && data.statusChangeReason !== undefined) {
		updatePayload.statusChangeReason = data.statusChangeReason;
	}

	// Log the processed data
	console.log(
		'Processed delegation update data:',
		JSON.stringify(updatePayload, null, 2)
	);

	return updatePayload;
};

export const createDelegation = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		// Log the incoming request for debugging
		console.log(
			'Creating delegation with data:',
			JSON.stringify(req.body, null, 2)
		);

		// Process and validate the data
		let delegationData;
		try {
			delegationData = processDelegationData(req.body);
		} catch (validationError: any) {
			// Return a 400 Bad Request with detailed validation error
			res.status(400).json({
				status: 'error',
				message: 'Validation failed',
				errors: [
					{
						path: validationError.path || 'unknown',
						message: validationError.message || 'Invalid data provided',
						received: validationError.received || req.body,
					},
				],
			});
			return;
		}

		// Attempt to create the delegation in the database
		const newDelegation = await delegationModel.createDelegation(
			delegationData
		);

		if (newDelegation) {
			// Notify clients about the new delegation via WebSockets
			emitDelegationChange(SOCKET_EVENTS.DELEGATION_CREATED, newDelegation);

			// Return the created delegation
			res.status(201).json(newDelegation);
		} else {
			// Creation failed
			res.status(400).json({message: 'Could not create delegation.'});
		}
	} catch (error: any) {
		// Log the detailed error for debugging
		console.error('Error creating delegation:', error);

		// Determine if this is a validation error or a server error
		if (
			error.message &&
			(error.message.includes('required fields') ||
				error.message.includes('Invalid') ||
				error.message.includes('format'))
		) {
			// Return a 400 Bad Request for validation errors
			res.status(400).json({
				status: 'error',
				message: 'Validation failed',
				errors: [
					{
						path: error.path || 'unknown',
						message: error.message,
						received: req.body,
					},
				],
			});
		} else {
			// Return a 500 Internal Server Error for other errors
			res.status(500).json({
				message: 'Error creating delegation',
				error: error.message,
				stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
			});
		}
	}
};

export const getAllDelegations = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const delegations = await delegationModel.getAllDelegations();
		res.status(200).json(delegations);
	} catch (error: any) {
		res
			.status(500)
			.json({message: 'Error fetching delegations', error: error.message});
	}
};

export const getDelegationById = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const {id} = req.params;
		const delegation = await delegationModel.getDelegationById(id);
		if (delegation) {
			res.status(200).json(delegation);
		} else {
			res.status(404).json({message: 'Delegation not found'});
		}
	} catch (error: any) {
		res
			.status(500)
			.json({message: 'Error fetching delegation', error: error.message});
	}
};

export const updateDelegation = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const {id} = req.params;

		// Log the incoming request for debugging
		console.log(
			`Updating delegation ${id} with data:`,
			JSON.stringify(req.body, null, 2)
		);

		// Process and validate the update data
		let delegationData;
		try {
			delegationData = processDelegationUpdateData(req.body);
		} catch (validationError: any) {
			// Return a 400 Bad Request with detailed validation error
			res.status(400).json({
				status: 'error',
				message: 'Validation failed',
				errors: [
					{
						path: validationError.path || 'unknown',
						message: validationError.message || 'Invalid data provided',
						received: validationError.received || req.body,
					},
				],
			});
			return;
		}

		// Attempt to update the delegation in the database
		const updatedDelegation = await delegationModel.updateDelegation(
			id,
			delegationData
		);

		if (updatedDelegation) {
			// Notify clients about the update via WebSockets
			emitDelegationChange(SOCKET_EVENTS.DELEGATION_UPDATED, updatedDelegation);

			// Return the updated delegation
			res.status(200).json(updatedDelegation);
		} else {
			// Delegation not found or update failed
			res
				.status(404)
				.json({message: 'Delegation not found or could not be updated.'});
		}
	} catch (error: any) {
		// Log the detailed error for debugging
		console.error('Error updating delegation:', error);

		// Determine if this is a validation error or a server error
		if (
			error.message &&
			(error.message.includes('required fields') ||
				error.message.includes('Invalid') ||
				error.message.includes('format'))
		) {
			// Return a 400 Bad Request for validation errors
			res.status(400).json({
				status: 'error',
				message: 'Validation failed',
				errors: [
					{
						path: error.path || 'unknown',
						message: error.message,
						received: req.body,
					},
				],
			});
		} else {
			// Return a 500 Internal Server Error for other errors
			res.status(500).json({
				message: 'Error updating delegation',
				error: error.message,
				stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
			});
		}
	}
};

export const deleteDelegation = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const {id} = req.params;
		const deletedDelegation = await delegationModel.deleteDelegation(id);
		if (deletedDelegation) {
			emitDelegationChange(SOCKET_EVENTS.DELEGATION_DELETED, {id});
			res.status(200).json({
				message: 'Delegation deleted successfully',
				delegation: deletedDelegation,
			});
		} else {
			res
				.status(404)
				.json({message: 'Delegation not found or could not be deleted'});
		}
	} catch (error: any) {
		res
			.status(500)
			.json({message: 'Error deleting delegation', error: error.message});
	}
};

// Exporting the custom type for model usage
export type {CustomDelegationUpdatePayload};
