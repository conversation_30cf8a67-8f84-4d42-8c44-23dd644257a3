import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ErrorLog } from '../ErrorLog';
import { getMockRecentErrors } from '@/lib/adminService';
import { usePaginatedApi } from '@/hooks/useApi';

// Mock the hooks
jest.mock('@/hooks/useApi', () => ({
  usePaginatedApi: jest.fn(),
}));

// Mock the components
jest.mock('@/components/ui/loading-states', () => ({
  LoadingError: ({ message, onRetry }: { message: string; onRetry: () => void }) => (
    <div data-testid="loading-error">
      <p>{message}</p>
      <button onClick={onRetry}>Retry</button>
    </div>
  ),
}));

jest.mock('@/components/ui/error-boundary', () => ({
  ErrorBoundary: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

jest.mock('@/components/ui/scroll-area', () => ({
  ScrollArea: ({ children }: { children: React.ReactNode }) => <div data-testid="scroll-area">{children}</div>,
}));

describe('ErrorLog Component', () => {
  const mockRefetch = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should render loading state correctly', () => {
    (usePaginatedApi as jest.Mock).mockReturnValue({
      data: [],
      isLoading: true,
      error: null,
      refetch: mockRefetch,
      page: 1,
      totalPages: 0,
      nextPage: jest.fn(),
      prevPage: jest.fn(),
      hasNextPage: false,
      hasPrevPage: false,
    });
    
    render(<ErrorLog />);
    
    expect(screen.getAllByTestId('skeleton')).toHaveLength(5);
  });
  
  it('should render error state correctly', () => {
    (usePaginatedApi as jest.Mock).mockReturnValue({
      data: [],
      isLoading: false,
      error: 'Failed to fetch error logs',
      refetch: mockRefetch,
      page: 1,
      totalPages: 0,
      nextPage: jest.fn(),
      prevPage: jest.fn(),
      hasNextPage: false,
      hasPrevPage: false,
    });
    
    render(<ErrorLog />);
    
    expect(screen.getByTestId('loading-error')).toBeInTheDocument();
    expect(screen.getByText('Failed to fetch error logs')).toBeInTheDocument();
    
    // Test retry functionality
    fireEvent.click(screen.getByText('Retry'));
    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });
  
  it('should render error logs correctly', () => {
    const mockErrors = getMockRecentErrors({ limit: 3 }).data;
    
    (usePaginatedApi as jest.Mock).mockReturnValue({
      data: mockErrors,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
      page: 1,
      totalPages: 1,
      nextPage: jest.fn(),
      prevPage: jest.fn(),
      hasNextPage: false,
      hasPrevPage: false,
    });
    
    render(<ErrorLog />);
    
    expect(screen.getByTestId('scroll-area')).toBeInTheDocument();
    
    // Check that all error messages are displayed
    mockErrors.forEach(error => {
      expect(screen.getByText(error.message)).toBeInTheDocument();
    });
  });
  
  it('should render empty state correctly', () => {
    (usePaginatedApi as jest.Mock).mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
      refetch: mockRefetch,
      page: 1,
      totalPages: 0,
      nextPage: jest.fn(),
      prevPage: jest.fn(),
      hasNextPage: false,
      hasPrevPage: false,
    });
    
    render(<ErrorLog />);
    
    expect(screen.getByText('No errors or warnings found')).toBeInTheDocument();
  });
  
  it('should handle pagination correctly', () => {
    const mockNextPage = jest.fn();
    const mockPrevPage = jest.fn();
    
    (usePaginatedApi as jest.Mock).mockReturnValue({
      data: getMockRecentErrors({ limit: 3 }).data,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
      page: 2,
      totalPages: 3,
      nextPage: mockNextPage,
      prevPage: mockPrevPage,
      hasNextPage: true,
      hasPrevPage: true,
    });
    
    render(<ErrorLog />);
    
    // Check pagination controls
    expect(screen.getByText('Page 2 of 3')).toBeInTheDocument();
    
    // Test next page button
    fireEvent.click(screen.getByText('Next'));
    expect(mockNextPage).toHaveBeenCalledTimes(1);
    
    // Test previous page button
    fireEvent.click(screen.getByText('Previous'));
    expect(mockPrevPage).toHaveBeenCalledTimes(1);
  });
  
  it('should handle refresh button correctly', async () => {
    (usePaginatedApi as jest.Mock).mockReturnValue({
      data: getMockRecentErrors({ limit: 3 }).data,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
      page: 1,
      totalPages: 1,
      nextPage: jest.fn(),
      prevPage: jest.fn(),
      hasNextPage: false,
      hasPrevPage: false,
    });
    
    render(<ErrorLog />);
    
    // Test refresh button
    fireEvent.click(screen.getByText('Refresh Logs'));
    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });
});
