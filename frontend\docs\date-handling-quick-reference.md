# Date Handling Quick Reference

## Overview

This quick reference guide provides a summary of the date handling utilities and patterns implemented in the Car Life Tracker application. For detailed information, see the [Date Handling Implementation Guide](./date-handling-implementation-guide.md).

## Date Utility Functions

| Function | Purpose | Example |
|----------|---------|---------|
| `formatDateForInput` | Format dates for HTML inputs | `formatDateForInput(date, 'datetime-local')` |
| `formatDateForDisplay` | Format dates for user display | `formatDateForDisplay(date, true)` |
| `formatDateForApi` | Format dates for API submission | `formatDateForApi(dateString)` |
| `parseDateFromApi` | Parse dates from API responses | `parseDateFromApi(apiDateString)` |
| `isValidIsoDateString` | Validate ISO date strings | `isValidIsoDateString(dateString)` |
| `isValidDateString` | Validate general date strings | `isValidDateString(dateString)` |
| `isDateAfter` | Compare two dates | `isDateAfter(date1, date2)` |

## Common Patterns

### Form Initialization

```typescript
const form = useForm<FormData>({
  resolver: zodResolver(Schema),
  defaultValues: {
    startDate: initialData
      ? formatDateForInput(initialData.startDate)
      : formatDateForInput(new Date()),
    endDate: initialData?.endDate
      ? formatDateForInput(initialData.endDate)
      : '',
  },
});
```

### Form Submission

```typescript
const handleSubmit = (data: FormData) => {
  const apiData = {
    ...data,
    startDate: formatDateForApi(data.startDate),
    endDate: data.endDate ? formatDateForApi(data.endDate) : undefined,
  };
  submitToApi(apiData);
};
```

### Date Validation in Zod Schema

```typescript
const Schema = z.object({
  startDate: z
    .string()
    .min(1, 'Start date is required')
    .refine((val) => isValidDateString(val), {
      message: 'Please enter a valid date in YYYY-MM-DD HH:MM format',
    }),
  endDate: z
    .string()
    .refine((val) => val === '' || isValidDateString(val), {
      message: 'Please enter a valid date in YYYY-MM-DD HH:MM format',
    })
    .optional()
    .transform((val) => (val === '' ? undefined : val)),
})
.superRefine((data, ctx) => {
  if (data.startDate && data.endDate) {
    const start = new Date(data.startDate);
    const end = new Date(data.endDate);
    
    if (end < start) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date must be after start date',
        path: ['endDate'],
      });
    }
  }
});
```

### Displaying Dates

```typescript
<div>Start Date: {formatDateForDisplay(task.startDate)}</div>
<div>Due Date: {formatDateForDisplay(task.dueDate, true)}</div>
```

## Best Practices

1. **Always use utility functions** for date operations
2. **Handle null/undefined values** gracefully
3. **Provide clear error messages** for date validation
4. **Use try/catch blocks** when parsing dates
5. **Test with various date formats** and edge cases

## Common Date Formats

| Context | Format | Example |
|---------|--------|---------|
| HTML date input | `yyyy-MM-dd` | `2023-05-15` |
| HTML datetime-local input | `yyyy-MM-dd'T'HH:mm` | `2023-05-15T14:30` |
| API (ISO 8601) | `yyyy-MM-dd'T'HH:mm:ss.SSSXXX` | `2023-05-15T14:30:00.000Z` |
| Display (date only) | `MMM d, yyyy` | `May 15, 2023` |
| Display (with time) | `MMM d, yyyy, HH:mm` | `May 15, 2023, 14:30` |

## Troubleshooting

| Issue | Possible Solution |
|-------|-------------------|
| Invalid date errors | Check input format matches expected format |
| Time zone issues | Be aware of local vs. UTC time differences |
| Empty dates not handled | Use conditional checks for optional dates |
| Validation errors | Ensure Zod schema is correctly configured |

---

*For detailed implementation information, see the [Date Handling Implementation Guide](./date-handling-implementation-guide.md)*