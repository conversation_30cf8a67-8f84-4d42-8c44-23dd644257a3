{"version": 3, "file": "rateLimiting.js", "sourceRoot": "", "sources": ["../../src/middleware/rateLimiting.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,SAAS,EAAC,MAAM,oBAAoB,CAAC;AAC7C,OAAO,EAAC,iBAAiB,EAAE,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AAE1E,OAAO,EAAC,KAAK,EAAC,MAAM,SAAS,CAAC;AAE9B;;;;;;;;;;;;;;;GAeG;AAEH,8BAA8B;AAC9B,MAAM,iBAAiB,GAAG;IACzB,yBAAyB;IACzB,MAAM,EAAE;QACP,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACvC,KAAK,EAAE,IAAI,EAAE,6BAA6B;QAC1C,OAAO,EAAE;YACR,KAAK,EAAE,yDAAyD;YAChE,IAAI,EAAE,qBAAqB;YAC3B,UAAU,EAAE,YAAY;SACxB;KACD;IAED,sCAAsC;IACtC,IAAI,EAAE;QACL,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACvC,KAAK,EAAE,EAAE,EAAE,6BAA6B;QACxC,OAAO,EAAE;YACR,KAAK,EAAE,2DAA2D;YAClE,IAAI,EAAE,0BAA0B;YAChC,UAAU,EAAE,YAAY;SACxB;KACD;IAED,2BAA2B;IAC3B,GAAG,EAAE;QACJ,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;QACpC,KAAK,EAAE,GAAG,EAAE,6BAA6B;QACzC,OAAO,EAAE;YACR,KAAK,EAAE,4CAA4C;YACnD,IAAI,EAAE,yBAAyB;YAC/B,UAAU,EAAE,UAAU;SACtB;KACD;IAED,gCAAgC;IAChC,KAAK,EAAE;QACN,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,YAAY;QACrC,KAAK,EAAE,EAAE,EAAE,6BAA6B;QACxC,OAAO,EAAE;YACR,KAAK,EAAE,qCAAqC;YAC5C,IAAI,EAAE,2BAA2B;YACjC,UAAU,EAAE,WAAW;SACvB;KACD;IAED,iCAAiC;IACjC,MAAM,EAAE;QACP,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACvC,KAAK,EAAE,CAAC,EAAE,6BAA6B;QACvC,OAAO,EAAE;YACR,KAAK,EAAE,iEAAiE;YACxE,IAAI,EAAE,4BAA4B;YAClC,UAAU,EAAE,YAAY;SACxB;KACD;CACD,CAAC;AAEF,oDAAoD;AACpD,IAAI,WAAW,GAAiB,IAAI,CAAC;AACrC,IAAI,gBAAgB,GAA4B,IAAI,CAAC;AAErD;;GAEG;AACH,MAAM,eAAe,GAAG,GAAS,EAAE;IAClC,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;IAEvC,IAAI,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QACvD,IAAI,CAAC;YACJ,WAAW,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE;gBACjC,oBAAoB,EAAE,CAAC;gBACvB,WAAW,EAAE,IAAI;aACjB,CAAC,CAAC;YAEH,gBAAgB,GAAG,IAAI,gBAAgB,CAAC;gBACvC,WAAW,EAAE,WAAW;gBACxB,SAAS,EAAE,YAAY;gBACvB,MAAM,EAAE,GAAG,EAAE,qBAAqB;gBAClC,QAAQ,EAAE,EAAE,EAAE,iBAAiB;aAC/B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACrE,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAC9D,CAAC;IACF,CAAC;AACF,CAAC,CAAC;AAEF,kCAAkC;AAClC,eAAe,EAAE,CAAC;AAElB;;GAEG;AACH,MAAM,iBAAiB,GAAG,CAAC,MAAuC,EAAE,EAAE;IACrE,OAAO,SAAS,CAAC;QAChB,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,eAAe,EAAE,SAAS,EAAE,8BAA8B;QAC1D,aAAa,EAAE,KAAK,EAAE,uCAAuC;QAE7D,+CAA+C;QAC/C,YAAY,EAAE,CAAC,GAAY,EAAU,EAAE;YACtC,yBAAyB;YACzB,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YACjD,IAAI,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;gBAChD,mCAAmC;gBACnC,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC1C,qEAAqE;gBACrE,OAAO,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;YACrC,CAAC;YAED,OAAO,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,aAAa,IAAI,SAAS,CAAC;QACxD,CAAC;QAED,yCAAyC;QACzC,OAAO,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;YAC5D,uBAAuB;YACvB,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,CAAC;YACzD,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,qBAAqB,CAAC,CAAC;YAC5D,GAAG,CAAC,SAAS,CAAC,sBAAsB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;YAEhE,2BAA2B;YAC3B,OAAO,CAAC,IAAI,CACX,mDAAmD,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,CAC1E,CAAC;YAEF,2BAA2B;YAC3B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,iEAAiE;QACjE,IAAI,EAAE,CAAC,GAAY,EAAW,EAAE;YAC/B,kDAAkD;YAClD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,GAAG,CAAC,EAAE,KAAK,WAAW,EAAE,CAAC;gBACtE,OAAO,IAAI,CAAC;YACb,CAAC;YAED,2CAA2C;YAC3C,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACrE,IAAI,GAAG,CAAC,EAAE,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC1C,OAAO,IAAI,CAAC;YACb,CAAC;YAED,OAAO,KAAK,CAAC;QACd,CAAC;QAED,gDAAgD;QAChD,oBAAoB,EAAE,CAAC,GAAY,EAAE,GAAa,EAAW,EAAE;YAC9D,iDAAiD;YACjD,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/D,OAAO,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;YAC7B,CAAC;YAED,0CAA0C;YAC1C,OAAO,IAAI,CAAC;QACb,CAAC;KACD,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,yBAAyB,GAAG,CAAC,MAAc,EAAE,QAAgB,EAAE,EAAE;IACtE,MAAM,OAAO,GACZ,gBAAgB;QAChB,IAAI,iBAAiB,CAAC;YACrB,MAAM,EAAE,qBAAqB;YAC7B,QAAQ,EAAE,0BAA0B;SACpC,CAAC,CAAC;IAEJ,OAAO,KAAK,EACX,GAAY,EACZ,GAAa,EACb,IAAkB,EACF,EAAE;QAClB,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,SAAS,CAAC;QAEhC,IAAI,CAAC;YACJ,MAAM,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3B,IAAI,EAAE,CAAC;QACR,CAAC;QAAC,OAAO,cAAmB,EAAE,CAAC;YAC9B,sBAAsB;YACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjE,GAAG,CAAC,GAAG,CAAC;gBACP,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC;gBAC3B,mBAAmB,EAAE,MAAM,CAAC,MAAM,CAAC;gBACnC,uBAAuB,EAAE,MAAM,CAAC,cAAc,CAAC,eAAe,IAAI,CAAC,CAAC;gBACpE,mBAAmB,EAAE,MAAM,CAC1B,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,CAC5D;gBACD,qBAAqB,EAAE,2BAA2B;aAClD,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CACX,4DAA4D,GAAG,CAAC,EAAE,EAAE,CACpE,CAAC;YAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,qBAAqB;gBAC5B,IAAI,EAAE,8BAA8B;gBACpC,UAAU,EAAE,GAAG,IAAI,UAAU;aAC7B,CAAC,CAAC;QACJ,CAAC;IACF,CAAC,CAAC;AACH,CAAC,CAAC;AAEF,+BAA+B;AAC/B,MAAM,CAAC,MAAM,eAAe,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAC3E,MAAM,CAAC,MAAM,aAAa,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACvE,MAAM,CAAC,MAAM,YAAY,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;AACrE,MAAM,CAAC,MAAM,cAAc,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACzE,MAAM,CAAC,MAAM,eAAe,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAE3E,yBAAyB;AACzB,MAAM,CAAC,MAAM,eAAe,GAAG,yBAAyB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,yBAAyB;AAC3F,MAAM,CAAC,MAAM,oBAAoB,GAAG,yBAAyB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,2BAA2B;AAElG;;GAEG;AACH,MAAM,CAAC,MAAM,2BAA2B,GAAG,CAC1C,GAAY,EACZ,GAAa,EACb,IAAkB,EACX,EAAE;IACT,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,CAAC;IACzD,GAAG,CAAC,SAAS,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;IACtD,GAAG,CAAC,SAAS,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC7E,IAAI,EAAE,CAAC;AACR,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,KAAK,EACtC,GAAY,EACZ,GAAa,EACG,EAAE;IAClB,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,SAAS,CAAC;IAEhC,IAAI,CAAC;QACJ,IAAI,gBAAgB,EAAE,CAAC;YACtB,MAAM,cAAc,GAAG,MAAM,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACvD,GAAG,CAAC,IAAI,CAAC;gBACR,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,cAAc,EAAE,eAAe,IAAI,CAAC;gBAC/C,SAAS,EAAE,cAAc;oBACxB,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,YAAY,CAAC;oBACpD,CAAC,CAAC,IAAI;aACP,CAAC,CAAC;QACJ,CAAC;aAAM,CAAC;YACP,GAAG,CAAC,IAAI,CAAC;gBACR,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,QAAQ;gBACjB,OAAO,EAAE,mCAAmC;aAC5C,CAAC,CAAC;QACJ,CAAC;IACF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACpB,MAAM,EAAE,OAAO;YACf,OAAO,EAAE,+BAA+B;SACxC,CAAC,CAAC;IACJ,CAAC;AACF,CAAC,CAAC;AAEF,eAAe;IACd,eAAe;IACf,aAAa;IACb,YAAY;IACZ,cAAc;IACd,eAAe;IACf,eAAe;IACf,oBAAoB;IACpB,2BAA2B;IAC3B,kBAAkB;CAClB,CAAC"}