
'use client';

import React, {useEffect, useState} from 'react';
import Link from 'next/link';
import {PlusCircle, Car, Search, RefreshCw} from 'lucide-react';
import VehicleCard from '@/components/vehicles/VehicleCard';
import type {Vehicle} from '@/lib/types';
import VehicleListContainer from '@/components/vehicles/VehicleList';
import {PageHeader} from '@/components/ui/PageHeader';
import {Input} from '@/components/ui/input';
import {useToast} from '@/hooks/use-toast';
import {ActionButton} from '@/components/ui/action-button';
import {SkeletonLoader, DataLoader} from '@/components/ui/loading';
import ErrorBoundary from '@/components/ErrorBoundary'; // Import ErrorBoundary

// Define the type for the render prop arguments to avoid implicit any
interface VehicleListData {
	vehicles: Vehicle[];
	loading: boolean;
	error: string | null;
	handleDelete: (id: number) => Promise<void>;
	fetchVehicles: () => Promise<void>;
	isRefreshing: boolean;
	isConnected: boolean;
	socketTriggered: boolean;
}

// We'll use our standardized SkeletonLoader component instead of a custom skeleton

// Main page component content
const VehiclesPageContent = () => {
	const [allVehicles, setAllVehicles] = useState<Vehicle[]>([]);
	const [filteredVehicles, setFilteredVehicles] = useState<Vehicle[]>([]);
	const [searchTerm, setSearchTerm] = useState('');
	const {toast} = useToast();

	return (
		<VehicleListContainer>
			{({
				vehicles,
				loading,
				error,
				handleDelete,
				fetchVehicles, // This is the manual refresh trigger from useSocketRefresh
				isRefreshing,
				isConnected,
				socketTriggered,
			}: VehicleListData) => {
				// Update local state when vehicles data changes from container
				useEffect(() => {
					if (!loading && !error) {
						setAllVehicles(vehicles);
						setFilteredVehicles(vehicles); // Initialize filtered list
					}
				}, [vehicles, loading, error]);

				// Search/filter effect
				useEffect(() => {
					const lowercasedFilter = searchTerm.toLowerCase();
					const filteredData = allVehicles.filter((vehicle) => {
						return (
							vehicle.make.toLowerCase().includes(lowercasedFilter) ||
							vehicle.model.toLowerCase().includes(lowercasedFilter) ||
							vehicle.year.toString().includes(lowercasedFilter) ||
							(vehicle.licensePlate &&
								vehicle.licensePlate
									.toLowerCase()
									.includes(lowercasedFilter)) ||
							(vehicle.vin &&
								vehicle.vin.toLowerCase().includes(lowercasedFilter)) ||
							(vehicle.ownerName &&
								vehicle.ownerName.toLowerCase().includes(lowercasedFilter))
						);
					});
					setFilteredVehicles(filteredData);
				}, [searchTerm, allVehicles]);

				return (
					<div className='space-y-8'>
						<PageHeader
							title='My Vehicle Fleet'
							description='Manage, track, and gain insights into your vehicles.'
							icon={Car}>
							<div className='flex gap-2'>
								<ActionButton
									actionType='primary'
									icon={<PlusCircle className='h-4 w-4' />}
									asChild>
									<Link href='/vehicles/new'>Add New Vehicle</Link>
								</ActionButton>
							</div>
						</PageHeader>

						{/* Search Bar & Status Indicators */}
						<div className='mb-6 p-4 bg-card rounded-lg shadow relative'>
							{(isRefreshing || socketTriggered) && !loading && (
								<div className='absolute right-4 top-4 text-xs flex items-center text-muted-foreground'>
									<RefreshCw className='h-3 w-3 mr-1 animate-spin' />
									{socketTriggered ? 'Real-time update...' : 'Updating list...'}
								</div>
							)}
							{!loading && (
								<div className='absolute right-40 top-4 text-xs inline-flex items-center sm:right-24'>
									{' '}
									{/* Adjusted right padding for smaller screens */}
									<div
										className={`h-2 w-2 rounded-full mr-2 ${
											isConnected ? 'bg-green-500' : 'bg-gray-400'
										}`}></div>
									<span className='text-muted-foreground text-xs'>
										{isConnected ? 'Real-time active' : 'Real-time inactive'}
									</span>
								</div>
							)}
							<div className='relative'>
								<Search className='absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground' />
								<Input
									type='text'
									placeholder='Search vehicles (Make, Model, Year, VIN, Plate, Owner...)'
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className='pl-10 w-full'
								/>
							</div>
						</div>

						<DataLoader
							isLoading={loading}
							error={error}
							data={filteredVehicles}
							onRetry={fetchVehicles}
							loadingComponent={<SkeletonLoader variant='card' count={3} />}
							emptyComponent={
								<div className='text-center py-12 bg-card rounded-lg shadow-md'>
									<Car className='mx-auto h-16 w-16 text-muted-foreground mb-6' />
									<h3 className='text-2xl font-semibold text-foreground mb-2'>
										{searchTerm
											? 'No Vehicles Match Your Search'
											: 'Your Garage is Empty!'}
									</h3>
									<p className='text-muted-foreground mt-2 mb-6 max-w-md mx-auto'>
										{searchTerm
											? 'Try adjusting your search terms or add a new vehicle to your fleet.'
											: "It looks like you haven't added any vehicles yet. Let's get your first one set up."}
									</p>
									{!searchTerm && (
										<ActionButton
											actionType='primary'
											size='lg'
											icon={<PlusCircle className='h-4 w-4' />}
											asChild>
											<Link href='/vehicles/new'>Add Your First Vehicle</Link>
										</ActionButton>
									)}
								</div>
							}>
							{(vehiclesData) => ( // Renamed data to vehiclesData to avoid conflict
								<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
									{vehiclesData.map((vehicle) => (
										<VehicleCard key={vehicle.id} vehicle={vehicle} />
									))}
								</div>
							)}
						</DataLoader>
					</div>
				);
			}}
		</VehicleListContainer>
	);
};

// The main export for the page
export default function VehiclesPage() {
	return (
    <ErrorBoundary>
      <VehiclesPageContent />
    </ErrorBoundary>
  );
}
