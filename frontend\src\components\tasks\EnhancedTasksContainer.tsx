'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import type { Task, Employee } from '@/lib/types';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Info, AlertTriangle, RefreshCw } from 'lucide-react';
import { TasksTable } from './TasksTable';
import { TaskSummary } from '@/components/reports/TaskSummary';
import { PaginationControls } from '@/components/ui/pagination';
import { SkeletonLoader } from '@/components/ui/loading';
import { isPast } from 'date-fns';

/**
 * Props for the EnhancedTasksContainer component
 */
interface EnhancedTasksContainerProps {
  /** Array of tasks to display */
  tasks: Task[];
  /** Whether data is currently loading */
  isLoading: boolean;
  /** Error message, if any */
  error: string | null;
  /** Function to retry loading data */
  onRetry: () => void;
  /** Additional CSS class names */
  className?: string;
}

/**
 * A container component that handles sorting, pagination, and display of tasks
 * 
 * @example
 * ```tsx
 * <EnhancedTasksContainer
 *   tasks={filteredTasks}
 *   isLoading={isLoading}
 *   error={error}
 *   onRetry={handleRetry}
 * />
 * ```
 */
export function EnhancedTasksContainer({
  tasks,
  isLoading,
  error,
  onRetry,
  className,
}: EnhancedTasksContainerProps) {
  // Sorting state
  const [sortField, setSortField] = useState<string>('dateTime');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Custom sort order for status
  const statusOrder = {
    'In_Progress': 1,
    'Assigned': 2,
    'Pending': 3,
    'Completed': 4,
    'Cancelled': 5
  };
  
  // Custom sort order for priority
  const priorityOrder = {
    'High': 1,
    'Medium': 2,
    'Low': 3
  };
  
  // Sort tasks
  const sortedTasks = useMemo(() => {
    return [...tasks].sort((a, b) => {
      let valueA, valueB;
      
      // Handle different field types
      switch (sortField) {
        case 'dateTime':
        case 'deadline':
          valueA = a[sortField] ? new Date(a[sortField]).getTime() : 0;
          valueB = b[sortField] ? new Date(b[sortField]).getTime() : 0;
          break;
        case 'status':
          valueA = statusOrder[a.status as keyof typeof statusOrder] || 999;
          valueB = statusOrder[b.status as keyof typeof statusOrder] || 999;
          break;
        case 'priority':
          valueA = priorityOrder[a.priority as keyof typeof priorityOrder] || 999;
          valueB = priorityOrder[b.priority as keyof typeof priorityOrder] || 999;
          break;
        case 'assignedEmployeeId':
          valueA = a.assignedEmployeeId || 'zzz'; // Sort unassigned last
          valueB = b.assignedEmployeeId || 'zzz';
          break;
        case 'description':
        case 'location':
          valueA = a[sortField]?.toLowerCase() || '';
          valueB = b[sortField]?.toLowerCase() || '';
          break;
        default:
          valueA = a[sortField as keyof Task];
          valueB = b[sortField as keyof Task];
      }
      
      // Compare values based on direction
      if (valueA < valueB) return sortDirection === 'asc' ? -1 : 1;
      if (valueA > valueB) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [tasks, sortField, sortDirection, statusOrder, priorityOrder]);
  
  // Handle sort
  const handleSort = useCallback((field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField, sortDirection]);
  
  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedTasks = useMemo(() => 
    sortedTasks.slice(indexOfFirstItem, indexOfLastItem),
  [sortedTasks, indexOfFirstItem, indexOfLastItem]);
  
  const totalPages = useMemo(() => 
    Math.ceil(sortedTasks.length / itemsPerPage),
  [sortedTasks.length, itemsPerPage]);
  
  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);
  
  // Reset pagination when tasks change
  useEffect(() => {
    setCurrentPage(1);
  }, [tasks]);
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4" data-testid="loading-skeleton">
        <SkeletonLoader variant="table" count={5} />
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
        <Button
          variant="outline"
          size="sm"
          onClick={onRetry}
          className="mt-2"
          aria-label="Try loading tasks again"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </Alert>
    );
  }
  
  // Render empty state
  if (tasks.length === 0) {
    return (
      <Alert variant="default" className="mb-6">
        <Info className="h-4 w-4" />
        <AlertTitle>No Tasks</AlertTitle>
        <AlertDescription>
          No tasks match your current filters. Try adjusting your search or filter criteria.
        </AlertDescription>
      </Alert>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <TaskSummary tasks={sortedTasks} />
      
      {/* Tasks Table */}
      <Card className="shadow-md card-print">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <TasksTable
              tasks={paginatedTasks}
              sortField={sortField}
              sortDirection={sortDirection}
              onSort={handleSort}
            />
          </div>
        </CardContent>
      </Card>
      
      {/* Pagination */}
      {sortedTasks.length > itemsPerPage && (
        <div className="flex justify-center mt-4 no-print">
          <PaginationControls
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
      
      {/* Task Count Summary */}
      <div className="text-sm text-muted-foreground text-center no-print">
        Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, sortedTasks.length)} of {sortedTasks.length} tasks
      </div>
    </div>
  );
}
