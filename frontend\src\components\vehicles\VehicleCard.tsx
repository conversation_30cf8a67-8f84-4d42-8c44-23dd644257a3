'use client';

import Image from 'next/image';
import Link from 'next/link';
import {
	<PERSON>,
	<PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>er,
	CardTitle,
	CardDescription,
} from '@/components/ui/card';
import {ActionButton} from '@/components/ui/action-button';
import type {Vehicle} from '@/lib/types';
import {
	ArrowRight,
	Palette,
	CalendarDays,
	Gauge,
	Tag,
	Wrench,
	Info,
} from 'lucide-react';
import {Badge} from '@/components/ui/badge';
import {Separator} from '@/components/ui/separator';

interface VehicleCardProps {
	vehicle: Vehicle;
}

interface InfoPillProps {
	icon: React.ElementType;
	label: string;
	value: string | number;
}

function InfoPill({icon: Icon, label, value}: InfoPillProps) {
	return (
		<div className='flex items-center space-x-2 rounded-full bg-muted/50 px-3 py-1 text-xs text-foreground'>
			<Icon className='h-3.5 w-3.5 text-accent' />
			<span>
				{label}: <strong>{value}</strong>
			</span>
		</div>
	);
}

export default function VehicleCard({vehicle}: VehicleCardProps) {
	// Calculate latest odometer, keeping track of whether we have real data
	const hasServiceHistory = vehicle.serviceHistory.length > 0;
	const hasInitialOdometer = vehicle.initialOdometer !== null;

	const latestOdometer = hasServiceHistory
		? Math.max(
				vehicle.initialOdometer || 0,
				...vehicle.serviceHistory.map((s) => s.odometer)
		  )
		: vehicle.initialOdometer || 0;

	const formatOdometerDisplay = (): string => {
		if (hasServiceHistory) {
			// If we have service history, we can always show the latest reading
			return `${latestOdometer.toLocaleString()} miles`;
		} else if (hasInitialOdometer) {
			// Only initial odometer, show it
			return `${latestOdometer.toLocaleString()} miles`;
		} else {
			// No odometer data at all
			return 'Not recorded';
		}
	};

	return (
		<Card className='overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60'>
			<CardHeader className='p-0 relative'>
				<div className='aspect-[16/10] w-full relative'>
					<Image
						src={
							vehicle.imageUrl ||
							`https://picsum.photos/seed/${vehicle.id}/600/375`
						}
						alt={`${vehicle.make} ${vehicle.model}`}
						layout='fill'
						objectFit='cover'
						className='bg-muted'
						data-ai-hint='luxury car'
						priority={true} // Prioritize loading for visible cards
					/>
				</div>
			</CardHeader>
			<CardContent className='p-5 flex-grow flex flex-col'>
				<CardTitle className='text-xl font-semibold mb-1 text-primary'>
					{vehicle.make} {vehicle.model}
				</CardTitle>
				<CardDescription className='text-sm text-muted-foreground mb-3'>
					{vehicle.year} {vehicle.color && `• ${vehicle.color}`}{' '}
					{vehicle.licensePlate && `• Plate: ${vehicle.licensePlate}`}
				</CardDescription>
				<Separator className='my-3 bg-border/50' />
				<div className='space-y-2.5 text-sm text-foreground flex-grow'>
					<div className='flex items-center'>
						<Gauge className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
						<div>
							<span className='text-muted-foreground'>Latest Odometer: </span>
							<strong className='font-semibold'>
								{formatOdometerDisplay()}
							</strong>
						</div>
					</div>
					<div className='flex items-center'>
						<Wrench className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
						<div>
							<span className='text-muted-foreground'>Services Logged: </span>
							<strong className='font-semibold'>
								{vehicle.serviceHistory.length}
							</strong>
						</div>
					</div>
				</div>
			</CardContent>
			<CardFooter className='p-4 border-t border-border/60 bg-muted/20'>
				<ActionButton
					actionType='tertiary'
					className='w-full'
					icon={<ArrowRight className='h-4 w-4' />}
					asChild>
					<Link href={`/vehicles/${vehicle.id}`}>Manage Vehicle</Link>
				</ActionButton>
			</CardFooter>
		</Card>
	);
}
