## Frontend Integration Plan (React/Next.js)

This document outlines the plan for integrating the frontend application (assuming React/Next.js) with the new backend report generation service.

**1. Overview of Frontend Changes**

The integration involves updating UI elements, implementing new API call logic, managing loading states with clear user feedback, and handling the PDF download process.

*   **User Interface (UI) Elements:**
    *   **Button Replacement/Repurposing:** Existing "Print," "Export PDF," or "Client-side Export PDF" buttons on report pages will be replaced or repurposed.
        *   A new, consistently styled "Download PDF" button will be implemented. If an existing button is repurposed, its text and associated action will change.
    *   **Placement and Styling:**
        *   The "Download PDF" button should be placed in a prominent and consistent location across all report pages (e.g., top-right corner of the report content area, or alongside other action buttons like "Save" or "Share").
        *   The button should adhere to the application's existing design system and styling guidelines for consistency. An appropriate icon (e.g., a download icon) can be used alongside the text.

*   **API Call Logic:**
    *   **Endpoint:** The frontend will make `POST` requests to the new backend endpoint: `/api/reports/generate/{reportType}`.
    *   **Data Collection:** Before making the API call, the frontend needs to gather all relevant data to be sent in the request body. This includes:
        *   `reportType`: A string identifier for the specific report (e.g., "delegation-summary," "vehicle-service-history"). This will likely be a prop passed to a reusable report generation button component or determined by the context of the page.
        *   `reportParams`: An object containing parameters relevant to the specific report, such as:
            *   Current filter values applied to the report view (e.g., status, region, departmentId).
            *   Selected date ranges (`dateRange: { from: "YYYY-MM-DD", to: "YYYY-MM-DD" }`).
            *   Specific identifiers related to the report content (e.g., `delegationId`, `vehicleId`, `userId`).
            *   Selected locale, if applicable.
    *   **API Service/Utility Function:**
        *   It's highly recommended to use a dedicated API service module or utility function to encapsulate the API call logic. This promotes reusability and centralizes API interaction.
        *   *Example (`services/apiService.js` or similar):*
            ```javascript
            // Example API service function
            export async function generateReportApi(reportType, reportParams, options = {}) {
              const response = await fetch(`/api/reports/generate/${reportType}`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  // Include Authorization header if required, e.g., for JWT
                  // 'Authorization': `Bearer ${getAuthToken()}`,
                },
                body: JSON.stringify(reportParams),
                ...options // Allow passing additional fetch options if needed (e.g. signal for cancellation)
              });

              if (!response.ok) {
                // Attempt to parse error response from backend for better messages
                const errorData = await response.json().catch(() => ({})); // Graceful catch if error response isn't JSON
                throw new Error(errorData.error || `Report generation failed with status: ${response.status}`);
              }

              // Determine how to handle the response based on Content-Type or backend agreement
              const contentType = response.headers.get("content-type");
              if (contentType && contentType.includes("application/pdf")) {
                return { type: 'blob', data: await response.blob(), headers: response.headers };
              } else if (contentType && contentType.includes("application/json")) {
                return { type: 'json_url', data: await response.json(), headers: response.headers };
              } else {
                // Fallback or throw error for unexpected content type
                throw new Error('Unexpected response type from server.');
              }
            }
            ```

*   **Loading States & User Feedback:**
    *   Clear visual feedback is crucial as PDF generation can be time-consuming.
    *   **Idle:** The "Download PDF" button is active and ready to be clicked. Button text is normal (e.g., "Download PDF").
    *   **Loading:**
        *   When clicked, the button should enter a loading state.
        *   The button should be disabled to prevent multiple clicks.
        *   Button text changes (e.g., "Generating PDF...", "Processing...").
        *   A spinner icon can be displayed within the button or next to it.
        *   The mouse cursor might change to 'wait'.
    *   **Progress:**
        *   Direct progress updates (e.g., percentage) are often not feasible with a simple headless browser conversion on the backend, as the operation is typically atomic from the client's perspective.
        *   If the backend *were* designed for long-running tasks and could provide progress (e.g., via WebSockets or polling a status endpoint for a `jobId`), the UI could display a progress bar or textual updates. However, for this scope, we assume a simple loading state is sufficient.
    *   **Success:**
        *   The PDF download will be initiated by the browser.
        *   Optionally, a temporary success message (e.g., "Report download started.") can be displayed briefly (e.g., using a toast notification) and then automatically dismissed or replaced by resetting the button to its idle state.
    *   **Error:**
        *   If the API call fails or the backend returns an error:
            *   A clear, user-friendly error message should be displayed. This could be near the button, in a modal dialog, or as a toast notification.
            *   The error message should ideally come from the backend's JSON error response if available (e.g., "Invalid date range provided."). If not, a generic message like "Failed to generate PDF. Please try again or contact support." can be used.
            *   The button should revert to its idle state, allowing the user to try again.

*   **PDF Download Handling:**
    *   The frontend needs to handle two primary types of successful responses:
        *   **Scenario 1: PDF is streamed directly (backend responds with `Content-Type: application/pdf`)**
            *   The API service function will return the response data as a `Blob`.
            *   A dynamic filename should be generated (e.g., `report-${reportType}-${reportParams.id}-${Date.now()}.pdf`) or derived from the `Content-Disposition` header if provided by the backend.
            *   **Native Browser Capabilities:**
                1.  Create an object URL from the blob: `const url = window.URL.createObjectURL(pdfBlob);`
                2.  Create a temporary anchor (`<a>`) element: `const a = document.createElement('a');`
                3.  Set `a.style.display = 'none';`, `a.href = url;`, `a.download = filename;`.
                4.  Append the anchor to the body: `document.body.appendChild(a);`
                5.  Trigger a click: `a.click();`
                6.  Clean up: `window.URL.revokeObjectURL(url);`, `document.body.removeChild(a);`.
            *   **Using `file-saver` library (optional):**
                *   `import { saveAs } from 'file-saver';`
                *   `saveAs(pdfBlob, filename);`
                *   This library simplifies the download process and handles cross-browser inconsistencies.
        *   **Scenario 2: URL to a stored PDF is returned (backend responds with JSON containing `pdfUrl` and `filename`)**
            *   The API service function will return the parsed JSON data (e.g., `{ pdfUrl: "...", filename: "..." }`).
            *   The frontend can initiate the download by:
                1.  Creating a temporary anchor (`<a>`) element.
                2.  Setting `a.href = pdfUrl;`.
                3.  Setting `a.download = filename;` (using the filename provided by the backend is preferred).
                4.  Appending, clicking, and removing the anchor as described above.
                *   Alternatively, for a simpler approach (though less ideal as it navigates the current tab if not handled carefully with `target="_blank"` on the anchor): `window.open(pdfUrl, '_blank');` or simply creating a visible link for the user. The anchor tag method is generally preferred for triggering a direct download.
    *   **Filename Suggestion:** The filename should be meaningful. It can be constructed on the frontend based on report type, key parameters, and date, or ideally, the backend suggests a filename via the `Content-Disposition` header (for blob responses) or in the JSON payload (for URL responses).

**2. Example React Component Snippet (Conceptual)**

```jsx
// components/DownloadReportButton.jsx
// (This is a guide; actual implementation might vary based on project structure and libraries)

import React, { useState, useCallback } from 'react';
// Assume an apiService exists for making backend calls
// For a real implementation, you would import your actual API service:
// import { generateReportApi } from '../services/apiService'; // Example path

// Optional: if using file-saver
// import { saveAs } from 'file-saver';

// Simulate the API service for this conceptual snippet
const generateReportApi = async (reportType, reportParams) => {
  console.log("Simulating API call for:", reportType, "with params:", reportParams);
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // Simulate success (blob response)
      if (reportParams.simulateSuccess === 'blob') {
        const simulatedBlob = new Blob(
          [`Dummy PDF content for ${reportType} with params ${JSON.stringify(reportParams)}`],
          { type: 'application/pdf' }
        );
        // Simulate backend sending Content-Disposition header
        const headers = new Headers();
        headers.append('Content-Disposition', `attachment; filename="simulated-${reportType}.pdf"`);
        resolve({ type: 'blob', data: simulatedBlob, headers });

      // Simulate success (json_url response)
      } else if (reportParams.simulateSuccess === 'json_url') {
        resolve({
          type: 'json_url',
          data: {
            pdfUrl: `https://example.com/reports/simulated-${reportType}.pdf`,
            filename: `simulated-${reportType}-from-url.pdf`
          },
          headers: new Headers({'Content-Type': 'application/json'})
        });

      // Simulate failure
      } else {
        // Simulate a backend error structure
        const errorResponse = {
          // response: { // Simulating structure if using axios or similar that nests response
            // data: { error: `Simulated error for ${reportType}` },
            // status: 500
          // }
          // For fetch, error might be simpler initially, actual error object structure depends on error handling in apiService
          message: `Simulated error for ${reportType}. Invalid parameters provided.`
        };
        reject(errorResponse); // Use 'new Error(...)' for actual error objects
      }
    }, 2000); // Simulate network delay
  });
};


function DownloadReportButton({ reportType, reportParams, buttonText = "Download PDF", buttonStyle = {} }) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleDownload = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await generateReportApi(reportType, reportParams); // Actual API call

      const getFilenameFromHeaders = (headers) => {
        const contentDisposition = headers.get('Content-Disposition');
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="?(.+)"?/i);
          if (filenameMatch && filenameMatch.length > 1) {
            return filenameMatch[1];
          }
        }
        return null;
      };

      if (response.type === 'blob') {
        const pdfBlob = response.data;
        const filename = getFilenameFromHeaders(response.headers) || `report-${reportType}-${Date.now()}.pdf`;

        // Using native browser capabilities:
        const url = window.URL.createObjectURL(pdfBlob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        console.log(`Blob download initiated for: ${filename}`);

        // Or using file-saver:
        // import { saveAs } from 'file-saver';
        // saveAs(pdfBlob, filename);

      } else if (response.type === 'json_url') {
        const { pdfUrl, filename: suggestedFilename } = response.data;
        const filename = suggestedFilename || `report-${reportType}-${Date.now()}.pdf`;

        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = pdfUrl;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        console.log(`URL download initiated for: ${pdfUrl} as ${filename}`);
      }

    } catch (err) {
      console.error("Error generating report:", err);
      // Attempt to extract a more specific error message
      let errorMessage = 'Failed to generate PDF. Please try again.';
      if (err.response && err.response.data && err.response.data.error) { // Common with axios
         errorMessage = err.response.data.error;
      } else if (err.data && err.data.error) { // If error is in response.data directly
         errorMessage = err.data.error;
      } else if (err.message) {
         errorMessage = err.message;
      }
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [reportType, reportParams]); // Dependencies for useCallback

  return (
    <div style={{ display: 'inline-block', marginRight: '10px', verticalAlign: 'top' }}>
      <button
        onClick={handleDownload}
        disabled={isLoading}
        style={{
          opacity: isLoading ? 0.7 : 1,
          cursor: isLoading ? 'wait' : 'pointer',
          padding: '10px 15px',
          fontSize: '14px',
          ...buttonStyle
        }}
      >
        {isLoading ? 'Generating PDF...' : buttonText}
      </button>
      {error && <p style={{ color: 'red', marginTop: '10px', fontSize: '12px' }}>Error: {error}</p>}
    </div>
  );
}

export default DownloadReportButton;

// Example Usage (elsewhere in your application):
//
// import DownloadReportButton from './DownloadReportButton';
//
// function MyReportPage() {
//   const reportFilters = { status: 'active', region: 'north' }; // From component state, context, etc.
//   const delegationId = 'del-123'; // Specific ID for this report
//
//   return (
//     <div>
//       <h1>Delegation Report</h1>
//       {/* Other report content */}
//
//       <DownloadReportButton
//         reportType="delegation-summary"
//         reportParams={{
//           filters: reportFilters,
//           delegationId: delegationId,
//           simulateSuccess: 'blob' // or 'json_url' or 'error' for testing snippet
//         }}
//         buttonText="Download Summary PDF"
//       />
//       <DownloadReportButton
//         reportType="detailed-activity"
//         reportParams={{
//           filters: reportFilters,
//           delegationId: delegationId,
//           includeComments: true,
//           simulateSuccess: 'json_url'
//         }}
//         buttonText="Download Detailed Activity"
//       />
//        <DownloadReportButton
//         reportType="error-test"
//         reportParams={{
//           simulateSuccess: 'error'
//         }}
//         buttonText="Test Error Handling"
//       />
//     </div>
//   );
// }
```

This plan provides a comprehensive guide for the frontend team to integrate the new server-side PDF generation service. It emphasizes user experience through clear feedback and robust error handling.The "Frontend Integration Plan" markdown file (`frontend_integration_plan.md`) has been successfully created in the previous turn.

This file includes:

1.  **Overview of Frontend Changes:**
    *   **User Interface (UI) Elements:** Replacement/repurposing of "Print" buttons to a new "Download PDF" button, with notes on placement and styling.
    *   **API Call Logic:** Explanation of API calls to `POST /api/reports/generate/{reportType}`, data collection from the frontend, and use of an API service/utility function (with a conceptual `generateReportApi` example).
    *   **Loading States & User Feedback:** Details on idle, loading, progress (and its typical infeasibility for simple cases), success, and error states, emphasizing clear visual feedback.
    *   **PDF Download Handling:** Description of handling streamed PDF blobs (native browser capabilities, optional `file-saver`) and URLs to stored PDFs, including filename suggestion.

2.  **Example React Component Snippet (Conceptual):**
    *   A `DownloadReportButton` functional component with hooks (`useState`, `useCallback`).
    *   State management for loading and error states.
    *   An asynchronous `handleDownload` function.
    *   Simulated API call logic (via `generateReportApi` simulation) covering blob and JSON URL responses, and error handling.
    *   Logic for initiating PDF download for both response types.
    *   Dynamic button text based on loading state.
    *   Display of an error message.
    *   Props for `reportType`, `reportParams`, and `buttonText`.
    *   Example usage of the component is also included within the snippet comments.

The created file addresses all requirements of the current subtask.
