import {useEffect, useRef, useState, useCallback} from 'react';
import {RefreshService, createRefreshService} from '@/lib/refreshService';

export interface AutoRefreshOptions {
	interval?: number;
	enabled?: boolean;
	immediate?: boolean;
	onRefresh?: () => void;
	onError?: (error: unknown) => void;
}

/**
 * Hook for automatically refreshing data at regular intervals
 *
 * @param fetchFn - The function to call for refreshing data
 * @param options - Configuration options for the refresh service
 * @returns Object with isRefreshing state and manual refresh trigger
 */
export function useAutoRefresh(
	fetchFn: () => Promise<void>,
	options: AutoRefreshOptions = {}
) {
	const {
		interval = 30000,
		enabled = true,
		immediate = false,
		onRefresh,
		onError,
	} = options;

	// Track if a refresh is in progress
	const [isRefreshing, setIsRefreshing] = useState(false);

	// Store the refresh service instance
	const serviceRef = useRef<RefreshService | null>(null);

	// Wrap the fetch function to manage local state
	const wrappedFetchFn = useCallback(async () => {
		setIsRefreshing(true);
		try {
			await fetchFn();
			onRefresh?.();
		} catch (error) {
			onError?.(error);
			throw error; // Re-throw to let the service handle retries
		} finally {
			setIsRefreshing(false);
		}
	}, [fetchFn, onRefresh, onError]);

	// Initialize the refresh service on mount
	useEffect(() => {
		serviceRef.current = createRefreshService(wrappedFetchFn, {
			interval,
			enabled,
			immediate,
			errorHandler: onError,
		});

		// Clean up on unmount
		return () => {
			if (serviceRef.current) {
				serviceRef.current.dispose();
				serviceRef.current = null;
			}
		};
	}, []);

	// Update service options when they change
	useEffect(() => {
		if (serviceRef.current) {
			serviceRef.current.updateOptions({
				interval,
				enabled,
			});
		}
	}, [interval, enabled]);

	// Provide a manual refresh function
	const refresh = useCallback(async () => {
		if (serviceRef.current) {
			await serviceRef.current.refresh();
		}
	}, []);

	return {
		isRefreshing,
		refresh,
	};
}

export default useAutoRefresh;
