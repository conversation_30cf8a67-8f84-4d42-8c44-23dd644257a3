(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9527],{57541:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(95155),a=r(36521),c=r(2730),l=r(35695),i=r(28328);function d(){let e=(0,l.useRouter)(),s=async s=>{try{await (0,c.addVehicle)(s),e.push("/vehicles")}catch(e){console.error("Error adding vehicle:",e)}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,t.jsx)(i.A,{className:"h-8 w-8 text-primary"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-primary",children:"Add New Vehicle"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Enter the details of your new vehicle."})]})]}),(0,t.jsx)(a.A,{onSubmit:s})]})}},67569:(e,s,r)=>{Promise.resolve().then(r.bind(r,57541))}},e=>{var s=s=>e(e.s=s);e.O(0,[5769,8360,832,4066,8162,2730,1568,8441,1684,7358],()=>s(67569)),_N_E=e.O()}]);