# =============================================================================
# PHASE 1 SECURITY HARDENING: Secure Environment Configuration Template
# =============================================================================
#
# 🚨 SECURITY WARNING: This is a template file with placeholder values.
# 🔐 NEVER commit real credentials to version control.
# 📋 Generate strong secrets using: npm run generate-secrets
#
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Set USE_SUPABASE to 'true' to use Supabase, 'false' to use local PostgreSQL
USE_SUPABASE=true

# Database URL - This will be used by Prisma
# For local development with Docker:
# DATABASE_URL=postgresql://postgres:postgres@localhost:5432/workhub_db?schema=public

# For Supabase (replace with your actual connection string):
DATABASE_URL=REPLACE_WITH_YOUR_SUPABASE_DATABASE_URL

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Replace these with your actual Supabase project credentials
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=REPLACE_WITH_YOUR_SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=REPLACE_WITH_YOUR_SUPABASE_SERVICE_ROLE_KEY

# =============================================================================
# SECURITY SECRETS (REQUIRED)
# =============================================================================
# 🔐 Generate strong secrets using: openssl rand -base64 32
# 🚨 These MUST be 32+ characters and cryptographically secure

# JWT Secret for authentication (REQUIRED - 32+ chars)
JWT_SECRET=GENERATE_32_CHAR_SECRET_IMMEDIATELY

# API Secret for internal services (REQUIRED - 32+ chars)
API_SECRET=GENERATE_32_CHAR_SECRET_IMMEDIATELY

# Session Secret for session management (RECOMMENDED - 32+ chars)
SESSION_SECRET=GENERATE_32_CHAR_SECRET_IMMEDIATELY

# Encryption Key for sensitive data (RECOMMENDED - 32+ chars)
ENCRYPTION_KEY=GENERATE_32_CHAR_SECRET_IMMEDIATELY

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Server configuration
PORT=3001
NODE_ENV=development

# Logging level
LOG_LEVEL=info

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Frontend URLs for CORS (comma-separated)
FRONTEND_URL=http://localhost:3000,http://localhost:9002

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
# Rate limiting settings (Phase 1 preparation)
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100  # requests per window

# =============================================================================
# THIRD-PARTY API CONFIGURATION
# =============================================================================
# OpenSky Network API doesn't require an API key for basic access
# For higher rate limits, you can register at https://opensky-network.org/
# OPENSKY_API_KEY=your_opensky_api_key_here

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Run 'npm run generate-secrets' to generate secure secrets
# 2. Never commit real credentials to version control
# 3. Use different secrets for different environments
# 4. Rotate secrets regularly in production
# 5. Validate secrets on application startup
# =============================================================================