{"version": 3, "file": "taskDataSanitizer.js", "sourceRoot": "", "sources": ["../../src/middleware/taskDataSanitizer.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAIH,oDAAoD;AACpD,IAAI,MAAW,CAAC;AAChB,IAAI,CAAC;IACJ,uCAAuC;IACvC,MAAM,EAAC,OAAO,EAAE,SAAS,EAAC,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACxD,MAAM,GAAG,SAAS,CAAC;AACpB,CAAC;AAAC,OAAO,KAAK,EAAE,CAAC;IAChB,iDAAiD;IACjD,MAAM,GAAG;QACR,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,KAAK,EAAE,OAAO,CAAC,KAAK;KACpB,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAC/B,GAAY,EACZ,GAAa,EACb,IAAkB,EACX,EAAE;IACT,IAAI,CAAC;QACJ,iCAAiC;QACjC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC;YAC7B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,SAAS,CAAC;YAE1C,gDAAgD;YAChD,IACC,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;gBAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAC3C,CAAC;gBACF,MAAM,aAAa,GAClB,GAAG,CAAC,IAAI,CAAC,mBAAmB,KAAK,IAAI;oBACpC,CAAC,CAAC,MAAM;oBACR,CAAC,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC;gBAExC,MAAM,CAAC,IAAI,CACV,iEAAiE,aAAa,qBAAqB;oBAClG,YAAY,MAAM,WAAW,WAAW,GAAG,CAC5C,CAAC;gBACF,GAAG,CAAC,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;YACnC,CAAC;YAED,2CAA2C;YAC3C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;gBACzE,MAAM,aAAa,GAClB,GAAG,CAAC,IAAI,CAAC,cAAc,KAAK,IAAI;oBAC/B,CAAC,CAAC,MAAM;oBACR,CAAC,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC;gBAEnC,MAAM,CAAC,IAAI,CACV,4DAA4D,aAAa,qBAAqB;oBAC7F,YAAY,MAAM,WAAW,WAAW,GAAG,CAC5C,CAAC;gBACF,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;YAC9B,CAAC;YAED,qCAAqC;YACrC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7D,MAAM,aAAa,GAClB,GAAG,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAEhE,MAAM,CAAC,IAAI,CACV,sDAAsD,aAAa,qBAAqB;oBACvF,YAAY,MAAM,WAAW,WAAW,GAAG,CAC5C,CAAC;gBACF,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;YACxB,CAAC;QACF,CAAC;QAED,IAAI,EAAE,CAAC;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,8EAA8E;QAC9E,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QAChE,IAAI,EAAE,CAAC;IACR,CAAC;AACF,CAAC,CAAC"}