'use client';

import React from 'react';
import {Loader2, AlertCircle} from 'lucide-react';
import {cn} from '@/lib/utils';
import {Skeleton} from '@/components/ui/skeleton';
import {Alert, AlertDescription, AlertTitle} from '@/components/ui/alert';
import {ActionButton} from '@/components/ui/action-button';

// Size variants for the spinner
export type SpinnerSize = 'sm' | 'md' | 'lg' | 'xl';

// Size mappings for the spinner
const spinnerSizeClasses: Record<SpinnerSize, string> = {
	sm: 'h-4 w-4',
	md: 'h-6 w-6',
	lg: 'h-8 w-8',
	xl: 'h-12 w-12',
};

// Text size mappings for the spinner
const spinnerTextSizeClasses: Record<SpinnerSize, string> = {
	sm: 'text-xs',
	md: 'text-sm',
	lg: 'text-base',
	xl: 'text-lg',
};

export interface LoadingSpinnerProps {
	/**
	 * Size of the spinner
	 */
	size?: SpinnerSize;

	/**
	 * Custom CSS class names
	 */
	className?: string;

	/**
	 * Text to display below the spinner
	 */
	text?: string;

	/**
	 * Whether to display as a full-page overlay
	 */
	fullPage?: boolean;
}

/**
 * Unified loading spinner component
 *
 * @example
 * <LoadingSpinner size="md" text="Loading data..." />
 */
export function LoadingSpinner({
	size = 'md',
	className,
	text,
	fullPage = false,
}: LoadingSpinnerProps) {
	return (
		<div
			className={cn(
				'flex items-center justify-center',
				fullPage && 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50',
				className
			)}>
			<div className='flex flex-col items-center'>
				<Loader2
					className={cn('animate-spin text-primary', spinnerSizeClasses[size])}
				/>
				{text && (
					<span
						className={cn(
							'mt-2 text-muted-foreground',
							spinnerTextSizeClasses[size]
						)}>
						{text}
					</span>
				)}
			</div>
		</div>
	);
}

// Skeleton variants
export type SkeletonVariant = 'default' | 'card' | 'table' | 'list' | 'stats';

export interface SkeletonLoaderProps {
	/**
	 * Type of content to show a skeleton for
	 */
	variant?: SkeletonVariant;

	/**
	 * Number of skeleton items to display
	 */
	count?: number;

	/**
	 * Custom CSS class names
	 */
	className?: string;

	/**
	 * Test ID for testing
	 */
	testId?: string;
}

/**
 * Unified skeleton loader component
 *
 * @example
 * <SkeletonLoader variant="card" count={3} />
 */
export function SkeletonLoader({
	variant = 'default',
	count = 1,
	className,
	testId = 'loading-skeleton',
}: SkeletonLoaderProps) {
	// Render card skeleton (for entity cards like vehicles, employees)
	if (variant === 'card') {
		return (
			<div
				className={cn(
					'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
					className
				)}
				data-testid={testId}>
				{Array(count)
					.fill(0)
					.map((_, i) => (
						<div
							key={i}
							className='overflow-hidden shadow-md rounded-lg border bg-card'>
							<Skeleton className='aspect-[16/10] w-full' />
							<div className='p-5'>
								<Skeleton className='h-7 w-3/4 mb-1' />
								<Skeleton className='h-4 w-1/2 mb-3' />
								<Skeleton className='h-px w-full my-3' />
								<div className='space-y-2.5'>
									{Array(3)
										.fill(0)
										.map((_, j) => (
											<div key={j} className='flex items-center'>
												<Skeleton className='mr-2.5 h-5 w-5 rounded-full' />
												<Skeleton className='h-5 w-2/3' />
											</div>
										))}
								</div>
							</div>
						</div>
					))}
			</div>
		);
	}

	// Render table skeleton
	if (variant === 'table') {
		return (
			<div className={cn('space-y-3', className)} data-testid={testId}>
				<div className='flex gap-4'>
					{Array(3)
						.fill(0)
						.map((_, i) => (
							<Skeleton key={i} className='h-8 flex-1' />
						))}
				</div>
				{Array(count)
					.fill(0)
					.map((_, i) => (
						<div key={i} className='flex gap-4'>
							{Array(3)
								.fill(0)
								.map((_, j) => (
									<Skeleton key={j} className='h-6 flex-1' />
								))}
						</div>
					))}
			</div>
		);
	}

	// Render list skeleton
	if (variant === 'list') {
		return (
			<div className={cn('space-y-3', className)} data-testid={testId}>
				{Array(count)
					.fill(0)
					.map((_, i) => (
						<div key={i} className='flex items-center gap-4'>
							<Skeleton className='h-12 w-12 rounded-full' />
							<div className='space-y-2 flex-1'>
								<Skeleton className='h-4 w-1/3' />
								<Skeleton className='h-4 w-full' />
							</div>
						</div>
					))}
			</div>
		);
	}

	// Render stats skeleton
	if (variant === 'stats') {
		return (
			<div
				className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}
				data-testid={testId}>
				{Array(count)
					.fill(0)
					.map((_, i) => (
						<div key={i} className='rounded-lg border bg-card p-5 shadow-sm'>
							<div className='flex justify-between'>
								<Skeleton className='h-5 w-1/3' />
								<Skeleton className='h-5 w-5 rounded-full' />
							</div>
							<Skeleton className='h-8 w-1/2 mt-3' />
							<Skeleton className='h-4 w-2/3 mt-2' />
						</div>
					))}
			</div>
		);
	}

	// Default skeleton
	return (
		<div className={cn('space-y-2', className)} data-testid={testId}>
			{Array(count)
				.fill(0)
				.map((_, i) => (
					<Skeleton key={i} className='w-full h-5' />
				))}
		</div>
	);
}

export interface ErrorDisplayProps {
	/**
	 * Error message to display
	 */
	message: string;

	/**
	 * Function to retry the operation
	 */
	onRetry?: () => void;

	/**
	 * Custom CSS class names
	 */
	className?: string;
}

/**
 * Unified error display component
 *
 * @example
 * <ErrorDisplay message="Failed to load data" onRetry={refetch} />
 */
export function ErrorDisplay({message, onRetry, className}: ErrorDisplayProps) {
	return (
		<Alert variant='destructive' className={cn('my-4', className)}>
			<AlertCircle className='h-4 w-4' />
			<AlertTitle>Error</AlertTitle>
			<AlertDescription>
				<div className='mt-2'>
					<p className='text-sm text-muted-foreground mb-4'>{message}</p>
					{onRetry && (
						<ActionButton
							actionType='tertiary'
							size='sm'
							onClick={onRetry}
							icon={<Loader2 className='h-4 w-4' />}>
							Try Again
						</ActionButton>
					)}
				</div>
			</AlertDescription>
		</Alert>
	);
}

export interface DataLoaderProps<T> {
	/**
	 * Whether data is currently loading
	 */
	isLoading: boolean;

	/**
	 * Error message, if any
	 */
	error?: string | null;

	/**
	 * The data to render
	 */
	data: T | null | undefined;

	/**
	 * Function to retry loading data
	 */
	onRetry?: () => void;

	/**
	 * Render function for the data
	 */
	children: (data: T) => React.ReactNode;

	/**
	 * Custom loading component
	 */
	loadingComponent?: React.ReactNode;

	/**
	 * Custom error component
	 */
	errorComponent?: React.ReactNode;

	/**
	 * Custom empty state component
	 */
	emptyComponent?: React.ReactNode;

	/**
	 * Custom CSS class names
	 */
	className?: string;
}

/**
 * DataLoader component for handling loading, error, and empty states
 *
 * @example
 * <DataLoader
 *   isLoading={isLoading}
 *   error={error}
 *   data={vehicles}
 *   onRetry={refetch}
 *   loadingComponent={<SkeletonLoader variant="card" count={3} />}
 * >
 *   {(vehicles) => (
 *     <div className="grid grid-cols-3 gap-4">
 *       {vehicles.map(vehicle => (
 *         <VehicleCard key={vehicle.id} vehicle={vehicle} />
 *       ))}
 *     </div>
 *   )}
 * </DataLoader>
 */
export function DataLoader<T>({
	isLoading,
	error,
	data,
	onRetry,
	children,
	loadingComponent,
	errorComponent,
	emptyComponent,
	className,
}: DataLoaderProps<T>) {
	if (isLoading) {
		return (
			loadingComponent || (
				<LoadingSpinner className={className} text='Loading...' />
			)
		);
	}

	if (error) {
		return (
			errorComponent || (
				<ErrorDisplay message={error} onRetry={onRetry} className={className} />
			)
		);
	}

	if (!data || (Array.isArray(data) && data.length === 0)) {
		return (
			emptyComponent || (
				<div className={cn('text-center py-8', className)}>
					<p className='text-muted-foreground'>No data available</p>
				</div>
			)
		);
	}

	return <div className={className}>{children(data)}</div>;
}
