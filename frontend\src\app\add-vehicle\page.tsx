'use client';

import VehicleForm from '@/components/vehicles/VehicleForm';
import {addVehicle as storeAddVehicle} from '@/lib/store';
import {useRouter} from 'next/navigation';
import type {Vehicle} from '@/lib/types';
import {Car} from 'lucide-react';

export default function AddVehiclePage() {
	const router = useRouter();

	const handleSubmit = async (
		data: Omit<Vehicle, 'id' | 'serviceHistory' | 'imageUrl'>
	) => {
		try {
			await storeAddVehicle(data);
			router.push('/vehicles');
		} catch (error) {
			console.error('Error adding vehicle:', error);
		}
	};

	return (
		<div className='space-y-6'>
			<div className='flex items-center space-x-2 mb-6'>
				<Car className='h-8 w-8 text-primary' />
				<div>
					<h1 className='text-3xl font-bold text-primary'>Add New Vehicle</h1>
					<p className='text-muted-foreground'>
						Enter the details of your new vehicle.
					</p>
				</div>
			</div>
			<VehicleForm onSubmit={handleSubmit} />
		</div>
	);
}
