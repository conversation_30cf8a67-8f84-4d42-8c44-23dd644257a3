'use client';

import DelegationForm from '@/components/delegations/DelegationForm';
import {addDelegation as storeAddDelegation} from '@/lib/store';
import {useRouter} from 'next/navigation';
import type {DelegationFormData} from '@/lib/schemas/delegationSchemas';
import {PageHeader} from '@/components/ui/PageHeader';
import {Briefcase} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';

export default function AddDelegationPage() {
	const router = useRouter();
	const {toast} = useToast();

	const handleSubmit = async (data: DelegationFormData) => {
		try {
			await storeAddDelegation(data);
			toast({
				title: 'Delegation Added',
				description: `The delegation "${data.eventName}" has been successfully created.`,
				variant: 'default',
			});
			router.push('/delegations');
		} catch (error) {
			console.error('Error adding delegation:', error);
			toast({
				title: 'Error',
				description: 'Failed to add delegation. Please try again.',
				variant: 'destructive',
			});
		}
	};

	return (
		<div className='space-y-6'>
			<PageHeader
				title='Add New Delegation'
				description='Enter the details for the new delegation or event.'
				icon={Briefcase}
			/>
			<DelegationForm onSubmit={handleSubmit} isEditing={false} />
		</div>
	);
}
