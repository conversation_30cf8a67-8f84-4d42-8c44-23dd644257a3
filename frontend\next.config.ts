import type {NextConfig} from 'next';

const nextConfig: NextConfig = {
	/* config options here */
	typescript: {
		ignoreBuildErrors: true,
	},
	eslint: {
		ignoreDuringBuilds: true,
	},
	// Transpile specific modules that might cause issues
	transpilePackages: ['html2canvas', 'jspdf', 'papaparse'],
	images: {
		remotePatterns: [
			{
				protocol: 'https',
				hostname: 'picsum.photos',
				port: '',
				pathname: '/**',
			},
			{
				protocol: 'https',
				hostname: 'images.app.goo.gl',
				port: '',
				pathname: '/**',
			},
			{
				protocol: 'https',
				hostname: 'as1.ftcdn.net',
				port: '',
				pathname: '/**',
			},
			{
				protocol: 'https',
				hostname: 't4.ftcdn.net',
				port: '',
				pathname: '/**',
			},
		],
	},
	// Add webpack configuration to properly resolve modules
	webpack: (config, {isServer}) => {
		// Fixes npm packages that depend on `fs` module
		if (!isServer) {
			config.resolve.fallback = {
				...config.resolve.fallback,
				fs: false,
				path: false,
				os: false,
			};
		}
		return config;
	},
	turbopack: {
		// Turbopack configuration options
		resolveAlias: {
			// Map aliased imports to their actual modules
			'@components': './src/components',
			'@utils': './src/utils',
			'@hooks': './src/hooks',
			'@styles': './src/styles',
		},
		resolveExtensions: ['.tsx', '.ts', '.jsx', '.js', '.json', '.css'],
		// Add rules for specific file types if needed
		rules: {
			'*.svg': {
				loaders: ['@svgr/webpack'],
				as: '*.js',
			},
		},
	},
};

export default nextConfig;
