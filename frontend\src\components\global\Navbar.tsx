
'use client';
import Link from 'next/link';
import {
	Building2 as WorkHubLogoIcon,
	LayoutDashboard,
	Car as MyVehiclesIcon,
	History,
	Briefcase,
	ClipboardList,
	UsersRound as EmployeesIcon,
	Settings as AdminIcon,
} from 'lucide-react';
import {Button} from '@/components/ui/button';
import {usePathname} from 'next/navigation';
import {cn} from '@/lib/utils';
import {ThemeToggle} from '@/components/theme-toggle';

export default function Navbar() {
	const pathname = usePathname();

	const navItems = [
		{href: '/', label: 'Dashboard', icon: LayoutDashboard},
		{href: '/vehicles', label: 'Assets', icon: MyVehiclesIcon},
		{href: '/service-history', label: 'Maintenance', icon: History},
		{href: '/delegations', label: 'Projects', icon: Briefcase},
		{href: '/tasks', label: 'Tasks', icon: ClipboardList},
		{href: '/employees', label: 'Team', icon: EmployeesIcon},
		{href: '/admin', label: 'Admin', icon: AdminIcon},
	];

	return (
		<header className='bg-card text-card-foreground shadow-md no-print border-b border-border'>
			<nav className='container mx-auto px-4 sm:px-6 lg:px-8'>
				<div className='flex items-center justify-between h-16'>
					<div className='flex items-center'>
						<Link
							href='/'
							className='flex items-center space-x-2 text-xl font-semibold hover:opacity-80 transition-opacity'>
							<WorkHubLogoIcon className='h-7 w-7 text-primary' />
							<span>WorkHub</span>
						</Link>
					</div>
					<div className='flex items-center space-x-1 md:space-x-2'>
						{navItems.map((item) => (
							<Button
								key={item.href}
								variant='ghost'
								asChild
								className={cn(
									'hover:bg-accent hover:text-accent-foreground px-2 md:px-3',
									pathname === item.href ||
										(item.href !== '/' && pathname.startsWith(item.href))
										? 'bg-accent text-accent-foreground'
										: 'text-foreground'
								)}>
								<Link href={item.href} className='flex items-center'>
									<item.icon className='mr-0 h-4 w-4 md:mr-2' />
									<span className='hidden md:inline'>{item.label}</span>
								</Link>
							</Button>
						))}
						<ThemeToggle />
					</div>
				</div>
			</nav>
		</header>
	);
}
