{"version": 3, "file": "vehicle.routes.js", "sourceRoot": "", "sources": ["../../src/routes/vehicle.routes.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,MAAM,EAAC,MAAM,SAAS,CAAC;AAC/B,OAAO,KAAK,iBAAiB,MAAM,sCAAsC,CAAC;AAC1E,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AACrD,OAAO,EACN,wBAAwB,EACxB,WAAW,GACX,MAAM,+BAA+B,CAAC;AACvC,OAAO,EACN,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,GACf,MAAM,8BAA8B,CAAC;AAEtC,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC;AAExB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AACH,mEAAmE;AACnE,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,wBAAwB,EAAE,iBAAiB,CAAC,cAAc,CAAC,CAAC;AAC5E,MAAM,CAAC,IAAI,CACV,GAAG,EACH,wBAAwB,EACxB,WAAW,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,EAChD,QAAQ,CAAC,mBAAmB,CAAC,EAC7B,iBAAiB,CAAC,aAAa,CAC/B,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuEG;AACH,MAAM,CAAC,GAAG,CACT,MAAM,EACN,wBAAwB,EACxB,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,EACnC,iBAAiB,CAAC,cAAc,CAChC,CAAC;AACF,MAAM,CAAC,GAAG,CACT,MAAM,EACN,wBAAwB,EACxB,WAAW,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,EAChD,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,EACnC,QAAQ,CAAC,mBAAmB,CAAC,EAC7B,iBAAiB,CAAC,aAAa,CAC/B,CAAC;AACF,MAAM,CAAC,MAAM,CACZ,MAAM,EACN,wBAAwB,EACxB,WAAW,CAAC,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,EACrC,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,EACnC,iBAAiB,CAAC,aAAa,CAC/B,CAAC;AAEF,eAAe,MAAM,CAAC"}