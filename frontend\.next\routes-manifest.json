{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/delegations/[id]", "regex": "^/delegations/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/delegations/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/delegations/[id]/edit", "regex": "^/delegations/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/delegations/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/delegations/[id]/report", "regex": "^/delegations/([^/]+?)/report(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/delegations/(?<nxtPid>[^/]+?)/report(?:/)?$"}, {"page": "/employees/edit/[id]", "regex": "^/employees/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/employees/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/employees/[id]", "regex": "^/employees/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/employees/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/employees/[id]/edit", "regex": "^/employees/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/employees/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/tasks/[id]", "regex": "^/tasks/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/tasks/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/tasks/[id]/edit", "regex": "^/tasks/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/tasks/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/vehicles/edit/[id]", "regex": "^/vehicles/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/vehicles/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/vehicles/[id]", "regex": "^/vehicles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/vehicles/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/vehicles/[id]/report", "regex": "^/vehicles/([^/]+?)/report(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/vehicles/(?<nxtPid>[^/]+?)/report(?:/)?$"}, {"page": "/vehicles/[id]/report/service-history", "regex": "^/vehicles/([^/]+?)/report/service\\-history(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/vehicles/(?<nxtPid>[^/]+?)/report/service\\-history(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/add-vehicle", "regex": "^/add\\-vehicle(?:/)?$", "routeKeys": {}, "namedRegex": "^/add\\-vehicle(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/auth-test", "regex": "^/auth\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth\\-test(?:/)?$"}, {"page": "/delegations", "regex": "^/delegations(?:/)?$", "routeKeys": {}, "namedRegex": "^/delegations(?:/)?$"}, {"page": "/delegations/add", "regex": "^/delegations/add(?:/)?$", "routeKeys": {}, "namedRegex": "^/delegations/add(?:/)?$"}, {"page": "/delegations/report/list", "regex": "^/delegations/report/list(?:/)?$", "routeKeys": {}, "namedRegex": "^/delegations/report/list(?:/)?$"}, {"page": "/employees", "regex": "^/employees(?:/)?$", "routeKeys": {}, "namedRegex": "^/employees(?:/)?$"}, {"page": "/employees/add", "regex": "^/employees/add(?:/)?$", "routeKeys": {}, "namedRegex": "^/employees/add(?:/)?$"}, {"page": "/employees/new", "regex": "^/employees/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/employees/new(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/service-history", "regex": "^/service\\-history(?:/)?$", "routeKeys": {}, "namedRegex": "^/service\\-history(?:/)?$"}, {"page": "/tasks", "regex": "^/tasks(?:/)?$", "routeKeys": {}, "namedRegex": "^/tasks(?:/)?$"}, {"page": "/tasks/add", "regex": "^/tasks/add(?:/)?$", "routeKeys": {}, "namedRegex": "^/tasks/add(?:/)?$"}, {"page": "/tasks/report", "regex": "^/tasks/report(?:/)?$", "routeKeys": {}, "namedRegex": "^/tasks/report(?:/)?$"}, {"page": "/vehicles", "regex": "^/vehicles(?:/)?$", "routeKeys": {}, "namedRegex": "^/vehicles(?:/)?$"}, {"page": "/vehicles/new", "regex": "^/vehicles/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/vehicles/new(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}