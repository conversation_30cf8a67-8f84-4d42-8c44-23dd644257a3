'use client';

import {useState, useEffect, useCallback} from 'react';
import {
	Search,
	Loader2,
	Plane,
	PlaneTakeoff,
	PlaneLanding,
	Calendar as CalendarIcon,
} from 'lucide-react';
import {format} from 'date-fns';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
} from '@/components/ui/dialog';
import {ActionButton} from '@/components/ui/action-button'; // Changed
import {Input} from '@/components/ui/input';
import {ScrollArea} from '@/components/ui/scroll-area';
import {useToast} from '@/hooks/use-toast';
import {
	FlightData,
	searchFlightsByCallsignAndDate,
} from '@/lib/api/flight.service';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover';
import {Calendar} from '@/components/ui/calendar';
import {cn} from '@/lib/utils';

interface FlightSearchModalProps {
	isOpen: boolean;
	onClose: () => void;
	onSelectFlight: (flight: FlightData) => void;
	type: 'arrival' | 'departure';
}

export default function FlightSearchModal({
	isOpen,
	onClose,
	onSelectFlight,
	type,
}: FlightSearchModalProps) {
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedDate, setSelectedDate] = useState<Date | undefined>(
		new Date()
	);
	const [isLoading, setIsLoading] = useState(false);
	const [flights, setFlights] = useState<FlightData[]>([]);
	const [error, setError] = useState<string | null>(null);
	const {toast} = useToast();

	const debouncedSearch = useCallback(
		debounce(async (term: string, date: Date | undefined) => {
			if (term.length < 2) {
				setFlights([]);
				return;
			}
			if (!date) {
				setError('Please select a date to search.');
				toast({
					title: 'Date Required',
					description: 'Please select a date before searching for flights.',
					variant: 'destructive',
				});
				setFlights([]);
				return;
			}

			setIsLoading(true);
			setError(null);
			const formattedDate = format(date, 'yyyy-MM-dd');
			try {
				const data = await searchFlightsByCallsignAndDate(term, formattedDate);
				setFlights(data);
				if (data.length === 0) {
					toast({
						title: 'No Flights Found',
						description: `No flights found matching "${term}" on ${formattedDate}.`,
						variant: 'default',
					});
				}
			} catch (err: any) {
				setError(err.message || 'Failed to search flights');
				toast({
					title: 'Error Searching Flights',
					description: `Failed to search flights: ${err.message}. Please try again.`,
					variant: 'destructive',
				});
			} finally {
				setIsLoading(false);
			}
		}, 500),
		[toast]
	);

	useEffect(() => {
		if (searchTerm && selectedDate) {
			debouncedSearch(searchTerm, selectedDate);
		} else {
			setFlights([]);
		}
		return () => {
			debouncedSearch.cancel();
		};
	}, [searchTerm, selectedDate, debouncedSearch]);

	const handleSelectFlight = (flight: FlightData) => {
		onSelectFlight(flight);
		onClose();
	};

	const formatTimestamp = (timestamp?: number) => {
		if (!timestamp) return 'Unknown';
		return new Date(timestamp * 1000).toLocaleString();
	};

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className='sm:max-w-[600px] max-h-[80vh] flex flex-col'>
				<DialogHeader>
					<DialogTitle className='flex items-center'>
						{type === 'arrival' ? (
							<PlaneLanding className='mr-2 h-5 w-5 text-accent' />
						) : (
							<PlaneTakeoff className='mr-2 h-5 w-5 text-accent' />
						)}
						Search {type === 'arrival' ? 'Arrival' : 'Departure'} Flights
					</DialogTitle>
					<DialogDescription>
						Enter a flight callsign (e.g., BA123) and select a date to search
						for flights.
					</DialogDescription>
				</DialogHeader>

				<div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
					<div className='relative'>
						<Search className='absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground' />
						<Input
							placeholder='Callsign (e.g., BA123)'
							className='pl-10'
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
						/>
					</div>
					<div>
						<Popover>
							<PopoverTrigger asChild>
								<ActionButton
									actionType='tertiary'
									className={cn(
										'w-full justify-start text-left font-normal',
										!selectedDate && 'text-muted-foreground'
									)}
									icon={<CalendarIcon className='h-4 w-4' />}>
									{selectedDate ? (
										format(selectedDate, 'PPP')
									) : (
										<span>Pick a date</span>
									)}
								</ActionButton>
							</PopoverTrigger>
							<PopoverContent className='w-auto p-0'>
								<Calendar
									mode='single'
									selected={selectedDate}
									onSelect={setSelectedDate}
									initialFocus
								/>
							</PopoverContent>
						</Popover>
					</div>
				</div>

				{isLoading ? (
					<div className='flex justify-center items-center py-8'>
						<Loader2 className='h-8 w-8 animate-spin text-primary' />
						<span className='ml-2 text-muted-foreground'>
							Searching flights...
						</span>
					</div>
				) : error ? (
					// ErrorDisplay can be used here if preferred
					<div className='text-center py-8'>
						<div className='bg-destructive/15 text-destructive p-4 rounded-md mb-4 text-sm'>
							<h4 className='font-bold mb-2'>Error Details:</h4>
							<p className='mb-2'>{error}</p>
							{error && (error as any).details && (
								<div className='mt-3 border-t border-destructive/20 pt-3'>
									{(error as any).details.possibleReasons && (
										<div className='mt-2'>
											<h5 className='font-semibold text-xs mb-1'>
												Possible Reasons:
											</h5>
											<ul className='list-disc list-inside text-xs'>
												{(error as any).details.possibleReasons.map(
													(reason: string, idx: number) => (
														<li key={idx} className='mb-1'>
															{reason}
														</li>
													)
												)}
											</ul>
										</div>
									)}
									{(error as any).details.apiInfo && (
										<div className='mt-2 text-xs'>
											<h5 className='font-semibold mb-1'>API Information:</h5>
											<p>{(error as any).details.apiInfo}</p>
										</div>
									)}
								</div>
							)}
							<p className='text-xs text-muted-foreground mt-3'>
								API URL:{' '}
								{process.env.NEXT_PUBLIC_API_BASE_URL ||
									'http://localhost:3001/api'}
							</p>
						</div>
						<div className='flex justify-center gap-2'>
							{error.toString().includes('future date') ? (
								<>
									<ActionButton
										actionType='tertiary'
										onClick={() => {
											setSelectedDate(new Date());
											setTimeout(
												() => debouncedSearch(searchTerm, new Date()),
												100
											);
										}}>
										Try with Today's Date
									</ActionButton>
									<ActionButton
										actionType='tertiary'
										onClick={() => {
											const yesterday = new Date();
											yesterday.setDate(yesterday.getDate() - 1);
											setSelectedDate(yesterday);
											setTimeout(
												() => debouncedSearch(searchTerm, yesterday),
												100
											);
										}}>
										Try with Yesterday
									</ActionButton>
								</>
							) : (
								<ActionButton
									actionType='tertiary'
									onClick={() => debouncedSearch(searchTerm, selectedDate)}>
									Try Again
								</ActionButton>
							)}
						</div>
					</div>
				) : flights.length === 0 ? (
					<div className='text-center py-8 text-muted-foreground'>
						{searchTerm.length > 0 ? (
							<div>
								<p className='text-amber-500 font-medium mb-2'>
									No flights found matching "{searchTerm}" on{' '}
									{selectedDate ? format(selectedDate, 'PPP') : 'selected date'}
								</p>
								<div className='bg-muted p-4 rounded-md mb-4 text-sm'>
									<h4 className='font-semibold mb-2'>Suggestions:</h4>
									<ul className='list-disc list-inside text-muted-foreground text-sm'>
										<li className='mb-1'>
											Check if the callsign format is correct (e.g., "RYR441J"
											for Ryanair flight 441J)
										</li>
										<li className='mb-1'>
											Try searching for a different date - OpenSky may not have
											data for all dates
										</li>
										<li className='mb-1'>
											OpenSky Network API primarily provides historical data,
											not future schedules
										</li>
										<li className='mb-1'>
											Some flights may not be tracked by OpenSky Network
										</li>
									</ul>
								</div>
								<div className='text-xs text-muted-foreground p-2 bg-muted rounded-md inline-block'>
									<p>
										API URL:{' '}
										{process.env.NEXT_PUBLIC_API_BASE_URL ||
											'http://localhost:3001/api'}
										/flights/search
									</p>
									<p>Search Term: {searchTerm}</p>
									<p>
										Date:{' '}
										{selectedDate
											? format(selectedDate, 'yyyy-MM-dd')
											: 'Not selected'}
									</p>
								</div>
							</div>
						) : (
							'Enter a flight callsign to search.'
						)}
					</div>
				) : (
					<ScrollArea className='flex-1 max-h-[400px] pr-4'>
						<div className='space-y-2'>
							{flights.map((flight) => (
								<div
									key={`${flight.icao24}-${flight.callsign}`}
									className='p-3 border rounded-md hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors'
									onClick={() => handleSelectFlight(flight)}>
									<div className='flex justify-between items-start'>
										<div>
											<p className='font-semibold'>{flight.callsign}</p>
											<p className='text-sm text-muted-foreground'>
												{flight.departureAirport || 'Unknown'} →{' '}
												{flight.arrivalAirport || 'Unknown'}
											</p>
										</div>
										<div className='text-right text-sm'>
											<p className='flex items-center'>
												<Plane className='inline-block h-3 w-3 mr-1 text-muted-foreground' />
												{flight.icao24}
											</p>
											{flight.onGround !== undefined && (
												<p
													className={
														flight.onGround
															? 'text-amber-500'
															: 'text-green-500'
													}>
													{flight.onGround ? 'On Ground' : 'In Air'}
												</p>
											)}
										</div>
									</div>
									{(flight.departureTime || flight.arrivalTime) && (
										<div className='mt-2 text-xs text-muted-foreground'>
											{flight.departureTime && (
												<p>
													Departure: {formatTimestamp(flight.departureTime)}
												</p>
											)}
											{flight.arrivalTime && (
												<p>Arrival: {formatTimestamp(flight.arrivalTime)}</p>
											)}
										</div>
									)}
								</div>
							))}
						</div>
					</ScrollArea>
				)}
				<DialogFooter className='mt-4'>
					<ActionButton actionType='tertiary' onClick={onClose}>
						Cancel
					</ActionButton>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}

function debounce<T extends (...args: any[]) => any>(
	func: T,
	wait: number
): T & {cancel: () => void} {
	let timeout: ReturnType<typeof setTimeout> | null = null;
	const debounced = function (this: any, ...args: Parameters<T>) {
		const context = this;
		if (timeout) clearTimeout(timeout);
		timeout = setTimeout(() => {
			timeout = null;
			func.apply(context, args);
		}, wait);
	} as T & {cancel: () => void};
	debounced.cancel = function () {
		if (timeout) {
			clearTimeout(timeout);
			timeout = null;
		}
	};
	return debounced;
}
