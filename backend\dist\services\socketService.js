import { io } from '../server.js';
// Socket.io event names
export const SOCKET_EVENTS = {
    VEHICLE_UPDATED: 'vehicle:updated',
    VEHICLE_CREATED: 'vehicle:created',
    VEHICLE_DELETED: 'vehicle:deleted',
    <PERSON><PERSON><PERSON><PERSON>YEE_UPDATED: 'employee:updated',
    <PERSON>MP<PERSON>OYEE_CREATED: 'employee:created',
    <PERSON>MPLOYEE_DELETED: 'employee:deleted',
    SERVICE_RECORD_CREATED: 'servicerecord:created',
    SERVICE_RECORD_UPDATED: 'servicerecord:updated',
    SERVICE_RECORD_DELETED: 'servicerecord:deleted',
    DELEGATION_CREATED: 'delegation:created',
    DELEGATION_UPDATED: 'delegation:updated',
    DELEGATION_DELETED: 'delegation:deleted',
    TASK_CREATED: 'task:created',
    TASK_UPDATED: 'task:updated',
    TASK_DELETED: 'task:deleted',
    REFRESH_VEHICLES: 'refresh:vehicles',
    REFRESH_EMPLOYEES: 'refresh:employees',
    REFRESH_DELEGATIONS: 'refresh:delegations',
    REFRESH_TASKS: 'refresh:tasks',
    REFRESH_SERVICE_RECORDS: 'refresh:servicerecords', // Added refresh for service records
};
/**
 * Emit vehicle data changes to connected clients
 */
export const emitVehicleChange = (event, data) => {
    io.emit(event, data);
};
/**
 * Emit employee data changes to connected clients
 */
export const emitEmployeeChange = (event, data) => {
    io.emit(event, data);
};
/**
 * Emit service record data changes to connected clients
 */
export const emitServiceRecordChange = (event, data) => {
    io.emit(event, data);
    // Also emit a vehicle update if a service record change implies the vehicle data is stale
    if (data.vehicleId && (event === SOCKET_EVENTS.SERVICE_RECORD_CREATED || event === SOCKET_EVENTS.SERVICE_RECORD_DELETED || event === SOCKET_EVENTS.SERVICE_RECORD_UPDATED)) {
        io.emit(SOCKET_EVENTS.VEHICLE_UPDATED, { id: data.vehicleId, needsRefresh: true });
    }
};
/**
 * Emit delegation data changes to connected clients
 */
export const emitDelegationChange = (event, data) => {
    io.emit(event, data);
};
/**
 * Emit task data changes to connected clients
 */
export const emitTaskChange = (event, data) => {
    io.emit(event, data);
};
/**
 * Emit a refresh event for all vehicles
 */
export const emitRefreshVehicles = () => {
    io.emit(SOCKET_EVENTS.REFRESH_VEHICLES);
};
/**
 * Emit a refresh event for all employees
 */
export const emitRefreshEmployees = () => {
    io.emit(SOCKET_EVENTS.REFRESH_EMPLOYEES);
};
/**
 * Emit a refresh event for all delegations
 */
export const emitRefreshDelegations = () => {
    io.emit(SOCKET_EVENTS.REFRESH_DELEGATIONS);
};
/**
 * Emit a refresh event for all tasks
 */
export const emitRefreshTasks = () => {
    io.emit(SOCKET_EVENTS.REFRESH_TASKS);
};
/**
 * Emit a refresh event for all service records (or for a specific vehicle)
 */
export const emitRefreshServiceRecords = (vehicleId) => {
    if (vehicleId) {
        io.emit(SOCKET_EVENTS.REFRESH_SERVICE_RECORDS, { vehicleId });
    }
    else {
        io.emit(SOCKET_EVENTS.REFRESH_SERVICE_RECORDS);
    }
};
//# sourceMappingURL=socketService.js.map