import {z} from 'zod';

export const EmployeeRoleSchema = z.enum([
	'driver',
	'mechanic',
	'administrator',
	'office_staff',
	'manager',
	'service_advisor',
	'technician',
	'other',
]);
export type EmployeeRoleType = z.infer<typeof EmployeeRoleSchema>;

export const EmployeeStatusSchema = z.enum([
	'Active',
	'On_Leave',
	'Terminated',
	'Inactive',
]);
export type EmployeeStatusType = z.infer<typeof EmployeeStatusSchema>;

export const DriverAvailabilitySchema = z.enum([
	'On_Shift',
	'Off_Shift',
	'On_Break',
	'Busy',
]);
export type DriverAvailabilityType = z.infer<typeof DriverAvailabilitySchema>;

const employeeBaseFields = {
	name: z.string().min(1, 'Name is required'),
	fullName: z.string().optional().nullable(), // Optional, can be derived or same as name
	role: EmployeeRoleSchema,
	employeeId: z.string().min(1, 'Employee ID (business key) is required'),
	contactInfo: z
		.string()
		.min(1, 'Contact information is required')
		.refine((val) => {
			const phoneRegex = /^[\d\s\+\-\(\)]{7,20}$/;
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			return phoneRegex.test(val) || emailRegex.test(val);
		}, 'Contact information must be a valid phone number or email address'),
	contactEmail: z.string().email('Invalid email address').optional().nullable(),
	contactPhone: z.string().optional().nullable(),
	contactMobile: z.string().optional().nullable(),
	position: z.string().optional().nullable(),
	department: z.string().optional().nullable(),
	hireDate: z
		.string()
		.datetime({message: 'Invalid hire date format. Expected ISO string.'})
		.optional()
		.nullable(),
	status: EmployeeStatusSchema.optional(),

	// Driver-specific, made optional
	availability: DriverAvailabilitySchema.optional().nullable(),
	currentLocation: z.string().optional().nullable(),
	workingHours: z.string().optional().nullable(),
	assignedVehicleId: z.number().int().positive().nullable().optional(), // Changed to number for Vehicle ID FK

	skills: z.array(z.string()).optional().default([]),
	shiftSchedule: z.string().optional().nullable(),
	generalAssignments: z.array(z.string()).optional().default([]),
	notes: z.string().optional().nullable(),
	profileImageUrl: z
		.string()
		.url('Invalid image URL')
		.optional()
		.nullable()
		.or(z.literal('')),
};

export const employeeCreateSchema = z.object({
	...employeeBaseFields,
	hireDate: z
		.string()
		.datetime({message: 'Invalid hire date format. Expected ISO string.'}), // Make hireDate required on create
});

export const employeeUpdateSchema = z
	.object({
		...employeeBaseFields,
		statusChangeReason: z.string().optional().nullable(), // For logging status changes during update
	})
	.partial(); // All fields are optional on update

export const employeeIdSchema = z.object({
	id: z
		.string()
		.refine((val) => val === 'enriched' || !isNaN(parseInt(val, 10)), {
			message: 'ID must be a valid number or the special value "enriched"',
		})
		.transform((val) => (val === 'enriched' ? val : parseInt(val, 10))),
});

export type EmployeeCreate = z.infer<typeof employeeCreateSchema>;
export type EmployeeUpdate = z.infer<typeof employeeUpdateSchema>;
export type EmployeeIdParam = z.infer<typeof employeeIdSchema>;
