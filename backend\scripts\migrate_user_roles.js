import {supabaseAdmin} from '../dist/lib/supabase.js';
import {randomUUID} from 'crypto';

async function migrateUserRoles() {
	console.log('🚀 Starting user role migration...');

	try {
		// Get all users from Supabase Auth
		const {
			data: {users},
			error: usersError,
		} = await supabaseAdmin.auth.admin.listUsers();

		if (usersError) {
			console.error('❌ Error fetching users:', usersError);
			return;
		}

		console.log(`📊 Found ${users.length} users to migrate`);

		let migrated = 0;
		let skipped = 0;
		let errors = 0;

		for (const user of users) {
			try {
				// Get role from user_metadata (current source)
				const currentRole = user.user_metadata?.role || 'USER';

				console.log(
					`👤 Processing user: ${user.email} (${user.id}) - Role: ${currentRole}`
				);

				// Check if user profile already exists
				const {data: existingProfile, error: checkError} = await supabaseAdmin
					.from('user_profiles')
					.select('id')
					.eq('user_id', user.id)
					.single();

				if (existingProfile) {
					console.log(
						`⏭️  User profile already exists for ${user.email}, skipping...`
					);
					skipped++;
					continue;
				}

				// Insert new user profile with explicit ID
				const {error: insertError} = await supabaseAdmin
					.from('user_profiles')
					.insert({
						id: randomUUID(), // Generate UUID explicitly
						user_id: user.id,
						role: currentRole.toUpperCase(), // Ensure uppercase to match enum
						is_active: true,
						created_at: new Date().toISOString(),
						updated_at: new Date().toISOString(),
					});

				if (insertError) {
					console.error(
						`❌ Error inserting profile for ${user.email}:`,
						insertError
					);
					errors++;
				} else {
					console.log(`✅ Migrated ${user.email} with role ${currentRole}`);
					migrated++;
				}
			} catch (error) {
				console.error(`❌ Error processing user ${user.email}:`, error);
				errors++;
			}
		}

		console.log('\n📈 Migration Summary:');
		console.log(`✅ Successfully migrated: ${migrated} users`);
		console.log(`⏭️  Skipped (already exists): ${skipped} users`);
		console.log(`❌ Errors: ${errors} users`);

		if (errors === 0) {
			console.log('\n🎉 Migration completed successfully!');
		} else {
			console.log(
				'\n⚠️  Migration completed with some errors. Please review the logs above.'
			);
		}
	} catch (error) {
		console.error('💥 Fatal error during migration:', error);
	}
}

// Run the migration
migrateUserRoles();
