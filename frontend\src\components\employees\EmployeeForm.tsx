'use client';

import React, {useEffect, useState} from 'react';
import {useRouter} from 'next/navigation';
import {useForm, SubmitHandler, Controller} from 'react-hook-form'; // Added Controller
import {zodResolver} from '@hookform/resolvers/zod';
import {Employee, Vehicle} from '@/lib/types';
import {
	EmployeeFormData,
	EmployeeFormSchema,
	EmployeeStatusSchema,
	EmployeeRoleSchema,
} from '@/lib/schemas/employeeSchemas';
import {DriverAvailabilitySchema} from '@/lib/schemas/driverSchemas';
import {getVehicles} from '@/lib/store';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {Textarea} from '@/components/ui/textarea';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardFooter,
	CardDescription,
} from '@/components/ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {Separator} from '@/components/ui/separator';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form'; // Added Form components
import {useToast} from '@/hooks/use-toast';

interface EmployeeFormProps {
	onSubmit: (data: EmployeeFormData) => Promise<void>;
	initialData?: Partial<Employee>;
	isEditing?: boolean;
	submitButtonText?: string;
	isLoading?: boolean;
}

const EmployeeForm: React.FC<EmployeeFormProps> = ({
	onSubmit,
	initialData = {},
	isEditing = false,
	submitButtonText = isEditing ? 'Save Changes' : 'Create Employee',
	isLoading = false,
}) => {
	const router = useRouter();
	const {toast} = useToast();
	const [vehicles, setVehicles] = useState<Vehicle[]>([]);

	const form = useForm<EmployeeFormData>({
		resolver: zodResolver(EmployeeFormSchema),
		defaultValues: {
			name: initialData?.name || initialData?.fullName || '',
			fullName: initialData?.fullName || initialData?.name || '',
			employeeId: (initialData as any)?.employeeId || '',
			position: initialData?.position || '',
			department: initialData?.department || '',
			contactInfo:
				(initialData as any)?.contactInfo || initialData?.contactEmail || '',
			contactEmail: initialData?.contactEmail || '',
			contactPhone: initialData?.contactPhone || '',
			contactMobile: initialData?.contactMobile || '',
			hireDate: initialData?.hireDate
				? new Date(initialData.hireDate).toISOString().split('T')[0]
				: '',
			status: initialData?.status || 'Active',
			role: initialData?.role || 'other',
			availability: initialData?.availability || null,
			currentLocation: initialData?.currentLocation || '',
			workingHours: initialData?.workingHours || '',
			assignedVehicleId: (initialData as any)?.assignedVehicleId
				? Number((initialData as any).assignedVehicleId)
				: initialData?.vehicleId
				? Number(initialData.vehicleId)
				: null,
			skills: initialData?.skills || [],
			shiftSchedule: initialData?.shiftSchedule || '',
			generalAssignments: initialData?.generalAssignments || [],
			notes: initialData?.notes || '',
			profileImageUrl: initialData?.profileImageUrl || '',
		},
	});
	const {
		reset,
		control,
		handleSubmit,
		watch,
		formState: {errors, isSubmitting},
	} = form;

	// Create a stable string representation of initialData for the useEffect dependency array
	// This helps prevent infinite loops if initialData reference changes but content doesn't.
	const initialDataString = JSON.stringify(initialData);

	useEffect(() => {
		if (isEditing && initialData && Object.keys(initialData).length > 0) {
			const mappedData = {
				name: initialData.name || initialData.fullName || '',
				fullName: initialData.fullName || initialData.name || '',
				employeeId: (initialData as any).employeeId || '',
				position: initialData.position || '',
				department: initialData.department || '',
				contactInfo:
					(initialData as any).contactInfo || initialData.contactEmail || '',
				contactEmail: initialData.contactEmail || '',
				contactPhone: initialData.contactPhone || '',
				contactMobile: initialData.contactMobile || '',
				hireDate: initialData.hireDate
					? new Date(initialData.hireDate).toISOString().split('T')[0]
					: '',
				status: initialData.status || 'Active',
				role: initialData.role || 'other',
				availability: initialData.availability || null,
				currentLocation: initialData.currentLocation || '',
				workingHours: initialData.workingHours || '',
				assignedVehicleId: (initialData as any).assignedVehicleId
					? Number((initialData as any).assignedVehicleId)
					: initialData?.vehicleId
					? Number(initialData.vehicleId)
					: null,
				skills: initialData.skills || [],
				shiftSchedule: initialData.shiftSchedule || '',
				generalAssignments: initialData.generalAssignments || [],
				notes: initialData.notes || '',
				profileImageUrl: initialData.profileImageUrl || '',
			};
			reset(mappedData);
		} else if (!isEditing) {
			// For "add" mode, ensure form is reset to initial defaults (or empty strings)
			// This is mostly handled by useForm's defaultValues.
			// reset(form.formState.defaultValues); // Or to a blank state if preferred
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [initialDataString, isEditing, reset]);

	// Fetch vehicles when component mounts
	useEffect(() => {
		const fetchVehicles = async () => {
			try {
				const vehData = await getVehicles();
				setVehicles(vehData || []);
			} catch (error) {
				console.error('Failed to fetch vehicles for employee form:', error);
				toast({
					title: 'Warning',
					description: 'Could not load vehicles for assignment.',
					variant: 'default',
				});
			}
		};
		fetchVehicles();
	}, [toast]);

	const currentRole = watch('role');

	const processSubmit: SubmitHandler<EmployeeFormData> = async (data) => {
		const processedData = {
			...data,
			assignedVehicleId: data.assignedVehicleId
				? Number(data.assignedVehicleId)
				: null,
			skills: Array.isArray(data.skills)
				? data.skills.map(String)
				: typeof data.skills === 'string'
				? (data.skills as string)
						.split(',')
						.map((s: string) => s.trim())
						.filter(Boolean)
				: [],
			generalAssignments: Array.isArray(data.generalAssignments)
				? data.generalAssignments.map(String)
				: typeof data.generalAssignments === 'string'
				? (data.generalAssignments as string)
						.split(',')
						.map((s: string) => s.trim())
						.filter(Boolean)
				: [],
		};
		await onSubmit(processedData);
	};

	return (
		<Form {...form}>
			<form onSubmit={handleSubmit(processSubmit)}>
				<Card className='max-w-3xl mx-auto'>
					<CardHeader>
						<CardTitle>
							{isEditing ? 'Edit Employee' : 'Add New Employee'}
						</CardTitle>
						<CardDescription>
							{isEditing
								? 'Update the details of the employee.'
								: 'Enter the details for the new employee.'}
						</CardDescription>
					</CardHeader>
					<CardContent className='space-y-6'>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
							<FormField
								control={control}
								name='name'
								render={({field}) => (
									<FormItem>
										<FormLabel>Display Name</FormLabel>
										<FormControl>
											<Input placeholder='e.g., Jane D.' {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={control}
								name='fullName'
								render={({field}) => (
									<FormItem>
										<FormLabel>Full Name (Optional)</FormLabel>
										<FormControl>
											<Input placeholder='e.g., Jane Marie Doe' {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
							<FormField
								control={control}
								name='employeeId'
								render={({field}) => (
									<FormItem>
										<FormLabel>Employee ID (Business Key)</FormLabel>
										<FormControl>
											<Input placeholder='e.g., EMP12345' {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={control}
								name='position'
								render={({field}) => (
									<FormItem>
										<FormLabel>Position/Title</FormLabel>
										<FormControl>
											<Input placeholder='e.g., Senior Mechanic' {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<FormField
							control={control}
							name='department'
							render={({field}) => (
								<FormItem>
									<FormLabel>Department</FormLabel>
									<FormControl>
										<Input placeholder='e.g., Maintenance' {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<Separator className='my-4' />
						<h3 className='text-lg font-medium text-foreground'>
							Contact Information
						</h3>

						<FormField
							control={control}
							name='contactInfo'
							render={({field}) => (
								<FormItem>
									<FormLabel>Primary Contact (Email/Phone)</FormLabel>
									<FormControl>
										<Input
											placeholder='e.g., <EMAIL> or 555-0101'
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
							<FormField
								control={control}
								name='contactEmail'
								render={({field}) => (
									<FormItem>
										<FormLabel>Contact Email (Optional)</FormLabel>
										<FormControl>
											<Input
												type='email'
												placeholder='e.g., <EMAIL>'
												{...field}
												value={field.value || ''}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={control}
								name='contactPhone'
								render={({field}) => (
									<FormItem>
										<FormLabel>Contact Phone (Optional)</FormLabel>
										<FormControl>
											<Input
												type='tel'
												placeholder='e.g., 555-0102'
												{...field}
												value={field.value || ''}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<FormField
							control={control}
							name='contactMobile'
							render={({field}) => (
								<FormItem>
									<FormLabel>Contact Mobile (Optional)</FormLabel>
									<FormControl>
										<Input
											type='tel'
											placeholder='e.g., 555-0103'
											{...field}
											value={field.value || ''}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<Separator className='my-4' />
						<h3 className='text-lg font-medium text-foreground'>
							Employment Details
						</h3>

						<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
							<FormField
								control={control}
								name='hireDate'
								render={({field}) => (
									<FormItem>
										<FormLabel>Hire Date</FormLabel>
										<FormControl>
											<Input type='date' {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={control}
								name='status'
								render={({field}) => (
									<FormItem>
										<FormLabel>Status</FormLabel>
										<Select
											onValueChange={field.onChange}
											value={field.value ?? undefined}>
											<FormControl>
												<SelectTrigger id='status'>
													<SelectValue placeholder='Select status' />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{EmployeeStatusSchema.options.map((s) => (
													<SelectItem key={s} value={s}>
														{s}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<Separator className='my-4' />
						<h3 className='text-lg font-medium text-foreground'>
							Role & Availability
						</h3>

						<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
							<FormField
								control={control}
								name='role'
								render={({field}) => (
									<FormItem>
										<FormLabel>Role</FormLabel>
										<Select
											onValueChange={field.onChange}
											value={field.value ?? undefined}>
											<FormControl>
												<SelectTrigger id='role'>
													<SelectValue placeholder='Select role' />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{EmployeeRoleSchema.options.map((r) => (
													<SelectItem key={r} value={r}>
														{r.charAt(0).toUpperCase() +
															r.slice(1).replace('_', ' ')}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
							{currentRole === 'driver' && (
								<FormField
									control={control}
									name='availability'
									render={({field}) => (
										<FormItem>
											<FormLabel>Availability (for Drivers)</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value ?? undefined}>
												<FormControl>
													<SelectTrigger id='availability'>
														<SelectValue placeholder='Select availability' />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{DriverAvailabilitySchema.options.map((a) => (
														<SelectItem key={a} value={a}>
															{a.replace('_', ' ')}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							)}
						</div>

						{currentRole === 'driver' && (
							<>
								<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
									<FormField
										control={control}
										name='currentLocation'
										render={({field}) => (
											<FormItem>
												<FormLabel>
													Current Location (Optional, for Drivers)
												</FormLabel>
												<FormControl>
													<Input
														placeholder='e.g., City, State or GPS link'
														{...field}
														value={field.value || ''}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={control}
										name='workingHours'
										render={({field}) => (
											<FormItem>
												<FormLabel>
													Working Hours (Optional, for Drivers)
												</FormLabel>
												<FormControl>
													<Input
														placeholder='e.g., Mon-Fri 9am-5pm'
														{...field}
														value={field.value || ''}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
								<FormField
									control={control}
									name='assignedVehicleId'
									render={({field}) => (
										<FormItem>
											<FormLabel>
												Assigned Vehicle (Optional, for Drivers)
											</FormLabel>
											<FormControl>
												<Select
													onValueChange={(value) =>
														field.onChange(
															value === 'null' ? null : parseInt(value, 10)
														)
													}
													value={
														field.value === null
															? 'null'
															: String(field.value ?? '')
													}>
													<FormControl>
														<SelectTrigger id='assignedVehicleId'>
															<SelectValue placeholder='Select vehicle (optional)' />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value='null'>No Vehicle</SelectItem>
														{vehicles.map((vehicle) => (
															<SelectItem
																key={vehicle.id}
																value={String(vehicle.id)}>
																{vehicle.make} {vehicle.model} ({vehicle.year})
																- {vehicle.licensePlate}
															</SelectItem>
														))}
													</SelectContent>
												</Select>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</>
						)}
						<Separator className='my-4' />
						<h3 className='text-lg font-medium text-foreground'>
							Other Details
						</h3>

						<FormField
							control={control}
							name='skills'
							render={({field}) => (
								<FormItem>
									<FormLabel>Skills (comma-separated)</FormLabel>
									<FormControl>
										<Input
											placeholder='e.g., Diesel Engine Repair, HVAC Systems, Welding'
											// Handle both array (from form state) and string (initial if not array)
											value={
												Array.isArray(field.value)
													? field.value.join(', ')
													: typeof field.value === 'string'
													? field.value
													: ''
											}
											onChange={(e) => {
												const stringValue = e.target.value;
												const arrayValue = stringValue
													.split(',')
													.map((s) => s.trim())
													.filter(Boolean);
												field.onChange(arrayValue);
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={control}
							name='shiftSchedule'
							render={({field}) => (
								<FormItem>
									<FormLabel>Shift Schedule (Optional)</FormLabel>
									<FormControl>
										<Input
											placeholder='e.g., Mon-Wed 8am-4pm, Thu-Fri 10am-6pm'
											{...field}
											value={field.value || ''}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={control}
							name='generalAssignments'
							render={({field}) => (
								<FormItem>
									<FormLabel>
										General Assignments (comma-separated, Optional)
									</FormLabel>
									<FormControl>
										<Input
											placeholder='e.g., Workshop Cleanup, Inventory Check'
											value={
												Array.isArray(field.value)
													? field.value.join(', ')
													: typeof field.value === 'string'
													? field.value
													: ''
											}
											onChange={(e) => {
												const stringValue = e.target.value;
												const arrayValue = stringValue
													.split(',')
													.map((s) => s.trim())
													.filter(Boolean);
												field.onChange(arrayValue);
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={control}
							name='profileImageUrl'
							render={({field}) => (
								<FormItem>
									<FormLabel>Profile Image URL (Optional)</FormLabel>
									<FormControl>
										<Input
											placeholder='https://example.com/profile.png'
											{...field}
											value={field.value || ''}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={control}
							name='notes'
							render={({field}) => (
								<FormItem>
									<FormLabel>Notes (Optional)</FormLabel>
									<FormControl>
										<Textarea
											placeholder='Any additional notes about the employee...'
											{...field}
											value={field.value || ''}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</CardContent>
					<CardFooter className='flex justify-end space-x-3 pt-6'>
						<Button
							type='button'
							variant='outline'
							onClick={() => router.back()}
							disabled={form.formState.isSubmitting || isLoading}>
							Cancel
						</Button>
						<Button
							type='submit'
							disabled={form.formState.isSubmitting || isLoading}>
							{form.formState.isSubmitting || isLoading
								? 'Processing...'
								: submitButtonText}
						</Button>
					</CardFooter>
				</Card>
			</form>
		</Form>
	);
};

export default EmployeeForm;
