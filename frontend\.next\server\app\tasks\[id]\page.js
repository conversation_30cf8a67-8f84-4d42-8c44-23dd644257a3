(()=>{var e={};e.id=6043,e.ids=[6043],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8105:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=a(65239),r=a(48088),l=a(88170),i=a.n(l),d=a(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);a.d(s,n);let c={children:["",{children:["tasks",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,94663)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\[id]\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/tasks/[id]/page",pathname:"/tasks/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15079:(e,s,a)=>{"use strict";a.d(s,{bq:()=>x,eb:()=>g,gC:()=>h,l6:()=>o,yv:()=>m});var t=a(60687),r=a(43210),l=a(22670),i=a(61662),d=a(89743),n=a(58450),c=a(4780);let o=l.bL;l.YJ;let m=l.WT,x=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(l.l9,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=l.l9.displayName;let p=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.PP,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(d.A,{className:"h-4 w-4"})}));p.displayName=l.PP.displayName;let u=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.wn,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=l.wn.displayName;let h=r.forwardRef(({className:e,children:s,position:a="popper",...r},i)=>(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:i,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,t.jsx)(p,{}),(0,t.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,t.jsx)(u,{})]})}));h.displayName=l.UC.displayName,r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.JU,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.JU.displayName;let g=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(l.q7,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(n.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:s})]}));g.displayName=l.q7.displayName,r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.wv,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29333:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(82614).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},32046:(e,s,a)=>{Promise.resolve().then(a.bind(a,61779))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35950:(e,s,a)=>{"use strict";a.d(s,{w:()=>d});var t=a(60687),r=a(43210),l=a(62369),i=a(4780);let d=r.forwardRef(({className:e,orientation:s="horizontal",decorative:a=!0,...r},d)=>(0,t.jsx)(l.b,{ref:d,decorative:a,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));d.displayName=l.b.displayName},48041:(e,s,a)=>{"use strict";a.d(s,{z:()=>r});var t=a(60687);function r({title:e,description:s,icon:a,children:r}){return(0,t.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[a&&(0,t.jsx)(a,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),s&&(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:s})]}),r&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:r})]})}a(43210)},52027:(e,s,a)=>{"use strict";a.d(s,{gO:()=>h,jt:()=>p});var t=a(60687);a(43210);var r=a(11516),l=a(72963),i=a(4780),d=a(85726),n=a(91821),c=a(68752);let o={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},m={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function x({size:e="md",className:s,text:a,fullPage:l=!1}){return(0,t.jsx)("div",{className:(0,i.cn)("flex items-center justify-center",l&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(r.A,{className:(0,i.cn)("animate-spin text-primary",o[e])}),a&&(0,t.jsx)("span",{className:(0,i.cn)("mt-2 text-muted-foreground",m[e]),children:a})]})})}function p({variant:e="default",count:s=1,className:a,testId:r="loading-skeleton"}){return"card"===e?(0,t.jsx)("div",{className:(0,i.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",a),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,t.jsx)(d.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(d.E,{className:"h-7 w-3/4 mb-1"}),(0,t.jsx)(d.E,{className:"h-4 w-1/2 mb-3"}),(0,t.jsx)(d.E,{className:"h-px w-full my-3"}),(0,t.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,t.jsx)(d.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===e?(0,t.jsxs)("div",{className:(0,i.cn)("space-y-3",a),"data-testid":r,children:[(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,t.jsx)(d.E,{className:"h-8 flex-1"},s))}),Array(s).fill(0).map((e,s)=>(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,t.jsx)(d.E,{className:"h-6 flex-1"},s))},s))]}):"list"===e?(0,t.jsx)("div",{className:(0,i.cn)("space-y-3",a),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(d.E,{className:"h-12 w-12 rounded-full"}),(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsx)(d.E,{className:"h-4 w-1/3"}),(0,t.jsx)(d.E,{className:"h-4 w-full"})]})]},s))}):"stats"===e?(0,t.jsx)("div",{className:(0,i.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",a),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(d.E,{className:"h-5 w-1/3"}),(0,t.jsx)(d.E,{className:"h-5 w-5 rounded-full"})]}),(0,t.jsx)(d.E,{className:"h-8 w-1/2 mt-3"}),(0,t.jsx)(d.E,{className:"h-4 w-2/3 mt-2"})]},s))}):(0,t.jsx)("div",{className:(0,i.cn)("space-y-2",a),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,t.jsx)(d.E,{className:"w-full h-5"},s))})}function u({message:e,onRetry:s,className:a}){return(0,t.jsxs)(n.Fc,{variant:"destructive",className:(0,i.cn)("my-4",a),children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)(n.XL,{children:"Error"}),(0,t.jsx)(n.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:e}),s&&(0,t.jsx)(c.r,{actionType:"tertiary",size:"sm",onClick:s,icon:(0,t.jsx)(r.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function h({isLoading:e,error:s,data:a,onRetry:r,children:l,loadingComponent:d,errorComponent:n,emptyComponent:c,className:o}){return e?d||(0,t.jsx)(x,{className:o,text:"Loading..."}):s?n||(0,t.jsx)(u,{message:s,onRetry:r,className:o}):!a||Array.isArray(a)&&0===a.length?c||(0,t.jsx)("div",{className:(0,i.cn)("text-center py-8",o),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:o,children:l(a)})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61779:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>H});var t=a(60687),r=a(43210),l=a(16189),i=a(85814),d=a.n(i),n=a(44493),c=a(35950),o=a(32584),m=a(29523),x=a(48206),p=a(82614);let u=(0,p.A)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var h=a(80489),g=a(14975),f=a(55817),j=a(35137),y=a(57207),N=a(26398),v=a(92876),b=a(15036),k=a(29333),w=a(24920),A=a(60368);let C=(0,p.A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),E=(0,p.A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),T=(0,p.A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var S=a(28840),q=a(48041),P=a(29867),z=a(96834),R=a(4780),$=a(76869),_=a(58261),M=a(93500),L=a(15079),U=a(80013),D=a(52027);let F=e=>{switch(e){case"Pending":return"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30";case"Assigned":return"bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30";case"In Progress":return"bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800/30";case"Completed":return"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30";case"Cancelled":return"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30";default:return"bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30"}},B=e=>{switch(e){case"Low":return"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30";case"Medium":return"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30";case"High":return"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30";default:return"bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30"}},Z=(e,s=!1)=>{if(!e)return"N/A";try{return(0,$.GP)((0,_.H)(e),s?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return"Invalid Date"}};function I({icon:e,label:s,value:a,children:r,className:l}){return a||r?(0,t.jsxs)("div",{className:(0,R.cn)("flex items-start gap-3",l),children:[(0,t.jsx)(e,{className:"h-4 w-4 text-muted-foreground mt-1 shrink-0"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:s}),a&&(0,t.jsx)("p",{className:"text-base font-medium text-foreground",children:a}),r]})]}):null}function G({assignedEmployees:e,onUnassign:s,isTaskCompleted:a=!1}){return 0===e.length?null:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Assigned To"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-3",children:e.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 rounded-lg border bg-muted/30",children:[(0,t.jsx)(o.eu,{className:"h-8 w-8",children:(0,t.jsx)(o.q5,{className:"text-xs font-medium",children:e.fullName.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)(d(),{href:`/employees/${e.id}`,className:"text-sm font-medium hover:text-primary",children:e.fullName}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:e.role.replace("_"," ")})]}),!a&&s&&(0,t.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>s(e.id),className:"h-6 w-6 p-0 text-muted-foreground hover:text-destructive",children:(0,t.jsx)(u,{className:"h-3 w-3"})})]},e.id))})]})}function H(){let e=(0,l.useParams)(),s=(0,l.useRouter)(),{toast:a}=(0,P.dj)(),[i,p]=(0,r.useState)(null),[$,_]=(0,r.useState)([]),[H,J]=(0,r.useState)(void 0),[V,O]=(0,r.useState)([]),[W,X]=(0,r.useState)(""),[Y,K]=(0,r.useState)(!0),[Q,ee]=(0,r.useState)(null),es=e.id,ea=(0,r.useCallback)(async()=>{if(es){K(!0),ee(null);try{let e=await (0,S.getTaskById)(es);if(e){if(p(e),e.assignedTo&&e.assignedTo.length>0){let s=await Promise.all(e.assignedTo.map(e=>(0,S.getEmployeeById)(Number(e))));_(s.filter(e=>void 0!==e))}else _([]);if(e.vehicleId){let s=await (0,S.getVehicleById)(Number(e.vehicleId));J(s)}else J(null);let s=await (0,S.getEmployees)();O(s.filter(s=>"Active"===s.status&&!e.assignedTo?.includes(String(s.id))))}else ee("Task not found."),a({title:"Error",description:"Task not found.",variant:"destructive"})}catch(s){console.error("Error fetching task data:",s);let e=s instanceof Error?s.message:"Failed to load task data.";ee(e),a({title:"Error",description:e,variant:"destructive"})}finally{K(!1)}}},[es,a]),et=async()=>{if(i)try{await (0,S.deleteTask)(i.id),a({title:"Task Deleted",description:`Task "${i.description.substring(0,30)}..." has been deleted.`}),s.push("/tasks")}catch(e){console.error("Error deleting task:",e),a({title:"Error",description:"Failed to delete task. Please try again.",variant:"destructive"})}},er=async()=>{if(i&&W)try{let e=await (0,S.assignTaskToEmployee)(i.id,W);e.task&&e.employee?(a({title:"Task Assigned",description:`Task assigned to employee ${e.employee.fullName}.`}),await ea(),X("")):a({title:"Error",description:"Failed to assign task.",variant:"destructive"})}catch(e){console.error("Error assigning task:",e),a({title:"Error",description:"Failed to assign task. Please try again.",variant:"destructive"})}},el=async e=>{if(i)try{let s=await (0,S.unassignTaskFromEmployee)(i.id,e?String(e):void 0);s?.task?(a({title:"Task Unassigned",description:e?"Employee unassigned from task.":"All employees unassigned from task."}),await ea()):a({title:"Error",description:"Failed to unassign task.",variant:"destructive"})}catch(e){console.error("Error unassigning task:",e),a({title:"Error",description:"Failed to unassign task. Please try again.",variant:"destructive"})}},ei=i?.status==="Completed"||i?.status==="Cancelled";return(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)(D.gO,{isLoading:Y,error:Q,data:i,onRetry:ea,loadingComponent:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(q.z,{title:"Loading Task...",icon:h.A}),(0,t.jsx)(D.jt,{variant:"card",count:1}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,t.jsx)(D.jt,{variant:"card",count:1,className:"lg:col-span-2"}),(0,t.jsx)(D.jt,{variant:"card",count:1})]})]}),emptyComponent:(0,t.jsxs)("div",{className:"text-center py-10",children:[(0,t.jsx)(q.z,{title:"Task Not Found",icon:g.A}),(0,t.jsx)("p",{className:"mb-4",children:"The requested task could not be found."}),(0,t.jsxs)(m.$,{onClick:()=>s.push("/tasks"),variant:"outline",className:"gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),"Back to Tasks"]})]}),children:e=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(q.z,{title:e.description,icon:h.A,description:"Manage details and assignment for this task.",children:(0,t.jsxs)("div",{className:"flex gap-2 items-center flex-wrap",children:[(0,t.jsxs)(m.$,{variant:"outline",onClick:()=>s.push("/tasks"),className:"gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),"Back to List"]}),(0,t.jsx)(m.$,{variant:"default",asChild:!0,className:"gap-2",children:(0,t.jsxs)(d(),{href:`/tasks/${e.id}/edit`,children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),"Edit"]})}),(0,t.jsxs)(M.Lt,{children:[(0,t.jsx)(M.tv,{asChild:!0,children:(0,t.jsxs)(m.$,{variant:"destructive",className:"gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),"Delete Task"]})}),(0,t.jsxs)(M.EO,{children:[(0,t.jsxs)(M.wd,{children:[(0,t.jsx)(M.r7,{children:"Are you sure?"}),(0,t.jsx)(M.$v,{children:"This action cannot be undone. This will permanently delete the task."})]}),(0,t.jsxs)(M.ck,{children:[(0,t.jsx)(M.Zr,{children:"Cancel"}),(0,t.jsx)(M.Rx,{onClick:et,className:"bg-destructive hover:bg-destructive/90",children:"Delete"})]})]})]})]})}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(n.Zp,{className:"lg:col-span-2 shadow-sm",children:[(0,t.jsx)(n.aR,{className:"border-b",children:(0,t.jsxs)("div",{className:"flex justify-between items-start gap-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(n.ZB,{className:"text-2xl font-bold leading-tight",children:e.description}),(0,t.jsx)(n.BT,{children:"Detailed overview of the task"})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,t.jsx)(z.E,{className:(0,R.cn)("justify-center",F(e.status)),children:e.status}),(0,t.jsxs)(z.E,{variant:"outline",className:(0,R.cn)("justify-center",B(e.priority)),children:[e.priority," Priority"]})]})]})}),(0,t.jsxs)(n.Wu,{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsx)(I,{icon:N.A,label:"Location",value:e.location}),(0,t.jsx)(I,{icon:v.A,label:"Start Date & Time",value:Z(e.dateTime,!0)}),e.deadline&&(0,t.jsx)(I,{icon:b.A,label:"Deadline",value:Z(e.deadline,!0)}),(0,t.jsx)(I,{icon:b.A,label:"Estimated Duration",value:`${e.estimatedDuration} minutes`})]}),e.requiredSkills&&e.requiredSkills.length>0&&(0,t.jsx)(I,{icon:k.A,label:"Required Skills",children:(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.requiredSkills.map(e=>(0,t.jsx)(z.E,{variant:"secondary",className:"text-xs",children:e},e))})}),H&&(0,t.jsx)(I,{icon:w.A,label:"Associated Vehicle",children:(0,t.jsxs)(d(),{href:`/vehicles/${H.id}`,className:"text-primary hover:underline flex items-center gap-1",children:[H.make," ",H.model," (",H.year,")",(0,t.jsx)(A.A,{className:"h-3 w-3"})]})}),$.length>0&&(0,t.jsx)(G,{assignedEmployees:$,onUnassign:el,isTaskCompleted:ei}),e.notes&&(0,t.jsx)(I,{icon:h.A,label:"Notes",className:"md:col-span-2",children:(0,t.jsx)("p",{className:"text-base font-medium whitespace-pre-wrap",children:e.notes})}),e.subTasks&&e.subTasks.length>0&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(c.w,{}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),"Sub-Tasks"]}),(0,t.jsx)("ul",{className:"space-y-2",children:e.subTasks.map(e=>(0,t.jsxs)("li",{className:(0,R.cn)("flex items-center gap-2 text-sm",e.completed&&"line-through text-muted-foreground"),children:[e.completed?(0,t.jsx)(C,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)("div",{className:"h-4 w-4 border rounded-sm"}),e.title]},e.id))})]})]})]}),(0,t.jsxs)(n.wL,{className:"border-t text-xs text-muted-foreground",children:["Created: ",Z(e.createdAt,!0)," | Updated:"," ",Z(e.updatedAt,!0)]})]}),(0,t.jsxs)(n.Zp,{className:"shadow-sm h-fit",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-primary"}),"Task Assignment"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[$.length>0?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Currently assigned to ",$.length," ","employee(s):"]}),$.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border bg-muted/30",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.eu,{className:"h-8 w-8",children:(0,t.jsx)(o.q5,{className:"text-xs",children:e.fullName.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d(),{href:`/employees/${e.id}`,className:"font-medium hover:text-primary text-sm",children:e.fullName}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground capitalize",children:e.role.replace("_"," ")})]})]}),!ei&&(0,t.jsxs)(m.$,{variant:"ghost",size:"sm",onClick:()=>el(e.id),className:"gap-1",children:[(0,t.jsx)(u,{className:"h-4 w-4"}),"Unassign"]})]},e.id))]}),!ei&&$.length>1&&(0,t.jsxs)(m.$,{variant:"outline",onClick:()=>el(),className:"w-full gap-2",children:[(0,t.jsx)(u,{className:"h-4 w-4"}),"Unassign All"]})]}):!ei&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"This task is currently unassigned."}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(U.J,{htmlFor:"assignee-select",className:"text-sm font-medium",children:"Select Employee to Assign"}),(0,t.jsxs)(L.l6,{value:W,onValueChange:X,children:[(0,t.jsx)(L.bq,{children:(0,t.jsx)(L.yv,{placeholder:"Select an employee"})}),(0,t.jsx)(L.gC,{children:V.length>0?V.map(e=>(0,t.jsxs)(L.eb,{value:String(e.id),children:[e.fullName," (",e.role.charAt(0).toUpperCase()+e.role.slice(1).replace("_"," "),")"]},e.id)):(0,t.jsx)("div",{className:"p-2 text-sm text-muted-foreground",children:"No available employees."})})]}),(0,t.jsxs)(m.$,{onClick:er,disabled:!W,className:"w-full gap-2",children:[(0,t.jsx)(E,{className:"h-4 w-4"}),"Assign to Selected Employee"]})]})]}),"Completed"===e.status&&(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800",children:[(0,t.jsx)(C,{className:"h-4 w-4 text-green-600"}),(0,t.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-400",children:["Task completed",$.length>0?` by ${$.map(e=>e.fullName).join(", ")}`:"","."]})]}),"Cancelled"===e.status&&(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800",children:[(0,t.jsx)(T,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:"Task cancelled."})]})]})]})]})]})})})}},62310:(e,s,a)=>{Promise.resolve().then(a.bind(a,94663))},62369:(e,s,a)=>{"use strict";a.d(s,{b:()=>c});var t=a(43210),r=a(14163),l=a(60687),i="horizontal",d=["horizontal","vertical"],n=t.forwardRef((e,s)=>{var a;let{decorative:t,orientation:n=i,...c}=e,o=(a=n,d.includes(a))?n:i;return(0,l.jsx)(r.sG.div,{"data-orientation":o,...t?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:s})});n.displayName="Separator";var c=n},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68752:(e,s,a)=>{"use strict";a.d(s,{r:()=>c});var t=a(60687),r=a(43210),l=a.n(r),i=a(29523),d=a(11516),n=a(4780);let c=l().forwardRef(({actionType:e="primary",icon:s,isLoading:a=!1,loadingText:r,className:l,children:c,disabled:o,asChild:m=!1,...x},p)=>{let{variant:u,className:h}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[e];return(0,t.jsx)(i.$,{ref:p,variant:u,className:(0,n.cn)(h,l),disabled:a||o,asChild:m,...x,children:a?(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),r||c]}):(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",s&&(0,t.jsx)("span",{className:"mr-2",children:s}),c]})})});c.displayName="ActionButton"},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85726:(e,s,a)=>{"use strict";a.d(s,{E:()=>l});var t=a(60687),r=a(4780);function l({className:e,...s}){return(0,t.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",e),...s})}},91645:e=>{"use strict";e.exports=require("net")},93500:(e,s,a)=>{"use strict";a.d(s,{$v:()=>g,EO:()=>x,Lt:()=>n,Rx:()=>f,Zr:()=>j,ck:()=>u,r7:()=>h,tv:()=>c,wd:()=>p});var t=a(60687),r=a(43210),l=a(97895),i=a(4780),d=a(29523);let n=l.bL,c=l.l9,o=l.ZL,m=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.hJ,{className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:a}));m.displayName=l.hJ.displayName;let x=r.forwardRef(({className:e,...s},a)=>(0,t.jsxs)(o,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(l.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s})]}));x.displayName=l.UC.displayName;let p=({className:e,...s})=>(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...s});p.displayName="AlertDialogHeader";let u=({className:e,...s})=>(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});u.displayName="AlertDialogFooter";let h=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold",e),...s}));h.displayName=l.hE.displayName;let g=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));g.displayName=l.VY.displayName;let f=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.rc,{ref:a,className:(0,i.cn)((0,d.r)(),e),...s}));f.displayName=l.rc.displayName;let j=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.ZD,{ref:a,className:(0,i.cn)((0,d.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...s}));j.displayName=l.ZD.displayName},94663:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\tasks\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\[id]\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4447,3744,1658,5880,2729,424,8141,3983],()=>a(8105));module.exports=t})();