-- Migration: Create Auth Helper Functions for RBAC
-- This creates the missing auth helper functions referenced in RLS policies

-- Function to get user role from user_profiles table
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM public.user_profiles
    WHERE id = user_id;
    
    RETURN COALESCE(user_role, 'USER');
END;
$$;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM public.user_profiles
    WHERE id = user_id;
    
    RETURN user_role IN ('ADMIN', 'SUPER_ADMIN');
END;
$$;

-- Function to check if user is manager or above
CREATE OR REPLACE FUNCTION public.is_manager_or_above(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM public.user_profiles
    WHERE id = user_id;
    
    RETURN user_role IN ('MANAGER', 'ADMIN', 'SUPER_ADMIN');
END;
$$;

-- Function to get user's employee ID
CREATE OR REPLACE FUNCTION public.get_user_employee_id(user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    emp_id INTEGER;
BEGIN
    SELECT employee_id INTO emp_id
    FROM public.user_profiles
    WHERE id = user_id;
    
    RETURN emp_id;
END;
$$;

-- Create the custom access token hook function (if it doesn't exist)
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event JSONB)
RETURNS JSONB
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    claims JSONB;
    user_role TEXT;
    is_active BOOLEAN;
    employee_id INTEGER;
BEGIN
    -- Get the claims from the event
    claims := event->'claims';

    -- Fetch user role, status, and employee_id from user_profiles
    SELECT role, is_active, employee_id 
    INTO user_role, is_active, employee_id
    FROM public.user_profiles 
    WHERE id = (event->>'user_id')::UUID;

    -- Set custom claims in the JWT
    IF user_role IS NOT NULL THEN
        claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
            'user_role', user_role,
            'is_active', COALESCE(is_active, true),
            'employee_id', employee_id
        ));
    ELSE
        -- Default claims for users without a profile
        claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
            'user_role', 'USER',
            'is_active', true,
            'employee_id', null
        ));
    END IF;

    -- Update the event with the new claims
    event := jsonb_set(event, '{claims}', claims);
    
    RETURN event;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.get_user_role(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_manager_or_above(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_employee_id(UUID) TO authenticated;

GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO service_role;

-- Create a trigger function to automatically create user profiles for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Insert a new user profile with default role
    INSERT INTO public.user_profiles (id, role, is_active, created_at, updated_at)
    VALUES (NEW.id, 'USER', true, NOW(), NOW())
    ON CONFLICT (id) DO NOTHING;
    
    RETURN NEW;
END;
$$;

-- Grant permissions for the trigger function
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO supabase_auth_admin;
