'use client';

import {Settings, Info} from 'lucide-react';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {Alert, AlertDescription, AlertTitle} from '@/components/ui/alert';
import {TabsContent} from '@/components/ui/tabs';

export default function AdminPage() {
	return (
		<div className='space-y-6'>
			<div className='flex items-center space-x-2 mb-6'>
				<Settings className='h-8 w-8 text-primary' />
				<div>
					<h1 className='text-3xl font-bold text-primary'>Admin Dashboard</h1>
					<p className='text-muted-foreground'>
						System administration and diagnostics
					</p>
				</div>
			</div>

			<Alert>
				<Info className='h-4 w-4' />
				<AlertTitle>Information</AlertTitle>
				<AlertDescription>
					This admin dashboard provides system diagnostics and monitoring tools.
					No authentication is required for demonstration purposes.
				</AlertDescription>
			</Alert>

			<Card className='shadow-md'>
				<CardHeader className='p-5'>
					<CardTitle className='text-xl font-semibold text-primary'>
						System Status
					</CardTitle>
					<CardDescription>
						Overview of system components and services
					</CardDescription>
				</CardHeader>
				<CardContent className='p-5'>
					<div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
						<Card className='shadow-sm'>
							<CardHeader className='pb-2 p-4'>
								<CardTitle className='text-base font-semibold'>
									Backend API
								</CardTitle>
								<CardDescription className='text-xs'>
									Node.js API Server
								</CardDescription>
							</CardHeader>
							<CardContent className='p-4 pt-0'>
								<div className='text-xl font-bold text-green-500'>Online</div>
								<p className='text-xs text-muted-foreground'>Version: 1.0.0</p>
							</CardContent>
						</Card>

						<Card className='shadow-sm'>
							<CardHeader className='pb-2 p-4'>
								<CardTitle className='text-base font-semibold'>
									Frontend
								</CardTitle>
								<CardDescription className='text-xs'>
									Next.js Application
								</CardDescription>
							</CardHeader>
							<CardContent className='p-4 pt-0'>
								<div className='text-xl font-bold text-green-500'>Online</div>
								<p className='text-xs text-muted-foreground'>Version: 1.0.0</p>
							</CardContent>
						</Card>

						<Card className='shadow-sm'>
							<CardHeader className='pb-2 p-4'>
								<CardTitle className='text-base font-semibold'>
									Database
								</CardTitle>
								<CardDescription className='text-xs'>
									Supabase PostgreSQL
								</CardDescription>
							</CardHeader>
							<CardContent className='p-4 pt-0'>
								<div className='text-xl font-bold text-green-500'>
									Connected
								</div>
								<p className='text-xs text-muted-foreground'>
									Phase 1 Security Active
								</p>
							</CardContent>
						</Card>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
