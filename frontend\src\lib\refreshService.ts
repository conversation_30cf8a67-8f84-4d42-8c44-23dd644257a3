/**
 * RefreshService
 * Manages automatic data refreshing via configurable polling intervals
 */

type RefreshCallback = () => Promise<void>;
type ErrorHandler = (error: unknown) => void;

interface RefreshOptions {
	interval?: number; // Polling interval in milliseconds
	enabled?: boolean; // Whether polling is initially enabled
	errorHandler?: ErrorHandler; // Custom error handler
	immediate?: boolean; // Whether to refresh immediately on start
	retryLimit?: number; // Maximum number of consecutive failures before pausing
	retryDelay?: number; // Base delay between retries (will be multiplied by attempt count)
}

const DEFAULT_OPTIONS: Required<RefreshOptions> = {
	interval: 30000, // Default: 30 seconds
	enabled: true,
	errorHandler: (error) => console.error('Refresh error:', error),
	immediate: false,
	retryLimit: 3,
	retryDelay: 2000,
};

export class RefreshService {
	private timerId: number | null = null;
	private options: Required<RefreshOptions>;
	private callback: RefreshCallback;
	private isRefreshing = false;
	private consecutiveFailures = 0;
	private lastSuccessTime = 0;
	private isVisible = true;
	private isOnline = true;

	constructor(callback: RefreshCallback, options: RefreshOptions = {}) {
		this.callback = callback;
		this.options = {...DEFAULT_OPTIONS, ...options};

		// Setup visibility and online status listeners
		this.setupBrowserEventListeners();

		if (this.options.enabled && this.options.immediate) {
			this.refresh();
		} else if (this.options.enabled) {
			this.start();
		}
	}

	/**
	 * Start the polling refresh service
	 */
	public start(): void {
		if (this.timerId !== null) return;

		this.timerId = window.setTimeout(
			() => this.refreshCycle(),
			this.options.interval
		);
	}

	/**
	 * Stop the polling refresh service
	 */
	public stop(): void {
		if (this.timerId !== null) {
			window.clearTimeout(this.timerId);
			this.timerId = null;
		}
	}

	/**
	 * Manually trigger a refresh
	 */
	public async refresh(): Promise<void> {
		if (this.isRefreshing) return;

		this.isRefreshing = true;
		try {
			await this.callback();
			this.consecutiveFailures = 0;
			this.lastSuccessTime = Date.now();
		} catch (error) {
			this.consecutiveFailures++;
			this.options.errorHandler(error);
		} finally {
			this.isRefreshing = false;
		}
	}

	/**
	 * Update service options
	 */
	public updateOptions(options: Partial<RefreshOptions>): void {
		const wasEnabled = this.options.enabled;
		this.options = {...this.options, ...options};

		// Restart if interval changed or if enabling
		if (this.timerId !== null) {
			this.stop();
		}

		if (this.options.enabled && (!wasEnabled || options.interval)) {
			this.start();
		}
	}

	/**
	 * Core refresh cycle with retry logic
	 */
	private async refreshCycle(): Promise<void> {
		// Skip refresh if offline or not visible and the page doesn't have focus
		if (!this.isOnline || !this.isVisible) {
			this.reschedule();
			return;
		}

		// Skip if already refreshing
		if (this.isRefreshing) {
			this.reschedule();
			return;
		}

		// Check if we've hit retry limit
		if (this.consecutiveFailures >= this.options.retryLimit) {
			// Implement exponential backoff
			const backoffDelay =
				this.options.retryDelay * Math.pow(2, this.consecutiveFailures - 1);
			const delayTime = Math.min(backoffDelay, 60000); // Cap at 1 minute

			this.timerId = window.setTimeout(() => this.refreshCycle(), delayTime);
			return;
		}

		await this.refresh();
		this.reschedule();
	}

	/**
	 * Schedule the next refresh
	 */
	private reschedule(): void {
		if (!this.options.enabled) return;

		this.timerId = window.setTimeout(
			() => this.refreshCycle(),
			this.options.interval
		);
	}

	/**
	 * Setup browser event listeners for document visibility and online status
	 */
	private setupBrowserEventListeners(): void {
		if (typeof window === 'undefined') return;

		// Track document visibility
		document.addEventListener('visibilitychange', () => {
			this.isVisible = document.visibilityState === 'visible';

			// If becoming visible, trigger an immediate refresh
			if (
				this.isVisible &&
				this.options.enabled &&
				Date.now() - this.lastSuccessTime > this.options.interval
			) {
				this.stop();
				this.refresh();
				this.start();
			}
		});

		// Track online status
		window.addEventListener('online', () => {
			this.isOnline = true;
			if (this.options.enabled) {
				this.stop();
				this.refresh();
				this.start();
			}
		});

		window.addEventListener('offline', () => {
			this.isOnline = false;
		});

		// Initial values
		this.isOnline = navigator.onLine;
		this.isVisible = document.visibilityState === 'visible';
	}

	/**
	 * Clean up resources
	 */
	public dispose(): void {
		this.stop();

		// Remove event listeners if needed
		// (usually not necessary as browser will clean up automatically)
	}
}

/**
 * Create a refresh service with the given callback and options
 */
export const createRefreshService = (
	callback: RefreshCallback,
	options: RefreshOptions = {}
): RefreshService => {
	return new RefreshService(callback, options);
};
