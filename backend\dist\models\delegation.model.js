import prisma from './index.js';
import { DelegationStatus as PrismaDelegationStatus, } from '../generated/prisma/index.js';
import { PrismaClientKnownRequestError } from '../generated/prisma/runtime/library.js';
export const createDelegation = async (data) => {
    try {
        return await prisma.delegation.create({
            data: {
                ...data,
                status: data.status
                    ? data.status
                    : PrismaDelegationStatus.Planned,
            },
            include: {
                delegates: true,
                flightArrivalDetails: true, // Relation name
                flightDepartureDetails: true, // Relation name
                statusHistory: { orderBy: { changedAt: 'desc' } },
            },
        });
    }
    catch (error) {
        console.error('Error creating delegation:', error);
        return null;
    }
};
export const getAllDelegations = async () => {
    try {
        return await prisma.delegation.findMany({
            include: {
                delegates: true,
                flightArrivalDetails: true, // Relation name
                flightDepartureDetails: true, // Relation name
                statusHistory: { orderBy: { changedAt: 'desc' } },
            },
            orderBy: { durationFrom: 'desc' },
        });
    }
    catch (error) {
        console.error('Error fetching all delegations:', error);
        return [];
    }
};
export const getDelegationById = async (id) => {
    try {
        return await prisma.delegation.findUnique({
            where: { id },
            include: {
                delegates: true,
                flightArrivalDetails: true, // Relation name
                flightDepartureDetails: true, // Relation name
                statusHistory: { orderBy: { changedAt: 'desc' } },
            },
        });
    }
    catch (error) {
        console.error(`Error fetching delegation with ID ${id}:`, error);
        return null;
    }
};
export const updateDelegation = async (id, data) => {
    try {
        const { delegates: delegatesData, flightArrivalDetails, flightDepartureDetails, statusChangeReason, status: newStatusInput, ...restOfData } = data;
        const newStatus = newStatusInput;
        const prismaDelegationUpdateData = {
            ...restOfData,
        };
        if (newStatus !== undefined) {
            prismaDelegationUpdateData.status = { set: newStatus };
        }
        return await prisma.$transaction(async (tx) => {
            if (newStatus && statusChangeReason !== undefined) {
                const currentDelegation = await tx.delegation.findUnique({
                    where: { id },
                    select: { status: true },
                });
                if (currentDelegation && newStatus !== currentDelegation.status) {
                    await tx.delegationStatusEntry.create({
                        data: {
                            delegationId: id,
                            status: newStatus,
                            reason: statusChangeReason,
                        },
                    });
                }
            }
            // Handle flightArrivalDetails
            if (data.hasOwnProperty('flightArrivalDetails')) {
                const currentDelegationForFlight = await tx.delegation.findUnique({
                    where: { id },
                    select: { flightArrivalId: true },
                });
                if (flightArrivalDetails === null) {
                    // Disconnect/delete
                    if (currentDelegationForFlight?.flightArrivalId) {
                        // First, delete the FlightDetails record if it exists and should be orphaned
                        await tx.flightDetails.delete({
                            where: { id: currentDelegationForFlight.flightArrivalId },
                        });
                        // Then, update the delegation to remove the link
                        prismaDelegationUpdateData.flightArrivalDetails = {
                            disconnect: true,
                        };
                    }
                }
                else if (flightArrivalDetails) {
                    // Create or Update
                    if (currentDelegationForFlight?.flightArrivalId) {
                        // Update existing
                        await tx.flightDetails.update({
                            where: { id: currentDelegationForFlight.flightArrivalId },
                            data: flightArrivalDetails,
                        });
                        // No change to prismaDelegationUpdateData.flightArrival as the relation is already there
                    }
                    else {
                        // Create new and connect
                        const newFlight = await tx.flightDetails.create({
                            data: flightArrivalDetails,
                        });
                        prismaDelegationUpdateData.flightArrivalDetails = {
                            connect: { id: newFlight.id },
                        };
                    }
                }
            }
            // Handle flightDepartureDetails
            if (data.hasOwnProperty('flightDepartureDetails')) {
                const currentDelegationForFlight = await tx.delegation.findUnique({
                    where: { id },
                    select: { flightDepartureId: true },
                });
                if (flightDepartureDetails === null) {
                    if (currentDelegationForFlight?.flightDepartureId) {
                        await tx.flightDetails.delete({
                            where: { id: currentDelegationForFlight.flightDepartureId },
                        });
                        prismaDelegationUpdateData.flightDepartureDetails = {
                            disconnect: true,
                        };
                    }
                }
                else if (flightDepartureDetails) {
                    if (currentDelegationForFlight?.flightDepartureId) {
                        await tx.flightDetails.update({
                            where: { id: currentDelegationForFlight.flightDepartureId },
                            data: flightDepartureDetails,
                        });
                    }
                    else {
                        const newFlight = await tx.flightDetails.create({
                            data: flightDepartureDetails,
                        });
                        prismaDelegationUpdateData.flightDepartureDetails = {
                            connect: { id: newFlight.id },
                        };
                    }
                }
            }
            // Update the delegation itself (ensure this happens after FKs might be set for connect)
            await tx.delegation.update({
                where: { id },
                data: prismaDelegationUpdateData,
            });
            if (delegatesData && Array.isArray(delegatesData)) {
                await tx.delegate.deleteMany({ where: { delegationId: id } });
                if (delegatesData.length > 0) {
                    await tx.delegate.createMany({
                        data: delegatesData.map((d) => ({ ...d, delegationId: id })),
                    });
                }
            }
            return tx.delegation.findUnique({
                where: { id },
                include: {
                    delegates: true,
                    flightArrivalDetails: true,
                    flightDepartureDetails: true,
                    statusHistory: { orderBy: { changedAt: 'desc' } },
                },
            });
        });
    }
    catch (error) {
        console.error(`Error updating delegation with ID ${id}:`, error);
        if (error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2025') {
            return null;
        }
        return null;
    }
};
export const deleteDelegation = async (id) => {
    try {
        return await prisma.$transaction(async (tx) => {
            const delegationToDelete = await tx.delegation.findUnique({
                where: { id },
                include: {
                    delegates: true,
                    flightArrivalDetails: true,
                    flightDepartureDetails: true,
                    statusHistory: true,
                },
            });
            if (!delegationToDelete) {
                return null;
            }
            await tx.delegate.deleteMany({ where: { delegationId: id } });
            await tx.delegationStatusEntry.deleteMany({ where: { delegationId: id } });
            await tx.delegation.delete({ where: { id } });
            return delegationToDelete;
        });
    }
    catch (error) {
        console.error(`Error deleting delegation with ID ${id}:`, error);
        if (error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2025') {
            return null;
        }
        return null;
    }
};
//# sourceMappingURL=delegation.model.js.map