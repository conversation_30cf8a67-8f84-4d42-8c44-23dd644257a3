// PHASE 1 SECURITY HARDENING: Load environment variables FIRST
import dotenv from 'dotenv';
dotenv.config();

// PHASE 1 SECURITY HARDENING: Validate secrets immediately after env loading
import {validateSecretsOrThrow} from './config/secrets.js';

console.log('🔐 PHASE 1 SECURITY: Validating application secrets...');
try {
	validateSecretsOrThrow();
} catch (error) {
	console.error(
		'❌ CRITICAL SECURITY ERROR:',
		error instanceof Error ? error.message : String(error)
	);
	console.error(
		'🚨 Application startup aborted due to security validation failure'
	);
	process.exit(1);
}

// Continue with other imports after secrets validation
import express, {Application, Request, Response, NextFunction} from 'express';
import morgan from 'morgan';
import cors from 'cors';
import {
	prisma,
	supabase,
	testDatabaseConnections,
	getDatabaseConfig,
} from './services/database.service.js';
import {testSupabaseConnection} from './lib/supabase.js';
import {authenticateSupabaseUser} from './middleware/supabaseAuth.js';
import vehicleRoutes from './routes/vehicle.routes.js';
import employeeRoutes from './routes/employee.routes.js';
import serviceRecordRoutes from './routes/serviceRecord.routes.js';
import directServiceRecordRoutes from './routes/directServiceRecord.routes.js';
import delegationRoutes from './routes/delegation.routes.js';
import taskRoutes from './routes/task.routes.js';
import flightRoutes from './routes/flight.routes.js';
import adminRoutes from './routes/admin.routes.js';
import errorHandler from './middleware/errorHandler.js';
import {
	securityHeaders,
	additionalSecurity,
	developmentSecurity,
	productionSecurity,
} from './middleware/security.js';
import {
	globalRateLimit,
	authRateLimit,
	apiRateLimit,
	adminRateLimit,
	addRateLimitSecurityHeaders,
	getRateLimitStatus,
} from './middleware/rateLimiting.js';
import swaggerUi from 'swagger-ui-express';
import swaggerSpec from './swaggerConfig.js';
// Import the newly created reporting routes
//import {reportRoutes} from './modules/reporting/index.js';

const app: Application = express();

// SECURITY: Remove X-Powered-By header to prevent technology stack disclosure
app.disable('x-powered-by');

// Test database connections on startup
testDatabaseConnections().then((results) => {
	const config = getDatabaseConfig();
	console.log('Database Configuration:', {
		useSupabase: config.useSupabase,
		databaseUrl: config.databaseUrl.replace(/\/\/.*?@/, '//****@'), // Hide password
		supabaseConfigured: !!config.supabaseUrl && !!config.supabaseKey,
	});

	console.log('Connection Test Results:', results);
});

// EMERGENCY SECURITY: Test Supabase Authentication Setup
console.log('🚨 EMERGENCY SECURITY: Testing Supabase Authentication Setup...');
testSupabaseConnection()
	.then((success) => {
		if (success) {
			console.log(
				'✅ EMERGENCY SECURITY: Supabase Authentication client configured successfully'
			);
		} else {
			console.error(
				'❌ EMERGENCY SECURITY: Supabase Authentication client configuration failed'
			);
			console.error(
				'⚠️  WARNING: API endpoints will be unprotected until this is resolved!'
			);
		}
	})
	.catch((error) => {
		console.error(
			'❌ EMERGENCY SECURITY: Supabase Authentication test error:',
			error.message
		);
		console.error(
			'⚠️  WARNING: API endpoints will be unprotected until this is resolved!'
		);
	});

// PHASE 1 SECURITY HARDENING: Apply security headers early in middleware chain
app.use(securityHeaders);
app.use(additionalSecurity);
app.use(developmentSecurity);
app.use(productionSecurity);

// PHASE 1 SECURITY HARDENING: Apply rate limiting protection
console.log('🛡️ PHASE 1 SECURITY: Applying rate limiting protection...');
app.use(addRateLimitSecurityHeaders);
app.use(globalRateLimit);

if (process.env.NODE_ENV === 'development') {
	app.use(morgan('dev'));
}

const defaultOrigins = ['http://localhost:3000', 'http://localhost:9002'];
const corsOriginSettingArray = process.env.FRONTEND_URL
	? process.env.FRONTEND_URL.split(',').map((url) => url.trim())
	: defaultOrigins;

console.log('[Express CORS] Allowed Origins:', corsOriginSettingArray);
console.log('[Express CORS] FRONTEND_URL env var:', process.env.FRONTEND_URL);

const corsOptions: cors.CorsOptions = {
	origin: (requestOrigin, callback) => {
		console.log(`[Express CORS] Request Origin: ${requestOrigin}`);
		// Allow requests with no origin (like mobile apps or curl requests)
		if (!requestOrigin) {
			console.log('[Express CORS] No origin, allowing.');
			return callback(null, true);
		}
		if (corsOriginSettingArray.includes(requestOrigin)) {
			console.log(
				`[Express CORS] Origin ${requestOrigin} allowed, reflecting.`
			);
			callback(null, requestOrigin); // Reflect the specific origin
		} else {
			console.log(`[Express CORS] Origin ${requestOrigin} NOT allowed.`);
			callback(new Error('Not allowed by CORS for Express app'));
		}
	},
	methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
	allowedHeaders: ['Content-Type', 'Authorization'],
	credentials: true,
	maxAge: 86400,
};

app.use(cors(corsOptions));
app.use(express.json());
app.use(express.urlencoded({extended: true}));

app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Mount existing and new routes
app.use('/api/vehicles', vehicleRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/delegations', delegationRoutes);
app.use('/api/tasks', taskRoutes);
// Use the direct service record routes for the /api/servicerecords endpoint
app.use('/api/servicerecords', directServiceRecordRoutes);
// Keep the nested service record routes for /api/vehicles/:vehicleId/servicerecords
app.use('/api/vehicles/:vehicleId/servicerecords', serviceRecordRoutes);
app.use('/api/flights', flightRoutes);
// Mount admin routes with strict rate limiting
app.use('/api/admin', adminRateLimit, adminRoutes);
// Mount the new reporting routes
//app.use('/api/reports', reportRoutes);

// EMERGENCY SECURITY: Test endpoint for Supabase authentication
app.get(
	'/api/auth/test',
	authenticateSupabaseUser,
	async (req: Request, res: Response): Promise<void> => {
		if (!req.user) {
			res.status(401).json({
				error: 'User not authenticated',
				code: 'NO_USER',
			});
			return;
		}

		res.status(200).json({
			message: '✅ EMERGENCY SECURITY: Authentication working correctly',
			user: {
				id: req.user.id,
				email: req.user.email,
				email_confirmed_at: req.user.email_confirmed_at,
				last_sign_in_at: req.user.last_sign_in_at,
			},
			timestamp: new Date().toISOString(),
		});
	}
);

// PHASE 1 SECURITY HARDENING: Rate limit status endpoint
app.get('/api/rate-limit-status', getRateLimitStatus);

// Simple health check endpoint for Docker health checks (no authentication required)
app.get('/api/health', async (req: Request, res: Response): Promise<void> => {
	try {
		// Simple health check - just verify the server is responding
		res.status(200).json({
			status: 'success',
			data: {
				status: 'UP',
				message: 'Backend service is healthy',
				timestamp: new Date().toISOString(),
			},
		});
	} catch (error: any) {
		res.status(503).json({
			status: 'error',
			message: 'Backend service is unhealthy',
			error: error.message,
		});
	}
});

// Add a more detailed diagnostics endpoint for debugging connection issues
app.get('/api/diagnostics', async (req: Request, res: Response) => {
	// Get database configuration
	const config = getDatabaseConfig();

	// Test database connections
	const connectionResults = await testDatabaseConnections();

	// Prepare detailed diagnostics response
	const diagnosticsResponse = {
		timestamp: new Date().toISOString(),
		environment: {
			NODE_ENV: process.env.NODE_ENV,
			PORT: process.env.PORT,
		},
		config: {
			useSupabase: config.useSupabase,
			databaseUrl: config.databaseUrl
				? config.databaseUrl.replace(/\/\/.*?@/, '//****@')
				: 'not set',
			supabaseUrl: config.supabaseUrl || 'not set',
			supabaseKeyProvided: !!config.supabaseKey,
			connectionMode: config.databaseUrl.includes(':6543/')
				? 'transaction'
				: 'session',
		},
		connectionResults: {
			...connectionResults,
			// Remove sensitive data from the response
			details: {
				...connectionResults.details,
				supabase: {
					...connectionResults.details.supabase,
					// Remove potentially sensitive data
					data: connectionResults.details.supabase.data
						? 'Data available'
						: null,
				},
			},
		},
		system: {
			platform: process.platform,
			arch: process.arch,
			nodeVersion: process.version,
			memoryUsage: process.memoryUsage(),
			uptime: process.uptime(),
		},
	};

	res.status(200).json(diagnosticsResponse);
});

app.use(errorHandler);

export default app;
