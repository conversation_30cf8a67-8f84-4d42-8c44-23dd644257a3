"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6045],{15300:(e,t,i)=>{i.d(t,{A:()=>r});let r=(0,i(40157).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},18018:(e,t,i)=>{i.d(t,{A:()=>r});let r=(0,i(40157).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},60408:(e,t)=>{var i,r;void 0===(r="function"==typeof(i=function e(){var t,i="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==i?i:{},r=!i.document&&!!i.postMessage,n=i.IS_PAPA_WORKER||!1,s={},a=0,o={};function h(e){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},(function(e){var t=k(e);t.chunkSize=parseInt(t.chunkSize),e.step||e.chunk||(t.chunkSize=null),this._handle=new c(t),(this._handle.streamer=this)._config=t}).call(this,e),this.parseChunk=function(e,t){var r=parseInt(this._config.skipFirstNLines)||0;if(this.isFirstChunk&&0<r){let t=this._config.newline;t||(s=this._config.quoteChar||'"',t=this._handle.guessLineEndings(e,s)),e=[...e.split(t).slice(r)].join(t)}this.isFirstChunk&&b(this._config.beforeFirstChunk)&&void 0!==(s=this._config.beforeFirstChunk(e))&&(e=s),this.isFirstChunk=!1,this._halted=!1;var r=this._partialLine+e,s=(this._partialLine="",this._handle.parse(r,this._baseIndex,!this._finished));if(!this._handle.paused()&&!this._handle.aborted()){if(e=s.meta.cursor,this._finished||(this._partialLine=r.substring(e-this._baseIndex),this._baseIndex=e),s&&s.data&&(this._rowCount+=s.data.length),r=this._finished||this._config.preview&&this._rowCount>=this._config.preview,n)i.postMessage({results:s,workerId:o.WORKER_ID,finished:r});else if(b(this._config.chunk)&&!t){if(this._config.chunk(s,this._handle),this._handle.paused()||this._handle.aborted())return void(this._halted=!0);this._completeResults=s=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(s.data),this._completeResults.errors=this._completeResults.errors.concat(s.errors),this._completeResults.meta=s.meta),this._completed||!r||!b(this._config.complete)||s&&s.meta.aborted||(this._config.complete(this._completeResults,this._input),this._completed=!0),r||s&&s.meta.paused||this._nextChunk(),s}this._halted=!0},this._sendError=function(e){b(this._config.error)?this._config.error(e):n&&this._config.error&&i.postMessage({workerId:o.WORKER_ID,error:e,finished:!1})}}function u(e){var t;(e=e||{}).chunkSize||(e.chunkSize=o.RemoteChunkSize),h.call(this,e),this._nextChunk=r?function(){this._readChunk(),this._chunkLoaded()}:function(){this._readChunk()},this.stream=function(e){this._input=e,this._nextChunk()},this._readChunk=function(){if(this._finished)this._chunkLoaded();else{if(t=new XMLHttpRequest,this._config.withCredentials&&(t.withCredentials=this._config.withCredentials),r||(t.onload=v(this._chunkLoaded,this),t.onerror=v(this._chunkError,this)),t.open(this._config.downloadRequestBody?"POST":"GET",this._input,!r),this._config.downloadRequestHeaders){var e,i,n=this._config.downloadRequestHeaders;for(i in n)t.setRequestHeader(i,n[i])}this._config.chunkSize&&(e=this._start+this._config.chunkSize-1,t.setRequestHeader("Range","bytes="+this._start+"-"+e));try{t.send(this._config.downloadRequestBody)}catch(e){this._chunkError(e.message)}r&&0===t.status&&this._chunkError()}},this._chunkLoaded=function(){let e;4===t.readyState&&(t.status<200||400<=t.status?this._chunkError():(this._start+=this._config.chunkSize||t.responseText.length,this._finished=!this._config.chunkSize||this._start>=(null!==(e=(e=t).getResponseHeader("Content-Range"))?parseInt(e.substring(e.lastIndexOf("/")+1)):-1),this.parseChunk(t.responseText)))},this._chunkError=function(e){e=t.statusText||e,this._sendError(Error(e))}}function d(e){(e=e||{}).chunkSize||(e.chunkSize=o.LocalChunkSize),h.call(this,e);var t,i,r="undefined"!=typeof FileReader;this.stream=function(e){this._input=e,i=e.slice||e.webkitSlice||e.mozSlice,r?((t=new FileReader).onload=v(this._chunkLoaded,this),t.onerror=v(this._chunkError,this)):t=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var e=this._input,n=(this._config.chunkSize&&(n=Math.min(this._start+this._config.chunkSize,this._input.size),e=i.call(e,this._start,n)),t.readAsText(e,this._config.encoding));r||this._chunkLoaded({target:{result:n}})},this._chunkLoaded=function(e){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(e.target.result)},this._chunkError=function(){this._sendError(t.error)}}function l(e){var t;h.call(this,e=e||{}),this.stream=function(e){return t=e,this._nextChunk()},this._nextChunk=function(){var e,i;if(!this._finished)return t=(e=this._config.chunkSize)?(i=t.substring(0,e),t.substring(e)):(i=t,""),this._finished=!t,this.parseChunk(i)}}function f(e){h.call(this,e=e||{});var t=[],i=!0,r=!1;this.pause=function(){h.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){h.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(e){this._input=e,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){r&&1===t.length&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),t.length?this.parseChunk(t.shift()):i=!0},this._streamData=v(function(e){try{t.push("string"==typeof e?e:e.toString(this._config.encoding)),i&&(i=!1,this._checkIsFinished(),this.parseChunk(t.shift()))}catch(e){this._streamError(e)}},this),this._streamError=v(function(e){this._streamCleanUp(),this._sendError(e)},this),this._streamEnd=v(function(){this._streamCleanUp(),r=!0,this._streamData("")},this),this._streamCleanUp=v(function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)},this)}function c(e){var t,i,r,n,s=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,a=/^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,h=this,u=0,d=0,l=!1,f=!1,c=[],_={data:[],errors:[],meta:{}};function m(t){return"greedy"===e.skipEmptyLines?""===t.join("").trim():1===t.length&&0===t[0].length}function y(){if(_&&r&&(E("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+o.DefaultDelimiter+"'"),r=!1),e.skipEmptyLines&&(_.data=_.data.filter(function(e){return!m(e)})),v()){if(_)if(Array.isArray(_.data[0])){for(var t,i=0;v()&&i<_.data.length;i++)_.data[i].forEach(n);_.data.splice(0,1)}else _.data.forEach(n);function n(t,i){b(e.transformHeader)&&(t=e.transformHeader(t,i)),c.push(t)}}function h(t,i){for(var r=e.header?{}:[],n=0;n<t.length;n++){var o=n,h=t[n],h=((t,i)=>(e.dynamicTypingFunction&&void 0===e.dynamicTyping[t]&&(e.dynamicTyping[t]=e.dynamicTypingFunction(t)),!0===(e.dynamicTyping[t]||e.dynamicTyping))?"true"===i||"TRUE"===i||"false"!==i&&"FALSE"!==i&&((e=>{if(s.test(e)&&-0x20000000000000<(e=parseFloat(e))&&e<0x20000000000000)return 1})(i)?parseFloat(i):a.test(i)?new Date(i):""===i?null:i):i)(o=e.header?n>=c.length?"__parsed_extra":c[n]:o,h=e.transform?e.transform(h,o):h);"__parsed_extra"===o?(r[o]=r[o]||[],r[o].push(h)):r[o]=h}return e.header&&(n>c.length?E("FieldMismatch","TooManyFields","Too many fields: expected "+c.length+" fields but parsed "+n,d+i):n<c.length&&E("FieldMismatch","TooFewFields","Too few fields: expected "+c.length+" fields but parsed "+n,d+i)),r}_&&(e.header||e.dynamicTyping||e.transform)&&(t=1,!_.data.length||Array.isArray(_.data[0])?(_.data=_.data.map(h),t=_.data.length):_.data=h(_.data,0),e.header&&_.meta&&(_.meta.fields=c),d+=t)}function v(){return e.header&&0===c.length}function E(e,t,i,r){e={type:e,code:t,message:i},void 0!==r&&(e.row=r),_.errors.push(e)}b(e.step)&&(n=e.step,e.step=function(t){_=t,v()?y():(y(),0!==_.data.length&&(u+=t.data.length,e.preview&&u>e.preview?i.abort():(_.data=_.data[0],n(_,h))))}),this.parse=function(n,s,a){var h=e.quoteChar||'"',h=(e.newline||(e.newline=this.guessLineEndings(n,h)),r=!1,e.delimiter?b(e.delimiter)&&(e.delimiter=e.delimiter(n),_.meta.delimiter=e.delimiter):((h=((t,i,r,n,s)=>{var a,h,u,d;s=s||[",","	","|",";",o.RECORD_SEP,o.UNIT_SEP];for(var l=0;l<s.length;l++){for(var f,c=s[l],p=0,_=0,y=0,k=(u=void 0,new g({comments:n,delimiter:c,newline:i,preview:10}).parse(t)),v=0;v<k.data.length;v++)r&&m(k.data[v])?y++:(_+=f=k.data[v].length,void 0===u?u=f:0<f&&(p+=Math.abs(f-u),u=f));0<k.data.length&&(_/=k.data.length-y),(void 0===h||p<=h)&&(void 0===d||d<_)&&1.99<_&&(h=p,a=c,d=_)}return{successful:!!(e.delimiter=a),bestDelimiter:a}})(n,e.newline,e.skipEmptyLines,e.comments,e.delimitersToGuess)).successful?e.delimiter=h.bestDelimiter:(r=!0,e.delimiter=o.DefaultDelimiter),_.meta.delimiter=e.delimiter),k(e));return e.preview&&e.header&&h.preview++,t=n,_=(i=new g(h)).parse(t,s,a),y(),l?{meta:{paused:!0}}:_||{meta:{paused:!1}}},this.paused=function(){return l},this.pause=function(){l=!0,i.abort(),t=b(e.chunk)?"":t.substring(i.getCharIndex())},this.resume=function(){h.streamer._halted?(l=!1,h.streamer.parseChunk(t,!0)):setTimeout(h.resume,3)},this.aborted=function(){return f},this.abort=function(){f=!0,i.abort(),_.meta.aborted=!0,b(e.complete)&&e.complete(_),t=""},this.guessLineEndings=function(e,t){e=e.substring(0,1048576);var t=RegExp(p(t)+"([^]*?)"+p(t),"gm"),i=(e=e.replace(t,"")).split("\r"),t=e.split("\n"),e=1<t.length&&t[0].length<i[0].length;if(1===i.length||e)return"\n";for(var r=0,n=0;n<i.length;n++)"\n"===i[n][0]&&r++;return r>=i.length/2?"\r\n":"\r"}}function p(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function g(e){var t=(e=e||{}).delimiter,i=e.newline,r=e.comments,n=e.step,s=e.preview,a=e.fastMode,h=null,u=!1,d=null==e.quoteChar?'"':e.quoteChar,l=d;if(void 0!==e.escapeChar&&(l=e.escapeChar),("string"!=typeof t||-1<o.BAD_DELIMITERS.indexOf(t))&&(t=","),r===t)throw Error("Comment character same as delimiter");!0===r?r="#":("string"!=typeof r||-1<o.BAD_DELIMITERS.indexOf(r))&&(r=!1),"\n"!==i&&"\r"!==i&&"\r\n"!==i&&(i="\n");var f=0,c=!1;this.parse=function(o,g,_){if("string"!=typeof o)throw Error("Input must be a string");var m=o.length,y=t.length,k=i.length,v=r.length,E=b(n),w=[],R=[],C=[],x=f=0;if(!o)return q();if(a||!1!==a&&-1===o.indexOf(d)){for(var S=o.split(i),O=0;O<S.length;O++){if(C=S[O],f+=C.length,O!==S.length-1)f+=i.length;else if(_)break;if(!r||C.substring(0,v)!==r){if(E){if(w=[],F(C.split(t)),P(),c)return q()}else F(C.split(t));if(s&&s<=O)return w=w.slice(0,s),q(!0)}}return q()}for(var A=o.indexOf(t,f),I=o.indexOf(i,f),T=RegExp(p(l)+p(d),"g"),D=o.indexOf(d,f);;)if(o[f]===d)for(D=f,f++;;){if(-1===(D=o.indexOf(d,D+1)))return _||R.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:w.length,index:f}),z();if(D===m-1)return z(o.substring(f,D).replace(T,d));if(d===l&&o[D+1]===l)D++;else if(d===l||0===D||o[D-1]!==l){-1!==A&&A<D+1&&(A=o.indexOf(t,D+1));var L=M(-1===(I=-1!==I&&I<D+1?o.indexOf(i,D+1):I)?A:Math.min(A,I));if(o.substr(D+1+L,y)===t){C.push(o.substring(f,D).replace(T,d)),o[f=D+1+L+y]!==d&&(D=o.indexOf(d,f)),A=o.indexOf(t,f),I=o.indexOf(i,f);break}if(L=M(I),o.substring(D+1+L,D+1+L+k)===i){if(C.push(o.substring(f,D).replace(T,d)),j(D+1+L+k),A=o.indexOf(t,f),D=o.indexOf(d,f),E&&(P(),c))return q();if(s&&w.length>=s)return q(!0);break}R.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:w.length,index:f}),D++}}else if(r&&0===C.length&&o.substring(f,f+v)===r){if(-1===I)return q();f=I+k,I=o.indexOf(i,f),A=o.indexOf(t,f)}else if(-1!==A&&(A<I||-1===I))C.push(o.substring(f,A)),f=A+y,A=o.indexOf(t,f);else{if(-1===I)break;if(C.push(o.substring(f,I)),j(I+k),E&&(P(),c))return q();if(s&&w.length>=s)return q(!0)}return z();function F(e){w.push(e),x=f}function M(e){return -1!==e&&(e=o.substring(D+1,e))&&""===e.trim()?e.length:0}function z(e){return _||(void 0===e&&(e=o.substring(f)),C.push(e),f=m,F(C),E&&P()),q()}function j(e){f=e,F(C),C=[],I=o.indexOf(i,f)}function q(r){if(e.header&&!g&&w.length&&!u){var n=w[0],s={},a=new Set(n);let t=!1;for(let i=0;i<n.length;i++){let r=n[i];if(s[r=b(e.transformHeader)?e.transformHeader(r,i):r]){let e,o=s[r];for(;e=r+"_"+o,o++,a.has(e););a.add(e),n[i]=e,s[r]++,t=!0,(h=null===h?{}:h)[e]=r}else s[r]=1,n[i]=r;a.add(r)}t&&console.warn("Duplicate headers found and renamed."),u=!0}return{data:w,errors:R,meta:{delimiter:t,linebreak:i,aborted:c,truncated:!!r,cursor:x+(g||0),renamedHeaders:h}}}function P(){n(q()),w=[],R=[]}},this.abort=function(){c=!0},this.getCharIndex=function(){return f}}function _(e){var t=e.data,i=s[t.workerId],r=!1;if(t.error)i.userError(t.error,t.file);else if(t.results&&t.results.data){var n={abort:function(){r=!0,m(t.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:y,resume:y};if(b(i.userStep)){for(var a=0;a<t.results.data.length&&(i.userStep({data:t.results.data[a],errors:t.results.errors,meta:t.results.meta},n),!r);a++);delete t.results}else b(i.userChunk)&&(i.userChunk(t.results,n,t.file),delete t.results)}t.finished&&!r&&m(t.workerId,t.results)}function m(e,t){var i=s[e];b(i.userComplete)&&i.userComplete(t),i.terminate(),delete s[e]}function y(){throw Error("Not implemented.")}function k(e){if("object"!=typeof e||null===e)return e;var t,i=Array.isArray(e)?[]:{};for(t in e)i[t]=k(e[t]);return i}function v(e,t){return function(){e.apply(t,arguments)}}function b(e){return"function"==typeof e}return o.parse=function(t,r){var n,h,c,p=(r=r||{}).dynamicTyping||!1;if(b(p)&&(r.dynamicTypingFunction=p,p={}),r.dynamicTyping=p,r.transform=!!b(r.transform)&&r.transform,!r.worker||!o.WORKERS_SUPPORTED){let e;return p=null,o.NODE_STREAM_INPUT,"string"==typeof t?(t=65279!==(e=t).charCodeAt(0)?e:e.slice(1),p=new(r.download?u:l)(r)):!0===t.readable&&b(t.read)&&b(t.on)?p=new f(r):(i.File&&t instanceof File||t instanceof Object)&&(p=new d(r)),p.stream(t)}(p=!!o.WORKERS_SUPPORTED&&(h=i.URL||i.webkitURL||null,c=e.toString(),n=o.BLOB_URL||(o.BLOB_URL=h.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ","(",c,")();"],{type:"text/javascript"}))),(n=new i.Worker(n)).onmessage=_,n.id=a++,s[n.id]=n)).userStep=r.step,p.userChunk=r.chunk,p.userComplete=r.complete,p.userError=r.error,r.step=b(r.step),r.chunk=b(r.chunk),r.complete=b(r.complete),r.error=b(r.error),delete r.worker,p.postMessage({input:t,config:r,workerId:p.id})},o.unparse=function(e,t){var i=!1,r=!0,n=",",s="\r\n",a='"',h=a+a,u=!1,d=null,l=!1,f=((()=>{if("object"==typeof t){if("string"!=typeof t.delimiter||o.BAD_DELIMITERS.filter(function(e){return -1!==t.delimiter.indexOf(e)}).length||(n=t.delimiter),("boolean"==typeof t.quotes||"function"==typeof t.quotes||Array.isArray(t.quotes))&&(i=t.quotes),"boolean"!=typeof t.skipEmptyLines&&"string"!=typeof t.skipEmptyLines||(u=t.skipEmptyLines),"string"==typeof t.newline&&(s=t.newline),"string"==typeof t.quoteChar&&(a=t.quoteChar),"boolean"==typeof t.header&&(r=t.header),Array.isArray(t.columns)){if(0===t.columns.length)throw Error("Option columns is empty");d=t.columns}void 0!==t.escapeChar&&(h=t.escapeChar+a),t.escapeFormulae instanceof RegExp?l=t.escapeFormulae:"boolean"==typeof t.escapeFormulae&&t.escapeFormulae&&(l=/^[=+\-@\t\r].*$/)}})(),RegExp(p(a),"g"));if("string"==typeof e&&(e=JSON.parse(e)),Array.isArray(e)){if(!e.length||Array.isArray(e[0]))return c(null,e,u);if("object"==typeof e[0])return c(d||Object.keys(e[0]),e,u)}else if("object"==typeof e)return"string"==typeof e.data&&(e.data=JSON.parse(e.data)),Array.isArray(e.data)&&(e.fields||(e.fields=e.meta&&e.meta.fields||d),e.fields||(e.fields=Array.isArray(e.data[0])?e.fields:"object"==typeof e.data[0]?Object.keys(e.data[0]):[]),Array.isArray(e.data[0])||"object"==typeof e.data[0]||(e.data=[e.data])),c(e.fields||[],e.data||[],u);throw Error("Unable to serialize unrecognized input");function c(e,t,i){var a="",o=("string"==typeof e&&(e=JSON.parse(e)),"string"==typeof t&&(t=JSON.parse(t)),Array.isArray(e)&&0<e.length),h=!Array.isArray(t[0]);if(o&&r){for(var u=0;u<e.length;u++)0<u&&(a+=n),a+=g(e[u],u);0<t.length&&(a+=s)}for(var d=0;d<t.length;d++){var l=(o?e:t[d]).length,f=!1,c=o?0===Object.keys(t[d]).length:0===t[d].length;if(i&&!o&&(f="greedy"===i?""===t[d].join("").trim():1===t[d].length&&0===t[d][0].length),"greedy"===i&&o){for(var p=[],_=0;_<l;_++){var m=h?e[_]:_;p.push(t[d][m])}f=""===p.join("").trim()}if(!f){for(var y=0;y<l;y++){0<y&&!c&&(a+=n);var k=o&&h?e[y]:y;a+=g(t[d][k],y)}d<t.length-1&&(!i||0<l&&!c)&&(a+=s)}}return a}function g(e,t){var r,s;return null==e?"":e.constructor===Date?JSON.stringify(e).slice(1,25):(s=!1,l&&"string"==typeof e&&l.test(e)&&(e="'"+e,s=!0),r=e.toString().replace(f,h),(s=s||!0===i||"function"==typeof i&&i(e,t)||Array.isArray(i)&&i[t]||((e,t)=>{for(var i=0;i<t.length;i++)if(-1<e.indexOf(t[i]))return!0;return!1})(r,o.BAD_DELIMITERS)||-1<r.indexOf(n)||" "===r.charAt(0)||" "===r.charAt(r.length-1))?a+r+a:r)}},o.RECORD_SEP="\x1e",o.UNIT_SEP="\x1f",o.BYTE_ORDER_MARK="\uFEFF",o.BAD_DELIMITERS=["\r","\n",'"',o.BYTE_ORDER_MARK],o.WORKERS_SUPPORTED=!r&&!!i.Worker,o.NODE_STREAM_INPUT=1,o.LocalChunkSize=0xa00000,o.RemoteChunkSize=5242880,o.DefaultDelimiter=",",o.Parser=g,o.ParserHandle=c,o.NetworkStreamer=u,o.FileStreamer=d,o.StringStreamer=l,o.ReadableStreamStreamer=f,i.jQuery&&((t=i.jQuery).fn.parse=function(e){var r=e.config||{},n=[];return this.each(function(e){if(!("INPUT"===t(this).prop("tagName").toUpperCase()&&"file"===t(this).attr("type").toLowerCase()&&i.FileReader)||!this.files||0===this.files.length)return!0;for(var s=0;s<this.files.length;s++)n.push({file:this.files[s],inputElem:this,instanceConfig:t.extend({},r)})}),s(),this;function s(){if(0===n.length)b(e.complete)&&e.complete();else{var i,r,s,h,u=n[0];if(b(e.before)){var d=e.before(u.file,u.inputElem);if("object"==typeof d){if("abort"===d.action)return i="AbortError",r=u.file,s=u.inputElem,h=d.reason,void(b(e.error)&&e.error({name:i},r,s,h));if("skip"===d.action)return void a();"object"==typeof d.config&&(u.instanceConfig=t.extend(u.instanceConfig,d.config))}else if("skip"===d)return void a()}var l=u.instanceConfig.complete;u.instanceConfig.complete=function(e){b(l)&&l(e,u.file,u.inputElem),a()},o.parse(u.file,u.instanceConfig)}}function a(){n.splice(0,1),s()}}),n&&(i.onmessage=function(e){e=e.data,void 0===o.WORKER_ID&&e&&(o.WORKER_ID=e.workerId),"string"==typeof e.input?i.postMessage({workerId:o.WORKER_ID,results:o.parse(e.input,e.config),finished:!0}):(i.File&&e.input instanceof File||e.input instanceof Object)&&(e=o.parse(e.input,e.config))&&i.postMessage({workerId:o.WORKER_ID,results:e,finished:!0})}),(u.prototype=Object.create(h.prototype)).constructor=u,(d.prototype=Object.create(h.prototype)).constructor=d,(l.prototype=Object.create(l.prototype)).constructor=l,(f.prototype=Object.create(h.prototype)).constructor=f,o})?i.apply(t,[]):i)||(e.exports=r)},60679:(e,t,i)=>{i.d(t,{A:()=>r});let r=(0,i(40157).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},68718:(e,t,i)=>{i.d(t,{A:()=>r});let r=(0,i(40157).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}}]);