// @ts-nocheck
'use client';

import {useEffect, useState} from 'react';
import {useParams} from 'next/navigation';
import type {Vehicle, ServiceRecord} from '@/lib/types';
import {getVehicleById} from '@/lib/store';
import Image from 'next/image';
import {ReportActions} from '@/components/reports/ReportActions';
import {History, Car} from 'lucide-react';
import {Button} from '@/components/ui/button';
import {SkeletonLoader, DataLoader} from '@/components/ui/loading';
import Link from 'next/link';
import {ActionButton} from '@/components/ui/action-button';

// Helper for dynamic metadata. This ideally is done on server for SEO.
// For client-side dynamic title, we use useEffect.
// export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
//   // This won't work here as getVehicleById is client-side.
//   // For true server-side metadata, data fetching must be server-compatible.
//   // const vehicle = getVehicleById(params.id);
//   // return { title: vehicle ? `${vehicle.make} ${vehicle.model} - Report` : 'Vehicle Report' };
//   return { title: 'Vehicle Report' };
// }

export default function VehicleReportPage() {
	const params = useParams();
	const [vehicle, setVehicle] = useState<Vehicle | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null); // Added error state
	const vehicleId = params.id as string;

	const fetchVehicleData = async () => {
		setIsLoading(true);
		setError(null);
		if (vehicleId) {
			try {
				const foundVehicle = await getVehicleById(Number(vehicleId)); // Ensure ID is number
				if (foundVehicle) {
					// Sort service history for display: oldest first for chronological report
					foundVehicle.serviceHistory.sort(
						(a, b) =>
							new Date(a.date).getTime() - new Date(b.date).getTime() ||
							a.odometer - b.odometer
					);
					setVehicle(foundVehicle);
					document.title = `${foundVehicle.make} ${foundVehicle.model} - Maintenance Report`;
				} else {
					setError('Vehicle not found.');
				}
			} catch (err) {
				console.error('Error fetching vehicle for report:', err);
				setError(
					err instanceof Error ? err.message : 'Failed to load vehicle data.'
				);
			} finally {
				setIsLoading(false);
			}
		} else {
			setError('No Vehicle ID provided.');
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchVehicleData();
	}, [vehicleId]); // Removed fetchVehicleData from deps array to avoid potential loop

	return (
		<div className='max-w-4xl mx-auto bg-white p-2 sm:p-4 text-gray-800'>
			<DataLoader
				isLoading={isLoading}
				error={error}
				data={vehicle}
				onRetry={fetchVehicleData}
				loadingComponent={
					<div className='space-y-6'>
						<h1 className='text-3xl font-bold text-gray-800 text-center'>
							Loading Report...
						</h1>
						<SkeletonLoader variant='card' count={1} />
						<SkeletonLoader variant='table' count={3} className='mt-6' />
					</div>
				}
				emptyComponent={
					<div className='text-center py-10'>
						Vehicle not found or could not be loaded.
					</div>
				}>
				{(loadedVehicle) => (
					<>
						<div className='flex justify-between items-center mb-4 no-print'>
							<ActionButton
								actionType='tertiary'
								asChild
								icon={<History className='h-4 w-4' />}>
								<Link href={`/vehicles/${vehicleId}/report/service-history`}>
									Detailed Service History
								</Link>
							</ActionButton>

							<ReportActions
								reportContentId='#vehicle-report-content'
								reportType='vehicle'
								entityId={vehicleId}
								tableId='#service-history-table'
								fileName={`vehicle-report-${loadedVehicle.make}-${loadedVehicle.model}`}
								enableCsv={loadedVehicle.serviceHistory.length > 0}
							/>
						</div>

						<div id='vehicle-report-content' className='report-content'>
							<header className='text-center mb-8 pb-4 border-b-2 border-gray-300 report-header'>
								<h1 className='text-3xl font-bold text-gray-800'>
									Maintenance Report
								</h1>
								<p className='text-xl text-gray-600'>
									{loadedVehicle.make} {loadedVehicle.model} (
									{loadedVehicle.year})
								</p>
								{loadedVehicle.licensePlate && (
									<p className='text-md text-gray-500'>
										Plate: {loadedVehicle.licensePlate}
									</p>
								)}
							</header>

							<section className='mb-8 card-print p-4 border border-gray-200 rounded'>
								<h2 className='text-2xl font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200'>
									Vehicle Details
								</h2>
								<div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm'>
									<div>
										<strong>Make:</strong> {loadedVehicle.make}
									</div>
									<div>
										<strong>Model:</strong> {loadedVehicle.model}
									</div>
									<div>
										<strong>Year:</strong> {loadedVehicle.year}
									</div>
									{loadedVehicle.licensePlate && (
										<div>
											<strong>Plate Number:</strong>{' '}
											{loadedVehicle.licensePlate}
										</div>
									)}
									{loadedVehicle.color && (
										<div>
											<strong>Color:</strong> {loadedVehicle.color}
										</div>
									)}
									<div>
										<strong>Initial Odometer:</strong>{' '}
										{loadedVehicle.initialOdometer !== null
											? `${loadedVehicle.initialOdometer.toLocaleString()} miles`
											: 'Not recorded'}
									</div>
								</div>
								{loadedVehicle.imageUrl && (
									<div className='mt-4 relative aspect-[16/9] w-full max-w-md mx-auto overflow-hidden rounded no-print'>
										<Image
											src={loadedVehicle.imageUrl}
											alt={`${loadedVehicle.make} ${loadedVehicle.model}`}
											layout='fill'
											objectFit='contain'
											data-ai-hint='car side'
										/>
									</div>
								)}
							</section>

							<section className='card-print p-4 border border-gray-200 rounded'>
								<div className='flex justify-between items-center mb-4 pb-2 border-b border-gray-200'>
									<h2 className='text-2xl font-semibold text-gray-700'>
										Service History
									</h2>
									<p className='text-sm text-gray-500 no-print'>
										<Link
											href={`/vehicles/${vehicleId}/report/service-history`}
											className='text-blue-600 hover:underline'>
											View detailed service history
										</Link>
									</p>
								</div>
								{loadedVehicle.serviceHistory.length === 0 ? (
									<p className='text-gray-500'>
										No service records available for this vehicle.
									</p>
								) : (
									<table
										id='service-history-table'
										className='w-full text-sm text-left text-gray-600'>
										<thead className='text-xs text-gray-700 uppercase bg-gray-50'>
											<tr>
												<th scope='col' className='px-3 py-2'>
													Date
												</th>
												<th scope='col' className='px-3 py-2'>
													Odometer
												</th>
												<th scope='col' className='px-3 py-2'>
													Service Performed
												</th>
												<th scope='col' className='px-3 py-2'>
													Notes
												</th>
												<th scope='col' className='px-3 py-2 text-right'>
													Cost
												</th>
											</tr>
										</thead>
										<tbody>
											{loadedVehicle.serviceHistory.map((record) => (
												<tr
													key={record.id}
													className='bg-white border-b hover:bg-gray-50'>
													<td className='px-3 py-2'>
														{new Date(record.date).toLocaleDateString()}
													</td>
													<td className='px-3 py-2'>
														{record.odometer.toLocaleString()}
													</td>
													<td className='px-3 py-2'>
														{record.servicePerformed.join(', ')}
													</td>
													<td className='px-3 py-2'>{record.notes || '-'}</td>
													<td className='px-3 py-2 text-right'>
														{record.cost
															? `$${Number(record.cost).toFixed(2)}`
															: '-'}
													</td>
												</tr>
											))}
										</tbody>
									</table>
								)}
							</section>

							<footer className='mt-12 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500 report-footer'>
								<p>Report generated on: {new Date().toLocaleDateString()}</p>
								<p>WorkHub</p>
							</footer>
						</div>
					</>
				)}
			</DataLoader>
		</div>
	);
}
