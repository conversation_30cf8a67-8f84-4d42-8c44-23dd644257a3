(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6359],{18104:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var i=a(95155),s=a(12115),l=a(35695),o=a(11612),n=a(2730),r=a(95647);let d=(0,a(40157).A)("UserCog",[["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m21.7 16.4-.9-.3",key:"12j9ji"}],["path",{d:"m15.2 13.9-.9-.3",key:"1fdjdi"}],["path",{d:"m16.6 18.7.3-.9",key:"heedtr"}],["path",{d:"m19.1 12.2.3-.9",key:"1af3ki"}],["path",{d:"m19.6 18.7-.4-1",key:"1x9vze"}],["path",{d:"m16.8 12.3-.4-1",key:"vqeiwj"}],["path",{d:"m14.3 16.6 1-.4",key:"1qlj63"}],["path",{d:"m20.7 13.8 1-.4",key:"1v5t8k"}]]);var c=a(87481),u=a(68856);function p(){let e=(0,l.useRouter)(),t=(0,l.useParams)(),{toast:a}=(0,c.dj)(),[p,m]=(0,s.useState)(null),[y,h]=(0,s.useState)(!0),[v,f]=(0,s.useState)(!1),g=t.id;(0,s.useEffect)(()=>{(async()=>{if(g)try{let t=await (0,n.getEmployeeById)(Number(g));t?m(t):(a({title:"Error",description:"Employee not found.",variant:"destructive"}),e.push("/employees"))}catch(t){console.error("Failed to fetch employee:",t),a({title:"Error",description:"Failed to load employee data.",variant:"destructive"}),e.push("/employees")}finally{h(!1)}})()},[g,e,a]);let k=async t=>{if(!g)return void a({title:"Error",description:"Invalid employee ID.",variant:"destructive"});f(!0);try{console.log("Updating employee ID: ".concat(g),t);let i={...t,statusChangeReason:t.statusChangeReason||void 0};await (0,n.updateEmployee)(Number(g),i),a({title:"Employee Updated Successfully",description:"".concat(t.fullName||t.name," has been updated with the latest information."),variant:"default"}),e.push("/employees/".concat(g))}catch(t){var i,s,l,o,r,d;console.error("Failed to update employee:",t);let e="Failed to update employee. Please try again.";if(null==(i=t.message)?void 0:i.includes("Network error"))e="Network error. Please check your connection and try again.";else if(null==(s=t.message)?void 0:s.includes("404"))e="Employee not found. They may have been deleted.";else if(null==(l=t.message)?void 0:l.includes("403"))e="You do not have permission to edit this employee.";else if(null==(o=t.message)?void 0:o.includes("Validation failed"))e="Please check your input data. Some fields may be invalid.";else if(t.validationErrors){let a=t.validationErrors.map(e=>"".concat(e.path,": ").concat(e.message)).join(", ");e="Validation errors: ".concat(a)}else(null==(d=t.response)||null==(r=d.data)?void 0:r.error)?e=t.response.data.error:t.message&&(e=t.message);a({title:"Update Failed",description:e,variant:"destructive"})}finally{f(!1)}};return y?(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(r.z,{title:"Loading...",icon:d}),(0,i.jsx)(u.E,{className:"h-[700px] w-full rounded-lg bg-card"})]}):p?(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(r.z,{title:"Edit Employee: ".concat(p.fullName),description:"Modify the details for this employee.",icon:d}),(0,i.jsx)(o.A,{onSubmit:k,initialData:p,isEditing:!0,isLoading:v})]}):(0,i.jsx)("p",{children:"Employee not found."})}},68856:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var i=a(95155),s=a(59434);function l(e){let{className:t,...a}=e;return(0,i.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-muted",t),...a})}},81899:(e,t,a)=>{Promise.resolve().then(a.bind(a,18104))}},e=>{var t=t=>e(e.s=t);e.O(0,[5769,8360,832,2688,2512,1859,4066,8162,2730,3222,8441,1684,7358],()=>t(81899)),_N_E=e.O()}]);