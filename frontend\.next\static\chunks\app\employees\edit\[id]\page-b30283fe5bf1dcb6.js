(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[393],{18763:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},31223:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>y});var t=s(95155),l=s(12115),r=s(35695),i=s(11612),d=s(2730),n=s(95647),o=s(18763),c=s(79239),p=s(87481),u=s(68856),m=s(30285);let y=()=>{let e=(0,r.useRouter)(),a=(0,r.useParams)(),{toast:s}=(0,p.dj)(),y=a.id,x=parseInt(y,10),[h,v]=(0,l.useState)(null),[E,N]=(0,l.useState)(!1),[f,j]=(0,l.useState)(!0),[b,k]=(0,l.useState)(null),g=(0,l.useCallback)(async()=>{if(isNaN(x)){k("Invalid employee ID provided."),j(!1);return}j(!0);try{let e=await (0,d.getEmployeeById)(x);e?v(e):k("Employee not found.")}catch(e){console.error("Failed to fetch employee:",e),k(e.message||"Failed to load employee data.")}finally{j(!1)}},[x]);(0,l.useEffect)(()=>{y&&g()},[y,g]);let w=async a=>{if(isNaN(x))return void k("Cannot update employee without a valid ID.");N(!0),k(null);try{await (0,d.updateEmployee)(x,a),s({title:"Employee Updated",description:"".concat(a.name," has been successfully updated."),variant:"default"}),e.push("/employees")}catch(a){var t,l;console.error("Failed to update employee:",a);let e=(null==(l=a.response)||null==(t=l.data)?void 0:t.error)||a.message||"An unexpected error occurred.";k(e),s({title:"Error Updating Employee",description:e,variant:"destructive"})}finally{N(!1)}};return f?(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsx)(n.z,{title:"Edit Employee",description:"Loading employee details...",icon:o.A}),(0,t.jsxs)("div",{className:"max-w-3xl mx-auto space-y-6",children:[(0,t.jsx)(u.E,{className:"h-10 w-1/3"}),[...Array(6)].map((e,a)=>(0,t.jsx)(u.E,{className:"h-12 w-full"},a)),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-6",children:[(0,t.jsx)(u.E,{className:"h-10 w-24"}),(0,t.jsx)(u.E,{className:"h-10 w-24"})]})]})]}):b&&!h?(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8 text-center",children:[(0,t.jsx)(n.z,{title:"Error",description:b,icon:c.A}),(0,t.jsx)(m.$,{onClick:()=>e.push("/employees"),children:"Back to Employees"})]}):(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsx)(n.z,{title:"Edit Employee",description:"Update details for ".concat((null==h?void 0:h.name)||(null==h?void 0:h.fullName)||"employee"),icon:o.A}),b&&h&&(0,t.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4",role:"alert",children:[(0,t.jsx)("strong",{className:"font-bold",children:"Error: "}),(0,t.jsx)("span",{className:"block sm:inline",children:b})]}),h&&(0,t.jsx)(i.A,{onSubmit:w,initialData:h,isEditing:!0,isLoading:E})]})}},68856:(e,a,s)=>{"use strict";s.d(a,{E:()=>r});var t=s(95155),l=s(59434);function r(e){let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",a),...s})}},79239:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("UsersRound",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])},92711:(e,a,s)=>{Promise.resolve().then(s.bind(s,31223))}},e=>{var a=a=>e(e.s=a);e.O(0,[5769,8360,832,2688,2512,1859,4066,8162,2730,3222,8441,1684,7358],()=>a(92711)),_N_E=e.O()}]);