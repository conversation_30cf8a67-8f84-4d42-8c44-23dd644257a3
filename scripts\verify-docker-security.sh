#!/bin/bash

# PHASE 1 SECURITY HARDENING: Docker Security Verification Script
# This script verifies that Docker containers are running with proper security hardening

set -e

echo "=============================================="
echo "  WorkHub Docker Security Verification"
echo "  Phase 1 Security Hardening Validation"
echo "=============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing: $test_name... "
    
    if eval "$test_command" | grep -q "$expected_result"; then
        echo -e "${GREEN}PASS${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}FAIL${NC}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        echo "  Expected: $expected_result"
        echo "  Command: $test_command"
    fi
}

# Function to check if image exists
check_image_exists() {
    local image_name="$1"
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "$image_name"; then
        return 0
    else
        return 1
    fi
}

echo "🔍 Checking Docker Security Hardening Implementation..."
echo ""

# Test 1: Check if security-hardened images exist
echo "📋 Test 1: Security-Hardened Images"
if check_image_exists "workhub-backend:security-hardened"; then
    echo -e "${GREEN}✅ PASS${NC} Backend security-hardened image exists"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ FAIL${NC} Backend security-hardened image not found"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

if check_image_exists "workhub-frontend:security-hardened"; then
    echo -e "${GREEN}✅ PASS${NC} Frontend security-hardened image exists"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ FAIL${NC} Frontend security-hardened image not found"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 2))
echo ""

# Test 2: Check security labels
echo "📋 Test 2: Security Labels Verification"
run_test "Backend security labels" \
    "docker inspect workhub-backend:security-hardened --format='{{.Config.Labels}}'" \
    "security.non-root:true"

run_test "Frontend security labels" \
    "docker inspect workhub-frontend:security-hardened --format='{{.Config.Labels}}'" \
    "security.non-root:true"

echo ""

# Test 3: Check non-root user configuration
echo "📋 Test 3: Non-Root User Configuration"
run_test "Backend non-root user" \
    "docker inspect workhub-backend:security-hardened --format='{{.Config.User}}'" \
    "workhub"

run_test "Frontend non-root user" \
    "docker inspect workhub-frontend:security-hardened --format='{{.Config.User}}'" \
    "nextjs"

echo ""

# Test 4: Check dumb-init entrypoint
echo "📋 Test 4: Dumb-init Signal Handling"
run_test "Backend dumb-init entrypoint" \
    "docker inspect workhub-backend:security-hardened --format='{{.Config.Entrypoint}}'" \
    "dumb-init"

run_test "Frontend dumb-init entrypoint" \
    "docker inspect workhub-frontend:security-hardened --format='{{.Config.Entrypoint}}'" \
    "dumb-init"

echo ""

# Test 5: Check for security phase labels
echo "📋 Test 5: Security Phase Labels"
run_test "Backend security phase" \
    "docker inspect workhub-backend:security-hardened --format='{{index .Config.Labels \"security.phase\"}}'" \
    "PHASE-1-HARDENED"

run_test "Frontend security phase" \
    "docker inspect workhub-frontend:security-hardened --format='{{index .Config.Labels \"security.phase\"}}'" \
    "PHASE-1-HARDENED"

echo ""

# Test 6: Verify no sensitive information in CMD
echo "📋 Test 6: Sensitive Information Protection"
BACKEND_CMD=$(docker inspect workhub-backend:security-hardened --format='{{.Config.Cmd}}')
if echo "$BACKEND_CMD" | grep -q "DATABASE_URL"; then
    echo -e "${RED}❌ FAIL${NC} Backend CMD contains DATABASE_URL exposure"
    TESTS_FAILED=$((TESTS_FAILED + 1))
else
    echo -e "${GREEN}✅ PASS${NC} Backend CMD does not expose DATABASE_URL"
    TESTS_PASSED=$((TESTS_PASSED + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))
echo ""

# Summary
echo "=============================================="
echo "  🔐 DOCKER SECURITY VERIFICATION RESULTS"
echo "=============================================="
echo ""
echo "📊 Test Summary:"
echo "  • Total Tests: $TOTAL_TESTS"
echo "  • Tests Passed: $TESTS_PASSED"
echo "  • Tests Failed: $TESTS_FAILED"
echo "  • Success Rate: $(( (TESTS_PASSED * 100) / TOTAL_TESTS ))%"
echo ""

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL DOCKER SECURITY TESTS PASSED!${NC}"
    echo -e "${GREEN}✅ Docker containers are properly security-hardened${NC}"
    echo ""
    echo "🛡️ Security Features Verified:"
    echo "  ✅ Non-root user execution"
    echo "  ✅ Proper signal handling (dumb-init)"
    echo "  ✅ Security labels for compliance"
    echo "  ✅ No sensitive information exposure"
    echo "  ✅ Phase 1 security hardening complete"
    echo ""
    exit 0
else
    echo -e "${RED}❌ DOCKER SECURITY ISSUES DETECTED${NC}"
    echo -e "${YELLOW}⚠️  Please address failed tests before deployment${NC}"
    echo ""
    exit 1
fi
