/**
 * Middleware to sanitize task data before it's processed by controllers
 * Ensures that array fields are always arrays, even if they're null or undefined
 */
// Import logger if available, otherwise use console
let logger;
try {
    // Try to import the application logger
    const { default: appLogger } = require('../utils/logger');
    logger = appLogger;
}
catch (error) {
    // Fallback to console if logger is not available
    logger = {
        warn: console.warn,
        error: console.error,
    };
}
/**
 * Ensures that task data has proper array fields
 * This prevents "findIndex is not a function" errors in the frontend
 */
export const sanitizeTaskData = (req, res, next) => {
    try {
        // Only process if there's a body
        if (req.body) {
            const requestPath = req.path;
            const taskId = req.params.id || 'unknown';
            // Ensure assignedEmployeeIds is always an array
            if (!req.body.assignedEmployeeIds ||
                !Array.isArray(req.body.assignedEmployeeIds)) {
                const originalValue = req.body.assignedEmployeeIds === null
                    ? 'null'
                    : typeof req.body.assignedEmployeeIds;
                logger.warn(`TaskDataSanitizer: Original value for assignedEmployeeIds was ${originalValue}, sanitized to []. ` +
                    `[TaskID: ${taskId}, Path: ${requestPath}]`);
                req.body.assignedEmployeeIds = [];
            }
            // Ensure requiredSkills is always an array
            if (!req.body.requiredSkills || !Array.isArray(req.body.requiredSkills)) {
                const originalValue = req.body.requiredSkills === null
                    ? 'null'
                    : typeof req.body.requiredSkills;
                logger.warn(`TaskDataSanitizer: Original value for requiredSkills was ${originalValue}, sanitized to []. ` +
                    `[TaskID: ${taskId}, Path: ${requestPath}]`);
                req.body.requiredSkills = [];
            }
            // Ensure subTasks is always an array
            if (!req.body.subTasks || !Array.isArray(req.body.subTasks)) {
                const originalValue = req.body.subTasks === null ? 'null' : typeof req.body.subTasks;
                logger.warn(`TaskDataSanitizer: Original value for subTasks was ${originalValue}, sanitized to []. ` +
                    `[TaskID: ${taskId}, Path: ${requestPath}]`);
                req.body.subTasks = [];
            }
        }
        next();
    }
    catch (error) {
        // If any error occurs during sanitization, log it but don't block the request
        logger.error('Error in task data sanitizer middleware:', error);
        next();
    }
};
//# sourceMappingURL=taskDataSanitizer.js.map