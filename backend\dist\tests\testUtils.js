// testUtils.ts - Helper functions for testing with Jest and ESM modules
/**
 * Creates a mock object with methods auto-setup as Jest spies.
 * Works with ESM modules where direct jest.mock() doesn't work properly.
 *
 * @param methods Methods to mock
 * @returns Object with all methods mocked as Jest spies
 */
export function createMock(methods = []) {
    const mock = {};
    methods.forEach((method) => {
        mock[method] = jest.fn();
    });
    return mock;
}
/**
 * Create Express request and response mocks for controller testing
 */
export function createExpressMocks() {
    const responseJson = jest.fn().mockReturnValue({ end: jest.fn() });
    const responseStatus = jest.fn().mockReturnThis();
    const responseEnd = jest.fn();
    const mockRequest = {
        params: {},
        query: {},
        body: {},
        headers: {},
        cookies: {},
    };
    const mockResponse = {
        json: responseJson,
        status: responseStatus,
        end: responseEnd,
        send: jest.fn().mockReturnThis(),
        cookie: jest.fn().mockReturnThis(),
        clearCookie: jest.fn().mockReturnThis(),
        redirect: jest.fn().mockReturnThis(),
    };
    return { mockRequest, mockResponse, responseJson, responseStatus };
}
/**
 * Creates mock Express app for testing routes
 */
export function createMockExpressApp() {
    const routes = {};
    const handlers = {};
    // Create mock implementations for Express app methods
    const app = {
        get: jest.fn((path, ...handlers) => {
            routes[`GET ${path}`] = handlers;
            return app;
        }),
        post: jest.fn((path, ...handlers) => {
            routes[`POST ${path}`] = handlers;
            return app;
        }),
        put: jest.fn((path, ...handlers) => {
            routes[`PUT ${path}`] = handlers;
            return app;
        }),
        delete: jest.fn((path, ...handlers) => {
            routes[`DELETE ${path}`] = handlers;
            return app;
        }),
        patch: jest.fn((path, ...handlers) => {
            routes[`PATCH ${path}`] = handlers;
            return app;
        }),
        use: jest.fn((path, handler) => {
            if (typeof path === 'function') {
                // Middleware without path
                handlers['middleware'] = handlers['middleware'] || [];
                handlers['middleware'].push(path);
            }
            else {
                // Middleware or sub-route with path
                handlers[path] = handler;
            }
            return app;
        }),
        listen: jest.fn(),
        routes,
        handlers,
    };
    return app;
}
/**
 * Mocks Prisma client for testing
 */
export function mockPrisma() {
    const prismaModelMethods = [
        'findUnique',
        'findFirst',
        'findMany',
        'create',
        'update',
        'upsert',
        'delete',
        'deleteMany',
        'count',
    ];
    // Create model mock generators
    const createModelMock = () => {
        const modelMock = {};
        prismaModelMethods.forEach((method) => {
            modelMock[method] = jest.fn();
        });
        return modelMock;
    };
    // Create a mock Prisma client with common models
    const prisma = {
        $connect: jest.fn().mockResolvedValue(undefined),
        $disconnect: jest.fn().mockResolvedValue(undefined),
        $transaction: jest.fn().mockImplementation((callback) => callback(prisma)),
        $queryRawUnsafe: jest.fn(),
        employee: createModelMock(),
        vehicle: createModelMock(),
        serviceRecord: createModelMock(),
        task: createModelMock(),
        delegation: createModelMock(),
        flight: createModelMock(),
        employeeStatusEntry: createModelMock(),
    };
    return prisma;
}
//# sourceMappingURL=testUtils.js.map