# Product Requirements Document: Backend API Service & Frontend Integration

## 1. Introduction

This document outlines the requirements for developing a new backend API service
with a PostgreSQL database for the Car Service Tracking System. It also covers
the necessary frontend modifications to integrate with this new backend,
replacing the current localStorage-based data persistence.

## 2. Goals

- Develop a robust and scalable backend API service.
- Ensure data persistence and integrity using a PostgreSQL database.
- Decouple frontend data logic from client-side storage.
- Enable future features and enhancements by providing a solid backend
  foundation.
- Improve data management capabilities for vehicles and employees.

## 3. Target Users

- Application Administrators
- Service Technicians
- Customers (potentially in future iterations)

## 4. Proposed Features

### 4.1. Backend API Service

- **Framework:** Node.js (Specific framework TBD: Express.js, NestJS, or Next.js
  API Routes)
- **Database:** PostgreSQL
- **Authentication:** (To be defined - e.g., JWT-based, session-based) -
  _Initial version might omit auth for simplicity if focused on data CRUD._
- **API Endpoints:**
  - **Vehicles:**
    - `POST /api/vehicles`: Create a new vehicle record.
    - `GET /api/vehicles`: Retrieve a list of all vehicles.
    - `GET /api/vehicles/{id}`: Retrieve a specific vehicle by ID.
    - `PUT /api/vehicles/{id}`: Update an existing vehicle record.
    - `DELETE /api/vehicles/{id}`: Delete a vehicle record.
  - **Employees:**
    - `POST /api/employees`: Create a new employee record.
    - `GET /api/employees`: Retrieve a list of all employees.
    - `GET /api/employees/{id}`: Retrieve a specific employee by ID.
    - `PUT /api/employees/{id}`: Update an existing employee record.
    - `DELETE /api/employees/{id}`: Delete an employee record.
  - **(Future) Service Records:**
    - `POST /api/servicerecords`: Create a new service record.
    - `GET /api/servicerecords`: Retrieve all service records (with filtering
      options, e.g., by vehicle ID, date).
    - `GET /api/servicerecords/{id}`: Retrieve a specific service record.
    - `PUT /api/servicerecords/{id}`: Update a service record.
    - `DELETE /api/servicerecords/{id}`: Delete a service record.
- **Data Access Layer:**
  - Use an ORM (e.g., Prisma, Sequelize, TypeORM) for database interactions.
- **Error Handling & Logging:**
  - Implement comprehensive error handling.
  - Set up basic logging for API requests and errors.

### 4.2. Frontend Updates

- **Data Fetching:**
  - Modify `src/lib/store.ts` (or create a new data service layer) to make HTTP
    requests (e.g., using `fetch` or `axios`) to the new backend API endpoints.
  - Remove `localStorage` usage for persistent data.
- **State Management:**
  - Ensure state management (e.g., Svelte stores) is updated to reflect
    asynchronous data fetching and updates.
  - Handle loading states and error states for API interactions.
- **UI Components:**
  - Update UI components to consume data from the new data layer.
  - Ensure forms for creating/editing vehicles and employees interact with the
    backend API.

## 5. Database Schema (Initial Draft)

- **`vehicles` table:**
  - `id` (Primary Key, e.g., SERIAL or UUID)
  - `make` (TEXT)
  - `model` (TEXT)
  - `year` (INTEGER)
  - `vin` (TEXT, Unique)
  - `license_plate` (TEXT)
  - `owner_name` (TEXT)
  - `owner_contact` (TEXT)
  - `created_at` (TIMESTAMP)
  - `updated_at` (TIMESTAMP)
- **`employees` table:**
  - `id` (Primary Key, e.g., SERIAL or UUID)
  - `name` (TEXT)
  - `role` (TEXT, e.g., 'Technician', 'Admin')
  - `employee_id` (TEXT, Unique)
  - `contact_info` (TEXT)
  - `created_at` (TIMESTAMP)
  - `updated_at` (TIMESTAMP)
- **(Future) `service_records` table:**
  - `id` (Primary Key)
  - `vehicle_id` (Foreign Key to `vehicles.id`)
  - `employee_id` (Foreign Key to `employees.id` - technician performing
    service)
  - `service_date` (DATE)
  - `description` (TEXT)
  - `cost` (DECIMAL)
  - `status` (TEXT - e.g., 'Scheduled', 'In Progress', 'Completed', 'Cancelled')
  - `created_at` (TIMESTAMP)
  - `updated_at` (TIMESTAMP)

## 6. Non-Goals (Initial Phase)

- Advanced user authentication and authorization (RBAC).
- Real-time updates (e.g., WebSockets).
- Complex reporting and analytics features.
- User accounts and login for customers.

## 7. Technical Stack Considerations

- **Backend:** Node.js, PostgreSQL, ORM (Prisma recommended for its type safety
  and ease of use with TypeScript, if applicable).
- **Frontend:** Existing Svelte/SvelteKit stack.
- **Deployment:** (To be defined - e.g., Docker, cloud platform).

## 8. Success Metrics

- All vehicle and employee data is successfully migrated/managed by the new
  backend.
- Frontend application successfully interacts with the backend API for all CRUD
  operations.
- Reduced reliance on `localStorage`, improving data integrity and scalability.
- The system remains stable and performant.

## 9. Open Questions

- Specific Node.js backend framework preference?
- Details on "context7" for new framework features?
- Location of the backend project (monorepo vs. separate repo)?
- Authentication requirements for the first version? (Assuming none or very
  basic for now).
- Specific requirements for data migration from `localStorage` if any existing
  data needs to be preserved.

---
