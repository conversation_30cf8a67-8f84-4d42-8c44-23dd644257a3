import app from './app.js';
import http from 'http';
import {Server} from 'socket.io';
import logger from './utils/logger.js';
import supabaseRealtimeService from './services/supabase-realtime.service.js';
import {getDatabaseConfig} from './services/database.service.js';

const PORT = process.env.PORT || 3001;
const server = http.createServer(app);

// Define allowed origins for Socket.IO, similar to app.ts
const defaultSocketOrigins = ['http://localhost:3000', 'http://localhost:9002'];
const socketOriginSettingArray = process.env.FRONTEND_URL
	? process.env.FRONTEND_URL.split(',').map((url) => url.trim())
	: defaultSocketOrigins;

logger.info('[Socket.IO CORS] Allowed Origins:', socketOriginSettingArray);
logger.info('[Socket.IO CORS] FRONTEND_URL env var:', process.env.FRONTEND_URL);

const io = new Server(server, {
	cors: {
		origin: (requestOrigin, callback) => {
			logger.debug(`[Socket.IO CORS] Request Origin: ${requestOrigin}`);
			// Allow requests with no origin (like mobile apps or curl requests for testing)
			if (!requestOrigin) {
				logger.debug('[Socket.IO CORS] No origin, allowing.');
				return callback(null, true);
			}
			if (socketOriginSettingArray.includes(requestOrigin)) {
				logger.debug(
					`[Socket.IO CORS] Origin ${requestOrigin} allowed, reflecting.`
				);
				callback(null, requestOrigin); // Reflect the specific (single) origin
			} else {
				logger.warn(`[Socket.IO CORS] Origin ${requestOrigin} NOT allowed.`);
				callback(new Error('Socket.IO: Not allowed by CORS'));
			}
		},
		methods: ['GET', 'POST'],
		credentials: true,
	},
});

// Socket.io connection handler
io.on('connection', (socket) => {
	logger.info('Client connected:', socket.id);

	// Handle disconnect
	socket.on('disconnect', () => {
		logger.info('Client disconnected:', socket.id);
	});
});

// Initialize Supabase real-time service if Supabase is configured
const dbConfig = getDatabaseConfig();
if (dbConfig.useSupabase) {
	logger.info('Initializing Supabase real-time service...');
	supabaseRealtimeService.initialize(io);
} else {
	logger.info(
		'Supabase not configured, skipping real-time service initialization'
	);
}

// Export for use in event emitters
export {io};

// Use http server instead of app.listen
server.listen(PORT, () => {
	logger.info(`Server is running on http://localhost:${PORT}`);
});
