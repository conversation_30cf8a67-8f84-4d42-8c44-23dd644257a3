
import { Request, Response } from 'express';
import * as taskModel from '../models/task.model.js';
import { Prisma, TaskPriority as PrismaTaskPriority, TaskStatus as PrismaTaskStatus } from '../generated/prisma/index.js';
import { emitTaskChange, SOCKET_EVENTS } from '../services/socketService.js';


const processTaskData = (data: any): Prisma.TaskCreateInput => {
  const createInput: Prisma.TaskCreateInput = {
    description: data.description,
    location: data.location,
    dateTime: new Date(data.dateTime), 
    estimatedDuration: data.estimatedDuration,
    priority: data.priority as PrismaTaskPriority, 
    status: (data.status || 'Pending') as PrismaTaskStatus, 
    statusHistory: { 
      create: [{ status: (data.status || 'Pending') as PrismaTaskStatus, reason: "Task created" }]
    },
    requiredSkills: data.requiredSkills,
    notes: data.notes,
  };

  if (data.deadline) createInput.deadline = new Date(data.deadline);
  
  if (data.vehicleId) {
    createInput.vehicle = { connect: { id: Number(data.vehicleId) } };
  }
  
  if (data.assignedEmployeeIds && Array.isArray(data.assignedEmployeeIds) && data.assignedEmployeeIds.length > 0) {
    createInput.assignedEmployees = {
      connect: data.assignedEmployeeIds.map((id: string | number) => ({ id: Number(id) }))
    };
  }
  
  if (data.subTasks && Array.isArray(data.subTasks)) {
    createInput.subTasks = {
      create: data.subTasks.map((st: any) => ({ title: st.title, completed: st.completed || false }))
    };
  }
  return createInput;
};

const processTaskUpdateData = (data: any): Prisma.TaskUpdateInput => {
  // Start with only non-relational, non-manual fields from data
  const updateInput: Prisma.TaskUpdateInput = {
    description: data.description,
    location: data.location,
    estimatedDuration: data.estimatedDuration,
    requiredSkills: data.requiredSkills,
    notes: data.notes,
    priority: data.priority ? data.priority as PrismaTaskPriority : undefined,
  };
  
  if (data.dateTime) updateInput.dateTime = new Date(data.dateTime);
  if (data.hasOwnProperty('deadline')) { // Check if deadline is explicitly passed
    updateInput.deadline = data.deadline ? new Date(data.deadline) : null;
  }

  if (data.hasOwnProperty('vehicleId')) { // Check if vehicleId is explicitly passed
    if (data.vehicleId === null) {
      updateInput.vehicle = { disconnect: true };
    } else if (data.vehicleId !== undefined) {
      updateInput.vehicle = { connect: { id: Number(data.vehicleId) } };
    }
  }

  if (data.hasOwnProperty('assignedEmployeeIds')) { // Check if assignedEmployeeIds is explicitly passed
    if (data.assignedEmployeeIds === null || (Array.isArray(data.assignedEmployeeIds) && data.assignedEmployeeIds.length === 0)) {
      updateInput.assignedEmployees = { set: [] }; // Disconnect all
    } else if (Array.isArray(data.assignedEmployeeIds) && data.assignedEmployeeIds.length > 0) {
      updateInput.assignedEmployees = { 
          set: data.assignedEmployeeIds.map((id: string | number) => ({ id: Number(id) }))
      };
    }
  }

  if (data.subTasks && Array.isArray(data.subTasks)) {
    updateInput.subTasks = {
      deleteMany: {}, 
      create: data.subTasks.map((st: any) => ({ title: st.title, completed: st.completed || false }))
    };
  }

  if (data.status && typeof data.status === 'string') { 
    updateInput.status = data.status as PrismaTaskStatus; 
  }

  // statusChangeReason is handled in the model, not directly in Prisma.TaskUpdateInput
  // It's passed separately to taskModel.updateTask

  return updateInput;
};


export const createTask = async (req: Request, res: Response): Promise<void> => {
  try {
    const taskData = processTaskData(req.body); 
    const newTask = await taskModel.createTask(taskData);
    if (newTask) {
      emitTaskChange(SOCKET_EVENTS.TASK_CREATED, newTask);
      res.status(201).json(newTask);
    } else {
      res.status(400).json({ message: 'Could not create task.' });
    }
  } catch (error: any) {
     if (error.message.includes('not found')) { 
        res.status(404).json({ message: error.message });
    } else {
        res.status(500).json({ message: 'Error creating task', error: error.message });
    }
  }
};

export const getAllTasks = async (req: Request, res: Response): Promise<void> => {
  try {
    const tasks = await taskModel.getAllTasks();
    res.status(200).json(tasks);
  } catch (error: any) {
    res.status(500).json({ message: 'Error fetching tasks', error: error.message });
  }
};

export const getTaskById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const task = await taskModel.getTaskById(id);
    if (task) {
      res.status(200).json(task);
    } else {
      res.status(404).json({ message: 'Task not found' });
    }
  } catch (error: any) {
    res.status(500).json({ message: 'Error fetching task', error: error.message });
  }
};

export const updateTask = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const taskDataForProcessing = req.body; 
    const prismaUpdateInput = processTaskUpdateData(taskDataForProcessing);
    
    const updatedTask = await taskModel.updateTask(id, prismaUpdateInput, taskDataForProcessing.statusChangeReason);
    
    if (updatedTask) {
      emitTaskChange(SOCKET_EVENTS.TASK_UPDATED, updatedTask);
      res.status(200).json(updatedTask);
    } else {
      res.status(404).json({ message: 'Task not found or could not be updated.' });
    }
  } catch (error: any) {
    if (error.message.includes('not found')) { 
        res.status(404).json({ message: error.message });
    } else {
        res.status(500).json({ message: 'Error updating task', error: error.message });
    }
  }
};

export const deleteTask = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const deletedTask = await taskModel.deleteTask(id);
    if (deletedTask) {
      emitTaskChange(SOCKET_EVENTS.TASK_DELETED, { id });
      res.status(200).json({ message: 'Task deleted successfully', task: deletedTask });
    } else {
      res.status(404).json({ message: 'Task not found or could not be deleted' });
    }
  } catch (error: any) {
    res.status(500).json({ message: 'Error deleting task', error: error.message });
  }
};
