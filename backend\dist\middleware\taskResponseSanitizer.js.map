{"version": 3, "file": "taskResponseSanitizer.js", "sourceRoot": "", "sources": ["../../src/middleware/taskResponseSanitizer.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAIH,oDAAoD;AACpD,IAAI,MAAW,CAAC;AAChB,IAAI,CAAC;IACJ,uCAAuC;IACvC,MAAM,EAAC,OAAO,EAAE,SAAS,EAAC,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACxD,MAAM,GAAG,SAAS,CAAC;AACpB,CAAC;AAAC,OAAO,KAAK,EAAE,CAAC;IAChB,iDAAiD;IACjD,MAAM,GAAG;QACR,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,KAAK,EAAE,OAAO,CAAC,KAAK;KACpB,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,kBAAkB,GAAG,CAC1B,IAAS,EACT,cAA+C,EAAC,IAAI,EAAE,SAAS,EAAC,EAC1D,EAAE;IACR,IAAI,CAAC,IAAI;QAAE,OAAO,IAAI,CAAC;IAEvB,4DAA4D;IAC5D,MAAM,aAAa,GAAG,EAAC,GAAG,IAAI,EAAC,CAAC;IAChC,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,IAAI,WAAW,CAAC,MAAM,IAAI,SAAS,CAAC;IAE1D,8CAA8C;IAC9C,IACC,CAAC,aAAa,CAAC,iBAAiB;QAChC,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,EAC9C,CAAC;QACF,MAAM,aAAa,GAClB,aAAa,CAAC,iBAAiB,KAAK,IAAI;YACvC,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,OAAO,aAAa,CAAC,iBAAiB,CAAC;QAE3C,MAAM,CAAC,IAAI,CACV,mEAAmE,aAAa,qBAAqB;YACpG,YAAY,MAAM,WAAW,WAAW,CAAC,IAAI,GAAG,CACjD,CAAC;QACF,aAAa,CAAC,iBAAiB,GAAG,EAAE,CAAC;IACtC,CAAC;IAED,2CAA2C;IAC3C,IACC,CAAC,aAAa,CAAC,cAAc;QAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC,EAC3C,CAAC;QACF,MAAM,aAAa,GAClB,aAAa,CAAC,cAAc,KAAK,IAAI;YACpC,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,OAAO,aAAa,CAAC,cAAc,CAAC;QAExC,MAAM,CAAC,IAAI,CACV,gEAAgE,aAAa,qBAAqB;YACjG,YAAY,MAAM,WAAW,WAAW,CAAC,IAAI,GAAG,CACjD,CAAC;QACF,aAAa,CAAC,cAAc,GAAG,EAAE,CAAC;IACnC,CAAC;IAED,qCAAqC;IACrC,IAAI,CAAC,aAAa,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;QACvE,MAAM,aAAa,GAClB,aAAa,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,aAAa,CAAC,QAAQ,CAAC;QAE1E,MAAM,CAAC,IAAI,CACV,0DAA0D,aAAa,qBAAqB;YAC3F,YAAY,MAAM,WAAW,WAAW,CAAC,IAAI,GAAG,CACjD,CAAC;QACF,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAC;IAC7B,CAAC;IAED,0CAA0C;IAC1C,IACC,CAAC,aAAa,CAAC,aAAa;QAC5B,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,EAC1C,CAAC;QACF,MAAM,aAAa,GAClB,aAAa,CAAC,aAAa,KAAK,IAAI;YACnC,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,OAAO,aAAa,CAAC,aAAa,CAAC;QAEvC,MAAM,CAAC,IAAI,CACV,+DAA+D,aAAa,qBAAqB;YAChG,YAAY,MAAM,WAAW,WAAW,CAAC,IAAI,GAAG,CACjD,CAAC;QACF,aAAa,CAAC,aAAa,GAAG,EAAE,CAAC;IAClC,CAAC;IAED,OAAO,aAAa,CAAC;AACtB,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CACnC,GAAY,EACZ,GAAa,EACb,IAAkB,EACX,EAAE;IACT,qCAAqC;IACrC,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;IAE9B,yCAAyC;IACzC,MAAM,WAAW,GAAG;QACnB,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,SAAS;KAClC,CAAC;IAEF,kDAAkD;IAClD,GAAG,CAAC,IAAI,GAAG,UAAU,IAAS;QAC7B,IAAI,CAAC;YACJ,uCAAuC;YACvC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,kBAAkB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;YAClE,CAAC;YACD,mCAAmC;iBAC9B,IACJ,IAAI;gBACJ,OAAO,IAAI,KAAK,QAAQ;gBACxB,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,iBAAiB,CAAC,EAC3C,CAAC;gBACF,IAAI,GAAG,kBAAkB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YAC9C,CAAC;QACF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,MAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACpE,wDAAwD;QACzD,CAAC;QAED,wDAAwD;QACxD,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC,CAAC;IAEF,IAAI,EAAE,CAAC;AACR,CAAC,CAAC"}