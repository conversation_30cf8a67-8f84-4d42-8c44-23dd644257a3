import * as z from 'zod';

export const DelegationStatusSchema = z.enum([
	'Planned',
	'Confirmed',
	'In_Progress', // Changed from 'In Progress' to match backend
	'Completed',
	'Cancelled',
	'No_details', // Changed from 'No details' to match backend
]);

export const StatusHistoryEntrySchema = z.object({
	id: z.string().uuid(),
	status: DelegationStatusSchema,
	changedAt: z
		.string()
		.datetime({message: 'Invalid date/time format for status change'}),
	reason: z.string().optional(),
});

export const DelegateSchema = z.object({
	id: z.string().uuid().optional(), // Optional for new delegates, will be generated
	name: z.string().min(1, 'Delegate name is required'),
	title: z.string().min(1, 'Delegate title is required'),
	notes: z.string().optional(),
});

export const FlightDetailsSchema = z.preprocess(
	// First preprocess to handle the entire object being null/undefined
	(val) => {
		// If the value is null or undefined, return null
		if (val === null || val === undefined) {
			return null;
		}

		// If it's an object, check if all required fields are empty
		if (typeof val === 'object') {
			const obj = val as any;
			const isEmpty =
				(!obj.flightNumber || obj.flightNumber.trim() === '') &&
				(!obj.dateTime || obj.dateTime.trim() === '') &&
				(!obj.airport || obj.airport.trim() === '');

			if (isEmpty) {
				return null;
			}
		}

		// Otherwise return the value as is
		return val;
	},
	z
		.object({
			flightNumber: z.preprocess(
				(val) => val ?? '', // Convert null/undefined to empty string
				z.string().min(1, 'Flight number is required')
			),
			// Ensure dateTime is a non-empty string and a valid date
			dateTime: z.preprocess(
				(val) => val ?? '', // Convert null/undefined to empty string
				z
					.string()
					.min(1, 'Date & Time is required') // Explicitly require non-empty
					.refine((val) => !isNaN(Date.parse(val)), {
						// Then check if it's a valid date
						message: 'Invalid date & time format',
					})
			),
			airport: z.preprocess(
				(val) => val ?? '', // Convert null/undefined to empty string
				z.string().min(1, 'Airport is required')
			),
			terminal: z
				.string()
				.trim()
				.nullable()
				.optional()
				.transform((val) => (val === '' ? null : val)), // Store empty string as null
			notes: z
				.string()
				.trim()
				.nullable()
				.optional()
				.transform((val) => (val === '' ? null : val)), // Store empty string as null
		})
		.nullable()
		.optional()
);

export const DelegationFormSchema = z
	.object({
		eventName: z.string().min(1, 'Event name is required'),
		location: z.string().min(1, 'Location is required'),
		durationFrom: z.string().refine((val) => !isNaN(Date.parse(val)), {
			message: 'Invalid start date',
		}),
		durationTo: z
			.string()
			.refine((val) => !isNaN(Date.parse(val)), {message: 'Invalid end date'}),
		invitationFrom: z.string().optional(),
		invitationTo: z.string().optional(),
		delegates: z
			.array(DelegateSchema)
			.min(1, 'At least one delegate is required'),
		flightArrivalDetails: FlightDetailsSchema.nullable().optional(),
		flightDepartureDetails: FlightDetailsSchema.nullable().optional(),
		status: DelegationStatusSchema.default('Planned'),
		notes: z.string().optional(),
		imageUrl: z
			.string()
			.url('Invalid URL for image')
			.optional()
			.or(z.literal('')),
		statusChangeReason: z.string().optional(),
	})
	.superRefine((data, ctx) => {
		if (new Date(data.durationFrom) > new Date(data.durationTo)) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'End date cannot be earlier than start date',
				path: ['durationTo'],
			});
		}

		// The detailed checks for partial filling of flight details are now largely handled
		// by the stricter FlightDetailsSchema fields using preprocess and min(1)/refine.
		// If flightArrivalDetails or flightDepartureDetails is an object, Zod will enforce
		// that its fields (flightNumber, dateTime, airport) are validly filled.
		// The .nullable().optional() on flightArrivalDetails/flightDepartureDetails in this schema
		// allows these entire objects to be null or undefined if no flight info is provided.

		// Example: if user provides arrival details but not departure.
		// data.flightArrivalDetails would be an object, validated by FlightDetailsSchema.
		// data.flightDepartureDetails would be null, which is allowed.
	});

export type DelegationFormData = z.infer<typeof DelegationFormSchema>;
