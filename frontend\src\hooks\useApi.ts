'use client';

import { useState, useEffect, useCallback } from 'react';
import { withRetry, RetryOptions } from '@/lib/utils/apiUtils';

/**
 * State for the useApi hook
 */
export interface ApiState<T> {
  /** The data returned from the API */
  data: T | null;
  /** Whether the API call is currently loading */
  isLoading: boolean;
  /** Error message, if any */
  error: string | null;
  /** Function to manually refetch the data */
  refetch: () => Promise<void>;
}

/**
 * Options for the useApi hook
 */
export interface UseApiOptions<T> extends RetryOptions {
  /** Initial data to use before the API call completes */
  initialData?: T | null;
  /** Whether to fetch data automatically on mount */
  autoFetch?: boolean;
  /** Dependencies array for refetching (similar to useEffect dependencies) */
  deps?: any[];
  /** Callback function to run when data is successfully fetched */
  onSuccess?: (data: T) => void;
  /** Callback function to run when an error occurs */
  onError?: (error: any) => void;
}

/**
 * Custom hook for making API calls with loading states, error handling, and retry logic
 * @param fetchFn Function that returns a promise with the API call
 * @param options Configuration options
 */
export function useApi<T>(
  fetchFn: () => Promise<T>,
  options: UseApiOptions<T> = {}
): ApiState<T> {
  const {
    initialData = null,
    autoFetch = true,
    deps = [],
    onSuccess,
    onError,
    ...retryOptions
  } = options;

  const [data, setData] = useState<T | null>(initialData);
  const [isLoading, setIsLoading] = useState<boolean>(autoFetch);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Use retry logic for the API call
      const result = await withRetry(() => fetchFn(), retryOptions);
      setData(result);
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(errorMessage);
      
      // Call onError callback if provided
      if (onError) {
        onError(err);
      }
      
      // Log the error
      console.error('API call failed:', err);
    } finally {
      setIsLoading(false);
    }
  }, [fetchFn, ...deps]);

  // Fetch data on mount or when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [fetchData, autoFetch]);

  return {
    data,
    isLoading,
    error,
    refetch: fetchData,
  };
}

/**
 * Custom hook for making paginated API calls
 */
export interface PaginatedApiState<T> extends ApiState<T> {
  /** Current page number */
  page: number;
  /** Total number of pages */
  totalPages: number;
  /** Total number of items */
  totalItems: number;
  /** Function to go to the next page */
  nextPage: () => void;
  /** Function to go to the previous page */
  prevPage: () => void;
  /** Function to go to a specific page */
  goToPage: (page: number) => void;
  /** Whether there is a next page */
  hasNextPage: boolean;
  /** Whether there is a previous page */
  hasPrevPage: boolean;
}

/**
 * Options for the usePaginatedApi hook
 */
export interface UsePaginatedApiOptions<T> extends UseApiOptions<T> {
  /** Initial page number */
  initialPage?: number;
  /** Number of items per page */
  pageSize?: number;
}

/**
 * Custom hook for making paginated API calls
 * @param fetchFn Function that returns a promise with the API call, taking page and pageSize as parameters
 * @param options Configuration options
 */
export function usePaginatedApi<T>(
  fetchFn: (page: number, pageSize: number) => Promise<{
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>,
  options: UsePaginatedApiOptions<T> = {}
): PaginatedApiState<T[]> {
  const {
    initialPage = 1,
    pageSize = 10,
    ...apiOptions
  } = options;

  const [page, setPage] = useState<number>(initialPage);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);

  // Create a fetch function that includes pagination parameters
  const paginatedFetchFn = useCallback(() => {
    return fetchFn(page, pageSize).then(response => {
      setTotalPages(response.pagination.totalPages);
      setTotalItems(response.pagination.total);
      return response.data;
    });
  }, [fetchFn, page, pageSize]);

  // Use the base useApi hook with our paginated fetch function
  const apiState = useApi<T[]>(paginatedFetchFn, {
    ...apiOptions,
    deps: [...(apiOptions.deps || []), page, pageSize],
  });

  // Navigation functions
  const nextPage = useCallback(() => {
    if (page < totalPages) {
      setPage(prev => prev + 1);
    }
  }, [page, totalPages]);

  const prevPage = useCallback(() => {
    if (page > 1) {
      setPage(prev => prev - 1);
    }
  }, [page]);

  const goToPage = useCallback((newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  }, [totalPages]);

  return {
    ...apiState,
    page,
    totalPages,
    totalItems,
    nextPage,
    prevPage,
    goToPage,
    hasNextPage: page < totalPages,
    hasPrevPage: page > 1,
  };
}
