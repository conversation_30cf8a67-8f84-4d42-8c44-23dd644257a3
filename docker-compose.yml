services:
  # PostgreSQL Database
  db:
    image: postgres:14
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=workhub_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5

  # Backend API service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: workhub-backend
    ports:
      - '3001:3001'
    env_file:
      - ./backend/.env
    environment:
      - NODE_ENV=production
      - PORT=3001
      # Use the .env file for database configuration
      # - DATABASE_URL=${DATABASE_URL:-**************************************/workhub_db}
      - USE_SUPABASE=true
      # FRONTEND_URL is loaded from the root .env file

      # OpenSky Network API doesn't require an API key for basic access
    depends_on:
      db:
        condition: service_healthy
        required: false # Makes the dependency optional
    restart: unless-stopped

  # Frontend web application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    image: workhub-frontend
    ports:
      - '3000:3000'
    env_file:
      - ./frontend/.env.local
      - ./frontend/.env
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=http://backend:3001/api
    depends_on:
      - backend
    restart: unless-stopped
  pgadmin:
    image: dpage/pgadmin4
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - '5050:80'
    depends_on:
      - db
    volumes:
      - pgadmin_data:/var/lib/pgadmin

volumes:
  postgres_data:
  pgadmin_data:
