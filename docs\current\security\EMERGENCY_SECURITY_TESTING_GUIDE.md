# 🚨 WorkHub Emergency Security Testing Guide

**Version:** 1.0 - Emergency Implementation Testing **Date:** January 23, 2025
**Status:** Phase 0 Emergency Security Implementation Complete **Last Updated:**
2025-01-23 12:36:00 UTC

## 📋 Prerequisites

### **Required Tools:**

- **curl** (for API testing) or **Postman**
- **Web browser** (Chrome/Firefox recommended)
- **Database client** (optional - for direct SQL testing)
- **Node.js** and **npm** (for running servers)

### **Environment Setup:**

```bash
# Backend server running
cd backend && npm run dev
# Expected: Server running on http://localhost:3001

# Frontend server running
cd frontend && npm run dev
# Expected: Frontend running on http://localhost:3000
```

### **Test Credentials (Placeholder Environment):**

```
Supabase URL: https://abcdefgh12345.supabase.co
Admin Email: <EMAIL>
Admin Password: WorkHub2025!Emergency
Test User Email: <EMAIL>
Test User Password: TestPassword123!

Anon API Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************.1a2b3c4d5e6f7g8h9i0j
```

---

## 🔐 Backend API Authentication Testing

### **Test 1: Unauthenticated Access (Should Fail)**

**Purpose:** Verify all API endpoints require authentication

```bash
# Test employee endpoint without token
curl -X GET http://localhost:3001/api/employees \
  -H "Content-Type: application/json"

# Expected Response:
{
  "error": "Access token required",
  "code": "NO_TOKEN",
  "message": "Please provide a valid authorization token in the format: Bearer <token>"
}
```

```bash
# Test vehicle endpoint without token
curl -X GET http://localhost:3001/api/vehicles \
  -H "Content-Type: application/json"

# Expected Response:
{
  "error": "Access token required",
  "code": "NO_TOKEN"
}
```

### **Test 2: Invalid Token Access (Should Fail)**

```bash
# Test with invalid token
curl -X GET http://localhost:3001/api/employees \
  -H "Authorization: Bearer invalid_token_here" \
  -H "Content-Type: application/json"

# Expected Response:
{
  "error": "Invalid or expired token",
  "code": "INVALID_TOKEN",
  "message": "The provided token is invalid, expired, or malformed"
}
```

### **Test 3: Authentication Test Endpoint**

```bash
# Test authentication endpoint (with placeholder credentials - will fail)
curl -X GET http://localhost:3001/api/auth/test \
  -H "Authorization: Bearer placeholder_jwt_token" \
  -H "Content-Type: application/json"

# Expected Response (Placeholder Environment):
{
  "error": "Invalid or expired token",
  "code": "INVALID_TOKEN"
}

# Expected Response (Real Environment with Valid Token):
{
  "message": "✅ EMERGENCY SECURITY: Authentication working correctly",
  "user": {
    "id": "user-uuid-here",
    "email": "<EMAIL>",
    "role": "SUPER_ADMIN"
  },
  "timestamp": "2025-01-23T12:36:00.000Z"
}
```

### **Test 4: Role-Based Access Control**

```bash
# Test admin-only endpoint with USER token (should fail)
curl -X POST http://localhost:3001/api/employees \
  -H "Authorization: Bearer USER_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Employee","email":"<EMAIL>"}'

# Expected Response:
{
  "error": "Insufficient permissions",
  "code": "INSUFFICIENT_PERMISSIONS",
  "message": "Your role does not have permission to perform this action"
}
```

```bash
# Test manager endpoint with USER token (should fail)
curl -X DELETE http://localhost:3001/api/vehicles/1 \
  -H "Authorization: Bearer USER_JWT_TOKEN" \
  -H "Content-Type: application/json"

# Expected Response:
{
  "error": "Insufficient permissions",
  "code": "INSUFFICIENT_PERMISSIONS"
}
```

---

## 🌐 Frontend Authentication Testing

### **Test 5: Authentication Test Page Access**

**Steps:**

1. **Navigate to:** `http://localhost:3000/auth-test`
2. **Expected Initial State:**
   - Page loads with emergency security branding
   - Loading spinner shows: "🚨 Verifying security credentials..."
   - Login form displays after loading completes

### **Test 6: Login Form Testing (Placeholder Environment)**

**Steps:**

1. **Enter credentials:**
   - Email: `<EMAIL>`
   - Password: `TestPassword123!`
2. **Click "Sign In"**
3. **Expected Behavior:**
   - Form validation passes ✅
   - Loading state shows: "Signing In..." with spinner
   - Network request fails (expected with placeholder credentials)
   - Error message displays: "An unexpected error occurred during sign in"

### **Test 7: Protected Route Behavior**

**Steps:**

1. **Without authentication:** Navigate to protected routes
2. **Expected Behavior:**
   - ProtectedRoute component blocks access
   - Redirects to login or shows authentication required message
   - Loading states display properly

### **Test 8: User Profile Component (With Real Authentication)**

**Steps (Real Environment Only):**

1. **Successfully authenticate** with real credentials
2. **Navigate to auth test page**
3. **Expected Display:**
   - User avatar with initials
   - Email address displayed
   - Role badge (USER/MANAGER/ADMIN/SUPER_ADMIN)
   - Account verification status
   - Emergency security status indicators

---

## 🗄️ Database RLS Security Testing

### **Test 9: Verify RLS Status**

```sql
-- Connect to Supabase database and run:
SELECT
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY tablename;

-- Expected Results: All tables show rls_enabled = true
```

```sql
-- Check RLS policies exist
SELECT
    schemaname,
    tablename,
    policyname,
    cmd,
    roles
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- Expected: 4+ policies per table (SELECT, INSERT, UPDATE, DELETE)
```

### **Test 10: Anonymous Access Blocked**

```bash
# Test direct Supabase REST API (anonymous)
curl -X GET "https://abcdefgh12345.supabase.co/rest/v1/Employee" \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************.1a2b3c4d5e6f7g8h9i0j" \
  -H "Content-Type: application/json"

# Expected Response (RLS Working):
{
  "code": "42501",
  "details": "Results contain 0 rows due to Row Level Security",
  "hint": null,
  "message": "permission denied for table Employee"
}

# SECURITY BREACH if returns actual data!
```

### **Test 11: Role-Based Data Filtering**

```sql
-- Test with authenticated user context
-- (This requires actual Supabase connection)

-- Set user context (example)
SELECT set_config('request.jwt.claims', '{"sub":"user-id","role":"USER"}', true);

-- Test data access
SELECT * FROM public."Employee";
-- Expected: Only user's own employee record or filtered results
```

---

## ✅ Expected Responses Reference

### **Success Responses:**

```json
// Authentication Test Success
{
  "message": "✅ EMERGENCY SECURITY: Authentication working correctly",
  "user": { "id": "...", "email": "...", "role": "..." },
  "timestamp": "2025-01-23T..."
}

// Successful Data Access
{
  "data": [...],
  "message": "Data retrieved successfully"
}
```

### **Security Error Responses:**

```json
// No Token
{
  "error": "Access token required",
  "code": "NO_TOKEN",
  "message": "Please provide a valid authorization token..."
}

// Invalid Token
{
  "error": "Invalid or expired token",
  "code": "INVALID_TOKEN",
  "message": "The provided token is invalid, expired, or malformed"
}

// Insufficient Permissions
{
  "error": "Insufficient permissions",
  "code": "INSUFFICIENT_PERMISSIONS",
  "message": "Your role does not have permission to perform this action"
}

// RLS Permission Denied
{
  "code": "42501",
  "message": "permission denied for table TableName"
}
```

---

## 🔧 Troubleshooting

### **Common Issues & Solutions:**

#### **Issue: "Cannot connect to backend server"**

```bash
# Solution: Verify backend is running
cd backend && npm run dev
# Check: http://localhost:3001/api/health
```

#### **Issue: "Supabase connection failed"**

```bash
# Expected with placeholder credentials
# Solution: Replace with real Supabase credentials in .env files
```

#### **Issue: "RLS policies not working"**

```sql
-- Solution: Re-run RLS migration
\i backend/supabase/migrations/EMERGENCY_enable_rls.sql
```

#### **Issue: "Frontend auth components not loading"**

```bash
# Solution: Verify frontend dependencies
cd frontend && npm install
npm run dev
```

#### **Issue: "CORS errors in browser"**

```bash
# Solution: Check FRONTEND_URL in backend .env
# Should include: http://localhost:3000
```

### **Environment-Specific Testing:**

#### **Placeholder Credentials Environment:**

- ✅ **Authentication middleware** should reject all tokens
- ✅ **Frontend login** should show network errors
- ✅ **API endpoints** should require authentication
- ✅ **RLS policies** should be enabled (verifiable via SQL)

#### **Real Credentials Environment:**

- ✅ **Authentication flow** should work end-to-end
- ✅ **Role-based access** should be enforced
- ✅ **Data filtering** should work based on user roles
- ✅ **All security measures** should be fully functional

---

## 🚨 Security Breach Indicators

### **CRITICAL: Immediate Action Required If Any Occur**

#### **Database-Level Security Failures:**

- ❌ **Supabase REST API returns actual data** to anonymous requests
- ❌ **HTTP 200 responses with real database records** instead of permission
  denied
- ❌ **Any table shows "RLS disabled"** in Supabase Studio Dashboard
- ❌ **Anonymous access returns data arrays** instead of error codes

#### **Backend API Security Failures:**

- ❌ **API endpoints allow access** without JWT Bearer tokens
- ❌ **Invalid tokens return data** instead of 401/403 errors
- ❌ **Role restrictions not enforced** (USER can delete employees)
- ❌ **Authentication middleware bypassed** on any route

#### **Frontend Security Failures:**

- ❌ **Protected routes accessible** without authentication
- ❌ **Authentication state not properly managed**
- ❌ **User roles not enforced** in UI components
- ❌ **Sensitive data displayed** to unauthorized users

### **Immediate Response Protocol:**

1. **🛑 STOP all testing immediately**
2. **🔄 Re-run emergency RLS migration script**
3. **✅ Verify all tables show "RLS Enabled" in Supabase Studio**
4. **🧪 Re-test anonymous access returns permission denied**
5. **⚠️ Do not proceed until all breaches resolved**

---

## 📊 Testing Scenarios Summary

### **Placeholder Credentials Testing (Development):**

| Test                       | Expected Result        | Status |
| -------------------------- | ---------------------- | ------ |
| Unauthenticated API access | ❌ NO_TOKEN error      | ✅     |
| Invalid token API access   | ❌ INVALID_TOKEN error | ✅     |
| Frontend login attempt     | ❌ Network error       | ✅     |
| Anonymous database access  | ❌ Permission denied   | ✅     |
| RLS policies enabled       | ✅ All tables secured  | ✅     |

### **Real Credentials Testing (Production-Ready):**

| Test                   | Expected Result           | Status |
| ---------------------- | ------------------------- | ------ |
| Valid authentication   | ✅ User logged in         | 🔄     |
| Role-based API access  | ✅ Permissions enforced   | 🔄     |
| Data filtering by role | ✅ Appropriate data shown | 🔄     |
| End-to-end auth flow   | ✅ Complete functionality | 🔄     |
| Security audit clean   | ✅ No vulnerabilities     | 🔄     |

---

## 📝 Test Execution Checklist

### **Pre-Testing Setup:**

- [ ] Backend server running on port 3001
- [ ] Frontend server running on port 3000
- [ ] Environment variables configured
- [ ] Database connection established
- [ ] Testing tools available (curl, browser)

### **Security Testing Execution:**

- [ ] **Test 1-4:** Backend API authentication tests
- [ ] **Test 5-8:** Frontend authentication tests
- [ ] **Test 9-11:** Database RLS security tests
- [ ] **Troubleshooting:** Verify common issues resolved
- [ ] **Breach Check:** Confirm no security indicators present

### **Post-Testing Verification:**

- [ ] All authentication endpoints secured
- [ ] Role-based access control working
- [ ] Database RLS policies active
- [ ] Frontend auth flow complete
- [ ] Documentation updated with results

---

**🔒 EMERGENCY SECURITY TESTING COMPLETE** **Next Steps:** Proceed to Day 2
Afternoon production deployment testing **Security Status:** ✅ Phase 0
Emergency Implementation Verified
