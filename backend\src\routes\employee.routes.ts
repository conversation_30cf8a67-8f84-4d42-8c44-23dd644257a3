import {Router} from 'express';
import * as employeeController from '../controllers/employee.controller.js';
import {validate} from '../middleware/validation.js';
import {
	authenticateSupabaseUser,
	requireRole,
} from '../middleware/supabaseAuth.js';
import {
	validateRequest,
	sanitizeInput,
	addValidationSecurityHeaders,
} from '../middleware/inputValidation.js';
import {employeeSchemas} from '../schemas/validation.js';
import {apiRateLimit} from '../middleware/rateLimiting.js';
import {
	employeeCreateSchema,
	employeeUpdateSchema,
	employeeIdSchema,
} from '../schemas/employee.schema.js';

const router = Router();

// PHASE 1 SECURITY HARDENING: Apply API rate limiting to all employee routes
router.use(apiRateLimit);

// 🚨 EMERGENCY SECURITY: All employee routes require authentication

// POST /api/employees - Create a new employee (ADMIN only)
// PHASE 1 SECURITY HARDENING: Enhanced input validation and sanitization
router.post(
	'/',
	authenticateSupabaseUser,
	requireRole(['ADMIN', 'SUPER_ADMIN']),
	validateRequest(employeeSchemas.create),
	(req, res, next) => {
		addValidationSecurityHeaders(res);
		next();
	},
	employeeController.createEmployee
);

// GET /api/employees - Get all employees (MANAGER+ can see all, USER sees limited)
// PHASE 1 SECURITY HARDENING: Enhanced input validation for query parameters
router.get(
	'/',
	authenticateSupabaseUser,
	validateRequest(employeeSchemas.list),
	(req, res, next) => {
		addValidationSecurityHeaders(res);
		next();
	},
	employeeController.getAllEmployees
);

// GET /api/employees/enriched - Get all employees enriched with vehicle information (MANAGER+)
router.get(
	'/enriched',
	authenticateSupabaseUser,
	requireRole(['MANAGER', 'ADMIN', 'SUPER_ADMIN']),
	sanitizeInput,
	(req, res, next) => {
		addValidationSecurityHeaders(res);
		next();
	},
	employeeController.getEnrichedEmployees
);

// GET /api/employees/:id - Get a specific employee by ID (authenticated users)
// PHASE 1 SECURITY HARDENING: Enhanced input validation for parameters
router.get(
	'/:id',
	authenticateSupabaseUser,
	validateRequest(employeeSchemas.getById),
	(req, res, next) => {
		addValidationSecurityHeaders(res);
		next();
	},
	employeeController.getEmployeeById
);

// PUT /api/employees/:id - Update a specific employee by ID (MANAGER+ or own record)
// PHASE 1 SECURITY HARDENING: Enhanced input validation for update operations
router.put(
	'/:id',
	authenticateSupabaseUser,
	validateRequest(employeeSchemas.update),
	(req, res, next) => {
		addValidationSecurityHeaders(res);
		next();
	},
	employeeController.updateEmployee
);

// DELETE /api/employees/:id - Delete a specific employee by ID (ADMIN only)
// PHASE 1 SECURITY HARDENING: Enhanced input validation for delete operations
router.delete(
	'/:id',
	authenticateSupabaseUser,
	requireRole(['ADMIN', 'SUPER_ADMIN']),
	validateRequest(employeeSchemas.getById),
	(req, res, next) => {
		addValidationSecurityHeaders(res);
		next();
	},
	employeeController.deleteEmployee
);

export default router;
