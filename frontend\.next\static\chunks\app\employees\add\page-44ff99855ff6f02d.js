(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[548],{9700:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(95155),i=a(11612),s=a(2730),l=a(35695),o=a(95647);let d=(0,a(40157).A)("UserRoundPlus",[["path",{d:"M2 21a8 8 0 0 1 13.292-6",key:"bjp14o"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M19 16v6",key:"tddt3s"}],["path",{d:"M22 19h-6",key:"vcuq98"}]]);var n=a(87481),c=a(12115);function u(){let e=(0,l.useRouter)(),{toast:t}=(0,n.dj)(),[a,u]=(0,c.useState)(!1),[p,y]=(0,c.useState)(null),m=async a=>{u(!0),y(null);try{await (0,s.addEmployee)(a),t({title:"Employee Added",description:'The employee "'.concat(a.fullName||a.name,'" has been successfully created.'),variant:"default"}),e.push("/employees")}catch(e){if(console.error("Error adding employee:",e),e.validationErrors&&Array.isArray(e.validationErrors)){let a=e.validationErrors.map(e=>"".concat(e.path,": ").concat(e.message)).join("\n");console.log("Validation errors details:",a),y("Validation failed: ".concat(a)),t({title:"Validation Error",description:"Please check the form for errors",variant:"destructive"})}else{let a=e.message||"Failed to add employee. Please try again.";y(a),t({title:"Error Adding Employee",description:a,variant:"destructive"})}}finally{u(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(o.z,{title:"Add New Employee",description:"Enter the details for the new employee.",icon:d}),p&&(0,r.jsx)("div",{className:"bg-destructive/20 p-3 rounded-md text-destructive text-sm",children:p}),(0,r.jsx)(i.A,{onSubmit:m,isEditing:!1,isLoading:a})]})}},74329:(e,t,a)=>{Promise.resolve().then(a.bind(a,9700))}},e=>{var t=t=>e(e.s=t);e.O(0,[5769,8360,832,2688,2512,1859,4066,8162,2730,3222,8441,1684,7358],()=>t(74329)),_N_E=e.O()}]);