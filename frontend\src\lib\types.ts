export interface ServiceRecord {
	id: string;
	vehicleId: number;
	date: string; // ISO date string, e.g., "2023-10-26"
	odometer: number;
	servicePerformed: string[];
	notes?: string;
	cost?: number;
	employeeId?: number;
	createdAt?: string;
	updatedAt?: string;
}

export interface ServiceRecordQueryParams {
	vehicleId?: string;
	startDate?: string;
	endDate?: string;
	limit?: number;
	offset?: number;
	sortBy?: 'date' | 'odometer' | 'cost';
	sortOrder?: 'asc' | 'desc';
}

export interface ServiceRecordResponse {
	status: 'success' | 'error';
	data?: ServiceRecord[];
	message?: string;
	errors?: Array<{
		field: string;
		message: string;
	}>;
	pagination?: {
		total: number;
		limit: number;
		offset: number;
		pages: number;
		currentPage: number;
	};
}

export interface Vehicle {
	id: number;
	make: string;
	model: string;
	year: number;
	vin: string;
	licensePlate: string;
	ownerName: string;
	ownerContact: string;
	color?: string;
	initialOdometer: number | null;
	imageUrl?: string;
	serviceHistory: ServiceRecord[];
	createdAt: string;
	updatedAt: string;
}

export interface EnrichedServiceRecord {
	id: string;
	vehicleId: string;
	date: string; // ISO date string, e.g., "2023-10-26"
	odometer: number;
	servicePerformed: string[];
	notes?: string;
	cost?: number;
	employeeId?: number;
	createdAt?: string;
	updatedAt?: string;
	vehicleMake: string;
	vehicleModel: string;
	vehicleYear: number;
	vehiclePlateNumber?: string;
}

export interface EnrichedEmployee extends Employee {
	assignedVehicleDetails?: {
		id: number;
		make: string;
		model: string;
		year: number;
		licensePlate: string;
		color?: string;
	} | null;
}

export type DelegationStatus =
	| 'Planned'
	| 'Confirmed'
	| 'In_Progress' // Changed from 'In Progress' to match backend
	| 'Completed'
	| 'Cancelled'
	| 'No_details'; // Changed from 'No details' to match backend

export type GenericStatus = DelegationStatus | TaskStatus | EmployeeStatus;

export interface StatusHistoryEntry {
	id: string;
	status: GenericStatus;
	changedAt: string; // ISO datetime string
	reason?: string;
}

export interface Delegate {
	id: string;
	name: string;
	title: string;
	notes?: string;
}

export interface FlightDetails {
	flightNumber: string;
	dateTime: string; // ISO datetime string
	airport: string;
	terminal?: string | null;
	notes?: string | null;
}

export interface Delegation {
	id: string;
	eventName: string;
	location: string;
	durationFrom: string; // ISO date string
	durationTo: string; // ISO date string
	invitationFrom?: string;
	invitationTo?: string;
	delegates: Delegate[];
	flightArrivalDetails?: FlightDetails | null;
	flightDepartureDetails?: FlightDetails | null;
	status: DelegationStatus;
	statusHistory: StatusHistoryEntry[];
	notes?: string;
	imageUrl?: string;
}

export type TaskStatus =
	| 'Pending'
	| 'Assigned'
	| 'In Progress'
	| 'Completed'
	| 'Cancelled';

// DriverAvailability is kept as it's used by Employee now for driver roles
export type DriverAvailability = 'On_Shift' | 'Off_Shift' | 'On_Break' | 'Busy';

export type TaskPriority = 'Low' | 'Medium' | 'High';

export interface Task {
	id: string;
	description: string;
	location: string;
	dateTime: string; // ISO datetime string
	estimatedDuration: number; // in minutes
	requiredSkills: string[];
	priority: TaskPriority;
	deadline?: string; // ISO datetime string
	status: TaskStatus;
	assignedTo: string[];
	notes?: string;
	vehicleId?: string | null;
	statusHistory: StatusHistoryEntry[];
	subTasks: SubTask[];
	createdAt: string;
	updatedAt: string;
	assignedEmployeeId?: string | null;
}

// Employee type is updated
export type EmployeeStatus = 'Active' | 'On Leave' | 'Terminated';
export type EmployeeRole =
	| 'driver'
	| 'mechanic'
	| 'administrator'
	| 'office_staff'
	| 'other';

export interface Employee {
	id: number;
	name: string;
	fullName: string;
	position: string;
	department: string;
	contactPhone?: string;
	contactMobile?: string;
	contactEmail: string;
	hireDate: string; // ISO date string
	status: EmployeeStatus;
	statusHistory: StatusHistoryEntry[];

	// New and merged fields
	role: EmployeeRole;
	availability?: DriverAvailability; // Optional, mainly for drivers
	currentLocation?: string; // Optional, mainly for drivers
	workingHours?: string; // Optional, mainly for drivers
	vehicleId?: string | null; // Optional, mainly for drivers
	assignedTasks: string[];

	skills: string[];
	shiftSchedule?: string;
	generalAssignments: string[]; // General assignments, not specific task system IDs
	notes?: string;
	profileImageUrl?: string;
	createdAt: string;
	updatedAt: string;
}

export interface SubTask {
	id: string;
	title: string;
	completed: boolean;
}

// The Driver interface is now removed as its fields are merged into Employee.
// export interface Driver { ... }
