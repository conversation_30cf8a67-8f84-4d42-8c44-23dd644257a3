# Supabase Backend-Only Integration Guide

This guide explains the secure backend-only approach to Supabase integration in the Car Service Tracking System application.

## Overview

Our application uses a secure backend-only approach for Supabase integration. This means:

1. Supabase credentials are stored **only** on the backend server
2. The frontend never directly connects to Supabase
3. All database operations go through our backend API
4. Real-time updates are bridged from Supabase to the frontend via WebSockets

This architecture provides better security while still maintaining real-time functionality.

## Architecture

```
┌─────────────┐         ┌─────────────┐         ┌─────────────┐
│             │  HTTP   │             │  HTTP   │             │
│   Frontend  │ ───────►│   Backend   │ ───────►│  Supabase   │
│             │         │             │         │             │
└──────▲──────┘         └──────┬──────┘         └─────────────┘
       │                       │
       │     WebSocket         │
       └───────────────────────┘
```

1. **Frontend to Backend**: Standard HTTP API requests
2. **Backend to Supabase**: Secure HTTP requests with credentials
3. **Real-time Updates**: Supabase → Backend → Frontend via WebSockets

## Configuration

### Backend Environment Variables

The backend requires the following environment variables to connect to Supabase:

```
# Supabase Configuration
USE_SUPABASE=true
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-anon-key
```

These should be added to your `.env` file in the backend directory.

### Finding Your Supabase Credentials

1. Go to your Supabase dashboard
2. Navigate to Project Settings > API
3. Copy the "Project URL" and "anon" public API key

## How It Works

### Database Service

The `database.service.ts` file initializes the Supabase client based on environment variables:

```typescript
// Initialize Supabase client if configured
let supabase: SupabaseClient | null = null;

const initializeSupabase = () => {
  const config = getDatabaseConfig();
  
  if (config.useSupabase && config.supabaseUrl && config.supabaseKey) {
    try {
      supabase = createClient(config.supabaseUrl, config.supabaseKey);
      logger.info('Supabase client initialized');
      return supabase;
    } catch (error) {
      logger.error('Failed to initialize Supabase client:', error);
      return null;
    }
  }
  
  return null;
};
```

### Real-time Updates

The `supabase-realtime.service.ts` file bridges Supabase real-time events to Socket.io:

```typescript
// Subscribe to vehicle changes
const vehiclesChannel = supabase
  .channel('vehicles-changes')
  .on(
    'postgres_changes',
    { event: 'INSERT', schema: 'public', table: 'vehicles' },
    (payload) => {
      logger.info('Vehicle created:', payload.new.id);
      this.io?.emit(SOCKET_EVENTS.VEHICLE_CREATED, payload.new);
      this.io?.emit(SOCKET_EVENTS.REFRESH_VEHICLES);
    }
  )
  // ... other event handlers
  .subscribe();
```

### Socket.io Integration

The `server.ts` file initializes the Supabase real-time service with Socket.io:

```typescript
// Initialize Supabase real-time service if Supabase is configured
const dbConfig = getDatabaseConfig();
if (dbConfig.useSupabase) {
  logger.info('Initializing Supabase real-time service...');
  supabaseRealtimeService.initialize(io);
} else {
  logger.info('Supabase not configured, skipping real-time service initialization');
}
```

## Security Benefits

This backend-only approach provides several security benefits:

1. **No Exposed Credentials**: Supabase credentials are never exposed in client-side code
2. **Centralized Access Control**: All database access is controlled by the backend API
3. **Reduced Attack Surface**: The frontend can't directly manipulate the database
4. **Consistent Authorization**: All requests go through the same authorization flow

## Setting Up Supabase for Real-time

To enable real-time functionality in Supabase:

1. Go to your Supabase dashboard
2. Navigate to Database > Replication
3. Enable the "Realtime" option for the tables you want to track (vehicles, employees, etc.)
4. Set up appropriate RLS policies for the tables

## Testing WebSocket Connection

To test the WebSocket connection with Supabase events:

1. Start your backend and frontend applications
2. Open your browser's developer tools and check the console for WebSocket connection messages
3. Make a change to a vehicle or employee through the API
4. Verify that the frontend receives the real-time update via WebSocket

You can also use the following code in your browser console to test the connection:

```javascript
// Get the socket instance
const socket = io('http://localhost:3001');

// Listen for vehicle updates
socket.on('vehicle:updated', (data) => {
  console.log('Vehicle updated:', data);
});

// Listen for employee updates
socket.on('employee:updated', (data) => {
  console.log('Employee updated:', data);
});
```

## Troubleshooting

### WebSocket Connection Issues

If you're having trouble with WebSocket connections:

1. Check that your backend is running and accessible
2. Verify that CORS is properly configured for your frontend domain
3. Check the backend logs for any WebSocket or Supabase connection errors
4. Ensure that Supabase real-time is enabled for your tables

### Supabase Connection Issues

If the backend can't connect to Supabase:

1. Verify your Supabase URL and anon key in the backend `.env` file
2. Check that your Supabase project is active and not in maintenance mode
3. Ensure your IP address is not blocked by Supabase
4. Check the backend logs for specific Supabase connection errors

## Production Deployment

For production deployment:

1. Set the environment variables securely in your hosting platform
2. Ensure your WebSocket server is properly configured for production
3. Set up appropriate RLS policies in Supabase for production use
4. Consider using a service role key instead of anon key for backend operations

## Conclusion

This backend-only approach to Supabase integration provides a secure and scalable solution for your application. By keeping all Supabase interactions on the backend and using WebSockets for real-time updates, you get the best of both worlds: security and real-time functionality.
