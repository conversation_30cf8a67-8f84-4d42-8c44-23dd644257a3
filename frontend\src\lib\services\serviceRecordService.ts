/**
 * Service Record API service
 */
import {api, ApiRequestOptions, extractApiData} from './apiService';
import {
	ServiceRecord,
	ServiceRecordQueryParams,
	EnrichedServiceRecord,
} from '../types';
import {ApiResponse} from '../types/api';

// Service Record response types
export interface ServiceRecordResponse extends ApiResponse<ServiceRecord> {}
export interface ServiceRecordsResponse extends ApiResponse<ServiceRecord[]> {}
export interface EnrichedServiceRecordsResponse
	extends ApiResponse<EnrichedServiceRecord[]> {}

// Service Record create/update input types
export interface ServiceRecordCreateInput {
	vehicleId: number;
	date: string; // ISO date string
	odometer: number;
	servicePerformed: string[];
	notes?: string;
	cost?: number;
	employeeId?: number;
}

export interface ServiceRecordUpdateInput {
	date?: string; // ISO date string
	odometer?: number;
	servicePerformed?: string[];
	notes?: string;
	cost?: number;
	employeeId?: number;
}

/**
 * Get all service records with optional filtering
 */
export async function getAllServiceRecords(
	params?: ServiceRecordQueryParams,
	options?: ApiRequestOptions
): Promise<ServiceRecord[]> {
	// Build URL with query parameters
	let url = '/servicerecords';
	if (params) {
		const queryParams = new URLSearchParams();
		Object.entries(params).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				queryParams.append(key, String(value));
			}
		});
		const queryString = queryParams.toString();
		if (queryString) {
			url += `?${queryString}`;
		}
	}

	const response = await api.get<ServiceRecord[] | ServiceRecordsResponse>(
		url,
		options
	);
	return extractApiData<ServiceRecord[]>(response, url) || [];
}

/**
 * Get all service records enriched with vehicle information
 */
export async function getAllEnrichedServiceRecords(
	options?: ApiRequestOptions
): Promise<EnrichedServiceRecord[]> {
	try {
		const response = await api.get<
			EnrichedServiceRecord[] | EnrichedServiceRecordsResponse
		>('/servicerecords/enriched', options);
		return (
			extractApiData<EnrichedServiceRecord[]>(
				response,
				'/servicerecords/enriched'
			) || []
		);
	} catch (error) {
		console.error('Error fetching enriched service records:', error);
		throw error;
	}
}

/**
 * Get service records for a specific vehicle
 */
export async function getServiceRecords(
	vehicleId: number,
	options?: ApiRequestOptions
): Promise<ServiceRecord[]> {
	try {
		const endpoint = `/vehicles/${vehicleId}/servicerecords`;
		const response = await api.get<ServiceRecord[] | ServiceRecordsResponse>(
			endpoint,
			options
		);
		return extractApiData<ServiceRecord[]>(response, endpoint) || [];
	} catch (error) {
		console.error(
			`Error fetching service records for vehicle ${vehicleId}:`,
			error
		);
		throw error;
	}
}

/**
 * Get enriched service records for a specific vehicle
 */
export async function getVehicleServiceRecords(
	vehicleId: number,
	options?: ApiRequestOptions
): Promise<EnrichedServiceRecord[]> {
	try {
		// First try to get from the enriched endpoint with vehicle filter
		try {
			const allRecords = await getAllEnrichedServiceRecords(options);
			return allRecords.filter(
				(record) => Number(record.vehicleId) === vehicleId
			);
		} catch (error) {
			console.warn(
				'Falling back to manual enrichment for vehicle service records',
				error
			);

			// If that fails, get regular service records and enrich them manually
			const serviceRecords = await getServiceRecords(vehicleId, options);
			const vehicle = await import('../store').then((m) =>
				m.getVehicleById(vehicleId)
			);

			if (!vehicle) {
				throw new Error(`Vehicle with ID ${vehicleId} not found`);
			}

			// Manually enrich the records
			return serviceRecords.map((record) => ({
				...record,
				vehicleId: String(record.vehicleId),
				vehicleMake: vehicle.make,
				vehicleModel: vehicle.model,
				vehicleYear: vehicle.year,
				vehiclePlateNumber: vehicle.licensePlate || undefined,
			}));
		}
	} catch (error) {
		console.error(
			`Error fetching enriched service records for vehicle ${vehicleId}:`,
			error
		);
		throw error;
	}
}

/**
 * Get a specific service record by ID
 */
export async function getServiceRecordById(
	vehicleId: number,
	serviceRecordId: string,
	options?: ApiRequestOptions
): Promise<ServiceRecord | null> {
	try {
		const endpoint = `/vehicles/${vehicleId}/servicerecords/${serviceRecordId}`;
		const response = await api.get<ServiceRecord | ServiceRecordResponse>(
			endpoint,
			options
		);
		return extractApiData<ServiceRecord>(response, endpoint) || null;
	} catch (error) {
		// Return null for 404 errors, rethrow others
		if (
			error instanceof Error &&
			'status' in error &&
			(error as any).status === 404
		) {
			console.warn(`Service record with ID ${serviceRecordId} not found`);
			return null;
		}
		throw error;
	}
}

/**
 * Get a service record by ID directly (without vehicle context)
 */
export async function getServiceRecordByIdDirect(
	serviceRecordId: string,
	options?: ApiRequestOptions
): Promise<ServiceRecord | null> {
	try {
		const endpoint = `/servicerecords/${serviceRecordId}`;
		const response = await api.get<ServiceRecord | ServiceRecordResponse>(
			endpoint,
			options
		);
		return extractApiData<ServiceRecord>(response, endpoint) || null;
	} catch (error) {
		// Return null for 404 errors, rethrow others
		if (
			error instanceof Error &&
			'status' in error &&
			(error as any).status === 404
		) {
			console.warn(`Service record with ID ${serviceRecordId} not found`);
			return null;
		}
		throw error;
	}
}

/**
 * Create a new service record
 */
export async function createServiceRecord(
	vehicleId: number,
	data: ServiceRecordCreateInput,
	options?: ApiRequestOptions
): Promise<ServiceRecord> {
	const endpoint = `/vehicles/${vehicleId}/servicerecords`;
	const response = await api.post<ServiceRecord | ServiceRecordResponse>(
		endpoint,
		data,
		options
	);
	const result = extractApiData<ServiceRecord>(response, endpoint);
	if (!result) {
		throw new Error(
			'Failed to create service record: No data returned from API'
		);
	}
	return result;
}

/**
 * Update a service record
 */
export async function updateServiceRecord(
	vehicleId: number,
	serviceRecordId: string,
	data: ServiceRecordUpdateInput,
	options?: ApiRequestOptions
): Promise<ServiceRecord> {
	const endpoint = `/vehicles/${vehicleId}/servicerecords/${serviceRecordId}`;
	const response = await api.put<ServiceRecord | ServiceRecordResponse>(
		endpoint,
		data,
		options
	);
	const result = extractApiData<ServiceRecord>(response, endpoint);
	if (!result) {
		throw new Error(
			`Failed to update service record ${serviceRecordId}: No data returned from API`
		);
	}
	return result;
}

/**
 * Update a service record directly (without vehicle context)
 */
export async function updateServiceRecordDirect(
	serviceRecordId: string,
	data: ServiceRecordUpdateInput,
	options?: ApiRequestOptions
): Promise<ServiceRecord> {
	const endpoint = `/servicerecords/${serviceRecordId}`;
	const response = await api.put<ServiceRecord | ServiceRecordResponse>(
		endpoint,
		data,
		options
	);
	const result = extractApiData<ServiceRecord>(response, endpoint);
	if (!result) {
		throw new Error(
			`Failed to update service record ${serviceRecordId}: No data returned from API`
		);
	}
	return result;
}

/**
 * Delete a service record
 */
export async function deleteServiceRecord(
	vehicleId: number,
	serviceRecordId: string,
	options?: ApiRequestOptions
): Promise<void> {
	await api.delete<ApiResponse<void>>(
		`/vehicles/${vehicleId}/servicerecords/${serviceRecordId}`,
		options
	);
}

/**
 * Delete a service record directly (without vehicle context)
 */
export async function deleteServiceRecordDirect(
	serviceRecordId: string,
	options?: ApiRequestOptions
): Promise<void> {
	await api.delete<ApiResponse<void>>(
		`/servicerecords/${serviceRecordId}`,
		options
	);
}
