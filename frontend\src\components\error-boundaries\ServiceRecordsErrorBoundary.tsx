'use client';

import React, {ReactNode} from 'react';
import ErrorBoundary from './ErrorBoundary';

interface ServiceRecordsErrorBoundaryProps {
	children: ReactNode;
	fallback?: ReactNode;
}

/**
 * Specialized error boundary for service records components
 * Uses the generic ErrorBoundary with service records specific messaging
 */
const ServiceRecordsErrorBoundary: React.FC<
	ServiceRecordsErrorBoundaryProps
> = ({children, fallback}) => {
	return (
		<ErrorBoundary
			title='Error Loading Service Records'
			description='An unexpected error occurred while loading service records.'
			resetLabel='Try Again'
			fallback={fallback}
			onError={(error, errorInfo) => {
				// Log the error with service records context
				console.error('ServiceRecords component error:', error);
				console.error('Component stack:', errorInfo.componentStack);

				// In a production app, you would send this to a monitoring service
				// Example: errorReportingService.captureError(error, {
				//   context: 'ServiceRecords',
				//   componentStack: errorInfo.componentStack
				// });
			}}>
			{children}
		</ErrorBoundary>
	);
};

export default ServiceRecordsErrorBoundary;
