"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3222],{11612:(e,l,r)=>{r.d(l,{A:()=>v});var t=r(95155),a=r(12115),s=r(35695),n=r(62177),i=r(90221),o=r(20636),d=r(33450),c=r(2730),m=r(30285),u=r(62523),p=r(88539),x=r(66695),h=r(59409),f=r(22346),j=r(17759),g=r(87481);let v=e=>{let{onSubmit:l,initialData:r={},isEditing:v=!1,submitButtonText:y=v?"Save Changes":"Create Employee",isLoading:b=!1}=e,N=(0,s.useRouter)(),{toast:I}=(0,g.dj)(),[w,C]=(0,a.useState)([]),S=(0,n.mN)({resolver:(0,i.u)(o.gT),defaultValues:{name:(null==r?void 0:r.name)||(null==r?void 0:r.fullName)||"",fullName:(null==r?void 0:r.fullName)||(null==r?void 0:r.name)||"",employeeId:(null==r?void 0:r.employeeId)||"",position:(null==r?void 0:r.position)||"",department:(null==r?void 0:r.department)||"",contactInfo:(null==r?void 0:r.contactInfo)||(null==r?void 0:r.contactEmail)||"",contactEmail:(null==r?void 0:r.contactEmail)||"",contactPhone:(null==r?void 0:r.contactPhone)||"",contactMobile:(null==r?void 0:r.contactMobile)||"",hireDate:(null==r?void 0:r.hireDate)?new Date(r.hireDate).toISOString().split("T")[0]:"",status:(null==r?void 0:r.status)||"Active",role:(null==r?void 0:r.role)||"other",availability:(null==r?void 0:r.availability)||null,currentLocation:(null==r?void 0:r.currentLocation)||"",workingHours:(null==r?void 0:r.workingHours)||"",assignedVehicleId:(null==r?void 0:r.assignedVehicleId)?Number(r.assignedVehicleId):(null==r?void 0:r.vehicleId)?Number(r.vehicleId):null,skills:(null==r?void 0:r.skills)||[],shiftSchedule:(null==r?void 0:r.shiftSchedule)||"",generalAssignments:(null==r?void 0:r.generalAssignments)||[],notes:(null==r?void 0:r.notes)||"",profileImageUrl:(null==r?void 0:r.profileImageUrl)||""}}),{reset:R,control:A,handleSubmit:M,watch:k,formState:{errors:D,isSubmitting:O}}=S,E=JSON.stringify(r);(0,a.useEffect)(()=>{v&&r&&Object.keys(r).length>0&&R({name:r.name||r.fullName||"",fullName:r.fullName||r.name||"",employeeId:r.employeeId||"",position:r.position||"",department:r.department||"",contactInfo:r.contactInfo||r.contactEmail||"",contactEmail:r.contactEmail||"",contactPhone:r.contactPhone||"",contactMobile:r.contactMobile||"",hireDate:r.hireDate?new Date(r.hireDate).toISOString().split("T")[0]:"",status:r.status||"Active",role:r.role||"other",availability:r.availability||null,currentLocation:r.currentLocation||"",workingHours:r.workingHours||"",assignedVehicleId:r.assignedVehicleId?Number(r.assignedVehicleId):(null==r?void 0:r.vehicleId)?Number(r.vehicleId):null,skills:r.skills||[],shiftSchedule:r.shiftSchedule||"",generalAssignments:r.generalAssignments||[],notes:r.notes||"",profileImageUrl:r.profileImageUrl||""})},[E,v,R]),(0,a.useEffect)(()=>{(async()=>{try{let e=await (0,c.getVehicles)();C(e||[])}catch(e){console.error("Failed to fetch vehicles for employee form:",e),I({title:"Warning",description:"Could not load vehicles for assignment.",variant:"default"})}})()},[I]);let T=k("role"),B=async e=>{let r={...e,assignedVehicleId:e.assignedVehicleId?Number(e.assignedVehicleId):null,skills:Array.isArray(e.skills)?e.skills.map(String):"string"==typeof e.skills?e.skills.split(",").map(e=>e.trim()).filter(Boolean):[],generalAssignments:Array.isArray(e.generalAssignments)?e.generalAssignments.map(String):"string"==typeof e.generalAssignments?e.generalAssignments.split(",").map(e=>e.trim()).filter(Boolean):[]};await l(r)};return(0,t.jsx)(j.lV,{...S,children:(0,t.jsx)("form",{onSubmit:M(B),children:(0,t.jsxs)(x.Zp,{className:"max-w-3xl mx-auto",children:[(0,t.jsxs)(x.aR,{children:[(0,t.jsx)(x.ZB,{children:v?"Edit Employee":"Add New Employee"}),(0,t.jsx)(x.BT,{children:v?"Update the details of the employee.":"Enter the details for the new employee."})]}),(0,t.jsxs)(x.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(j.zB,{control:A,name:"name",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Display Name"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., Jane D.",...l})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(j.zB,{control:A,name:"fullName",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Full Name (Optional)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., Jane Marie Doe",...l})}),(0,t.jsx)(j.C5,{})]})}})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(j.zB,{control:A,name:"employeeId",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Employee ID (Business Key)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., EMP12345",...l})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(j.zB,{control:A,name:"position",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Position/Title"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., Senior Mechanic",...l})}),(0,t.jsx)(j.C5,{})]})}})]}),(0,t.jsx)(j.zB,{control:A,name:"department",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Department"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., Maintenance",...l})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(f.w,{className:"my-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Contact Information"}),(0,t.jsx)(j.zB,{control:A,name:"contactInfo",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Primary Contact (Email/Phone)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., <EMAIL> or 555-0101",...l})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(j.zB,{control:A,name:"contactEmail",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Contact Email (Optional)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{type:"email",placeholder:"e.g., <EMAIL>",...l,value:l.value||""})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(j.zB,{control:A,name:"contactPhone",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Contact Phone (Optional)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{type:"tel",placeholder:"e.g., 555-0102",...l,value:l.value||""})}),(0,t.jsx)(j.C5,{})]})}})]}),(0,t.jsx)(j.zB,{control:A,name:"contactMobile",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Contact Mobile (Optional)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{type:"tel",placeholder:"e.g., 555-0103",...l,value:l.value||""})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(f.w,{className:"my-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Employment Details"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(j.zB,{control:A,name:"hireDate",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Hire Date"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{type:"date",...l})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(j.zB,{control:A,name:"status",render:e=>{var l;let{field:r}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Status"}),(0,t.jsxs)(h.l6,{onValueChange:r.onChange,value:null!=(l=r.value)?l:void 0,children:[(0,t.jsx)(j.MJ,{children:(0,t.jsx)(h.bq,{id:"status",children:(0,t.jsx)(h.yv,{placeholder:"Select status"})})}),(0,t.jsx)(h.gC,{children:o.O2.options.map(e=>(0,t.jsx)(h.eb,{value:e,children:e},e))})]}),(0,t.jsx)(j.C5,{})]})}})]}),(0,t.jsx)(f.w,{className:"my-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Role & Availability"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(j.zB,{control:A,name:"role",render:e=>{var l;let{field:r}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Role"}),(0,t.jsxs)(h.l6,{onValueChange:r.onChange,value:null!=(l=r.value)?l:void 0,children:[(0,t.jsx)(j.MJ,{children:(0,t.jsx)(h.bq,{id:"role",children:(0,t.jsx)(h.yv,{placeholder:"Select role"})})}),(0,t.jsx)(h.gC,{children:o.Q.options.map(e=>(0,t.jsx)(h.eb,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1).replace("_"," ")},e))})]}),(0,t.jsx)(j.C5,{})]})}}),"driver"===T&&(0,t.jsx)(j.zB,{control:A,name:"availability",render:e=>{var l;let{field:r}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Availability (for Drivers)"}),(0,t.jsxs)(h.l6,{onValueChange:r.onChange,value:null!=(l=r.value)?l:void 0,children:[(0,t.jsx)(j.MJ,{children:(0,t.jsx)(h.bq,{id:"availability",children:(0,t.jsx)(h.yv,{placeholder:"Select availability"})})}),(0,t.jsx)(h.gC,{children:d.X.options.map(e=>(0,t.jsx)(h.eb,{value:e,children:e.replace("_"," ")},e))})]}),(0,t.jsx)(j.C5,{})]})}})]}),"driver"===T&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(j.zB,{control:A,name:"currentLocation",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Current Location (Optional, for Drivers)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., City, State or GPS link",...l,value:l.value||""})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(j.zB,{control:A,name:"workingHours",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Working Hours (Optional, for Drivers)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., Mon-Fri 9am-5pm",...l,value:l.value||""})}),(0,t.jsx)(j.C5,{})]})}})]}),(0,t.jsx)(j.zB,{control:A,name:"assignedVehicleId",render:e=>{var l;let{field:r}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Assigned Vehicle (Optional, for Drivers)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsxs)(h.l6,{onValueChange:e=>r.onChange("null"===e?null:parseInt(e,10)),value:null===r.value?"null":String(null!=(l=r.value)?l:""),children:[(0,t.jsx)(j.MJ,{children:(0,t.jsx)(h.bq,{id:"assignedVehicleId",children:(0,t.jsx)(h.yv,{placeholder:"Select vehicle (optional)"})})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"null",children:"No Vehicle"}),w.map(e=>(0,t.jsxs)(h.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,") - ",e.licensePlate]},e.id))]})]})}),(0,t.jsx)(j.C5,{})]})}})]}),(0,t.jsx)(f.w,{className:"my-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Other Details"}),(0,t.jsx)(j.zB,{control:A,name:"skills",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Skills (comma-separated)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., Diesel Engine Repair, HVAC Systems, Welding",value:Array.isArray(l.value)?l.value.join(", "):"string"==typeof l.value?l.value:"",onChange:e=>{let r=e.target.value.split(",").map(e=>e.trim()).filter(Boolean);l.onChange(r)}})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(j.zB,{control:A,name:"shiftSchedule",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Shift Schedule (Optional)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., Mon-Wed 8am-4pm, Thu-Fri 10am-6pm",...l,value:l.value||""})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(j.zB,{control:A,name:"generalAssignments",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"General Assignments (comma-separated, Optional)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"e.g., Workshop Cleanup, Inventory Check",value:Array.isArray(l.value)?l.value.join(", "):"string"==typeof l.value?l.value:"",onChange:e=>{let r=e.target.value.split(",").map(e=>e.trim()).filter(Boolean);l.onChange(r)}})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(j.zB,{control:A,name:"profileImageUrl",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Profile Image URL (Optional)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(u.p,{placeholder:"https://example.com/profile.png",...l,value:l.value||""})}),(0,t.jsx)(j.C5,{})]})}}),(0,t.jsx)(j.zB,{control:A,name:"notes",render:e=>{let{field:l}=e;return(0,t.jsxs)(j.eI,{children:[(0,t.jsx)(j.lR,{children:"Notes (Optional)"}),(0,t.jsx)(j.MJ,{children:(0,t.jsx)(p.T,{placeholder:"Any additional notes about the employee...",...l,value:l.value||""})}),(0,t.jsx)(j.C5,{})]})}})]}),(0,t.jsxs)(x.wL,{className:"flex justify-end space-x-3 pt-6",children:[(0,t.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>N.back(),disabled:S.formState.isSubmitting||b,children:"Cancel"}),(0,t.jsx)(m.$,{type:"submit",disabled:S.formState.isSubmitting||b,children:S.formState.isSubmitting||b?"Processing...":y})]})]})})})}},17759:(e,l,r)=>{r.d(l,{C5:()=>j,MJ:()=>f,eI:()=>x,lR:()=>h,lV:()=>d,zB:()=>m});var t=r(95155),a=r(12115),s=r(99708),n=r(62177),i=r(59434),o=r(85057);let d=n.Op,c=a.createContext({}),m=e=>{let{...l}=e;return(0,t.jsx)(c.Provider,{value:{name:l.name},children:(0,t.jsx)(n.xI,{...l})})},u=()=>{let e=a.useContext(c),l=a.useContext(p),{getFieldState:r,formState:t}=(0,n.xW)(),s=r(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=l;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...s}},p=a.createContext({}),x=a.forwardRef((e,l)=>{let{className:r,...s}=e,n=a.useId();return(0,t.jsx)(p.Provider,{value:{id:n},children:(0,t.jsx)("div",{ref:l,className:(0,i.cn)("space-y-2",r),...s})})});x.displayName="FormItem";let h=a.forwardRef((e,l)=>{let{className:r,...a}=e,{error:s,formItemId:n}=u();return(0,t.jsx)(o.J,{ref:l,className:(0,i.cn)(s&&"text-destructive",r),htmlFor:n,...a})});h.displayName="FormLabel";let f=a.forwardRef((e,l)=>{let{...r}=e,{error:a,formItemId:n,formDescriptionId:i,formMessageId:o}=u();return(0,t.jsx)(s.DX,{ref:l,id:n,"aria-describedby":a?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!a,...r})});f.displayName="FormControl",a.forwardRef((e,l)=>{let{className:r,...a}=e,{formDescriptionId:s}=u();return(0,t.jsx)("p",{ref:l,id:s,className:(0,i.cn)("text-sm text-muted-foreground",r),...a})}).displayName="FormDescription";let j=a.forwardRef((e,l)=>{var r;let{className:a,children:s,...n}=e,{error:o,formMessageId:d}=u(),c=o?String(null!=(r=null==o?void 0:o.message)?r:""):s;return c?(0,t.jsx)("p",{ref:l,id:d,className:(0,i.cn)("text-sm font-medium text-destructive",a),...n,children:c}):null});j.displayName="FormMessage"},20636:(e,l,r)=>{r.d(l,{O2:()=>s,Q:()=>n,gT:()=>i});var t=r(55594),a=r(33450);let s=t.k5(["Active","On Leave","Terminated","Inactive"]),n=t.k5(["driver","mechanic","administrator","office_staff","manager","service_advisor","technician","other"]),i=t.Ik({name:t.Yj().min(1,"Name is required"),fullName:t.Yj().min(1,"Full name is required").optional(),employeeId:t.Yj().min(1,"Employee ID (unique business ID) is required"),position:t.Yj().min(1,"Position/Title is required"),department:t.Yj().min(1,"Department is required"),contactInfo:t.Yj().min(1,"Primary contact (email or phone) is required").refine(e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)||/^[\d\s()+-]{7,20}$/.test(e),"Must be a valid email or phone number."),contactEmail:t.Yj().email("Invalid email address").optional().nullable().or(t.eu("")),contactPhone:t.Yj().optional().nullable().or(t.eu("")),contactMobile:t.Yj().optional().nullable().or(t.eu("")),hireDate:t.Yj().refine(e=>e&&!isNaN(Date.parse(e)),{message:"Invalid hire date"}),status:s.default("Active"),role:n.default("other"),availability:a.X.optional().nullable(),currentLocation:t.Yj().optional().or(t.eu("")),workingHours:t.Yj().optional().or(t.eu("")),assignedVehicleId:t.ai().int().positive().nullable().optional(),skills:t.YO(t.Yj()).optional().default([]),shiftSchedule:t.Yj().optional().or(t.eu("")),generalAssignments:t.YO(t.Yj()).optional().default([]),notes:t.Yj().optional().or(t.eu("")),profileImageUrl:t.Yj().url("Invalid URL for profile image").optional().or(t.eu("")),statusChangeReason:t.Yj().optional().nullable()})},22346:(e,l,r)=>{r.d(l,{w:()=>i});var t=r(95155),a=r(12115),s=r(87489),n=r(59434);let i=a.forwardRef((e,l)=>{let{className:r,orientation:a="horizontal",decorative:i=!0,...o}=e;return(0,t.jsx)(s.b,{ref:l,decorative:i,orientation:a,className:(0,n.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...o})});i.displayName=s.b.displayName},33450:(e,l,r)=>{r.d(l,{X:()=>t});let t=r(55594).k5(["On_Shift","Off_Shift","On_Break","Busy"])},59409:(e,l,r)=>{r.d(l,{bq:()=>u,eb:()=>f,gC:()=>h,l6:()=>c,yv:()=>m});var t=r(95155),a=r(12115),s=r(31992),n=r(79556),i=r(77381),o=r(10518),d=r(59434);let c=s.bL;s.YJ;let m=s.WT,u=a.forwardRef((e,l)=>{let{className:r,children:a,...i}=e;return(0,t.jsxs)(s.l9,{ref:l,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...i,children:[a,(0,t.jsx)(s.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=s.l9.displayName;let p=a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)(s.PP,{ref:l,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})});p.displayName=s.PP.displayName;let x=a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)(s.wn,{ref:l,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=s.wn.displayName;let h=a.forwardRef((e,l)=>{let{className:r,children:a,position:n="popper",...i}=e;return(0,t.jsx)(s.ZL,{children:(0,t.jsxs)(s.UC,{ref:l,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:n,...i,children:[(0,t.jsx)(p,{}),(0,t.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,t.jsx)(x,{})]})})});h.displayName=s.UC.displayName,a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)(s.JU,{ref:l,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...a})}).displayName=s.JU.displayName;let f=a.forwardRef((e,l)=>{let{className:r,children:a,...n}=e;return(0,t.jsxs)(s.q7,{ref:l,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(s.VF,{children:(0,t.jsx)(o.A,{className:"h-4 w-4"})})}),(0,t.jsx)(s.p4,{children:a})]})});f.displayName=s.q7.displayName,a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)(s.wv,{ref:l,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...a})}).displayName=s.wv.displayName},62523:(e,l,r)=>{r.d(l,{p:()=>n});var t=r(95155),a=r(12115),s=r(59434);let n=a.forwardRef((e,l)=>{let{className:r,type:a,...n}=e;return(0,t.jsx)("input",{type:a,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:l,...n})});n.displayName="Input"},66695:(e,l,r)=>{r.d(l,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>m});var t=r(95155),a=r(12115),s=r(59434);let n=a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:l,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card";let i=a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:l,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let o=a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:l,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:l,className:(0,s.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:l,className:(0,s.cn)("p-6 pt-0",r),...a})});c.displayName="CardContent";let m=a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:l,className:(0,s.cn)("flex items-center p-6 pt-0",r),...a})});m.displayName="CardFooter"},85057:(e,l,r)=>{r.d(l,{J:()=>d});var t=r(95155),a=r(12115),s=r(40968),n=r(74466),i=r(59434);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)(s.b,{ref:l,className:(0,i.cn)(o(),r),...a})});d.displayName=s.b.displayName},87481:(e,l,r)=>{r.d(l,{dj:()=>u});var t=r(12115);let a=0,s=new Map,n=e=>{if(s.has(e))return;let l=setTimeout(()=>{s.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,l)},i=(e,l)=>{switch(l.type){case"ADD_TOAST":return{...e,toasts:[l.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===l.toast.id?{...e,...l.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=l;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===l.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==l.toastId)}}},o=[],d={toasts:[]};function c(e){d=i(d,e),o.forEach(e=>{e(d)})}function m(e){let{...l}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),t=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...l,id:r,open:!0,onOpenChange:e=>{e||t()}}}),{id:r,dismiss:t,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function u(){let[e,l]=t.useState(d);return t.useEffect(()=>(o.push(l),()=>{let e=o.indexOf(l);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},87489:(e,l,r)=>{r.d(l,{b:()=>d});var t=r(12115),a=r(63655),s=r(95155),n="horizontal",i=["horizontal","vertical"],o=t.forwardRef((e,l)=>{var r;let{decorative:t,orientation:o=n,...d}=e,c=(r=o,i.includes(r))?o:n;return(0,s.jsx)(a.sG.div,{"data-orientation":c,...t?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:l})});o.displayName="Separator";var d=o},88539:(e,l,r)=>{r.d(l,{T:()=>n});var t=r(95155),a=r(12115),s=r(59434);let n=a.forwardRef((e,l)=>{let{className:r,...a}=e;return(0,t.jsx)("textarea",{className:(0,s.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:l,...a})});n.displayName="Textarea"},95647:(e,l,r)=>{r.d(l,{z:()=>a});var t=r(95155);function a(e){let{title:l,description:r,icon:a,children:s}=e;return(0,t.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[a&&(0,t.jsx)(a,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:l})]}),r&&(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:r})]}),s&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:s})]})}r(12115)}}]);