import React, { ReactNode } from 'react';
import { Shield, Loader2, <PERSON><PERSON><PERSON>riangle } from 'lucide-react';
import { useAuthContext } from '../../contexts/AuthContext';
import { LoginForm } from './LoginForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Alert, AlertDescription } from '../ui/alert';

interface ProtectedRouteProps {
	children: ReactNode;
	fallback?: ReactNode;
	requireEmailVerification?: boolean;
	allowedRoles?: string[];
}

/**
 * EMERGENCY SECURITY COMPONENT - Protected Route
 * 
 * This component protects routes by requiring authentication.
 * It shows a login form if the user is not authenticated.
 * 
 * CRITICAL: This component is part of the emergency security implementation
 */
export function ProtectedRoute({ 
	children, 
	fallback,
	requireEmailVerification = true,
	allowedRoles = []
}: ProtectedRouteProps) {
	const { user, session, loading, error } = useAuthContext();

	// Show loading state while checking authentication
	if (loading) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50">
				<Card className="w-full max-w-md mx-auto">
					<CardContent className="flex flex-col items-center justify-center py-8">
						<Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-4" />
						<p className="text-sm text-muted-foreground">
							🚨 Verifying security credentials...
						</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Show error state if there's an authentication error
	if (error && !user) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
				<Card className="w-full max-w-md mx-auto">
					<CardHeader>
						<CardTitle className="flex items-center text-red-600">
							<AlertTriangle className="h-5 w-5 mr-2" />
							Authentication Error
						</CardTitle>
						<CardDescription>
							There was a problem with the security system
						</CardDescription>
					</CardHeader>
					<CardContent>
						<Alert variant="destructive">
							<AlertDescription>{error}</AlertDescription>
						</Alert>
						<div className="mt-4">
							<LoginForm />
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Show login form if user is not authenticated
	if (!user || !session) {
		if (fallback) {
			return <>{fallback}</>;
		}

		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
				<div className="w-full max-w-md">
					<LoginForm 
						onSuccess={() => {
							// The auth state will update automatically
							console.log('✅ User authenticated successfully');
						}}
					/>
				</div>
			</div>
		);
	}

	// Check email verification if required
	if (requireEmailVerification && !user.email_confirmed_at) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
				<Card className="w-full max-w-md mx-auto">
					<CardHeader>
						<CardTitle className="flex items-center text-yellow-600">
							<Shield className="h-5 w-5 mr-2" />
							Email Verification Required
						</CardTitle>
						<CardDescription>
							Please verify your email address to continue
						</CardDescription>
					</CardHeader>
					<CardContent>
						<Alert>
							<AlertTriangle className="h-4 w-4" />
							<AlertDescription>
								We've sent a verification email to <strong>{user.email}</strong>. 
								Please check your inbox and click the verification link to access the system.
							</AlertDescription>
						</Alert>
						<div className="mt-4 text-center">
							<p className="text-sm text-muted-foreground">
								Didn't receive the email? Check your spam folder or contact your administrator.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Check role-based access if roles are specified
	if (allowedRoles.length > 0) {
		const userRole = user.user_metadata?.role || 'USER';
		
		if (!allowedRoles.includes(userRole)) {
			return (
				<div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
					<Card className="w-full max-w-md mx-auto">
						<CardHeader>
							<CardTitle className="flex items-center text-red-600">
								<Shield className="h-5 w-5 mr-2" />
								Access Denied
							</CardTitle>
							<CardDescription>
								Insufficient permissions to access this resource
							</CardDescription>
						</CardHeader>
						<CardContent>
							<Alert variant="destructive">
								<AlertDescription>
									Your account ({userRole}) does not have permission to access this area. 
									Required roles: {allowedRoles.join(', ')}
								</AlertDescription>
							</Alert>
							<div className="mt-4 text-center">
								<p className="text-sm text-muted-foreground">
									Contact your administrator if you believe this is an error.
								</p>
							</div>
						</CardContent>
					</Card>
				</div>
			);
		}
	}

	// User is authenticated and authorized - render children
	return <>{children}</>;
}
