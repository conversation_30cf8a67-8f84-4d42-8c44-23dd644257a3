'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  text?: string;
  fullPage?: boolean;
}

/**
 * Loading spinner component with optional text
 * Can be used inline or as a full-page overlay
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className,
  text,
  fullPage = false,
}) => {
  // Size mappings
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
  };

  // Text size mappings
  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg',
  };

  const spinnerContent = (
    <div className={cn(
      'flex flex-col items-center justify-center',
      fullPage ? 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50' : '',
      className
    )}>
      <Loader2 className={cn(
        'animate-spin text-primary',
        sizeClasses[size]
      )} />
      {text && (
        <p className={cn(
          'mt-2 text-muted-foreground',
          textSizeClasses[size]
        )}>
          {text}
        </p>
      )}
    </div>
  );

  return spinnerContent;
};

export default LoadingSpinner;
