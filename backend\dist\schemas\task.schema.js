import { z } from 'zod';
export const TaskStatusEnum = z.enum([
    'Pending',
    'Assigned',
    'In_Progress',
    'Completed',
    'Cancelled',
]);
export const TaskPriorityEnum = z.enum(['Low', 'Medium', 'High']); // Exported
const SubTaskSchema = z.object({
    id: z.string().uuid().optional(),
    title: z.string().min(1, 'Subtask title cannot be empty'),
    completed: z.boolean().default(false),
});
/**
 * @openapi
 * components:
 *   schemas:
 *     SubTaskInput:
 *       type: object
 *       required: [title]
 *       properties:
 *         title: { type: string }
 *         completed: { type: boolean, default: false }
 *     TaskCreateInput:
 *       type: object
 *       required:
 *         - description
 *         - location
 *         - dateTime
 *         - estimatedDuration
 *       properties:
 *         description: { type: string }
 *         location: { type: string }
 *         dateTime: { type: string, format: date-time, description: "Start date and time of the task" }
 *         estimatedDuration: { type: integer, minimum: 1, description: "Duration in minutes" }
 *         requiredSkills: { type: array, items: { type: string }, default: [] }
 *         priority: { $ref: '#/components/schemas/TaskPriorityEnum', default: "Medium" }
 *         deadline: { type: string, format: date-time, nullable: true }
 *         status: { $ref: '#/components/schemas/TaskStatusEnum', default: "Pending" }
 *         notes: { type: string, nullable: true }
 *         vehicleId: { type: integer, nullable: true, description: "ID of the associated vehicle" }
 *         assignedEmployeeIds: { type: array, items: { type: integer }, default: [], description: "Array of Employee IDs to assign this task to" }
 *         subTasks: { type: array, items: { $ref: '#/components/schemas/SubTaskInput' }, default: [] }
 *     TaskUpdateInput:
 *       type: object
 *       properties:
 *         description: { type: string }
 *         location: { type: string }
 *         dateTime: { type: string, format: date-time }
 *         estimatedDuration: { type: integer, minimum: 1 }
 *         requiredSkills: { type: array, items: { type: string } }
 *         priority: { $ref: '#/components/schemas/TaskPriorityEnum' }
 *         deadline: { type: string, format: date-time, nullable: true }
 *         status: { $ref: '#/components/schemas/TaskStatusEnum' }
 *         statusChangeReason: { type: string, nullable: true, description: "Reason for status change, if status is updated." }
 *         notes: { type: string, nullable: true }
 *         vehicleId: { type: integer, nullable: true }
 *         assignedEmployeeIds: { type: array, items: { type: integer }, description: "Full new list of Employee IDs. Employees not in list will be unassigned." }
 *         subTasks: { type: array, items: { $ref: '#/components/schemas/SubTaskInput' }, description: "Full new list of subtasks. Existing subtasks not in the list might be removed or handled by specific update logic." }
 *     TaskStatusEnum:
 *       type: string
 *       enum: [Pending, Assigned, In_Progress, Completed, Cancelled]
 *     TaskPriorityEnum:
 *       type: string
 *       enum: [Low, Medium, High]
 *     SubTask:
 *       allOf:
 *         - $ref: '#/components/schemas/SubTaskInput'
 *         - type: object
 *           properties:
 *             id: { type: string, format: uuid }
 *     Task:
 *       allOf:
 *         - $ref: '#/components/schemas/TaskCreateInput'
 *         - type: object
 *           properties:
 *             id: { type: string, format: uuid }
 *             createdAt: { type: string, format: date-time }
 *             updatedAt: { type: string, format: date-time }
 *             statusHistory: { type: array, items: { $ref: '#/components/schemas/TaskStatusEntry' } }
 *             subTasks: { type: array, items: { $ref: '#/components/schemas/SubTask' } }
 *             assignedEmployees: { type: array, items: { $ref: '#/components/schemas/Employee' } }
 *             vehicle: { $ref: '#/components/schemas/Vehicle', nullable: true }
 *     TaskStatusEntry:
 *       type: object
 *       properties:
 *         id: { type: string, format: uuid }
 *         status: { $ref: '#/components/schemas/TaskStatusEnum' }
 *         changedAt: { type: string, format: date-time }
 *         reason: { type: string, nullable: true }
 */
export const taskCreateSchema = z.object({
    description: z.string().min(1, 'Task description is required'),
    location: z.string().min(1, 'Location is required'),
    dateTime: z.string().datetime({ message: "Invalid start date/time format" }),
    estimatedDuration: z.coerce.number().int().min(1, 'Estimated duration must be at least 1 minute'),
    requiredSkills: z.array(z.string()).optional().default([]),
    priority: TaskPriorityEnum.default('Medium'),
    deadline: z.string().datetime({ message: "Invalid deadline date/time format" }).optional().nullable(),
    status: TaskStatusEnum.default('Pending'),
    notes: z.string().optional().nullable(),
    vehicleId: z.number().int().positive("Vehicle ID must be a positive integer.").optional().nullable(),
    assignedEmployeeIds: z.array(z.number().int().positive("Employee ID must be a positive integer.")).optional().default([]),
    subTasks: z.array(SubTaskSchema.omit({ id: true })).optional().default([]),
});
export const taskUpdateSchema = taskCreateSchema.partial().extend({
    statusChangeReason: z.string().optional().nullable(),
});
export const taskIdSchema = z.object({
    id: z.string().uuid('Invalid Task ID format (must be UUID)'),
});
//# sourceMappingURL=task.schema.js.map