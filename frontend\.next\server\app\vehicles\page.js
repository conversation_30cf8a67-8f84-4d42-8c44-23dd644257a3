(()=>{var e={};e.id=1602,e.ids=[1602],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24501:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(82614).A)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30222:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\vehicles\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36069:(e,r,t)=>{Promise.resolve().then(t.bind(t,30222))},36767:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>A});var s=t(60687),i=t(43210),a=t(85814),l=t.n(a),o=t(24920),n=t(35265),c=t(77368),d=t(41936),u=t(30474),m=t(44493),h=t(68752),x=t(24501),p=t(29333),f=t(8760),g=t(35950);function v({vehicle:e}){let r=e.serviceHistory.length>0,t=null!==e.initialOdometer,i=r?Math.max(e.initialOdometer||0,...e.serviceHistory.map(e=>e.odometer)):e.initialOdometer||0;return(0,s.jsxs)(m.Zp,{className:"overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60",children:[(0,s.jsx)(m.aR,{className:"p-0 relative",children:(0,s.jsx)("div",{className:"aspect-[16/10] w-full relative",children:(0,s.jsx)(u.default,{src:e.imageUrl||`https://picsum.photos/seed/${e.id}/600/375`,alt:`${e.make} ${e.model}`,layout:"fill",objectFit:"cover",className:"bg-muted","data-ai-hint":"luxury car",priority:!0})})}),(0,s.jsxs)(m.Wu,{className:"p-5 flex-grow flex flex-col",children:[(0,s.jsxs)(m.ZB,{className:"text-xl font-semibold mb-1 text-primary",children:[e.make," ",e.model]}),(0,s.jsxs)(m.BT,{className:"text-sm text-muted-foreground mb-3",children:[e.year," ",e.color&&`• ${e.color}`," ",e.licensePlate&&`• Plate: ${e.licensePlate}`]}),(0,s.jsx)(g.w,{className:"my-3 bg-border/50"}),(0,s.jsxs)("div",{className:"space-y-2.5 text-sm text-foreground flex-grow",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Latest Odometer: "}),(0,s.jsx)("strong",{className:"font-semibold",children:r||t?`${i.toLocaleString()} miles`:"Not recorded"})]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Services Logged: "}),(0,s.jsx)("strong",{className:"font-semibold",children:e.serviceHistory.length})]})]})]})]}),(0,s.jsx)(m.wL,{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,s.jsx)(h.r,{actionType:"tertiary",className:"w-full",icon:(0,s.jsx)(f.A,{className:"h-4 w-4"}),asChild:!0,children:(0,s.jsx)(l(),{href:`/vehicles/${e.id}`,children:"Manage Vehicle"})})})]})}var j=t(28840),y=t(50933),b=t(73992);let w=({children:e})=>{let[r,t]=(0,i.useState)([]),[s,a]=(0,i.useState)(!0),[l,o]=(0,i.useState)(null),n=(0,i.useCallback)(async()=>{s&&a(!0);try{let e=await (0,j.getVehicles)();t(e),o(null)}catch(e){console.error("Error fetching vehicles:",e),o("Failed to fetch vehicles. Please try again.")}finally{s&&a(!1)}},[s]),{isRefreshing:c,refresh:d,isConnected:u,socketTriggered:m}=(0,y.a)(n,[b.A.VEHICLE_CREATED,b.A.VEHICLE_UPDATED,b.A.VEHICLE_DELETED,b.A.REFRESH_VEHICLES],{interval:3e4,enabled:!0,immediate:!0,enableSocket:!0,enablePolling:!0,onError:e=>{console.error("Socket/Polling refresh error:",e)}});return(0,i.useEffect)(()=>{s&&!m&&n()},[n,s,m]),e({vehicles:r,loading:s,error:l,handleDelete:async e=>{if(window.confirm("Are you sure you want to delete this vehicle?"))try{await (0,j.deleteVehicle)(e),t(r=>r.filter(r=>r.id!==e))}catch(e){throw console.error("Error deleting vehicle:",e),o("Failed to delete vehicle. Please try again."),e}},fetchVehicles:d,isRefreshing:c||m,isConnected:u,socketTriggered:m})};var N=t(48041),C=t(89667),k=t(29867),S=t(52027),E=t(95758);let P=()=>{let[e,r]=(0,i.useState)([]),[t,a]=(0,i.useState)([]),[u,m]=(0,i.useState)(""),{toast:x}=(0,k.dj)();return(0,s.jsx)(w,{children:({vehicles:x,loading:p,error:f,handleDelete:g,fetchVehicles:j,isRefreshing:y,isConnected:b,socketTriggered:w})=>((0,i.useEffect)(()=>{p||f||(r(x),a(x))},[x,p,f]),(0,i.useEffect)(()=>{let r=u.toLowerCase();a(e.filter(e=>e.make.toLowerCase().includes(r)||e.model.toLowerCase().includes(r)||e.year.toString().includes(r)||e.licensePlate&&e.licensePlate.toLowerCase().includes(r)||e.vin&&e.vin.toLowerCase().includes(r)||e.ownerName&&e.ownerName.toLowerCase().includes(r)))},[u,e]),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)(N.z,{title:"My Vehicle Fleet",description:"Manage, track, and gain insights into your vehicles.",icon:o.A,children:(0,s.jsx)("div",{className:"flex gap-2",children:(0,s.jsx)(h.r,{actionType:"primary",icon:(0,s.jsx)(n.A,{className:"h-4 w-4"}),asChild:!0,children:(0,s.jsx)(l(),{href:"/vehicles/new",children:"Add New Vehicle"})})})}),(0,s.jsxs)("div",{className:"mb-6 p-4 bg-card rounded-lg shadow relative",children:[(y||w)&&!p&&(0,s.jsxs)("div",{className:"absolute right-4 top-4 text-xs flex items-center text-muted-foreground",children:[(0,s.jsx)(c.A,{className:"h-3 w-3 mr-1 animate-spin"}),w?"Real-time update...":"Updating list..."]}),!p&&(0,s.jsxs)("div",{className:"absolute right-40 top-4 text-xs inline-flex items-center sm:right-24",children:[" ",(0,s.jsx)("div",{className:`h-2 w-2 rounded-full mr-2 ${b?"bg-green-500":"bg-gray-400"}`}),(0,s.jsx)("span",{className:"text-muted-foreground text-xs",children:b?"Real-time active":"Real-time inactive"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground"}),(0,s.jsx)(C.p,{type:"text",placeholder:"Search vehicles (Make, Model, Year, VIN, Plate, Owner...)",value:u,onChange:e=>m(e.target.value),className:"pl-10 w-full"})]})]}),(0,s.jsx)(S.gO,{isLoading:p,error:f,data:t,onRetry:j,loadingComponent:(0,s.jsx)(S.jt,{variant:"card",count:3}),emptyComponent:(0,s.jsxs)("div",{className:"text-center py-12 bg-card rounded-lg shadow-md",children:[(0,s.jsx)(o.A,{className:"mx-auto h-16 w-16 text-muted-foreground mb-6"}),(0,s.jsx)("h3",{className:"text-2xl font-semibold text-foreground mb-2",children:u?"No Vehicles Match Your Search":"Your Garage is Empty!"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2 mb-6 max-w-md mx-auto",children:u?"Try adjusting your search terms or add a new vehicle to your fleet.":"It looks like you haven't added any vehicles yet. Let's get your first one set up."}),!u&&(0,s.jsx)(h.r,{actionType:"primary",size:"lg",icon:(0,s.jsx)(n.A,{className:"h-4 w-4"}),asChild:!0,children:(0,s.jsx)(l(),{href:"/vehicles/new",children:"Add Your First Vehicle"})})]}),children:e=>(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,s.jsx)(v,{vehicle:e},e.id))})})]}))})};function A(){return(0,s.jsx)(E.A,{children:(0,s.jsx)(P,{})})}},47834:(e,r,t)=>{Promise.resolve().then(t.bind(t,36767))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76633:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(65239),i=t(48088),a=t(88170),l=t.n(a),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(r,n);let c={children:["",{children:["vehicles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,30222)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/vehicles/page",pathname:"/vehicles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3744,1658,5880,474,8464,8141,3983,7982],()=>t(76633));module.exports=s})();