'use client';

import React, {useEffect, useState, useCallback} from 'react';
import {useRouter, useParams} from 'next/navigation';
import EmployeeForm from '@/components/employees/EmployeeForm';
import {getEmployeeById, updateEmployee} from '@/lib/store';
import {EmployeeFormData} from '@/lib/schemas/employeeSchemas';
import type {Employee} from '@/lib/types';
import {PageHeader} from '@/components/ui/PageHeader';
import {UsersRound, Edit} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';
import {Skeleton} from '@/components/ui/skeleton';
import {Button} from '@/components/ui/button'; // Added Button for error state

const EditEmployeePage = () => {
	const router = useRouter();
	const params = useParams();
	const {toast} = useToast();

	// Ensure params.id is treated as string, then converted to number for API calls
	const employeeIdString = params.id as string;
	const employeeId = parseInt(employeeIdString, 10);

	const [employee, setEmployee] = useState<Partial<Employee> | null>(null);
	const [isLoading, setIsLoading] = useState(false); // For form submission
	const [isFetching, setIsFetching] = useState(true); // For initial data fetch
	const [error, setError] = useState<string | null>(null);

	const fetchEmployee = useCallback(async () => {
		if (isNaN(employeeId)) {
			setError('Invalid employee ID provided.');
			setIsFetching(false);
			return;
		}
		setIsFetching(true);
		try {
			const data = await getEmployeeById(employeeId);
			if (data) {
				setEmployee(data);
			} else {
				setError('Employee not found.');
			}
		} catch (err: any) {
			console.error('Failed to fetch employee:', err);
			setError(err.message || 'Failed to load employee data.');
		} finally {
			setIsFetching(false);
		}
	}, [employeeId]);

	useEffect(() => {
		if (employeeIdString) {
			// Only fetch if ID string is present
			fetchEmployee();
		}
	}, [employeeIdString, fetchEmployee]);

	const handleSubmit = async (data: EmployeeFormData) => {
		if (isNaN(employeeId)) {
			setError('Cannot update employee without a valid ID.');
			return;
		}
		setIsLoading(true);
		setError(null);
		try {
			await updateEmployee(employeeId, data);
			toast({
				title: 'Employee Updated',
				description: `${data.name} has been successfully updated.`,
				variant: 'default',
			});
			router.push('/employees');
		} catch (err: any) {
			console.error('Failed to update employee:', err);
			const errorMessage =
				err.response?.data?.error ||
				err.message ||
				'An unexpected error occurred.';
			setError(errorMessage);
			toast({
				title: 'Error Updating Employee',
				description: errorMessage,
				variant: 'destructive',
			});
		} finally {
			setIsLoading(false);
		}
	};

	if (isFetching) {
		return (
			<div className='container mx-auto py-8 space-y-8'>
				<PageHeader
					title='Edit Employee'
					description='Loading employee details...'
					icon={Edit}
				/>
				<div className='max-w-3xl mx-auto space-y-6'>
					<Skeleton className='h-10 w-1/3' />
					{[...Array(6)].map((_, i) => (
						<Skeleton key={i} className='h-12 w-full' />
					))}
					<div className='flex justify-end space-x-3 pt-6'>
						<Skeleton className='h-10 w-24' />
						<Skeleton className='h-10 w-24' />
					</div>
				</div>
			</div>
		);
	}

	if (error && !employee) {
		return (
			<div className='container mx-auto py-8 space-y-8 text-center'>
				<PageHeader title='Error' description={error} icon={UsersRound} />
				<Button onClick={() => router.push('/employees')}>
					Back to Employees
				</Button>
			</div>
		);
	}

	return (
		<div className='container mx-auto py-8 space-y-8'>
			<PageHeader
				title='Edit Employee'
				description={`Update details for ${
					employee?.name || employee?.fullName || 'employee'
				}`}
				icon={Edit}
			/>
			{error && employee && (
				<div
					className='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4'
					role='alert'>
					<strong className='font-bold'>Error: </strong>
					<span className='block sm:inline'>{error}</span>
				</div>
			)}
			{employee && (
				<EmployeeForm
					onSubmit={handleSubmit}
					initialData={employee}
					isEditing={true}
					isLoading={isLoading}
				/>
			)}
		</div>
	);
};

export default EditEmployeePage;
