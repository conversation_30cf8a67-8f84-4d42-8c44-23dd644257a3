import * as z from 'zod';

export const VehicleFormSchema = z.object({
	make: z.string().min(1, 'Make is required'),
	model: z.string().min(1, 'Model is required'),
	year: z.coerce
		.number()
		.min(1900, 'Year must be 1900 or later')
		.max(
			new Date().getFullYear() + 1,
			`Year cannot be more than ${new Date().getFullYear() + 1}`
		),
	vin: z
		.string()
		.min(1, 'VIN is required')
		.regex(
			/^[A-HJ-NPR-Z0-9]{17}$/,
			'VIN must be a valid 17-character format (only capital letters A-H, J-N, P-R, Z and numbers 0-9)'
		), // Match backend validation
	licensePlate: z.string().min(1, 'License plate is required'),
	ownerName: z.string().min(1, 'Owner name is required'),
	ownerContact: z.string().min(1, 'Owner contact is required'), // Could be email or phone
	color: z.string().optional(),
	initialOdometer: z.coerce
		.number()
		.min(0, 'Odometer reading cannot be negative')
		.optional(),
	imageUrl: z.string().url('Invalid image URL').optional().or(z.literal('')),
	// serviceHistory is not part of the form for new/edit, handled separately
	// id, createdAt, updatedAt are not part of the form
});

export type VehicleFormData = z.infer<typeof VehicleFormSchema>;
