## Backend Report Generation Service Design (Node.js)

This document outlines the design for a new, dedicated backend microservice or API module for generating PDF reports. It assumes a Node.js environment and leverages a headless browser for high-fidelity rendering.

**1. API Endpoint Definition**

*   **Endpoint:** `POST /api/reports/generate/{reportType}`
*   **Method:** `POST`
*   **Description:** Initiates the generation of a specific report type.

**Path Parameters:**

*   `reportType` (string, required): An identifier for the specific report to be generated (e.g., `delegation-summary`, `vehicle-service-history`, `user-activity-log`).

**Request Body (JSON):**

The request body should be a JSON object containing parameters to customize the report.

*   **Common Parameters:**
    *   `dateRange` (object, optional): Defines a date range for the report.
        *   `from` (string, YYYY-MM-DD): Start date.
        *   `to` (string, YYYY-MM-DD): End date.
    *   `filters` (object, optional): Key-value pairs for filtering the data.
        *   *Example:* `{ "status": "active", "region": "north", "departmentId": "dept-789" }`
    *   `outputFormat` (string, optional): Defines the desired output, primarily "pdf". Could be extended in the future (e.g., "csv"). Defaults to "pdf".
    *   `locale` (string, optional): Preferred locale for the report, e.g., "en-US", "fr-CA".

*   **Report-Specific Parameters:**
    *   These are dynamic and depend on the `reportType`.
    *   *Example for `delegation-summary` reportType:* `delegationId: "string"`
    *   *Example for `vehicle-service-history` reportType:* `vehicleId: "string"`
    *   *Example for `user-activity-log` reportType:* `userId: "string"`

*   **Example Request Body:**
    ```json
    {
      "dateRange": { "from": "2023-01-01", "to": "2023-12-31" },
      "filters": { "status": "active", "region": "north" },
      "delegationId": "del-12345", // Report-specific parameter
      "locale": "en-GB"
    }
    ```

**Success Responses:**

*   **Option A: Direct PDF Stream (Synchronous Completion)**
    *   **Status Code:** `200 OK`
    *   **Headers:**
        *   `Content-Type: application/pdf`
        *   `Content-Disposition: attachment; filename="delegation_summary_del-12345_20231231.pdf"` (Filename should be dynamically generated based on report type, key identifiers, and date)
    *   **Body:** Raw PDF binary data.

*   **Option B: Link to Stored PDF (Asynchronous or for Large Reports)**
    *   **Status Code:** `200 OK` (if PDF is generated quickly and link is immediately available) or `202 Accepted` (if generation is offloaded to a background job).
    *   **Body (JSON):**
        ```json
        {
          "success": true,
          "message": "Report generated successfully." // Or "Report generation initiated. You will be notified."
          "pdfUrl": "https://your-storage-service.com/reports/delegation_summary_del-12345_20231231.pdf", // If available immediately
          "jobId": "bg-job-xyz789", // If processing asynchronously
          "filename": "delegation_summary_del-12345_20231231.pdf" // Suggested filename
        }
        ```

**Error Responses (JSON):**

All error responses will return a JSON body.

*   **`400 Bad Request`:** Invalid or missing parameters.
    ```json
    { "success": false, "error": "Invalid input: 'dateRange.from' must be a valid date." }
    ```
*   **`401 Unauthorized`:** Missing, invalid, or expired authentication token.
    ```json
    { "success": false, "error": "Authentication failed: Token is invalid." }
    ```
*   **`403 Forbidden`:** Authenticated user does not have permission to generate this `reportType` or access the requested data.
    ```json
    { "success": false, "error": "Authorization failed: User not permitted to generate 'delegation-summary' reports." }
    ```
*   **`404 Not Found`:** The specified `reportType` is not recognized by the system.
    ```json
    { "success": false, "error": "Report type 'unknown-report' not found." }
    ```
*   **`422 Unprocessable Entity`:** Valid request format, but semantic errors in data making it impossible to process (e.g., `dateRange.from` is after `dateRange.to`).
    ```json
    { "success": false, "error": "Invalid data: Start date cannot be after end date." }
    ```
*   **`500 Internal Server Error`:** An unexpected error occurred on the server during data fetching, template rendering, PDF generation, or other internal processes.
    ```json
    { "success": false, "error": "An internal server error occurred. Please try again later." }
    ```
    (A more specific error code or message might be included in logs but not necessarily exposed to the client for security reasons).

**2. High-Level Component Breakdown (Node.js Example)**

This outlines the key modules and their responsibilities within the service.

*   **Router/Controller (`reportRoutes.js`, `reportController.js`):**
    *   **Framework:** Typically Express.js (`const router = require('express').Router();`).
    *   **Route Definition:** `router.post('/generate/:reportType', authMiddleware, validationMiddleware, reportController.generateReport);`
    *   **Responsibilities (`reportController.js`):**
        *   Extracts `reportType` from `req.params`.
        *   Extracts validated request body data from `req.body`.
        *   Calls the main `ReportService.createReport(reportType, data, req.user)`.
        *   Handles responses:
            *   If PDF buffer is returned: sets `Content-Type` and `Content-Disposition` headers, and streams the PDF using `res.send(pdfBuffer)`.
            *   If JSON (e.g., link or async job ID): sends JSON response using `res.status(code).json(responseBody)`.
            *   Passes errors to the centralized error handler.

*   **Authentication Middleware (`authMiddleware.js`):**
    *   **Responsibilities:**
        *   Verifies JWT (JSON Web Token) from the `Authorization` header (e.g., `Bearer <token>`).
        *   Uses libraries like `jsonwebtoken` or `passport-jwt`.
        *   If valid, decodes the token and attaches user information (e.g., `userId`, `roles`) to the `req.user` object.
        *   If invalid or missing, sends a `401 Unauthorized` response.
    *   **Integration:** Applied globally or to protected routes like the report generation endpoint.

*   **Input Validation Service/Middleware (`validationService.js` or library-based):**
    *   **Responsibilities:**
        *   Defines validation schemas for common parameters and for each specific `reportType`. Libraries like **Joi**, **Zod**, or **Yup** are highly recommended for this.
        *   Validates `req.params.reportType` against a list of known/supported report types.
        *   Validates `req.body` against the corresponding schema.
        *   If validation fails, sends a `400 Bad Request` or `422 Unprocessable Entity` response with detailed error messages.
    *   **Integration:** Used as middleware before the main controller logic.

*   **Report Service (`reportService.js`):**
    *   **Responsibilities (Orchestration):**
        *   Acts as the central coordinator for report generation.
        *   Contains a primary method like `async createReport(reportType, reportParams, userInfo)`.
        *   Based on `reportType`, it:
            *   Determines the appropriate data fetching function from `DataFetchingService`.
            *   Determines the correct HTML template name/path.
        *   Calls `DataFetchingService` to get the report data: `const data = await dataFetchingService.fetchDataForReport(reportType, reportParams, userInfo);`.
        *   Handles cases where data is not found or access is denied (may throw specific errors).
        *   Calls `TemplateRenderingService` to get the HTML string: `const htmlContent = await templateRenderingService.renderReportTemplate(reportType, data, reportParams.locale);`.
        *   Calls `PdfGenerationService` to convert HTML to PDF: `const pdfBuffer = await pdfGenerationService.generatePdfFromHtml(htmlContent, pdfOptions);`.
        *   (Optional) If using storage (Option B): `const pdfUrl = await storageService.uploadPdf(pdfBuffer, generatedFilename);` and returns the URL.
        *   Returns the PDF buffer or storage information to the controller.

*   **Data Fetching Service (`dataFetchingService.js`):**
    *   **Responsibilities:**
        *   Abstracts all database interactions.
        *   Contains specific methods for each `reportType` or data entity, e.g.:
            *   `async fetchDelegationReportData(params, userInfo)`
            *   `async fetchVehicleServiceHistoryData(params, userInfo)`
        *   Constructs secure database queries (e.g., using an ORM like Sequelize, TypeORM, or a query builder like Knex.js).
        *   Applies filters, date ranges, and handles pagination if necessary.
        *   Performs any necessary data transformations or aggregations to structure data optimally for the templates.
        *   Enforces data access permissions based on `userInfo` if not already handled by database policies.

*   **HTML Template Rendering Service (`templateRenderingService.js`):**
    *   **Responsibilities:**
        *   Manages loading and rendering of HTML templates.
        *   Supports a templating engine (e.g., **EJS**, **Handlebars**, **Nunjucks**). Alternatively, for complex scenarios, it could render a dedicated server-side React/Vue component to a string.
        *   Methods like `async renderReportTemplate(reportType, data, locale)`.
        *   Selects the template file based on `reportType` (e.g., `templates/delegation-summary.ejs`).
        *   Injects the fetched `data` and `locale` information into the template.
        *   Ensures templates are designed for print:
            *   Use CSS units like `mm`, `pt`, `in`.
            *   Define page size (A4, Letter) and margins using `@page` CSS rules.
            *   Implement `page-break-before/after/inside` for pagination control.
            *   Embed necessary styles and fonts directly or ensure they are accessible to the headless browser.
        *   Returns the fully rendered HTML string.

*   **PDF Generation Service (`pdfGenerationService.js`):**
    *   **Responsibilities:**
        *   Integrates with **Puppeteer** (or Playwright).
        *   Manages browser instances:
            *   Consider a browser instance pool (e.g., using `puppeteer-cluster`) for better performance and resource management in high-load scenarios, reducing launch overhead for each request.
            *   Handles browser launch options (e.g., `--no-sandbox`, `--disable-dev-shm-usage` for Docker environments).
        *   Primary method like `async generatePdfFromHtml(htmlContent, pdfOptions)`.
        *   Uses `page.setContent(htmlContent, { waitUntil: 'networkidle0' })` to load the HTML. (`networkidle0` ensures all resources are loaded and JS execution is complete).
        *   Alternatively, if HTML contains complex relative paths or needs to be served, it can be temporarily written to a file or served via an internal HTTP endpoint that `page.goto()` navigates to.
        *   Invokes `await page.pdf(pdfOptions)` with configurations for:
            *   `format`: 'A4', 'Letter'.
            *   `printBackground`: `true` (to include CSS backgrounds).
            *   `margin`: (e.g., `{ top: '20mm', bottom: '20mm' }`) - often better controlled by `@page` CSS in the template itself for more complex headers/footers.
            *   `displayHeaderFooter`: `false` if using HTML-based headers/footers in the template.
            *   `headerTemplate`, `footerTemplate`: Can be used for simple headers/footers, but HTML elements in the main template offer more control.
        *   Returns the generated PDF as a Buffer.

*   **Storage Service (Optional - `storageService.js`):**
    *   **Responsibilities (if implementing Option B for PDF delivery):**
        *   Handles uploading the PDF buffer to a cloud storage provider (AWS S3, Google Cloud Storage, Azure Blob Storage).
        *   Uses SDKs like `aws-sdk`, `@google-cloud/storage`.
        *   Generates a unique filename for the stored PDF.
        *   Sets appropriate ACLs/permissions for the uploaded file.
        *   Returns a publicly accessible (or pre-signed) URL for the PDF.
        *   Method: `async uploadPdf(pdfBuffer, filename)`

*   **Error Handling & Logging Module (`errorHandler.js`, `logger.js`):**
    *   **`errorHandler.js` (Middleware):**
        *   Central Express.js error handling middleware (e.g., `app.use((err, req, res, next) => { ... });`).
        *   Catches errors propagated from other services/controllers.
        *   Maps custom error types (e.g., `AuthenticationError`, `ValidationError`, `NotFoundError`) to appropriate HTTP status codes and JSON error responses.
        *   Logs the error details using the `Logger` module before sending the response.
        *   Provides generic error messages for unexpected `500` errors to avoid exposing sensitive details.
    *   **`logger.js`:**
        *   Configures a logging library (e.g., **Winston**, **Pino**).
        *   Supports different log levels (info, warn, error, debug).
        *   Outputs logs to console, files, or log management services.
        *   Logs key events: incoming requests, validation errors, data fetching successes/failures, PDF generation times, errors, etc.

**Security Considerations:**

*   **Authentication & Authorization:** Enforced by `authMiddleware` and within services (e.g., `DataFetchingService` checking if `req.user` has rights to access specific data for a report).
*   **Secrets Management:** Database credentials, API keys for external services (e.g., cloud storage), JWT secrets must be managed securely using environment variables (e.g., via `.env` files with `dotenv` package in development) or a dedicated secrets management service (like HashiCorp Vault, AWS Secrets Manager, Google Secret Manager) in production.
*   **Input Validation:** Crucial to prevent NoSQL injection, XSS (if data is ever directly rendered without proper templating engine escaping), and other injection attacks. Use robust validation schemas.
*   **Headless Browser Security:**
    *   Run Puppeteer with minimal necessary privileges. The `['--no-sandbox', '--disable-dev-shm-usage']` arguments are often required in Docker containers but understand their implications.
    *   Ensure HTML content loaded into Puppeteer is trusted or properly sanitized, especially if any part of it can be user-influenced, to prevent server-side request forgery (SSRF) or other vulnerabilities if the page makes internal requests.
    *   Keep Puppeteer and the underlying browser (Chromium) updated to patch security vulnerabilities.
*   **Resource Limits:** Implement rate limiting and request size limits (e.g., using Express middleware) to protect the service from abuse or denial-of-service attacks.
*   **Error Handling:** Avoid leaking sensitive stack traces or internal system details in error messages sent to the client. Log them server-side.
*   **Data Minimization:** Fetch only the data necessary for the specific report being generated.

**3. Example Puppeteer PDF Generation Snippet (Conceptual - inside `pdfGenerationService.js`)**

```javascript
const puppeteer = require('puppeteer');

// Consider initializing the browser once and reusing instances or using a pool
// This is a simplified example of a single generation.
// let browserInstance;
// async function getBrowserInstance() {
//   if (!browserInstance) {
//     browserInstance = await puppeteer.launch({
//       headless: true,
//       args: ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu'] // --disable-gpu often useful in servers
//     });
//   }
//   return browserInstance;
// }

async function generatePdfFromHtml(htmlContent, pdfOptions = {}) {
  let browser;
  try {
    // For production, manage a persistent browser instance or pool (e.g., puppeteer-cluster)
    // to avoid the overhead of launching a new browser for every request.
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox', // Required for running in many Docker/CI environments
        '--disable-dev-shm-usage', // Overcomes limited resource problems
        '--disable-gpu', // Often not needed for server-side rendering
        '--font-render-hinting=none', // May improve font rendering consistency
        // '--single-process', // Alternative for some environments, test performance
      ],
    });
    const page = await browser.newPage();

    // 1. Set HTML content
    // waitUntil: 'networkidle0' waits for network connections to be idle for 500ms.
    // This ensures that images, fonts, and other resources linked in the HTML are loaded.
    await page.setContent(htmlContent, { waitUntil: 'networkidle0' });

    // 2. (Optional) Emulate media type if your CSS is heavily screen-dependent
    // However, it's best to use @page and print-specific CSS in your HTML template.
    // await page.emulateMediaType('print'); // More direct for print styles

    // 3. Generate PDF
    const defaultPdfOptions = {
      format: 'A4',
      printBackground: true, // Crucial for including CSS background colors/images
      margin: { // Default margins if not overridden by @page CSS in the HTML
        top: '20mm',
        right: '20mm',
        bottom: '20mm',
        left: '20mm',
      },
      // Prefer controlling headers/footers via HTML/CSS in the template for more flexibility
      displayHeaderFooter: false,
    };

    const finalPdfOptions = { ...defaultPdfOptions, ...pdfOptions };
    const pdfBuffer = await page.pdf(finalPdfOptions);

    return pdfBuffer;

  } catch (error) {
    // Log the detailed error for server-side debugging
    console.error('Error generating PDF with Puppeteer:', error);
    // Throw a more generic or specific custom error to be handled by the global error handler
    throw new Error('PDF generation failed due to an internal error.');
  } finally {
    if (browser) {
      // If managing a pool, you'd return the page/browser to the pool instead of closing.
      await browser.close();
    }
  }
}

module.exports = { generatePdfFromHtml /*, initializeBrowserPool, closeBrowserPool */ };
```

This comprehensive design provides a solid foundation for building a robust and scalable backend PDF report generation service.The "Backend Report Generation Service Design" markdown file (`backend_report_service_design.md`) has been successfully created in the previous turn.

This file includes:
1.  **API Endpoint Definition:**
    *   Endpoint: `POST /api/reports/generate/{reportType}`
    *   Path Parameter: `reportType`
    *   Request Body (JSON): Common and report-specific parameters with an example.
    *   Success Responses: Option A (Direct PDF Stream) and Option B (Link to Stored PDF), including status codes, headers, and body examples.
    *   Error Responses: `400`, `401`, `403`, `404`, `422`, `500` with example bodies.

2.  **High-Level Component Breakdown (Node.js example):**
    *   Router/Controller (`reportRoutes.js`, `reportController.js`)
    *   Authentication Middleware (`authMiddleware.js`)
    *   Input Validation Service/Middleware (`validationService.js` or Joi/Zod)
    *   Report Service (`reportService.js`) - Orchestrator
    *   Data Fetching Service (`dataFetchingService.js`)
    *   HTML Template Rendering Service (`templateRenderingService.js`)
    *   PDF Generation Service (`pdfGenerationService.js`) - Puppeteer integration
    *   Storage Service (Optional - `storageService.js`)
    *   Error Handling & Logging Module (`errorHandler.js`, `logger.js`)
    *   Security Considerations throughout.

3.  **Example Puppeteer PDF Generation Snippet:**
    *   A conceptual JavaScript snippet for `pdfGenerationService.js` showing Puppeteer usage, including browser launch, setting content, PDF generation, and error handling.

The created file addresses all requirements of the current subtask.
