import {
	vehicleCreateSchema,
	vehicleUpdateSchema,
	vehicleIdSchema,
} from '../schemas/vehicle.schema.js';

import {
	employeeCreateSchema,
	employeeUpdateSchema,
	employeeIdSchema,
} from '../schemas/employee.schema.js';

console.log('Running validation tests...');

// Test Vehicle Create Schema
console.log('\n--- Testing Vehicle Create Schema ---');

// Valid vehicle data
const validVehicle = {
	make: 'Toyota',
	model: 'Camry',
	year: 2022,
	vin: '4T1BF1FK5CU123456',
	licensePlate: 'ABC123',
	ownerName: '<PERSON>',
	ownerContact: '<EMAIL>',
};

// Test valid vehicle
const validVehicleResult = vehicleCreateSchema.safeParse(validVehicle);
console.log(
	'Valid vehicle test:',
	validVehicleResult.success ? 'PASSED' : 'FAILED'
);
if (!validVehicleResult.success) {
	console.log('Errors:', validVehicleResult.error.format());
}

// Test invalid vehicle (missing required field)
const invalidVehicle1 = {
	make: 'Toyota',
	model: 'Camry',
	year: 2022,
	// missing vin
	licensePlate: 'ABC123',
	ownerName: 'John Doe',
	ownerContact: '<EMAIL>',
};

const invalidVehicleResult1 = vehicleCreateSchema.safeParse(invalidVehicle1);
console.log(
	'Invalid vehicle (missing vin) test:',
	!invalidVehicleResult1.success ? 'PASSED' : 'FAILED'
);
if (!invalidVehicleResult1.success) {
	console.log('Expected errors:', invalidVehicleResult1.error.format());
}

// Test invalid vehicle (invalid type)
const invalidVehicle2 = {
	make: 'Toyota',
	model: 'Camry',
	year: '2022', // string instead of number
	vin: '4T1BF1FK5CU123456',
	licensePlate: 'ABC123',
	ownerName: 'John Doe',
	ownerContact: '<EMAIL>',
};

const invalidVehicleResult2 = vehicleCreateSchema.safeParse(invalidVehicle2);
console.log(
	'Invalid vehicle (invalid year type) test:',
	!invalidVehicleResult2.success ? 'PASSED' : 'FAILED'
);
if (!invalidVehicleResult2.success) {
	console.log('Expected errors:', invalidVehicleResult2.error.format());
}

// Test invalid vehicle (invalid format)
const invalidVehicle3 = {
	make: 'Toyota',
	model: 'Camry',
	year: 2022,
	vin: 'short', // Invalid VIN format
	licensePlate: 'ABC123',
	ownerName: 'John Doe',
	ownerContact: '<EMAIL>',
};

const invalidVehicleResult3 = vehicleCreateSchema.safeParse(invalidVehicle3);
console.log(
	'Invalid vehicle (invalid vin format) test:',
	!invalidVehicleResult3.success ? 'PASSED' : 'FAILED'
);
if (!invalidVehicleResult3.success) {
	console.log('Expected errors:', invalidVehicleResult3.error.format());
}

// Test Vehicle Update Schema (should allow partial updates)
console.log('\n--- Testing Vehicle Update Schema ---');

// Test partial update (only updating make and model)
const partialUpdate = {
	make: 'Honda',
	model: 'Accord',
};

const partialUpdateResult = vehicleUpdateSchema.safeParse(partialUpdate);
console.log(
	'Partial update test:',
	partialUpdateResult.success ? 'PASSED' : 'FAILED'
);
if (!partialUpdateResult.success) {
	console.log('Errors:', partialUpdateResult.error.format());
}

// Test Employee Create Schema
console.log('\n--- Testing Employee Create Schema ---');

// Valid employee data
const validEmployee = {
	name: 'Jane Smith',
	role: 'technician',
	employeeId: 'EMP-12345',
	contactInfo: '<EMAIL>',
	department: 'Maintenance',
};

// Test valid employee
const validEmployeeResult = employeeCreateSchema.safeParse(validEmployee);
console.log(
	'Valid employee test:',
	validEmployeeResult.success ? 'PASSED' : 'FAILED'
);
if (!validEmployeeResult.success) {
	console.log('Errors:', validEmployeeResult.error.format());
}

// Test driver without required fields
const invalidDriver = {
	name: 'Bob Driver',
	role: 'driver',
	employeeId: 'DRV-12345',
	contactInfo: '<EMAIL>',
	// Missing availability and currentLocation
};

const invalidDriverResult = employeeCreateSchema.safeParse(invalidDriver);
console.log(
	'Invalid driver (missing required driver fields) test:',
	!invalidDriverResult.success ? 'PASSED' : 'FAILED'
);
if (!invalidDriverResult.success) {
	console.log('Expected errors:', invalidDriverResult.error.format());
}

// Test valid driver
const validDriver = {
	name: 'Bob Driver',
	role: 'driver',
	employeeId: 'DRV-12345',
	contactInfo: '<EMAIL>',
	availability: 'Mon-Fri, 9-5',
	currentLocation: 'Garage A',
	vehicleId: 123,
};

const validDriverResult = employeeCreateSchema.safeParse(validDriver);
console.log(
	'Valid driver test:',
	validDriverResult.success ? 'PASSED' : 'FAILED'
);
if (!validDriverResult.success) {
	console.log('Errors:', validDriverResult.error.format());
}

// Test Employee Update Schema
console.log('\n--- Testing Employee Update Schema ---');

// Test partial update
const partialEmployeeUpdate = {
	name: 'Jane Smith-Updated',
	contactInfo: '<EMAIL>',
};

const partialEmployeeUpdateResult = employeeUpdateSchema.safeParse(
	partialEmployeeUpdate
);
console.log(
	'Partial employee update test:',
	partialEmployeeUpdateResult.success ? 'PASSED' : 'FAILED'
);
if (!partialEmployeeUpdateResult.success) {
	console.log('Errors:', partialEmployeeUpdateResult.error.format());
}

// Test ID param schemas
console.log('\n--- Testing ID Param Schemas ---');

// Test valid ID
const validIdResult = vehicleIdSchema.safeParse({id: '123'});
console.log(
	'Valid vehicle ID test:',
	validIdResult.success ? 'PASSED' : 'FAILED'
);
if (!validIdResult.success) {
	console.log('Errors:', validIdResult.error.format());
} else {
	console.log(
		'Transformed ID:',
		validIdResult.data.id,
		'Type:',
		typeof validIdResult.data.id
	);
}

// Test invalid ID
const invalidIdResult = vehicleIdSchema.safeParse({id: 'abc'});
console.log(
	'Invalid vehicle ID test:',
	!invalidIdResult.success ? 'PASSED' : 'FAILED'
);
if (!invalidIdResult.success) {
	console.log('Expected errors:', invalidIdResult.error.format());
}

console.log('\nValidation tests completed. Check the results above.');
