'use client';

import React from 'react';
import type { Task, Employee } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { ChevronUp, ChevronDown, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, isPast, parseISO } from 'date-fns';
import { getEmployeeById } from '@/lib/store';

/**
 * Props for the TasksTable component
 */
interface TasksTableProps {
  /** Array of tasks to display */
  tasks: Task[];
  /** Current sort field */
  sortField?: string;
  /** Current sort direction */
  sortDirection?: 'asc' | 'desc';
  /** Callback function when a column is sorted */
  onSort?: (field: string) => void;
  /** Additional CSS class names */
  className?: string;
}

/**
 * A component that displays tasks in a table with sortable columns
 * 
 * @example
 * ```tsx
 * <TasksTable
 *   tasks={paginatedTasks}
 *   sortField="dateTime"
 *   sortDirection="desc"
 *   onSort={handleSort}
 * />
 * ```
 */
export function TasksTable({
  tasks,
  sortField,
  sortDirection,
  onSort,
  className,
}: TasksTableProps) {
  // Handle sort click
  const handleSort = (field: string) => {
    if (onSort) {
      onSort(field);
    }
  };

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    
    return sortDirection === 'asc' 
      ? <ChevronUp className="inline-block h-4 w-4 ml-1" aria-hidden="true" /> 
      : <ChevronDown className="inline-block h-4 w-4 ml-1" aria-hidden="true" />;
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (error) {
      return 'Invalid date';
    }
  };
  
  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Assigned':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'In_Progress':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  
  // Get priority badge color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'Medium':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  
  // Format status for display
  const formatStatus = (status: string) => {
    return status.replace('_', ' ');
  };
  
  // Check if task is overdue
  const isOverdue = (task: Task) => {
    return task.deadline && 
           isPast(new Date(task.deadline)) && 
           task.status !== 'Completed' && 
           task.status !== 'Cancelled';
  };
  
  // Get assignee name
  const getAssigneeName = (task: Task) => {
    if (!task.assignedEmployeeId) {
      return 'Unassigned';
    }
    
    const employee = getEmployeeById(task.assignedEmployeeId);
    return employee ? employee.fullName : 'Unknown';
  };

  return (
    <Table id="tasks-table" className={className}>
      <TableHeader>
        <TableRow>
          <TableHead 
            onClick={() => handleSort('description')} 
            className={cn(onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'description' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('description');
              }
            }}
            aria-label="Sort by description"
          >
            Description {renderSortIndicator('description')}
          </TableHead>
          
          <TableHead 
            onClick={() => handleSort('status')} 
            className={cn(onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'status' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('status');
              }
            }}
            aria-label="Sort by status"
          >
            Status {renderSortIndicator('status')}
          </TableHead>
          
          <TableHead 
            onClick={() => handleSort('priority')} 
            className={cn(onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'priority' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('priority');
              }
            }}
            aria-label="Sort by priority"
          >
            Priority {renderSortIndicator('priority')}
          </TableHead>
          
          <TableHead 
            onClick={() => handleSort('assignedEmployeeId')} 
            className={cn(onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'assignedEmployeeId' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('assignedEmployeeId');
              }
            }}
            aria-label="Sort by assignee"
          >
            Assignee {renderSortIndicator('assignedEmployeeId')}
          </TableHead>
          
          <TableHead 
            onClick={() => handleSort('dateTime')} 
            className={cn(onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'dateTime' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('dateTime');
              }
            }}
            aria-label="Sort by start time"
          >
            Start Time {renderSortIndicator('dateTime')}
          </TableHead>
          
          <TableHead 
            onClick={() => handleSort('deadline')} 
            className={cn(onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'deadline' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('deadline');
              }
            }}
            aria-label="Sort by deadline"
          >
            Deadline {renderSortIndicator('deadline')}
          </TableHead>
          
          <TableHead 
            onClick={() => handleSort('location')} 
            className={cn(onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'location' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('location');
              }
            }}
            aria-label="Sort by location"
          >
            Location {renderSortIndicator('location')}
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {tasks.map((task) => (
          <TableRow key={task.id}>
            <TableCell
              className="max-w-xs truncate print-description"
              title={task.description}
            >
              {task.description}
            </TableCell>
            
            <TableCell>
              <Badge className={cn(getStatusColor(task.status))}>
                {formatStatus(task.status)}
              </Badge>
            </TableCell>
            
            <TableCell>
              <Badge className={cn(getPriorityColor(task.priority))}>
                {task.priority}
              </Badge>
            </TableCell>
            
            <TableCell>
              {task.assignedEmployeeId ? (
                getAssigneeName(task)
              ) : (
                <span className="text-gray-500 text-sm">Unassigned</span>
              )}
            </TableCell>
            
            <TableCell>
              {formatDate(task.dateTime)}
            </TableCell>
            
            <TableCell>
              {task.deadline ? (
                <div className="flex items-center gap-1">
                  <span className={cn(isOverdue(task) ? "text-red-600 font-medium" : "")}>
                    {formatDate(task.deadline)}
                  </span>
                  {isOverdue(task) && (
                    <AlertTriangle className="h-4 w-4 text-red-600" aria-label="Overdue" />
                  )}
                </div>
              ) : (
                <span className="text-gray-500 text-sm">No deadline</span>
              )}
            </TableCell>
            
            <TableCell
              className="max-w-xs truncate print-location"
              title={task.location}
            >
              {task.location}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
