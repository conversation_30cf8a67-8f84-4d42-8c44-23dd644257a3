## Frontend Removal Plan: Deprecating Client-Side PDF Generation Assets

This document outlines the frontend assets and code sections related to the old client-side HTML-to-PDF generation (primarily using `html2canvas` and `jsPDF`) that are targeted for removal or deprecation. The goal is to streamline the codebase and eliminate redundant or problematic features now superseded by the server-side headless browser solution.

**Assumed Stack:** Node.js backend, React/Next.js frontend.

**1. Utility Functions:**

*   **Files to inspect:** `utils/pdfUtils.js`, `utils/reportHelpers.ts`, `lib/fileUtils.ts`, or similar utility/helper modules.
*   **Specific functions to remove/deprecate:**
    *   Any function that directly calls `html2canvas()` to capture an HTML element.
        *   *Example:* `captureElementAsCanvas(elementId, options)`
    *   Any function that takes a canvas (often from `html2canvas`) and uses `jsPDF` to add it as an image to a PDF.
        *   *Example:* `addCanvasToPdf(canvas, pdfDoc, options)`
    *   Wrapper functions that orchestrate the `html2canvas` and `jsPDF` process.
        *   *Example:* `generatePdfFromHtml(elementId, fileName)`
    *   Helper functions for manipulating HTML content specifically for client-side PDF export, such as:
        *   Functions that clone DOM elements to prepare them for `html2canvas` (e.g., to re-style or isolate them).
        *   Functions that temporarily alter styles or classes on elements before capture.
        *   *Example:* `prepareReportForCanvasCapture(reportElement)`
    *   Any utility that converts canvas output to an image format (e.g., PNG) if its primary use was for `jsPDF`.

**2. Core Library Calls & Dependencies:**

*   **Across all frontend files (`.js`, `.jsx`, `.ts`, `.tsx`):**
    *   Remove `import html2canvas from 'html2canvas';`
    *   Remove `import jsPDF from 'jspdf';` (or specific imports like `import { jsPDF } from 'jspdf';`)
    *   Remove any direct instantiations like `new jsPDF(...)`.
*   **In `package.json`:**
    *   Remove `"html2canvas": "..."` from `dependencies` or `devDependencies`.
    *   Remove `"jspdf": "..."` from `dependencies` or `devDependencies`.
    *   After removing from `package.json`, run `npm uninstall html2canvas jspdf` or `yarn remove html2canvas jspdf` to also remove them from `node_modules` and update the lock file (`package-lock.json` or `yarn.lock`).
*   **Typescript Definitions (if applicable):**
    *   Remove `@types/html2canvas` and `@types/jspdf` from `devDependencies` in `package.json` and run the corresponding uninstall command.

**3. Component-Specific Logic:**

*   **React Components (typically in `components/...` or `pages/...` directories):**
    *   **Event Handlers:**
        *   Remove functions like `handlePrintPdf()`, `exportToPdf()`, `downloadClientPdf()` that were previously wired to buttons and initiated the `html2canvas`/`jsPDF` process.
        *   *Example:* The `onClick` prop on a `<button onClick={generateClientPdf}>Download PDF</button>` should be rewired to call the new server-side generation API or removed if the button's sole purpose was client-side PDF.
    *   **State Variables:**
        *   Remove state variables like `isLoadingPdf`, `isGeneratingPdf`, `pdfProgress` that were used to manage the UI during client-side PDF creation.
        *   *Example:* `const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);`
    *   **JSX/HTML Elements for Capture:**
        *   Remove any hidden or temporarily cloned JSX/HTML structures that were specifically created or manipulated for `html2canvas` to capture. This might include:
            *   Elements with IDs like `id="pdf-capture-area"` or `id="report-clone-for-pdf"`.
            *   Elements rendered outside the main view, possibly with absolute positioning, just for capture.
        *   Conditional rendering logic tied to the client-side PDF generation state (e.g., `{isGeneratingPdf && <Spinner />}`). While a spinner will still be needed for the server-side call, the old state variable controlling it will change.

**4. CSS and Styling:**

*   **Global Stylesheets (`styles/globals.css`, `styles/main.scss`, etc.) and Component-Specific Stylesheets (`*.module.css`, styled-components):**
    *   **Targeted Print Overrides:**
        *   Remove `@media print` styles that were primarily intended to fix layout issues or appearance problems specifically for `html2canvas` or `jsPDF` output. These often include aggressive `display: none !important;` rules for elements not meant for the PDF, or complex flex/grid adjustments that were fighting with `html2canvas` rendering.
        *   *Example:*
            ```css
            /* In a _print.scss or similar, targeting old client-side PDF issues */
            @media print {
              .sidebar, .navbar, .unrelated-widget {
                display: none !important; /* May still be valid, but review if only for old PDF */
              }
              .report-container-for-canvas {
                width: 100% !important; /* Forcing width for html2canvas */
                transform: scale(0.8); /* Attempting to fit content */
              }
              /* Complex table overrides that were fighting jsPDF's image rendering */
              table { /* ... styles to make tables look barely acceptable in canvas ... */ }
            }
            ```
    *   **Styles for Hidden/Shown Elements for Capture:**
        *   Remove CSS rules that hide or show elements specifically for the client-side PDF snapshot process.
        *   *Example:*
            ```css
            .element-for-pdf-only { display: none; }
            .print-active .element-for-pdf-only { display: block; } /* If a class was added during capture */
            .hide-for-client-pdf { display: block !important; }
            .print-active .hide-for-client-pdf { display: none !important; }
            ```
    *   **Font Adjustments for Client-Side PDF:**
        *   Remove any font overrides or sizing adjustments that were made because `html2canvas` was not rendering fonts correctly or at the right scale.
    *   **Important Note:** Basic, general-purpose `@media print` styles that improve the output of `window.print()` (direct browser printing, not PDF generation) might still be valuable and should be reviewed rather than wholesale removed. The focus is on removing styles that were workarounds for `html2canvas`/`jsPDF` deficiencies.

**5. Old Report Components (if applicable):**

*   **Identify and list for deprecation/removal:**
    *   Any React components or Next.js pages that were created *exclusively* to structure content for the old `html2canvas`/`jsPDF` export pipeline.
    *   These might be simplified versions of existing views, or views that had significant structural changes just to make them "snapshot-friendly" for `html2canvas`.
    *   *Example:* `components/reports/ClientSidePdfReportView.js` or `pages/reports/[id]/print-preview.js` if these were solely for the old method and are not used by the new server-side flow.

By systematically addressing these areas, the frontend codebase can be cleaned of outdated client-side PDF generation logic, reducing complexity, improving maintainability, and fully leveraging the benefits of the new server-side headless browser solution. Remember to update any associated unit tests or integration tests that might have been targeting the old client-side functionality.The "Frontend Removal Plan" has been created as `frontend_removal_plan.md` in the previous turn. This file details the types of files, components, code snippets, and CSS related to the old client-side HTML-to-PDF generation (using `html2canvas` and `jsPDF`) and inconsistent report styles that should be deprecated or removed.

The plan includes:
1.  **Utility Functions:** Specific functions in utility files wrapping `html2canvas` or `jsPDF`.
2.  **Core Library Calls:** Import statements and usage of `html2canvas` and `jsPDF`, and their dependencies in `package.json`.
3.  **Component-Specific Logic:** Event handlers, state variables, and JSX/HTML elements for client-side capture.
4.  **CSS and Styling:** Custom print CSS overrides and styles for hidden/shown elements related to client-side PDF generation.
5.  **Old Report Components (if applicable):** Components built only for the old `html2canvas`/`jsPDF` export.

This existing file fulfills all the requirements of the current subtask. No further modifications are needed.
