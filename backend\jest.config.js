/** @type {import('ts-jest').JestConfigWithTsJest} */
export default {
	displayName: 'backend',
	preset: 'ts-jest/presets/default-esm',
	testEnvironment: 'node',
	moduleFileExtensions: ['ts', 'js', 'json', 'node'],
	transform: {
		'^.+\\.ts$': [
			'ts-jest',
			{
				useESM: true,
				// Make Jest globals available in tests
				isolatedModules: false,
			},
		],
	},
	moduleNameMapper: {
		// To handle .js extensions in imports if your tsconfig outputs them
		'^(\\.{1,2}/.*)\\.js$': '$1',
		// Specific mappings for problematic mocks
		'^src/services/database.service.ts$':
			'<rootDir>/src/services/database.service.ts',
		'^src/utils/prisma.ts$': '<rootDir>/src/utils/prisma.ts',
		'^src/controllers/admin.controller.ts$':
			'<rootDir>/src/controllers/admin.controller.ts',
		'^src/services/admin.service.ts$':
			'<rootDir>/src/services/admin.service.ts',
		'^src/utils/logger.ts$': '<rootDir>/src/utils/logger.ts',
	},
	testMatch: [
		'**/src/tests/**/*.test.ts',
		'<rootDir>/src/**/__tests__/**/*.test.ts',
	],
	// Setup file that helps with ESM compatibility
	setupFilesAfterEnv: ['<rootDir>/src/tests/setup.ts'],
	// If you have global setup/teardown files:
	// globalSetup: './tests/setup.ts',
	// globalTeardown: './tests/teardown.ts',
	extensionsToTreatAsEsm: ['.ts'], // Treat .ts files as ESM

	// Specify the root directory
	rootDir: '.',
	// Specify test directory
	roots: ['<rootDir>/src', '<rootDir>/tests'],
	// Automatically clear mock calls between tests
	clearMocks: true,

	// Collect coverage from src directory, excluding certain files
	collectCoverageFrom: [
		'src/**/*.ts',
		'!src/**/*.d.ts',
		'!src/server.ts', // Example: exclude server entry point
		'!src/swaggerConfig.ts', // Example: exclude swagger config
		'!src/utils/prisma.ts', // Example: exclude prisma client instantiation
	],
	// Coverage thresholds (optional)
	// coverageThreshold: {
	//   global: {
	//     branches: 80,
	//     functions: 80,
	//     lines: 80,
	//     statements: -10,
	//   },
	// },
	// Tell Jest to handle ES modules correctly
	transformIgnorePatterns: ['node_modules/(?!.*\\.mjs$)'],
};
