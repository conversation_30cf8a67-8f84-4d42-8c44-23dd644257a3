(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[494],{6560:(e,s,t)=>{"use strict";t.d(s,{r:()=>c});var a=t(95155),r=t(12115),l=t(30285),i=t(50172),n=t(59434);let c=r.forwardRef((e,s)=>{let{actionType:t="primary",icon:r,isLoading:c=!1,loadingText:d,className:o,children:m,disabled:x,asChild:u=!1,...h}=e,{variant:p,className:g}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[t];return(0,a.jsx)(l.$,{ref:s,variant:p,className:(0,n.cn)(g,o),disabled:c||x,asChild:u,...h,children:c?(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),d||m]}):(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",r&&(0,a.jsx)("span",{className:"mr-2",children:r}),m]})})});c.displayName="ActionButton"},22346:(e,s,t)=>{"use strict";t.d(s,{w:()=>n});var a=t(95155),r=t(12115),l=t(87489),i=t(59434);let n=r.forwardRef((e,s)=>{let{className:t,orientation:r="horizontal",decorative:n=!0,...c}=e;return(0,a.jsx)(l.b,{ref:s,decorative:n,orientation:r,className:(0,i.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",t),...c})});n.displayName=l.b.displayName},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var a=t(95155);t(12115);var r=t(74466),l=t(59434);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:t}),s),...r})}},30466:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>q});var a=t(95155),r=t(12115),l=t(35695),i=t(6874),n=t.n(i),c=t(66766),d=t(2730),o=t(95647),m=t(6560),x=t(66695),u=t(55365),h=t(22346),p=t(26126),g=t(79239),f=t(31949),j=t(12543),N=t(67270),v=t(60335),y=t(67554),b=t(18763),w=t(77223),A=t(19637),k=t(31554),E=t(98328),S=t(57082),T=t(86950),D=t(76570),L=t(28328),M=t(3235),R=t(37648),I=t(50594),C=t(87481),O=t(59434),_=t(73168),F=t(83343),z=t(77023),B=t(30285),P=t(58127),Z=t(40207),U=t(88628),V=t(83662),W=t(88234),H=t(85752);let $=e=>{let{employee:s,refreshInterval:t=3e4}=e,{toast:l}=(0,C.dj)(),[i,n]=(0,r.useState)(null),[c,o]=(0,r.useState)(null),[m,u]=(0,r.useState)({status:"active",lastUpdate:new Date}),[h,g]=(0,r.useState)(!1),[j,N]=(0,r.useState)(null),v=(0,r.useRef)(null),y=(0,r.useRef)(null),b=(0,r.useRef)(null);return(0,r.useEffect)(()=>{(async()=>{if(s.vehicleId)try{let e=await (0,d.getVehicleById)(Number(s.vehicleId));if(e){let t={...e,status:"Available",mileage:Math.floor(1e5*Math.random())+1e4,fuelLevel:Math.floor(100*Math.random())+1,currentLocation:s.currentLocation||"Main Garage"};n(t)}}catch(e){console.error("Failed to fetch vehicle data:",e)}})()},[s.vehicleId,s.currentLocation]),(0,r.useEffect)(()=>((async()=>{if(v.current)try{let e=H.map(v.current).setView([40.7128,-74.006],13);H.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png",{maxZoom:19,attribution:'&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'}).addTo(e),y.current=e,b.current=H.marker([40.7128,-74.006]).addTo(e).bindPopup("".concat(s.name," - ").concat(s.role))}catch(e){console.error("Failed to initialize map:",e),l({title:"Map Error",description:"Failed to load map component.",variant:"destructive"})}})(),()=>{y.current&&y.current.remove()}),[s.name,s.role,l]),(0,r.useEffect)(()=>{if(!h)return;let e=setInterval(()=>{let e={latitude:40.7128+(Math.random()-.5)*.01,longitude:-74.006+(Math.random()-.5)*.01,timestamp:new Date,accuracy:Math.floor(10*Math.random())+5,speed:Math.floor(60*Math.random())+20,heading:Math.floor(360*Math.random())};if(o(e),N(new Date),y.current&&b.current){let s=H.latLng(e.latitude,e.longitude);b.current.setLatLng(s),y.current.setView(s)}let s=["active","break","active","active"],t=s[Math.floor(Math.random()*s.length)];u(e=>({...e,status:t,lastUpdate:new Date,currentTask:"active"===t?"Delivering to Main Street":void 0}))},t);return()=>clearInterval(e)},[h,t]),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(x.Zp,{children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(Z.A,{className:"h-5 w-5"}),"Driver Status"]}),(0,a.jsx)("div",{className:"flex gap-2",children:h?(0,a.jsx)(B.$,{onClick:()=>{g(!1),l({title:"Tracking Stopped",description:"Real-time tracking disabled for ".concat(s.name),variant:"default"})},variant:"outline",size:"sm",children:"Stop Tracking"}):(0,a.jsxs)(B.$,{onClick:()=>{g(!0),l({title:"Tracking Started",description:"Real-time tracking enabled for ".concat(s.name),variant:"default"})},size:"sm",children:[(0,a.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Start Tracking"]})})]})}),(0,a.jsx)(x.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat((e=>{switch(e){case"active":return"bg-green-500";case"break":return"bg-yellow-500";case"offline":default:return"bg-gray-500";case"emergency":return"bg-red-500"}})(m.status))}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(e=>{switch(e){case"active":return(0,a.jsx)(P.A,{className:"h-4 w-4"});case"break":return(0,a.jsx)(R.A,{className:"h-4 w-4"});case"offline":default:return(0,a.jsx)(Z.A,{className:"h-4 w-4"});case"emergency":return(0,a.jsx)(f.A,{className:"h-4 w-4"})}})(m.status),(0,a.jsx)("span",{className:"font-medium capitalize",children:m.status})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Updated: ",m.lastUpdate.toLocaleTimeString()]})]})]}),m.currentTask&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(V.A,{className:"h-4 w-4 text-blue-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Current Task"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:m.currentTask})]})]}),j&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(R.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Last Location Update"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:j.toLocaleTimeString()})]})]})]})})]}),c&&(0,a.jsxs)(x.Zp,{children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(V.A,{className:"h-5 w-5"}),"Live Location"]})}),(0,a.jsxs)(x.Wu,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Coordinates"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[c.latitude.toFixed(6),","," ",c.longitude.toFixed(6)]})]}),c.speed&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Speed"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[c.speed," mph"]})]}),c.accuracy&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Accuracy"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["\xb1",c.accuracy,"m"]})]}),c.heading&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Heading"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[c.heading,"\xb0"]})]})]}),(0,a.jsx)("div",{ref:v,className:"h-64 w-full rounded-lg border",style:{minHeight:"250px"}})]})]}),i&&(0,a.jsxs)(x.Zp,{children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(L.A,{className:"h-5 w-5"}),"Assigned Vehicle"]})}),(0,a.jsx)(x.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Vehicle"}),(0,a.jsxs)("p",{className:"text-lg",children:[i.make," ",i.model," (",i.year,")"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"License Plate"}),(0,a.jsx)("p",{className:"text-sm font-mono bg-muted px-2 py-1 rounded w-fit",children:i.licensePlate})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Status"}),(0,a.jsx)(p.E,{variant:"Available"===i.status?"default":"secondary",children:i.status})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[i.mileage&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Mileage"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[i.mileage.toLocaleString()," miles"]})]}),i.fuelLevel&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Fuel Level"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(W.A,{className:"h-4 w-4"}),(0,a.jsx)("div",{className:"flex-1 bg-muted rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:"".concat(i.fuelLevel,"%")}})}),(0,a.jsxs)("span",{className:"text-sm",children:[i.fuelLevel,"%"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Location"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:i.currentLocation||"Location unknown"})]})]})]})})]}),(0,a.jsxs)(x.Zp,{children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-5 w-5"}),"Communication"]})}),(0,a.jsx)(x.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)(B.$,{variant:"outline",className:"justify-start",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Call Driver"]}),(0,a.jsxs)(B.$,{variant:"outline",className:"justify-start",children:[(0,a.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Send Location"]})]})})]})]})},G=e=>{if(!e)return"bg-gray-500/20 text-gray-700 border-gray-500/30";switch(e){case"Active":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"On Leave":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Terminated":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},X=e=>{if(!e)return"bg-gray-500/20 text-gray-700 border-gray-500/30";switch(e){case"On_Shift":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Off_Shift":default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20";case"On_Break":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Busy":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20"}},q=()=>{let e=(0,l.useRouter)(),s=(0,l.useParams)(),{toast:t}=(0,C.dj)(),i=s.id,B=parseInt(i,10),[P,Z]=(0,r.useState)(null),[U,V]=(0,r.useState)(null),[W,H]=(0,r.useState)(!0),[q,Y]=(0,r.useState)(null),[J,K]=(0,r.useState)(!1),Q=(0,r.useCallback)(async()=>{if(isNaN(B)){Y("Invalid employee ID."),H(!1);return}H(!0),Y(null);try{let e=await (0,d.getEmployeeById)(B);if(e){if(Z(e),"driver"===e.role&&e.vehicleId)try{let s=await (0,d.getVehicleById)(Number(e.vehicleId));V(s||null)}catch(e){console.warn("Failed to fetch assigned vehicle:",e),V(null)}}else Y("Employee not found.")}catch(e){console.error("Failed to fetch employee details:",e),Y(e.message||"Failed to load employee data.")}finally{H(!1)}},[B]);(0,r.useEffect)(()=>{i&&Q()},[i,Q]);let ee=async()=>{if(isNaN(B)||!P)return void t({title:"Error",description:"Invalid employee ID or employee data missing.",variant:"destructive"});let s=P.name||P.fullName||"Employee";if(window.confirm("Are you sure you want to permanently delete ".concat(s,"?\n\nThis action cannot be undone and will remove all employee data from the system."))){K(!0),Y(null);try{console.log("Attempting to delete employee with ID: ".concat(B)),await (0,d.deleteEmployee)(B),t({title:"Employee Deleted Successfully",description:"".concat(s," has been permanently removed from the system."),variant:"default"}),e.push("/employees")}catch(s){var a,r,l,i,n,c;console.error("Failed to delete employee:",s);let e="Could not delete employee. Please try again.";(null==(a=s.message)?void 0:a.includes("Network error"))?e="Network error. Please check your connection and try again.":(null==(r=s.message)?void 0:r.includes("404"))?e="Employee not found or already deleted.":(null==(l=s.message)?void 0:l.includes("403"))?e="You do not have permission to delete this employee.":(null==(i=s.message)?void 0:i.includes("500"))?e="Server error. Please contact support if this persists.":(null==(c=s.response)||null==(n=c.data)?void 0:n.error)?e=s.response.data.error:s.message&&(e=s.message),Y(e),t({title:"Failed to Delete Employee",description:e,variant:"destructive"})}finally{K(!1)}}},es=e=>{let{icon:s,label:t,value:r,valueClassName:l}=e;return r||0===r?(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(s,{className:"h-5 w-5 text-accent mr-3 mt-0.5 shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:t}),(0,a.jsx)("p",{className:(0,O.cn)("text-base font-semibold text-foreground",l),children:r})]})]}):null};return(0,a.jsx)("div",{className:"container mx-auto py-8 space-y-8",children:(0,a.jsx)(z.gO,{isLoading:W,error:q,data:P,onRetry:Q,loadingComponent:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(o.z,{title:"Loading Employee...",icon:g.A}),(0,a.jsx)(z.jt,{variant:"card",count:1})]}),emptyComponent:(0,a.jsxs)("div",{className:"text-center py-10",children:[(0,a.jsx)(o.z,{title:"Employee Not Found",icon:f.A}),(0,a.jsx)("p",{className:"mb-4",children:"The requested employee could not be found."}),(0,a.jsx)(m.r,{actionType:"primary",onClick:()=>e.push("/employees"),icon:(0,a.jsx)(j.A,{className:"h-4 w-4"}),children:"Back to Employees"})]}),children:e=>{var s;let t=e.name||e.fullName||"Employee";return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.z,{title:t,description:"Details for Employee ID: ".concat(e.id),icon:g.A}),q&&(0,a.jsxs)(u.Fc,{variant:"destructive",children:[(0,a.jsx)(N.A,{className:"h-5 w-5"}),(0,a.jsx)(u.XL,{children:"An Error Occurred"}),(0,a.jsx)(u.TN,{children:q})]}),(0,a.jsxs)(x.Zp,{className:"shadow-lg",children:[(0,a.jsx)(x.aR,{className:"p-5",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"relative w-20 h-20 rounded-full overflow-hidden bg-muted flex items-center justify-center ring-2 ring-primary/30",children:e.profileImageUrl?(0,a.jsx)(c.default,{src:e.profileImageUrl,alt:t,layout:"fill",objectFit:"cover","data-ai-hint":"employee profile"}):(0,a.jsx)(v.A,{className:"h-12 w-12 text-muted-foreground"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.ZB,{className:"text-2xl font-bold text-primary",children:t}),(0,a.jsxs)(x.BT,{className:"text-sm",children:[e.position," -"," ",e.department]}),(0,a.jsxs)("div",{className:"mt-1 flex items-center gap-2",children:[(0,a.jsx)(p.E,{className:(0,O.cn)("text-xs",G(e.status)),children:e.status}),"driver"===e.role&&e.availability&&(0,a.jsx)(p.E,{className:(0,O.cn)("text-xs",X(e.availability)),children:null==(s=e.availability)?void 0:s.replace("_"," ")})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 self-start sm:self-center",children:[(0,a.jsxs)(m.r,{actionType:"tertiary",size:"icon",title:"Refresh Employee Data",onClick:Q,disabled:W,className:"relative group",children:[(0,a.jsx)(y.A,{className:(0,O.cn)("h-4 w-4",W&&"animate-spin")}),(0,a.jsx)("span",{className:"sr-only",children:W?"Refreshing...":"Refresh employee data"})]}),(0,a.jsx)(m.r,{actionType:"secondary",size:"icon",asChild:!0,title:"Edit Employee Details",className:"relative group",children:(0,a.jsxs)(n(),{href:"/employees/".concat(e.id,"/edit"),className:"inline-flex items-center justify-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:["Edit ",t]})]})}),(0,a.jsxs)(m.r,{actionType:"danger",size:"icon",title:"Delete ".concat(t),onClick:ee,isLoading:J,disabled:J,className:"relative group",loadingText:"",children:[(0,a.jsx)(w.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:J?"Deleting...":"Delete ".concat(t)})]})]})]})}),(0,a.jsxs)(x.Wu,{className:"mt-2 space-y-8 p-5",children:[(0,a.jsxs)("section",{children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold mb-3 text-primary flex items-center",children:[(0,a.jsx)(v.A,{className:"mr-2 h-5 w-5 text-accent"})," ","Contact & Employment"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4",children:[(0,a.jsx)(es,{icon:A.A,label:"Email / Primary Contact",value:e.contactEmail}),e.contactMobile&&(0,a.jsx)(es,{icon:k.A,label:"Mobile",value:e.contactMobile}),e.contactPhone&&!e.contactMobile&&(0,a.jsx)(es,{icon:k.A,label:"Phone",value:e.contactPhone}),(0,a.jsx)(es,{icon:E.A,label:"Hire Date",value:e.hireDate?(0,_.GP)((0,F.H)(e.hireDate),"MMMM d, yyyy"):"N/A"}),(0,a.jsx)(es,{icon:S.A,label:"Role",value:e.role?e.role.charAt(0).toUpperCase()+e.role.slice(1).replace("_"," "):"N/A"}),(0,a.jsx)(es,{icon:T.A,label:"Department",value:e.department}),(0,a.jsx)(es,{icon:D.A,label:"Employee ID (System)",value:e.id.toString()})]})]}),("driver"===e.role||e.skills&&e.skills.length>0||e.shiftSchedule||e.generalAssignments&&e.generalAssignments.length>0)&&(0,a.jsx)(h.w,{className:"my-4"}),"driver"===e.role&&(0,a.jsxs)("section",{children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold mb-3 text-primary flex items-center",children:[(0,a.jsx)(L.A,{className:"mr-2 h-5 w-5 text-accent"})," Driver Information"]}),(0,a.jsx)($,{employee:e})]}),e.skills&&e.skills.length>0||e.shiftSchedule||e.generalAssignments&&e.generalAssignments.length>0?(0,a.jsxs)("section",{children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold mb-3 text-primary flex items-center",children:[(0,a.jsx)(M.A,{className:"mr-2 h-5 w-5 text-accent"})," Work Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4",children:[e.skills&&e.skills.length>0&&(0,a.jsx)(es,{icon:M.A,label:"Skills",value:e.skills.join(", ")}),(0,a.jsx)(es,{icon:R.A,label:"Shift Schedule",value:e.shiftSchedule}),e.generalAssignments&&e.generalAssignments.length>0&&(0,a.jsx)(es,{icon:S.A,label:"General Assignments",value:e.generalAssignments.join(", ")})]})]}):null,e.notes&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.w,{className:"my-4"}),(0,a.jsxs)("section",{children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold mb-2 text-primary flex items-center",children:[(0,a.jsx)(I.A,{className:"mr-2 h-5 w-5 text-accent"})," Notes"]}),(0,a.jsx)("p",{className:"text-sm text-foreground whitespace-pre-wrap bg-muted/50 p-3 rounded-md",children:e.notes})]})]})]}),(0,a.jsxs)(x.wL,{className:"text-xs text-muted-foreground p-5 pt-4 border-t",children:["Registered:"," ",new Date(e.createdAt).toLocaleString()," | Last updated: ",new Date(e.updatedAt).toLocaleString()]})]})]})}})})}},55365:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>c,TN:()=>o,XL:()=>d});var a=t(95155),r=t(12115),l=t(74466),i=t(59434);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=r.forwardRef((e,s)=>{let{className:t,variant:r,...l}=e;return(0,a.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(n({variant:r}),t),...l})});c.displayName="Alert";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})});d.displayName="AlertTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...r})});o.displayName="AlertDescription"},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>n,wL:()=>m});var a=t(95155),r=t(12115),l=t(59434);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});n.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})});m.displayName="CardFooter"},68856:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(95155),r=t(59434);function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",s),...t})}},77023:(e,s,t)=>{"use strict";t.d(s,{gO:()=>p,jt:()=>u});var a=t(95155);t(12115);var r=t(50172),l=t(11133),i=t(59434),n=t(68856),c=t(55365),d=t(6560);let o={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},m={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function x(e){let{size:s="md",className:t,text:l,fullPage:n=!1}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex items-center justify-center",n&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",t),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(r.A,{className:(0,i.cn)("animate-spin text-primary",o[s])}),l&&(0,a.jsx)("span",{className:(0,i.cn)("mt-2 text-muted-foreground",m[s]),children:l})]})})}function u(e){let{variant:s="default",count:t=1,className:r,testId:l="loading-skeleton"}=e;return"card"===s?(0,a.jsx)("div",{className:(0,i.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",r),"data-testid":l,children:Array(t).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,a.jsx)(n.E,{className:"aspect-[16/10] w-full"}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)(n.E,{className:"h-7 w-3/4 mb-1"}),(0,a.jsx)(n.E,{className:"h-4 w-1/2 mb-3"}),(0,a.jsx)(n.E,{className:"h-px w-full my-3"}),(0,a.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,a.jsx)(n.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===s?(0,a.jsxs)("div",{className:(0,i.cn)("space-y-3",r),"data-testid":l,children:[(0,a.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,a.jsx)(n.E,{className:"h-8 flex-1"},s))}),Array(t).fill(0).map((e,s)=>(0,a.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,a.jsx)(n.E,{className:"h-6 flex-1"},s))},s))]}):"list"===s?(0,a.jsx)("div",{className:(0,i.cn)("space-y-3",r),"data-testid":l,children:Array(t).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(n.E,{className:"h-12 w-12 rounded-full"}),(0,a.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,a.jsx)(n.E,{className:"h-4 w-1/3"}),(0,a.jsx)(n.E,{className:"h-4 w-full"})]})]},s))}):"stats"===s?(0,a.jsx)("div",{className:(0,i.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",r),"data-testid":l,children:Array(t).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(n.E,{className:"h-5 w-1/3"}),(0,a.jsx)(n.E,{className:"h-5 w-5 rounded-full"})]}),(0,a.jsx)(n.E,{className:"h-8 w-1/2 mt-3"}),(0,a.jsx)(n.E,{className:"h-4 w-2/3 mt-2"})]},s))}):(0,a.jsx)("div",{className:(0,i.cn)("space-y-2",r),"data-testid":l,children:Array(t).fill(0).map((e,s)=>(0,a.jsx)(n.E,{className:"w-full h-5"},s))})}function h(e){let{message:s,onRetry:t,className:n}=e;return(0,a.jsxs)(c.Fc,{variant:"destructive",className:(0,i.cn)("my-4",n),children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)(c.XL,{children:"Error"}),(0,a.jsx)(c.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:s}),t&&(0,a.jsx)(d.r,{actionType:"tertiary",size:"sm",onClick:t,icon:(0,a.jsx)(r.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function p(e){let{isLoading:s,error:t,data:r,onRetry:l,children:n,loadingComponent:c,errorComponent:d,emptyComponent:o,className:m}=e;return s?c||(0,a.jsx)(x,{className:m,text:"Loading..."}):t?d||(0,a.jsx)(h,{message:t,onRetry:l,className:m}):!r||Array.isArray(r)&&0===r.length?o||(0,a.jsx)("div",{className:(0,i.cn)("text-center py-8",m),children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,a.jsx)("div",{className:m,children:n(r)})}},87481:(e,s,t)=>{"use strict";t.d(s,{dj:()=>x});var a=t(12115);let r=0,l=new Map,i=e=>{if(l.has(e))return;let s=setTimeout(()=>{l.delete(e),o({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,s)},n=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=s;return t?i(t):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},c=[],d={toasts:[]};function o(e){d=n(d,e),c.forEach(e=>{e(d)})}function m(e){let{...s}=e,t=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>o({type:"DISMISS_TOAST",toastId:t});return o({type:"ADD_TOAST",toast:{...s,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function x(){let[e,s]=a.useState(d);return a.useEffect(()=>(c.push(s),()=>{let e=c.indexOf(s);e>-1&&c.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>o({type:"DISMISS_TOAST",toastId:e})}}},91209:(e,s,t)=>{Promise.resolve().then(t.bind(t,30466))},95647:(e,s,t)=>{"use strict";t.d(s,{z:()=>r});var a=t(95155);function r(e){let{title:s,description:t,icon:r,children:l}=e;return(0,a.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,a.jsx)(r,{className:"h-8 w-8 text-primary"}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:s})]}),t&&(0,a.jsx)("p",{className:"text-muted-foreground mt-1",children:t})]}),l&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:l})]})}t(12115)}},e=>{var s=s=>e(e.s=s);e.O(0,[1761,5769,8360,832,7529,6766,2642,8162,2730,8441,1684,7358],()=>s(91209)),_N_E=e.O()}]);