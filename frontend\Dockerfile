# PHASE 1 SECURITY HARDENING: Frontend Dockerfile - <PERSON><PERSON>IT<PERSON> HARDENED VERSION
FROM node:18-alpine AS builder

# Security: Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# Copy package files with proper ownership
COPY --chown=nextjs:nodejs package*.json ./
RUN npm ci && npm cache clean --force

# Copy source code with proper ownership
COPY --chown=nextjs:nodejs . .

# Create public directory if it doesn't exist
RUN mkdir -p public

# Accept build arguments for environment variables
ARG NEXT_PUBLIC_SUPABASE_URL
ARG NEXT_PUBLIC_SUPABASE_ANON_KEY
ARG NEXT_PUBLIC_API_BASE_URL
ARG NEXT_PUBLIC_API_URL

# Set environment variables for build
ENV NEXT_PUBLIC_SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=$NEXT_PUBLIC_SUPABASE_ANON_KEY
ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS runtime

# Security: Install security updates and dumb-init
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# Security: Copy built application with proper ownership
COPY --from=builder --chown=nextjs:nodejs /app/next.config.ts ./next.config.ts
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules

# Security: Remove package manager tools to reduce attack surface
RUN apk del apk-tools

# Security: Switch to non-root user
USER nextjs

# Expose the port the app runs on
EXPOSE 3000

# Security: Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Security: Start application
CMD ["npm", "start"]

# Security: Add labels for scanning and compliance
LABEL security.scan="enabled"
LABEL security.last-updated="2025-05-24"
LABEL security.non-root="true"
LABEL security.phase="PHASE-1-HARDENED"
LABEL maintainer="WorkHub Security Team"