(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8708],{11284:(e,t,s)=>{"use strict";s.d(t,{a:()=>o});var r=s(12115),l=s(46750);let a={interval:3e4,enabled:!0,errorHandler:e=>console.error("Refresh error:",e),immediate:!1,retryLimit:3,retryDelay:2e3};class i{start(){null===this.timerId&&(this.timerId=window.setTimeout(()=>this.refreshCycle(),this.options.interval))}stop(){null!==this.timerId&&(window.clearTimeout(this.timerId),this.timerId=null)}async refresh(){if(!this.isRefreshing){this.isRefreshing=!0;try{await this.callback(),this.consecutiveFailures=0,this.lastSuccessTime=Date.now()}catch(e){this.consecutiveFailures++,this.options.errorHandler(e)}finally{this.isRefreshing=!1}}}updateOptions(e){let t=this.options.enabled;this.options={...this.options,...e},null!==this.timerId&&this.stop(),this.options.enabled&&(!t||e.interval)&&this.start()}async refreshCycle(){if(!this.isOnline||!this.isVisible||this.isRefreshing)return void this.reschedule();if(this.consecutiveFailures>=this.options.retryLimit){let e=Math.min(this.options.retryDelay*Math.pow(2,this.consecutiveFailures-1),6e4);this.timerId=window.setTimeout(()=>this.refreshCycle(),e);return}await this.refresh(),this.reschedule()}reschedule(){this.options.enabled&&(this.timerId=window.setTimeout(()=>this.refreshCycle(),this.options.interval))}setupBrowserEventListeners(){document.addEventListener("visibilitychange",()=>{this.isVisible="visible"===document.visibilityState,this.isVisible&&this.options.enabled&&Date.now()-this.lastSuccessTime>this.options.interval&&(this.stop(),this.refresh(),this.start())}),window.addEventListener("online",()=>{this.isOnline=!0,this.options.enabled&&(this.stop(),this.refresh(),this.start())}),window.addEventListener("offline",()=>{this.isOnline=!1}),this.isOnline=navigator.onLine,this.isVisible="visible"===document.visibilityState}dispose(){this.stop()}constructor(e,t={}){this.timerId=null,this.isRefreshing=!1,this.consecutiveFailures=0,this.lastSuccessTime=0,this.isVisible=!0,this.isOnline=!0,this.callback=e,this.options={...a,...t},this.setupBrowserEventListeners(),this.options.enabled&&this.options.immediate?this.refresh():this.options.enabled&&this.start()}}let n=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new i(e,t)};function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{interval:a=6e4,enabled:i=!0,immediate:o=!0,socketUrl:d,enableSocket:c=!0,enablePolling:u=!0,onRefresh:m,onError:h}=s,[f,p]=(0,r.useState)(null),[x,g]=(0,r.useState)(!1),{isConnected:y,subscribe:b}=(0,l.F)({url:d,autoConnect:c&&i}),j=(0,r.useCallback)(async()=>{try{let t=await e();return p(new Date),null==m||m(),t}catch(e){throw null==h||h(e),e}},[e,m,h]),{isRefreshing:v,refresh:w}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{interval:s=3e4,enabled:l=!0,immediate:a=!1,onRefresh:i,onError:o}=t,[d,c]=(0,r.useState)(!1),u=(0,r.useRef)(null),m=(0,r.useCallback)(async()=>{c(!0);try{await e(),null==i||i()}catch(e){throw null==o||o(e),e}finally{c(!1)}},[e,i,o]);return(0,r.useEffect)(()=>(u.current=n(m,{interval:s,enabled:l,immediate:a,errorHandler:o}),()=>{u.current&&(u.current.dispose(),u.current=null)}),[]),(0,r.useEffect)(()=>{u.current&&u.current.updateOptions({interval:s,enabled:l})},[s,l]),{isRefreshing:d,refresh:(0,r.useCallback)(async()=>{u.current&&await u.current.refresh()},[])}}(async()=>{await j()},{interval:a,enabled:u&&i&&(!y||!c),immediate:o&&(!y||!c||!t.length),onError:h});return(0,r.useEffect)(()=>{if(!c||!i||!y||0===t.length)return;let e=t.map(e=>b(e,t=>{console.log("Socket event received: ".concat(e),t),g(!0),w().catch(console.error),setTimeout(()=>g(!1),1e3)}));return()=>{e.forEach(e=>e())}},[c,i,y,t,b,w]),{isRefreshing:v,socketTriggered:x,isConnected:y,lastUpdated:f,refresh:(0,r.useCallback)(async()=>await w(),[w])}}},19637:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},20636:(e,t,s)=>{"use strict";s.d(t,{O2:()=>a,Q:()=>i,gT:()=>n});var r=s(55594),l=s(33450);let a=r.k5(["Active","On Leave","Terminated","Inactive"]),i=r.k5(["driver","mechanic","administrator","office_staff","manager","service_advisor","technician","other"]),n=r.Ik({name:r.Yj().min(1,"Name is required"),fullName:r.Yj().min(1,"Full name is required").optional(),employeeId:r.Yj().min(1,"Employee ID (unique business ID) is required"),position:r.Yj().min(1,"Position/Title is required"),department:r.Yj().min(1,"Department is required"),contactInfo:r.Yj().min(1,"Primary contact (email or phone) is required").refine(e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)||/^[\d\s()+-]{7,20}$/.test(e),"Must be a valid email or phone number."),contactEmail:r.Yj().email("Invalid email address").optional().nullable().or(r.eu("")),contactPhone:r.Yj().optional().nullable().or(r.eu("")),contactMobile:r.Yj().optional().nullable().or(r.eu("")),hireDate:r.Yj().refine(e=>e&&!isNaN(Date.parse(e)),{message:"Invalid hire date"}),status:a.default("Active"),role:i.default("other"),availability:l.X.optional().nullable(),currentLocation:r.Yj().optional().or(r.eu("")),workingHours:r.Yj().optional().or(r.eu("")),assignedVehicleId:r.ai().int().positive().nullable().optional(),skills:r.YO(r.Yj()).optional().default([]),shiftSchedule:r.Yj().optional().or(r.eu("")),generalAssignments:r.YO(r.Yj()).optional().default([]),notes:r.Yj().optional().or(r.eu("")),profileImageUrl:r.Yj().url("Invalid URL for profile image").optional().or(r.eu("")),statusChangeReason:r.Yj().optional().nullable()})},22346:(e,t,s)=>{"use strict";s.d(t,{w:()=>n});var r=s(95155),l=s(12115),a=s(87489),i=s(59434);let n=l.forwardRef((e,t)=>{let{className:s,orientation:l="horizontal",decorative:n=!0,...o}=e;return(0,r.jsx)(a.b,{ref:t,decorative:n,orientation:l,className:(0,i.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",s),...o})});n.displayName=a.b.displayName},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(95155);s(12115);var l=s(74466),a=s(59434);let i=(0,l.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:s,...l}=e;return(0,r.jsx)("div",{className:(0,a.cn)(i({variant:s}),t),...l})}},31554:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},33450:(e,t,s)=>{"use strict";s.d(t,{X:()=>r});let r=s(55594).k5(["On_Shift","Off_Shift","On_Break","Busy"])},39718:(e,t,s)=>{Promise.resolve().then(s.bind(s,61128))},40968:(e,t,s)=>{"use strict";s.d(t,{b:()=>n});var r=s(12115),l=s(63655),a=s(95155),i=r.forwardRef((e,t)=>(0,a.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var n=i},46750:(e,t,s)=>{"use strict";s.d(t,{A:()=>a,F:()=>i});var r=s(12115),l=s(14298);let a={VEHICLE_UPDATED:"vehicle:updated",VEHICLE_CREATED:"vehicle:created",VEHICLE_DELETED:"vehicle:deleted",EMPLOYEE_UPDATED:"employee:updated",EMPLOYEE_CREATED:"employee:created",EMPLOYEE_DELETED:"employee:deleted",REFRESH_VEHICLES:"refresh:vehicles",REFRESH_EMPLOYEES:"refresh:employees"};function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{url:t="http://localhost:3001/api".replace("/api","")||"http://localhost:4000",autoConnect:s=!0}=e,[a,i]=(0,r.useState)(!1),[n,o]=(0,r.useState)(null),d=(0,r.useRef)(null);(0,r.useEffect)(()=>{if(!d.current&&s)try{d.current=(0,l.io)(t,{reconnectionAttempts:5,reconnectionDelay:1e3,autoConnect:!0}),d.current.on("connect",()=>{var e;i(!0),o(null),console.log("Socket connected:",null==(e=d.current)?void 0:e.id)}),d.current.on("disconnect",e=>{i(!1),console.log("Socket disconnected:",e)}),d.current.on("connect_error",e=>{o(e),console.error("Socket connection error:",e)})}catch(e){o(e instanceof Error?e:Error("Unknown socket error")),console.error("Error initializing socket:",e)}return()=>{d.current&&(d.current.disconnect(),d.current=null,i(!1))}},[t,s]);let c=(0,r.useCallback)((e,t)=>d.current?(d.current.on(e,t),()=>{var s;null==(s=d.current)||s.off(e,t)}):()=>{},[]),u=(0,r.useCallback)(()=>{if(d.current)d.current.connected||d.current.connect();else try{d.current=(0,l.io)(t),d.current.connect()}catch(e){o(e instanceof Error?e:Error("Failed to connect"))}},[t]),m=(0,r.useCallback)(()=>{d.current&&d.current.disconnect()},[]);return{socket:d.current,isConnected:a,error:n,subscribe:c,connect:u,disconnect:m}}},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>m,eb:()=>x,gC:()=>p,l6:()=>c,yv:()=>u});var r=s(95155),l=s(12115),a=s(31992),i=s(79556),n=s(77381),o=s(10518),d=s(59434);let c=a.bL;a.YJ;let u=a.WT,m=l.forwardRef((e,t)=>{let{className:s,children:l,...n}=e;return(0,r.jsxs)(a.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...n,children:[l,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=a.l9.displayName;let h=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...l,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=a.PP.displayName;let f=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...l,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})});f.displayName=a.wn.displayName;let p=l.forwardRef((e,t)=>{let{className:s,children:l,position:i="popper",...n}=e;return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,...n,children:[(0,r.jsx)(h,{}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,r.jsx)(f,{})]})})});p.displayName=a.UC.displayName,l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...l})}).displayName=a.JU.displayName;let x=l.forwardRef((e,t)=>{let{className:s,children:l,...i}=e;return(0,r.jsxs)(a.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(a.p4,{children:l})]})});x.displayName=a.q7.displayName,l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),...l})}).displayName=a.wv.displayName},60335:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("CircleUserRound",[["path",{d:"M18 20a6 6 0 0 0-12 0",key:"1qehca"}],["circle",{cx:"12",cy:"10",r:"4",key:"1h16sb"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},61128:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>J});var r=s(95155),l=s(12115),a=s(6874),i=s.n(a),n=s(79239),o=s(67554),d=s(34301),c=s(75074),u=s(2730),m=s(11284),h=s(46750);let f=e=>{let{children:t}=e,[s,a]=(0,l.useState)([]),[i,n]=(0,l.useState)(!0),[o,d]=(0,l.useState)(null),c=(0,l.useCallback)(async()=>{i&&n(!0);try{try{console.info("Fetching enriched employees");let e=await (0,u.getEnrichedEmployees)();a(e),d(null);return}catch(t){console.warn("Failed to fetch enriched employees, falling back to regular endpoint:",t);let e=await (0,u.getEmployees)();a(e),d(null)}}catch(e){console.error("Error fetching employees:",e),d("Failed to fetch employees. Please try again.")}finally{n(!1)}},[i]),{isRefreshing:f,refresh:p,isConnected:x,socketTriggered:g}=(0,m.a)(c,[h.A.EMPLOYEE_CREATED,h.A.EMPLOYEE_UPDATED,h.A.EMPLOYEE_DELETED,h.A.REFRESH_EMPLOYEES],{interval:3e4,enabled:!0,immediate:!0,enableSocket:!0,enablePolling:!0,onError:e=>{console.error("Socket refresh error:",e)}});(0,l.useEffect)(()=>{g||c()},[c,g]);let y=async e=>{try{await (0,u.deleteEmployee)(e),a(t=>t.filter(t=>t.id!==e))}catch(e){throw console.error("Error deleting employee:",e),d("Failed to delete employee: "+(e.message||"Unknown error")),e}};return(0,r.jsx)(r.Fragment,{children:t({employees:s,loading:i,error:o,handleDelete:y,fetchEmployees:p,isRefreshing:f,isConnected:x,socketTriggered:g})})};var p=s(95647),x=s(62523),g=s(68856),y=s(66766),b=s(66695),j=s(6560),v=s(60335),w=s(86950),N=s(19637),E=s(31554),k=s(76570),A=s(83662),S=s(28328),C=s(3235),D=s(19968),T=s(26126),L=s(22346),R=s(59434),M=s(73168),_=s(83343);let O=e=>{switch(e){case"Active":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"On Leave":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Terminated":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},I=e=>{if(!e)return"";switch(e){case"On_Shift":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Off_Shift":default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20";case"On_Break":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Busy":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20"}};function F(e){var t;let{employee:s}=e,l="assignedVehicleDetails"in s;return(0,r.jsxs)(b.Zp,{className:"overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60",children:[(0,r.jsx)(b.aR,{className:"p-5",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"relative w-12 h-12 rounded-full overflow-hidden bg-muted flex items-center justify-center ring-2 ring-primary/30",children:s.profileImageUrl?(0,r.jsx)(y.default,{src:s.profileImageUrl,alt:s.fullName||s.name,layout:"fill",objectFit:"cover","data-ai-hint":"employee photo"}):(0,r.jsx)(v.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b.ZB,{className:"text-xl font-semibold text-primary",children:s.fullName||s.name}),(0,r.jsxs)(b.BT,{className:"text-sm text-muted-foreground",children:[s.position," (",s.role?s.role.charAt(0).toUpperCase()+s.role.slice(1).replace("_"," "):"N/A",")"]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col items-end gap-1",children:[(0,r.jsx)(T.E,{className:(0,R.cn)("text-xs py-1 px-2 font-semibold",O(s.status)),children:s.status}),"driver"===s.role&&s.availability&&(0,r.jsx)(T.E,{className:(0,R.cn)("text-xs py-1 px-2 font-semibold",I(s.availability)),children:null==(t=s.availability)?void 0:t.replace("_"," ")})]})]})}),(0,r.jsxs)(b.Wu,{className:"p-5 flex-grow flex flex-col",children:[(0,r.jsx)(L.w,{className:"my-3 bg-border/50"}),(0,r.jsxs)("div",{className:"space-y-2.5 text-sm text-foreground flex-grow",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Department: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.department})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Email: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.contactEmail})]})]}),s.contactMobile&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(E.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Mobile: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.contactMobile})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(k.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Hire Date: "}),(0,r.jsx)("strong",{className:"font-semibold",children:(0,M.GP)((0,_.H)(s.hireDate),"MMM d, yyyy")})]})]}),"driver"===s.role&&(0,r.jsxs)(r.Fragment,{children:[s.currentLocation&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Location: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.currentLocation})]})]}),s.assignedVehicleId&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Vehicle: "}),(0,r.jsx)("strong",{className:"font-semibold",children:l&&s.assignedVehicleDetails?(0,r.jsxs)(r.Fragment,{children:[s.assignedVehicleDetails.make," ",s.assignedVehicleDetails.model," (",s.assignedVehicleDetails.year,")",s.assignedVehicleDetails.licensePlate&&(0,r.jsxs)("span",{className:"ml-1 text-xs text-muted-foreground",children:["[",s.assignedVehicleDetails.licensePlate,"]"]}),s.assignedVehicleDetails.color&&(0,r.jsxs)("span",{className:"ml-1 text-xs text-muted-foreground",children:["• ",s.assignedVehicleDetails.color]})]}):(0,r.jsxs)(r.Fragment,{children:["Vehicle ID: ",s.assignedVehicleId]})})]})]})]}),s.skills&&s.skills.length>0&&(0,r.jsxs)("div",{className:"flex items-start pt-1",children:[(0,r.jsx)(C.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Skills: "}),(0,r.jsx)("p",{className:"text-xs font-semibold leading-tight",children:s.skills.join(", ")})]})]})]})]}),(0,r.jsx)(b.wL,{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,r.jsx)(j.r,{actionType:"tertiary",className:"w-full",icon:(0,r.jsx)(D.A,{className:"h-4 w-4"}),asChild:!0,children:(0,r.jsx)(i(),{href:"/employees/".concat(s.id),children:"View Details"})})})]})}var V=s(85057),Y=s(59409),P=s(20636),U=s(87481),q=s(77023),z=s(15080);function H(){return(0,r.jsxs)("div",{className:"overflow-hidden shadow-lg flex flex-col h-full bg-card border-border/60 rounded-lg",children:[(0,r.jsxs)("div",{className:"p-5 flex-grow flex flex-col",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)(g.E,{className:"h-8 w-3/5 bg-muted/50"}),(0,r.jsx)(g.E,{className:"h-5 w-1/4 bg-muted/50 rounded-full"})]}),(0,r.jsx)(g.E,{className:"h-4 w-1/2 mb-1 bg-muted/50"}),(0,r.jsx)(g.E,{className:"h-4 w-1/3 mb-3 bg-muted/50"}),(0,r.jsx)(g.E,{className:"h-px w-full my-3 bg-border/50"}),(0,r.jsx)("div",{className:"space-y-2.5 flex-grow",children:[void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.E,{className:"mr-2.5 h-5 w-5 rounded-full bg-muted/50"}),(0,r.jsx)(g.E,{className:"h-5 w-2/3 bg-muted/50"})]},t))})]}),(0,r.jsx)("div",{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,r.jsx)(g.E,{className:"h-10 w-full bg-muted/50"})})]})}let B=()=>{let[e,t]=(0,l.useState)([]),[s,a]=(0,l.useState)([]),[u,m]=(0,l.useState)(""),[h,g]=(0,l.useState)("all"),[y,v]=(0,l.useState)("all"),[w,N]=(0,l.useState)("all"),{toast:E}=(0,U.dj)(),k=Array.from(new Set(e.map(e=>e.department))).sort();return(0,l.useEffect)(()=>{let t=[...e],s=u.toLowerCase();"all"!==h&&(t=t.filter(e=>e.status===h)),"all"!==y&&(t=t.filter(e=>e.department===y)),"all"!==w&&(t=t.filter(e=>e.role===w)),s&&(t=t.filter(e=>(e.name||e.fullName||"").toLowerCase().includes(s)||(e.position||"").toLowerCase().includes(s)||(e.department||"").toLowerCase().includes(s)||(e.role||"").toLowerCase().includes(s)||(e.contactEmail||"").toLowerCase().includes(s)||e.skills&&e.skills.some(e=>(e||"").toLowerCase().includes(s))||"driver"===e.role&&e.availability&&(e.availability||"").toLowerCase().includes(s)||"driver"===e.role&&e.currentLocation&&(e.currentLocation||"").toLowerCase().includes(s))),a(t)},[u,e,h,y,w]),(0,r.jsx)(f,{children:e=>{let{employees:a,loading:f,error:A,fetchEmployees:S,isRefreshing:C,isConnected:D,socketTriggered:T}=e;(0,l.useEffect)(()=>{f||A||t([...a].sort((e,t)=>(e.name||e.fullName||"").localeCompare(t.name||t.fullName||"")))},[a,f,A]);let L=async()=>{try{await S(),E({title:"Refresh Complete",description:"Employee list has been updated."})}catch(e){console.error("Error refreshing employees:",e),E({title:"Refresh Failed",description:"Could not update employee list. Please try again.",variant:"destructive"})}},R=u||"all"!==h||"all"!==y||"all"!==w;return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)(p.z,{title:"Manage Employees",description:"Oversee employee profiles, roles, status, and assignments.",icon:n.A,children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(j.r,{actionType:"tertiary",onClick:L,isLoading:f||C,icon:(0,r.jsx)(o.A,{className:"h-4 w-4 ".concat(C||T?"animate-spin":"")}),loadingText:C?"Updating...":"Refresh",children:!(C||T)&&"Refresh"}),(0,r.jsx)(j.r,{actionType:"primary",icon:(0,r.jsx)(d.A,{className:"h-4 w-4"}),asChild:!0,children:(0,r.jsx)(i(),{href:"/employees/new",children:"Add New Employee"})})]})}),(0,r.jsxs)(b.Zp,{className:"mb-6 p-4 shadow relative",children:[(C||T)&&!f&&(0,r.jsxs)("div",{className:"absolute right-4 top-4 text-xs flex items-center text-muted-foreground",children:[(0,r.jsx)(o.A,{className:"h-3 w-3 mr-1 animate-spin"}),T?"Real-time update...":"Refreshing..."]}),!f&&(0,r.jsxs)("div",{className:"absolute right-24 top-4 text-xs inline-flex items-center sm:right-24",children:[(0,r.jsx)("div",{className:"h-2 w-2 rounded-full mr-2 ".concat(D?"bg-green-500":"bg-gray-400")}),(0,r.jsx)("span",{className:"text-muted-foreground text-xs",children:D?"Real-time active":"Real-time inactive"})]}),(0,r.jsx)(b.Wu,{className:"pt-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end",children:[(0,r.jsxs)("div",{className:"relative lg:col-span-1",children:[(0,r.jsx)(V.J,{htmlFor:"search-employees",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Search Employees"}),(0,r.jsx)(c.A,{className:"absolute left-3 top-[calc(50%_-_0.5rem_+_12px)] h-5 w-5 text-muted-foreground"}),(0,r.jsx)(x.p,{id:"search-employees",type:"text",placeholder:"Name, Role, Dept, Skill...",value:u,onChange:e=>m(e.target.value),className:"pl-10 w-full"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(V.J,{htmlFor:"status-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Status"}),(0,r.jsxs)(Y.l6,{value:h,onValueChange:g,children:[(0,r.jsx)(Y.bq,{id:"status-filter",children:(0,r.jsx)(Y.yv,{placeholder:"All Statuses"})}),(0,r.jsxs)(Y.gC,{children:[(0,r.jsx)(Y.eb,{value:"all",children:"All Statuses"}),P.O2.options.map(e=>(0,r.jsx)(Y.eb,{value:e,children:e},e))]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(V.J,{htmlFor:"department-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Department"}),(0,r.jsxs)(Y.l6,{value:y,onValueChange:v,children:[(0,r.jsx)(Y.bq,{id:"department-filter",children:(0,r.jsx)(Y.yv,{placeholder:"All Departments"})}),(0,r.jsxs)(Y.gC,{children:[(0,r.jsx)(Y.eb,{value:"all",children:"All Departments"}),k.map(e=>(0,r.jsx)(Y.eb,{value:e,children:e},e))]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(V.J,{htmlFor:"role-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Role"}),(0,r.jsxs)(Y.l6,{value:w,onValueChange:N,children:[(0,r.jsx)(Y.bq,{id:"role-filter",children:(0,r.jsx)(Y.yv,{placeholder:"All Roles"})}),(0,r.jsxs)(Y.gC,{children:[(0,r.jsx)(Y.eb,{value:"all",children:"All Roles"}),P.Q.options.map(e=>(0,r.jsx)(Y.eb,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1).replace("_"," ")},e))]})]})]})]})})]}),(0,r.jsx)(q.gO,{isLoading:f,error:A,data:s,onRetry:S,loadingComponent:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(H,{},t))}),emptyComponent:(0,r.jsxs)("div",{className:"text-center py-12 bg-card rounded-lg shadow-md",children:[(0,r.jsx)(n.A,{className:"mx-auto h-16 w-16 text-muted-foreground mb-6"}),(0,r.jsx)("h3",{className:"text-2xl font-semibold text-foreground mb-2",children:R?"No Employees Match Your Filters":"No Employees Registered Yet"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-2 mb-6 max-w-md mx-auto",children:R?"Try adjusting your search or filter criteria.":"Get started by adding an employee."}),!R&&(0,r.jsx)(j.r,{actionType:"primary",size:"lg",icon:(0,r.jsx)(d.A,{className:"h-4 w-4"}),asChild:!0,children:(0,r.jsx)(i(),{href:"/employees/add",children:"Add Your First Employee"})})]}),children:e=>(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,r.jsx)(F,{employee:e},e.id))})})]})}})};function J(){return(0,r.jsx)(z.A,{children:(0,r.jsx)(B,{})})}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(95155),l=s(12115),a=s(59434);let i=l.forwardRef((e,t)=>{let{className:s,type:l,...i}=e;return(0,r.jsx)("input",{type:l,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...i})});i.displayName="Input"},76570:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},79239:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("UsersRound",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])},83662:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},85057:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var r=s(95155),l=s(12115),a=s(40968),i=s(74466),n=s(59434);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.b,{ref:t,className:(0,n.cn)(o(),s),...l})});d.displayName=a.b.displayName},86950:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},87481:(e,t,s)=>{"use strict";s.d(t,{dj:()=>m});var r=s(12115);let l=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?i(s):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],d={toasts:[]};function c(e){d=n(d,e),o.forEach(e=>{e(d)})}function u(e){let{...t}=e,s=(l=(l+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||r()}}}),{id:s,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function m(){let[e,t]=r.useState(d);return r.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}}},e=>{var t=t=>e(e.s=t);e.O(0,[5769,8360,832,2688,2512,1859,7529,6766,3150,8162,2730,536,8441,1684,7358],()=>t(39718)),_N_E=e.O()}]);