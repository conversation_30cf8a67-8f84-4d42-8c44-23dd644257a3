(()=>{var e={};e.id=2003,e.ids=[2003],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9662:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>_});var t=s(60687),a=s(76180),i=s.n(a),l=s(43210),n=s(28840),o=s(57697),c=s(48041),d=s(29523),p=s(89667),m=s(15079),h=s(44493),x=s(80013),u=s(96834),f=s(44610),j=s(35265),v=s(41936),y=s(78726),b=s(24920),g=s(29333),N=s(85814),w=s.n(N),S=s(85726),k=s(24847),C=s(91821),A=s(14975),E=s(77368);class q extends l.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){this.setState({errorInfo:r}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",r.componentStack),this.props.onError&&this.props.onError(e,r)}render(){let{title:e="Something went wrong",description:r="An unexpected error occurred.",resetLabel:s="Try Again"}=this.props;return this.state.hasError?this.props.fallback?this.props.fallback:(0,t.jsxs)(C.Fc,{variant:"destructive",className:"my-4",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)(C.XL,{className:"text-lg font-semibold",children:e}),(0,t.jsxs)(C.TN,{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-2",children:this.state.error?.message||r}),!1,(0,t.jsxs)(d.$,{variant:"outline",size:"sm",onClick:this.handleRetry,className:"mt-4",children:[(0,t.jsx)(E.A,{className:"mr-2 h-4 w-4"}),s]})]})]}):this.props.children}}let P=({children:e,fallback:r})=>(0,t.jsx)(q,{title:"Error Loading Service Records",description:"An unexpected error occurred while loading service records.",resetLabel:"Try Again",fallback:r,onError:(e,r)=>{console.error("ServiceRecords component error:",e),console.error("Component stack:",r.componentStack)},children:e});var R=s(68752),T=s(95758),F=s(18578);function _(){let[e,r]=(0,l.useState)([]),[s,a]=(0,l.useState)([]),[N,C]=(0,l.useState)([]),[A,E]=(0,l.useState)(!0),[q,_]=(0,l.useState)(!0),[z,V]=(0,l.useState)(null),[L,I]=(0,l.useState)("all"),[M,G]=(0,l.useState)("all"),[J,$]=(0,l.useState)(""),[D,W]=(0,l.useState)(""),[H,U]=(0,l.useState)([]),[Z,B]=(0,l.useState)(0);(0,l.useCallback)(async()=>{E(!0);try{let e=await (0,n.getVehicles)();r(Array.isArray(e)?e:[])}catch(e){console.error("Failed to fetch vehicles:",e),r([])}finally{E(!1)}},[]),(0,l.useCallback)(async()=>{_(!0),V(null);try{let e=await (0,o.Lv)();a(e)}catch(e){console.error("Failed to fetch service records:",e),V("Failed to load service records. Please try again.")}finally{_(!1)}},[]);let O=(0,l.useCallback)(()=>{B(e=>e+1)},[]);return A?(0,t.jsx)(T.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(c.z,{title:"Service History",icon:f.A,children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(S.E,{className:"h-10 w-32"}),(0,t.jsx)(S.E,{className:"h-10 w-32"})]})}),(0,t.jsx)(h.Zp,{children:(0,t.jsx)(h.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,t.jsx)(S.E,{className:"h-10 w-full"}),(0,t.jsx)(S.E,{className:"h-10 w-full"}),(0,t.jsx)(S.E,{className:"h-10 w-full"})]})})}),(0,t.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,r)=>(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 border-b",children:[(0,t.jsx)(S.E,{className:"h-6 w-1/6"}),(0,t.jsx)(S.E,{className:"h-6 w-1/6"}),(0,t.jsx)(S.E,{className:"h-6 w-2/6"}),(0,t.jsx)(S.E,{className:"h-6 w-1/6"}),(0,t.jsx)(S.E,{className:"h-6 w-1/6"})]},r))})]})}):(0,t.jsx)(T.A,{children:(0,t.jsxs)("div",{className:"jsx-5e2e520e6dad691 space-y-6 print-container",children:[(0,t.jsx)(c.z,{title:"Service History Report",description:"View and manage all service records for your vehicles.",icon:f.A,children:(0,t.jsxs)("div",{className:"jsx-5e2e520e6dad691 flex gap-2 no-print",children:[(0,t.jsx)(R.r,{actionType:"tertiary",asChild:!0,icon:(0,t.jsx)(j.A,{className:"h-4 w-4"}),children:(0,t.jsx)(w(),{href:"/vehicles",children:"Log New Service"})}),(0,t.jsx)(F.k,{reportContentId:"#service-history-report-content",reportType:"service-history",tableId:"#service-history-table",fileName:`service-history-report-${new Date().toISOString().split("T")[0]}`,enableCsv:N.length>0})]})}),(0,t.jsxs)(h.Zp,{className:"shadow-sm no-print",children:[(0,t.jsxs)(h.aR,{className:"pb-2",children:[(0,t.jsx)(h.ZB,{className:"text-lg",children:"Filter Options"}),(0,t.jsx)(h.BT,{children:"Filter service records by vehicle, service type, or search terms"})]}),(0,t.jsxs)(h.Wu,{children:[(0,t.jsxs)("div",{className:"jsx-5e2e520e6dad691 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-end filter-grid",children:[(0,t.jsxs)("div",{className:"jsx-5e2e520e6dad691",children:[(0,t.jsx)(x.J,{htmlFor:"vehicle-filter",children:"Filter by Vehicle"}),(0,t.jsxs)(m.l6,{value:L,onValueChange:I,"aria-label":"Filter by vehicle",children:[(0,t.jsx)(m.bq,{id:"vehicle-filter",className:"w-full mt-1.5",children:(0,t.jsx)(m.yv,{placeholder:"All Vehicles"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"All Vehicles"}),e.map(e=>(0,t.jsxs)(m.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,")"]},e.id))]})]})]}),(0,t.jsxs)("div",{className:"jsx-5e2e520e6dad691",children:[(0,t.jsx)(x.J,{htmlFor:"service-filter",children:"Filter by Service Type"}),(0,t.jsxs)(m.l6,{value:M,onValueChange:G,"aria-label":"Filter by service type",children:[(0,t.jsx)(m.bq,{id:"service-filter",className:"w-full mt-1.5",children:(0,t.jsx)(m.yv,{placeholder:"All Services"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"All Services"}),H.map(e=>(0,t.jsx)(m.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{className:"jsx-5e2e520e6dad691",children:[(0,t.jsx)(x.J,{htmlFor:"search-records",children:"Search Records"}),(0,t.jsxs)("div",{className:"jsx-5e2e520e6dad691 relative mt-1.5",children:[(0,t.jsx)(p.p,{id:"search-records",type:"text",placeholder:"Search by keyword, notes, plate...",value:J,onChange:e=>$(e.target.value),className:"pl-10 pr-10","aria-label":"Search service records"}),(0,t.jsx)(v.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),J&&(0,t.jsxs)(d.$,{variant:"ghost",size:"icon",className:"absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7",onClick:()=>$(""),"aria-label":"Clear search",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"jsx-5e2e520e6dad691 sr-only",children:"Clear search"})]})]})]})]}),("all"!==L||"all"!==M||J)&&(0,t.jsxs)("div",{className:"jsx-5e2e520e6dad691 mt-6 flex flex-wrap items-center justify-between border border-border p-3 rounded-md",children:[(0,t.jsxs)("div",{className:"jsx-5e2e520e6dad691 flex flex-wrap gap-2 items-center",children:[(0,t.jsx)("span",{className:"jsx-5e2e520e6dad691 font-medium text-sm",children:"Active Filters:"}),"all"!==L&&(0,t.jsxs)(u.E,{variant:"outline",className:"flex items-center gap-1",children:[(0,t.jsx)(b.A,{className:"h-3 w-3"}),e.find(e=>e.id===Number(L))?.make," ",e.find(e=>e.id===Number(L))?.model]}),"all"!==M&&(0,t.jsxs)(u.E,{variant:"outline",className:"flex items-center gap-1",children:[(0,t.jsx)(g.A,{className:"h-3 w-3"}),M]}),J&&(0,t.jsxs)(u.E,{variant:"outline",className:"flex items-center gap-1",children:[(0,t.jsx)(v.A,{className:"h-3 w-3"}),'"',J,'"']})]}),(0,t.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{I("all"),G("all"),$("")},className:"mt-2 sm:mt-0","aria-label":"Reset all filters",children:[(0,t.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"Reset Filters"]})]})]})]}),(0,t.jsxs)("div",{id:"service-history-report-content",className:"jsx-5e2e520e6dad691 report-content",children:[(0,t.jsxs)("header",{className:"jsx-5e2e520e6dad691 text-center mb-8 pb-4 border-b-2 border-gray-300 print-only",children:[(0,t.jsx)("h1",{className:"jsx-5e2e520e6dad691 text-3xl font-bold text-gray-800",children:"Service History Report"}),(0,t.jsx)("p",{className:"jsx-5e2e520e6dad691 text-md text-gray-600",children:"all"!==L?`Vehicle: ${e.find(e=>e.id===Number(L))?.make} ${e.find(e=>e.id===Number(L))?.model}`:"All Vehicles"})]}),(0,t.jsx)(P,{children:(0,t.jsx)(k.R,{records:N,isLoading:q,error:z,onRetry:O,showVehicleInfo:!0,vehicleSpecific:!1})}),(0,t.jsxs)("footer",{className:"jsx-5e2e520e6dad691 mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500",children:[(0,t.jsxs)("p",{className:"jsx-5e2e520e6dad691",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,t.jsx)("p",{className:"jsx-5e2e520e6dad691",children:"WorkHub - Vehicle Service Management"})]})]}),(0,t.jsx)(i(),{id:"5e2e520e6dad691",children:".print-only{display:none}@media print{.no-print{display:none!important}.print-only{display:block}.print-container{padding:1rem}.card-print{-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;border:none!important}.print-service-col{max-width:200px;white-space:normal!important}.print-notes-col{max-width:200px;white-space:normal!important}.print-text-wrap{word-break:break-word;white-space:normal!important}}@media(max-width:640px){.delegations-table-container,.overflow-x-auto{overflow-x:auto}.filter-grid{grid-template-columns:1fr!important}.summary-grid{grid-template-columns:1fr 1fr!important}}"})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15079:(e,r,s)=>{"use strict";s.d(r,{bq:()=>m,eb:()=>f,gC:()=>u,l6:()=>d,yv:()=>p});var t=s(60687),a=s(43210),i=s(22670),l=s(61662),n=s(89743),o=s(58450),c=s(4780);let d=i.bL;i.YJ;let p=i.WT,m=a.forwardRef(({className:e,children:r,...s},a)=>(0,t.jsxs)(i.l9,{ref:a,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[r,(0,t.jsx)(i.In,{asChild:!0,children:(0,t.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=i.l9.displayName;let h=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(i.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=i.PP.displayName;let x=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(i.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,t.jsx)(l.A,{className:"h-4 w-4"})}));x.displayName=i.wn.displayName;let u=a.forwardRef(({className:e,children:r,position:s="popper",...a},l)=>(0,t.jsx)(i.ZL,{children:(0,t.jsxs)(i.UC,{ref:l,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...a,children:[(0,t.jsx)(h,{}),(0,t.jsx)(i.LM,{className:(0,c.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,t.jsx)(x,{})]})}));u.displayName=i.UC.displayName,a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(i.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...r})).displayName=i.JU.displayName;let f=a.forwardRef(({className:e,children:r,...s},a)=>(0,t.jsxs)(i.q7,{ref:a,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(i.VF,{children:(0,t.jsx)(o.A,{className:"h-4 w-4"})})}),(0,t.jsx)(i.p4,{children:r})]}));f.displayName=i.q7.displayName,a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(i.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...r})).displayName=i.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25373:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\service-history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\service-history\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29333:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35265:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},41936:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},45063:(e,r,s)=>{Promise.resolve().then(s.bind(s,25373))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71165:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(r,o);let c={children:["",{children:["service-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,25373)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\service-history\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\service-history\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/service-history/page",pathname:"/service-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74075:e=>{"use strict";e.exports=require("zlib")},75327:(e,r,s)=>{Promise.resolve().then(s.bind(s,9662))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,3744,1658,5880,2729,6055,9584,8141,3983,4318,4550],()=>s(71165));module.exports=t})();