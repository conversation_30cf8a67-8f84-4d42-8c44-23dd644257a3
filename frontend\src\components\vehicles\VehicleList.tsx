'use client';

import React, {useEffect, useState, useCallback} from 'react';
import Link from 'next/link';
import {Vehicle} from '@/lib/types';
import {
	getVehicles,
	deleteVehicle as deleteVehicleFromStore,
} from '@/lib/store';
import {Button} from '@/components/ui/button';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {PlusCircle, Edit, Trash2} from 'lucide-react';
import {Skeleton} from '@/components/ui/skeleton';
import {Alert, AlertDescription, AlertTitle} from '@/components/ui/alert';
import {CircleAlert} from 'lucide-react';
import {useSocketRefresh} from '@/hooks/useSocketRefresh';
import {SOCKET_EVENTS} from '@/hooks/useSocket';

interface VehicleListProps {
	children: (data: {
		vehicles: Vehicle[];
		loading: boolean;
		error: string | null;
		handleDelete: (id: number) => Promise<void>;
		fetchVehicles: () => Promise<void>; // This is the manual refresh trigger
		isRefreshing: boolean; // Combined refresh state
		isConnected: boolean;
		socketTriggered: boolean;
	}) => React.ReactNode;
}

const VehicleListContainer: React.FC<VehicleListProps> = ({children}) => {
	const [vehicles, setVehicles] = useState<Vehicle[]>([]);
	const [loading, setLoading] = useState<boolean>(true);
	const [error, setError] = useState<string | null>(null);

	const fetchVehicles = useCallback(async () => {
		// Don't set loading if it's a background refresh triggered by socket/polling unless it's the initial load.
		// The `isRefreshing` state from useSocketRefresh will handle the background refresh indication.
		if (loading) { // Only set to true if it's already considered loading (e.g. initial)
			setLoading(true);
		}
		try {
			const data = await getVehicles();
			setVehicles(data);
			setError(null);
		} catch (err) {
			console.error('Error fetching vehicles:', err);
			setError('Failed to fetch vehicles. Please try again.');
		} finally {
			if(loading) setLoading(false); // Only turn off if it was an initial load
		}
	}, [loading]); // Keep loading as dependency if its change should re-trigger the memoization of fetchVehicles

	const {
		isRefreshing: socketOrPollingRefreshing, // Renamed to avoid clash
		refresh: refreshData,
		isConnected,
		socketTriggered,
	} = useSocketRefresh(
		fetchVehicles,
		[
			SOCKET_EVENTS.VEHICLE_CREATED,
			SOCKET_EVENTS.VEHICLE_UPDATED,
			SOCKET_EVENTS.VEHICLE_DELETED,
			SOCKET_EVENTS.REFRESH_VEHICLES,
		],
		{
			interval: 30000, // Polling interval
			enabled: true,    // Enable both socket and polling
			immediate: true,  // Fetch data immediately on mount
			enableSocket: true,
			enablePolling: true, // Polling as fallback or primary if socket disabled/fails
			onError: (error) => {
				console.error('Socket/Polling refresh error:', error);
				// setError('Failed to refresh data in real-time. Polling may be active.'); // Optionally inform user
			},
		}
	);

	// Initial data fetch on mount - useSocketRefresh's immediate option handles this
	useEffect(() => {
    // Initial fetch is handled by useSocketRefresh if immediate: true
    // If not immediate, or if you need more control for the very first load:
    if (loading && !socketTriggered) { // Ensure this only runs for the very first load if not immediate
      fetchVehicles();
    }
	}, [fetchVehicles, loading, socketTriggered]); // `loading` can be part of initial setup


	const handleDelete = async (id: number) => {
		if (window.confirm('Are you sure you want to delete this vehicle?')) {
			try {
				await deleteVehicleFromStore(id);
				setVehicles((prevVehicles) =>
					prevVehicles.filter((vehicle) => vehicle.id !== id)
				);
			} catch (err) {
				console.error('Error deleting vehicle:', err);
				setError('Failed to delete vehicle. Please try again.');
				throw err; // Rethrow so caller (e.g., UI) can handle if needed
			}
		}
	};
	
	// Combine isRefreshing from polling and socketTriggered for a general "something is happening" state
	const isCurrentlyRefreshing = socketOrPollingRefreshing || socketTriggered;

	return children({
		vehicles,
		loading, // Represents initial load state
		error,
		handleDelete,
		fetchVehicles: refreshData, // Expose manual refresh from useSocketRefresh
		isRefreshing: isCurrentlyRefreshing, // Combined refresh state for UI
		isConnected,
		socketTriggered,
	});
};

export default VehicleListContainer;

