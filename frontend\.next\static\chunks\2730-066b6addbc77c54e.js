"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2730],{2730:(e,t,a)=>{a.r(t),a.d(t,{addDelegation:()=>k,addDelegationToLocalStorage:()=>w,addEmployee:()=>Q,addServiceRecord:()=>g,addTask:()=>Y,addVehicle:()=>d,assignTaskToEmployee:()=>G,deleteDelegation:()=>P,deleteDelegationFromLocalStorage:()=>O,deleteEmployee:()=>J,deleteServiceRecord:()=>y,deleteTask:()=>R,deleteVehicle:()=>u,getAllEnrichedServiceRecords:()=>p,getAllServiceRecords:()=>m,getDelegationById:()=>S,getDelegationByIdFromLocalStorage:()=>T,getDelegations:()=>I,getDelegationsFromLocalStorage:()=>A,getEmployeeById:()=>x,getEmployees:()=>V,getEnrichedEmployees:()=>_,getServiceRecordsForVehicle:()=>h,getTaskById:()=>C,getTasks:()=>U,getVehicleById:()=>l,getVehicles:()=>n,saveDelegations:()=>v,saveDelegationsToLocalStorage:()=>D,unassignTaskFromEmployee:()=>M,updateDelegation:()=>b,updateDelegationInLocalStorage:()=>E,updateDelegationStatus:()=>H,updateDelegationStatusInLocalStorage:()=>N,updateEmployee:()=>B,updateTask:()=>j,updateVehicle:()=>c});var r=a(92122),s=a(52582);let i="carLifeTrackerDelegations",o=e=>{let t=new Date,a=new Date(e.durationTo),s=!1;if(a<t&&e.status!==r.Qw.Enum.No_details&&e.status!==r.Qw.Enum.Completed&&e.status!==r.Qw.Enum.Cancelled){let a=r.Qw.Enum.No_details,i={id:crypto.randomUUID(),status:a,changedAt:t.toISOString(),reason:"Delegation duration has passed."};return{updatedDelegation:{...e,status:a,statusHistory:[...e.statusHistory,i]},modified:s=!0}}return{updatedDelegation:e,modified:s}},n=async()=>{try{return(await s.kw()).map(e=>({...e,id:Number(e.id),serviceHistory:Array.isArray(e.serviceHistory)?e.serviceHistory:[]}))}catch(e){throw console.error("Store: Error fetching vehicles from API",e),e}},l=async e=>{try{let t=await s.nz(e);if(t)return{...t,id:Number(t.id),serviceHistory:Array.isArray(t.serviceHistory)?t.serviceHistory:[]};return}catch(t){throw console.error("Store: Error fetching vehicle with ID ".concat(e," from API"),t),t}},d=async e=>{let t={make:e.make,model:e.model,year:e.year,vin:e.vin,licensePlate:e.licensePlate,ownerName:e.ownerName,ownerContact:e.ownerContact,color:e.color,initialOdometer:e.initialOdometer,imageUrl:e.imageUrl};try{let e=await s.xn(t);return{...e,id:Number(e.id),serviceHistory:Array.isArray(e.serviceHistory)?e.serviceHistory:[]}}catch(e){throw console.error("Store: Error adding vehicle via API",e),e}},c=async(e,t)=>{try{let a=await s.Q3(e,t);if(a)return{...a,id:Number(a.id),serviceHistory:Array.isArray(a.serviceHistory)?a.serviceHistory:[]};return}catch(t){throw console.error("Store: Error updating vehicle with ID ".concat(e," via API"),t),t}},u=async e=>{try{await s.ih(e)}catch(t){throw console.error("Store: Error deleting vehicle with ID ".concat(e," via API"),t),t}},g=async(e,t)=>{try{let a=Number(e);if(isNaN(a))return void console.error("Invalid vehicle ID format for adding service record");return await s.dC(a,t),await l(a)}catch(t){console.error("Store: Error adding service record to vehicle ".concat(e),t);return}},y=async(e,t)=>{try{let a=Number(e);if(isNaN(a))return void console.error("Invalid vehicle ID format for deleting service record");return await s.Yv(a,t),await l(a)}catch(a){console.error("Store: Error deleting service record ".concat(t," from vehicle ").concat(e),a);return}},m=async()=>{try{return await s.xY()}catch(e){throw console.error("Store: Error fetching all service records",e),e}},h=async e=>{try{return await s.y_(e)}catch(t){throw console.error("Store: Error fetching service records for vehicle ".concat(e),t),t}},p=async()=>{try{return await s.Lv()}catch(e){throw console.error("Store: Error fetching enriched service records",e),e}},f=e=>{let t,{formatDateForApi:r,isValidDateString:s}=a(21876);if(!e||"object"!=typeof e)return null;console.debug("Processing flight details for storage:",e);let i=e.flightNumber&&""!==e.flightNumber.trim(),o=e.dateTime&&""!==e.dateTime.trim(),n=e.airport&&""!==e.airport.trim();if(!i&&!o&&!n)return null;if(!i||!o||!n)return console.warn("Partial flight details provided, but all key fields (flightNumber, dateTime, airport) are required if any are set.",{details:e}),null;try{if(!(t=r(e.dateTime))||!s(t))return console.warn("Invalid flight dateTime after formatting:",e.dateTime,t),null}catch(t){return console.error("Error formatting flight dateTime:",t,e.dateTime),null}return{flightNumber:e.flightNumber,dateTime:t,airport:e.airport,terminal:e.terminal||null,notes:e.notes||null}},A=()=>{try{let e=localStorage.getItem(i),t=e?JSON.parse(e):[],a=!1,r=t.map(e=>{let{updatedDelegation:t,modified:r}=o(e);return r&&(a=!0),t});return a&&D(r),r}catch(e){return console.error("Error reading delegations from localStorage",e),[]}},D=e=>{try{localStorage.setItem(i,JSON.stringify(e))}catch(e){console.error("Error saving delegations to localStorage",e)}},I=async()=>{try{let e=(await s.sI()).map(e=>({...e,id:e.id,delegates:Array.isArray(e.delegates)?e.delegates:[],statusHistory:Array.isArray(e.statusHistory)?e.statusHistory:[],durationFrom:e.durationFrom,durationTo:e.durationTo,flightArrivalDetails:e.flightArrivalDetails||null,flightDepartureDetails:e.flightDepartureDetails||null}));return e.map(e=>{let{updatedDelegation:t,modified:a}=o(e);return a&&(console.log("Updating delegation status automatically for ID:",t.id,"New status:",t.status),s.mh(t.id,{status:t.status}).catch(e=>{console.error("Store: Error auto-updating delegation status for ID ".concat(t.id,":"),e)})),t})}catch(e){return console.error("Store: Error fetching delegations from API",e),console.warn("Falling back to localStorage for delegations"),A()}},v=async e=>{console.warn("saveDelegations is deprecated. Use individual API calls instead."),D(e)},T=e=>A().find(t=>t.id===e),S=async e=>{try{let t=await s.bt(e);if(t){let e={...t,delegates:Array.isArray(t.delegates)?t.delegates:[],statusHistory:Array.isArray(t.statusHistory)?t.statusHistory:[],flightArrivalDetails:t.flightArrivalDetails||null,flightDepartureDetails:t.flightDepartureDetails||null},{updatedDelegation:a}=o(e);return a}return}catch(a){console.error("Store: Error fetching delegation with ID ".concat(e," from API"),a),console.warn("Falling back to localStorage for delegation ID ".concat(e));let t=T(e);if(t){let{updatedDelegation:e}=o(t);return e}return}},w=e=>{let t=A(),a=e.status||r.Qw.Enum.Planned,s={id:crypto.randomUUID(),eventName:e.eventName,location:e.location,durationFrom:new Date(e.durationFrom).toISOString(),durationTo:new Date(e.durationTo).toISOString(),invitationFrom:e.invitationFrom,invitationTo:e.invitationTo,delegates:e.delegates.map(e=>({...e,id:e.id||crypto.randomUUID()})),flightArrivalDetails:f(e.flightArrivalDetails),flightDepartureDetails:f(e.flightDepartureDetails),status:a,statusHistory:[{id:crypto.randomUUID(),status:a,changedAt:new Date().toISOString(),reason:"Delegation created"}],notes:e.notes,imageUrl:e.imageUrl||"https://picsum.photos/seed/".concat(encodeURIComponent(e.eventName),"/400/250")};return D([...t,s]),s},k=async e=>{try{let t=e.status||r.Qw.Enum.Planned,{formatDateForApi:i}=await Promise.resolve().then(a.bind(a,21876)),o={eventName:e.eventName,location:e.location,durationFrom:i(e.durationFrom),durationTo:i(e.durationTo),invitationFrom:e.invitationFrom,invitationTo:e.invitationTo,delegates:e.delegates.map(e=>{let{id:t,...a}=e;return a}),flightArrivalDetails:f(e.flightArrivalDetails),flightDepartureDetails:f(e.flightDepartureDetails),status:t,notes:e.notes,imageUrl:e.imageUrl||"https://picsum.photos/seed/".concat(encodeURIComponent(e.eventName),"/400/250")};if(!o.durationFrom||!o.durationTo)throw Error("Invalid date format provided. Please ensure dates are valid.");console.log("=== DELEGATION PAYLOAD DEBUG ==="),console.log("durationFrom:",o.durationFrom,"Type:",typeof o.durationFrom),console.log("durationTo:",o.durationTo,"Type:",typeof o.durationTo),console.log("Flight arrival details:",o.flightArrivalDetails),console.log("Flight departure details:",o.flightDepartureDetails),console.log("Complete payload:",JSON.stringify(o,null,2)),console.log("=== END DELEGATION PAYLOAD DEBUG ===");let n=await s.VH(o);return{...n,delegates:Array.isArray(n.delegates)?n.delegates:[],statusHistory:Array.isArray(n.statusHistory)?n.statusHistory:[],flightArrivalDetails:n.flightArrivalDetails||null,flightDepartureDetails:n.flightDepartureDetails||null}}catch(t){return console.error("Store: Error adding delegation via API",t),console.warn("Falling back to localStorage for adding delegation"),w(e)}},E=(e,t)=>{let a=A(),r=a.findIndex(t=>t.id===e);if(-1===r)return;let s=a[r],i={...s,...t,durationFrom:new Date(t.durationFrom).toISOString(),durationTo:new Date(t.durationTo).toISOString(),delegates:t.delegates.map(e=>({...e,id:e.id||crypto.randomUUID()})),flightArrivalDetails:f(t.flightArrivalDetails),flightDepartureDetails:f(t.flightDepartureDetails),status:t.status};if(t.status&&t.status!==s.status){let e={id:crypto.randomUUID(),status:t.status,changedAt:new Date().toISOString(),reason:t.statusChangeReason||"Status updated manually"};i.statusHistory=[...s.statusHistory||[],e]}else i.statusHistory=s.statusHistory;return a[r]=i,D(a),a[r]},b=async(e,t)=>{try{let{formatDateForApi:r}=a(21876),i={eventName:t.eventName,location:t.location,durationFrom:r(t.durationFrom),durationTo:r(t.durationTo),invitationFrom:t.invitationFrom,invitationTo:t.invitationTo,delegates:t.delegates.map(e=>{let{id:t,...a}=e;return e.id?{id:e.id,...a}:a}),notes:t.notes,imageUrl:t.imageUrl,status:t.status,statusChangeReason:t.statusChangeReason,flightArrivalDetails:f(t.flightArrivalDetails),flightDepartureDetails:f(t.flightDepartureDetails)};console.debug("Store: Updating delegation ".concat(e," with data:"),JSON.stringify(i,null,2));let o=await s.mh(e,i);if(o)return{...o,delegates:Array.isArray(o.delegates)?o.delegates:[],statusHistory:Array.isArray(o.statusHistory)?o.statusHistory:[],flightArrivalDetails:o.flightArrivalDetails||null,flightDepartureDetails:o.flightDepartureDetails||null};return}catch(a){return console.error("Store: Error updating delegation with ID ".concat(e," via API"),a),console.warn("Falling back to localStorage for updating delegation ID ".concat(e)),E(e,t)}},N=(e,t,a)=>{let r=A(),s=r.findIndex(t=>t.id===e);if(-1===s)return;let i=r[s],o={id:crypto.randomUUID(),status:t,changedAt:new Date().toISOString(),reason:a||"Status updated"};return r[s]={...i,status:t,statusHistory:[...i.statusHistory,o]},D(r),r[s]},H=async(e,t,a)=>{try{let r=await s.mh(e,{status:t,statusChangeReason:a||"Status updated"});if(r)return{...r,delegates:Array.isArray(r.delegates)?r.delegates:[],statusHistory:Array.isArray(r.statusHistory)?r.statusHistory:[],flightArrivalDetails:r.flightArrivalDetails||null,flightDepartureDetails:r.flightDepartureDetails||null};return}catch(r){return console.error("Store: Error updating delegation status for ID ".concat(e," via API"),r),console.warn("Falling back to localStorage for updating delegation status for ID ".concat(e)),N(e,t,a)}},O=e=>{let t=A();D(t=t.filter(t=>t.id!==e))},P=async e=>{try{await s.Y3(e)}catch(t){console.error("Store: Error deleting delegation with ID ".concat(e," via API"),t),console.warn("Falling back to localStorage for deleting delegation ID ".concat(e)),O(e)}},U=async()=>{try{return(await s.x1()).map(e=>({...e,statusHistory:Array.isArray(e.statusHistory)?e.statusHistory:[],subTasks:Array.isArray(e.subTasks)?e.subTasks:[],assignedTo:Array.isArray(e.assignedEmployees)?e.assignedEmployees.map(e=>String(e.id)):[]}))}catch(e){throw console.error("Store: Error fetching tasks from API",e),e}},F=e=>{console.warn("saveTasks is deprecated as data is now saved via API calls")},C=async e=>{try{let t=await s.a9(e);if(t)return{...t,statusHistory:Array.isArray(t.statusHistory)?t.statusHistory:[],subTasks:Array.isArray(t.subTasks)?t.subTasks:[],assignedTo:Array.isArray(t.assignedEmployees)?t.assignedEmployees.map(e=>String(e.id)):[]};return}catch(t){throw console.error("Store: Error fetching task with ID ".concat(e," from API"),t),t}},Y=async e=>{try{let t={description:e.description,location:e.location,dateTime:new Date(e.dateTime).toISOString(),estimatedDuration:e.estimatedDuration,requiredSkills:e.requiredSkills||[],priority:e.priority||"Medium",deadline:e.deadline?new Date(e.deadline).toISOString():void 0,status:e.status||"Pending",notes:e.notes,vehicleId:e.vehicleId?Number(e.vehicleId):void 0,assignedEmployeeIds:e.assignedTo||[],subTasks:e.subTasks?e.subTasks.map(e=>({...e,id:e.id||void 0,completed:e.completed||!1})):[]},a=await s.UT(t);return{...a,statusHistory:Array.isArray(a.statusHistory)?a.statusHistory:[],subTasks:Array.isArray(a.subTasks)?a.subTasks:[],assignedTo:Array.isArray(a.assignedEmployees)?a.assignedEmployees.map(e=>String(e.id)):[]}}catch(e){throw console.error("Store: Error adding task via API",e),e}},j=async(e,t)=>{try{let a={description:t.description,location:t.location,dateTime:t.dateTime?new Date(t.dateTime).toISOString():void 0,estimatedDuration:t.estimatedDuration,requiredSkills:t.requiredSkills,priority:t.priority,deadline:t.deadline?new Date(t.deadline).toISOString():void 0,status:t.status,notes:t.notes,vehicleId:t.vehicleId?Number(t.vehicleId):void 0,assignedEmployeeIds:t.assignedTo,subTasks:t.subTasks,statusChangeReason:t.statusChangeReason};Object.keys(a).forEach(e=>{void 0===a[e]&&delete a[e]});let r=await s.lC(e,a);if(r)return{...r,statusHistory:Array.isArray(r.statusHistory)?r.statusHistory:[],subTasks:Array.isArray(r.subTasks)?r.subTasks:[],assignedTo:Array.isArray(r.assignedEmployees)?r.assignedEmployees.map(e=>String(e.id)):[]};return}catch(t){throw console.error("Store: Error updating task with ID ".concat(e," via API"),t),t}},R=async e=>{try{await s.vq(e)}catch(t){throw console.error("Store: Error deleting task with ID ".concat(e," via API"),t),t}},L=()=>{try{let e=localStorage.getItem("carLifeTrackerEmployees_INTERNAL_CACHE");return e?JSON.parse(e):[]}catch(e){return console.error("Error reading internal employee cache from localStorage",e),[]}},q=e=>{try{localStorage.setItem("carLifeTrackerEmployees_INTERNAL_CACHE",JSON.stringify(e))}catch(e){console.error("Error saving internal employee cache to localStorage",e)}},V=async()=>{try{let e=(await s.Oc()).map(e=>({...e,id:Number(e.id),vehicleId:e.assignedVehicleId?String(e.assignedVehicleId):null,assignedTasks:Array.isArray(e.assignedTasks)?e.assignedTasks:[],statusHistory:Array.isArray(e.statusHistory)?e.statusHistory:[],createdAt:e.createdAt||new Date(0).toISOString(),updatedAt:e.updatedAt||new Date(0).toISOString()}));return q(e),e}catch(e){throw console.error("Store: Error fetching employees from API",e),e}},_=async()=>{try{let{getEnrichedEmployees:e}=await a.e(1773).then(a.bind(a,11773)),t=(await e()).map(e=>({...e,id:Number(e.id),vehicleId:e.assignedVehicleId?String(e.assignedVehicleId):null,assignedTasks:Array.isArray(e.assignedTasks)?e.assignedTasks:[],statusHistory:Array.isArray(e.statusHistory)?e.statusHistory:[],createdAt:e.createdAt||new Date(0).toISOString(),updatedAt:e.updatedAt||new Date(0).toISOString()}));return q(t),t}catch(e){return console.error("Store: Error fetching enriched employees",e),V()}},x=async e=>{try{let t=await s.Fv(e);if(t)return{...t,id:Number(t.id),vehicleId:t.assignedVehicleId?String(t.assignedVehicleId):null,assignedTasks:Array.isArray(t.assignedTasks)?t.assignedTasks:[],statusHistory:Array.isArray(t.statusHistory)?t.statusHistory:[],createdAt:t.createdAt||new Date(0).toISOString(),updatedAt:t.updatedAt||new Date(0).toISOString()};return}catch(t){throw console.error("Store: Error fetching employee with ID ".concat(e," from API"),t),t}},Q=async e=>{let t=e.hireDate;e.hireDate&&(t=e.hireDate.includes("T")?e.hireDate:new Date(e.hireDate+"T00:00:00.000Z").toISOString());let a={name:e.name,role:e.role,employeeId:e.employeeId,contactInfo:e.contactInfo,position:e.position,department:e.department,hireDate:t,status:e.status,availability:e.availability};try{let t=await s.bV(a),r={...t,id:Number(t.id),fullName:e.name,assignedTasks:[],statusHistory:[{id:crypto.randomUUID(),status:e.status||"Active",changedAt:new Date().toISOString(),reason:"Employee created"}],contactPhone:"",contactMobile:"",contactEmail:e.contactInfo||"",skills:e.skills||[],shiftSchedule:e.shiftSchedule||"",generalAssignments:e.generalAssignments||[],notes:e.notes,profileImageUrl:e.profileImageUrl,createdAt:t.createdAt||new Date().toISOString(),updatedAt:t.updatedAt||new Date().toISOString()},i=L();return i.push(r),q(i),r}catch(e){throw console.error("Store: Error adding employee via API",e),e}},B=async(e,t)=>{let{...a}=t;try{let r=await s.Y$(e,a);if(r){let e=L(),s=e.findIndex(e=>e.id===Number(r.id));if(-1!==s){let i=e[s],o={...i,...r,id:Number(r.id),status:a.status||i.status,assignedTasks:i.assignedTasks||r.assignedTasks||[],statusHistory:i.statusHistory||r.statusHistory||[],createdAt:r.createdAt||i.createdAt,updatedAt:r.updatedAt||new Date().toISOString()};return a.status&&a.status!==i.status&&(o.statusHistory=[...i.statusHistory||[],{id:crypto.randomUUID(),status:a.status,changedAt:new Date().toISOString(),reason:t.statusChangeReason||"Status updated"}]),e[s]=o,q(e),o}return{...r,id:Number(r.id),fullName:r.name||"",position:r.position||"",department:r.department||"",contactPhone:"",contactMobile:"",contactEmail:r.contactInfo||"",hireDate:r.hireDate||new Date(0).toISOString(),statusHistory:Array.isArray(r.statusHistory)?r.statusHistory:[],assignedTasks:Array.isArray(r.assignedTasks)?r.assignedTasks:[],skills:r.skills||[],shiftSchedule:r.shiftSchedule||"",generalAssignments:r.generalAssignments||[],notes:r.notes||"",profileImageUrl:r.profileImageUrl||"",createdAt:r.createdAt||new Date(0).toISOString(),updatedAt:r.updatedAt||new Date().toISOString()}}return}catch(t){throw console.error("Store: Error updating employee with ID ".concat(e," via API"),t),t}},J=async e=>{try{await s.Qu(e);let t=L();t=t.filter(t=>t.id!==e),q(t);let a=await U(),r=!1;a.forEach(t=>{t.assignedTo&&t.assignedTo.includes(String(e))&&(t.assignedTo=t.assignedTo.filter(t=>t!==String(e)),r=!0)}),r&&F(a)}catch(t){throw console.error("Store: Error deleting employee with ID ".concat(e," via API"),t),t}},G=async function(e,t){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],r=await U(),i=L();if(!Array.isArray(r))return console.warn("Tasks is not an array, cannot assign task. Received:",r),{};let o=r.findIndex(t=>t.id===e),n=i.findIndex(e=>e.id===Number(t));if(-1===o||-1===n)return console.warn("Task or Employee not found for assignment"),{};let l=r[o],d=i[n];if(l.assignedTo&&Array.isArray(l.assignedTo)||(l.assignedTo=[]),l.assignedTo.includes(String(d.id)))return console.log("Task already assigned to this employee"),{task:l,employee:d};if(l.assignedTo.push(String(d.id)),l.updatedAt=new Date().toISOString(),d.assignedTasks&&Array.isArray(d.assignedTasks)||(d.assignedTasks=[]),d.assignedTasks.includes(e)||d.assignedTasks.push(e),a)try{let t=await s.lC(e,{assignedEmployeeIds:l.assignedTo.map(e=>Number(e)),status:"Assigned"});t&&Object.assign(l,t)}catch(t){throw console.error("Error saving task assignment to backend:",t),l.assignedTo=l.assignedTo.filter(e=>e!==String(d.id)),d.assignedTasks=d.assignedTasks.filter(t=>t!==e),t}return q(i),{task:l,employee:d}},M=async function(e,t){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],r=await U(),i=L();if(!Array.isArray(r))return console.warn("Tasks is not an array, cannot unassign task. Received:",r),{};let o=r.findIndex(t=>t.id===e);if(-1===o)return console.warn("Task not found for unassignment"),{};let n=r[o];if(n.assignedTo&&Array.isArray(n.assignedTo)||(n.assignedTo=[]),t){let r=i.findIndex(e=>e.id===Number(t));if(-1!==r){if(i[r].assignedTasks&&Array.isArray(i[r].assignedTasks)?i[r].assignedTasks=i[r].assignedTasks.filter(t=>t!==e):i[r].assignedTasks=[],n.assignedTo=n.assignedTo.filter(e=>e!==String(i[r].id)),n.updatedAt=new Date().toISOString(),a)try{let t=await s.lC(e,{assignedEmployeeIds:n.assignedTo.map(e=>Number(e)),status:n.assignedTo.length>0?"Assigned":"Pending"});t&&Object.assign(n,t)}catch(t){throw console.error("Error saving task unassignment to backend:",t),n.assignedTo.push(String(i[r].id)),i[r].assignedTasks.push(e),t}return q(i),{task:n,employee:i[r]}}}else{let t=[...n.assignedTo];if((n.assignedTo||[]).forEach(t=>{let a=i.findIndex(e=>e.id===Number(t));-1!==a&&(i[a].assignedTasks&&Array.isArray(i[a].assignedTasks)?i[a].assignedTasks=i[a].assignedTasks.filter(t=>t!==e):i[a].assignedTasks=[])}),n.assignedTo=[],n.updatedAt=new Date().toISOString(),a)try{let t=await s.lC(e,{assignedEmployeeIds:[],status:"Pending"});t&&Object.assign(n,t)}catch(a){throw console.error("Error saving task unassignment to backend:",a),n.assignedTo=t,t.forEach(t=>{let a=i.findIndex(e=>e.id===Number(t));-1!==a&&i[a].assignedTasks.push(e)}),a}q(i)}return{task:n}}},92122:(e,t,a)=>{a.d(t,{Qw:()=>s,eL:()=>n});var r=a(55594);let s=r.k5(["Planned","Confirmed","In_Progress","Completed","Cancelled","No_details"]);r.Ik({id:r.Yj().uuid(),status:s,changedAt:r.Yj().datetime({message:"Invalid date/time format for status change"}),reason:r.Yj().optional()});let i=r.Ik({id:r.Yj().uuid().optional(),name:r.Yj().min(1,"Delegate name is required"),title:r.Yj().min(1,"Delegate title is required"),notes:r.Yj().optional()}),o=r.vk(e=>null!=e&&("object"!=typeof e||e.flightNumber&&""!==e.flightNumber.trim()||e.dateTime&&""!==e.dateTime.trim()||e.airport&&""!==e.airport.trim())?e:null,r.Ik({flightNumber:r.vk(e=>null!=e?e:"",r.Yj().min(1,"Flight number is required")),dateTime:r.vk(e=>null!=e?e:"",r.Yj().min(1,"Date & Time is required").refine(e=>!isNaN(Date.parse(e)),{message:"Invalid date & time format"})),airport:r.vk(e=>null!=e?e:"",r.Yj().min(1,"Airport is required")),terminal:r.Yj().trim().nullable().optional().transform(e=>""===e?null:e),notes:r.Yj().trim().nullable().optional().transform(e=>""===e?null:e)}).nullable().optional()),n=r.Ik({eventName:r.Yj().min(1,"Event name is required"),location:r.Yj().min(1,"Location is required"),durationFrom:r.Yj().refine(e=>!isNaN(Date.parse(e)),{message:"Invalid start date"}),durationTo:r.Yj().refine(e=>!isNaN(Date.parse(e)),{message:"Invalid end date"}),invitationFrom:r.Yj().optional(),invitationTo:r.Yj().optional(),delegates:r.YO(i).min(1,"At least one delegate is required"),flightArrivalDetails:o.nullable().optional(),flightDepartureDetails:o.nullable().optional(),status:s.default("Planned"),notes:r.Yj().optional(),imageUrl:r.Yj().url("Invalid URL for image").optional().or(r.eu("")),statusChangeReason:r.Yj().optional()}).superRefine((e,t)=>{new Date(e.durationFrom)>new Date(e.durationTo)&&t.addIssue({code:r.eq.custom,message:"End date cannot be earlier than start date",path:["durationTo"]})})}}]);