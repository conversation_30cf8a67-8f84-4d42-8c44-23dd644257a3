(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6683],{14247:(e,t,a)=>{Promise.resolve().then(a.bind(a,55201))},18763:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(40157).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},55201:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var s=a(95155),i=a(12115),l=a(35695),c=a(36521),r=a(2730),d=a(95647),n=a(18763),o=a(28328),u=a(87481),h=a(68856),m=a(30285);let x=()=>{let e=(0,l.useRouter)(),t=(0,l.useParams)(),{toast:a}=(0,u.dj)(),x=t.id?Number(t.id):null,[p,v]=(0,i.useState)(null),[f,j]=(0,i.useState)(!1),[N,y]=(0,i.useState)(!0),[g,b]=(0,i.useState)(null),E=(0,i.useCallback)(async()=>{if(!x){b("No vehicle ID provided."),y(!1);return}y(!0);try{let e=await (0,r.getVehicleById)(x);e?v(e):b("Vehicle not found.")}catch(e){console.error("Failed to fetch vehicle:",e),b(e.message||"Failed to load vehicle data.")}finally{y(!1)}},[x]);(0,i.useEffect)(()=>{E()},[E]);let w=async t=>{if(!x)return void b("Cannot update vehicle without an ID.");j(!0),b(null);try{let s={...t,initialOdometer:void 0===t.initialOdometer?(null==p?void 0:p.initialOdometer)||0:t.initialOdometer};await (0,r.updateVehicle)(x,s),a({title:"Vehicle Updated",description:"".concat(t.make," ").concat(t.model," has been successfully updated."),variant:"default"}),e.push("/vehicles")}catch(e){console.error("Failed to update vehicle:",e),b(e.message||"An unexpected error occurred. Please try again."),a({title:"Error Updating Vehicle",description:e.message||"Could not update the vehicle. Please check the details and try again.",variant:"destructive"})}finally{j(!1)}};return N?(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,s.jsx)(d.z,{title:"Edit Vehicle",description:"Loading vehicle details...",icon:n.A}),(0,s.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,s.jsx)(h.E,{className:"h-10 w-1/3"}),(0,s.jsx)(h.E,{className:"h-12 w-full"}),(0,s.jsx)(h.E,{className:"h-12 w-full"}),(0,s.jsx)(h.E,{className:"h-12 w-full"}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-6",children:[(0,s.jsx)(h.E,{className:"h-10 w-24"}),(0,s.jsx)(h.E,{className:"h-10 w-24"})]})]})]}):g&&!p?(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8 text-center",children:[(0,s.jsx)(d.z,{title:"Error",description:g,icon:o.A}),(0,s.jsx)(m.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]}):(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,s.jsx)(d.z,{title:"Edit Vehicle",description:"Update details for ".concat((null==p?void 0:p.make)||"vehicle"," ").concat((null==p?void 0:p.model)||""),icon:n.A}),g&&p&&(0,s.jsxs)("p",{className:"text-red-500 bg-red-100 p-3 rounded-md",children:["Error: ",g]}),p&&(0,s.jsx)(c.A,{onSubmit:w,initialData:p,isEditing:!0,isLoading:f})]})}},68856:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var s=a(95155),i=a(59434);function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",t),...a})}},95647:(e,t,a)=>{"use strict";a.d(t,{z:()=>i});var s=a(95155);function i(e){let{title:t,description:a,icon:i,children:l}=e;return(0,s.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[i&&(0,s.jsx)(i,{className:"h-8 w-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:t})]}),a&&(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:a})]}),l&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:l})]})}a(12115)}},e=>{var t=t=>e(e.s=t);e.O(0,[5769,8360,832,4066,8162,2730,1568,8441,1684,7358],()=>t(14247)),_N_E=e.O()}]);