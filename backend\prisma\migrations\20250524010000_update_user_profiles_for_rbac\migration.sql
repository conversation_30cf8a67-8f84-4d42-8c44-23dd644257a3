-- Migration: Update user_profiles table for RBAC implementation
-- This migration updates the user_profiles table to support the Hybrid RBAC system

-- Step 1: Add employee_id column
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'user_profiles' AND column_name = 'employee_id') THEN
        ALTER TABLE "user_profiles" ADD COLUMN "employee_id" INTEGER;
    END IF;
END $$;

-- Step 2: Add unique constraint on employee_id
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'user_profiles_employee_id_key') THEN
        ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_employee_id_key" UNIQUE ("employee_id");
    END IF;
END $$;

-- Step 3: Add foreign key constraint to Employee table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'user_profiles_employee_id_fkey') THEN
        ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_employee_id_fkey"
            FOREIGN KEY ("employee_id") REFERENCES "Employee"("id") ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
END $$;

-- Step 4: Remove user_id column if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'user_profiles' AND column_name = 'user_id') THEN
        ALTER TABLE "user_profiles" DROP COLUMN "user_id";
    END IF;
END $$;

-- Step 5: Create index on employee_id for performance
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes
                   WHERE indexname = 'user_profiles_employee_id_idx') THEN
        CREATE INDEX "user_profiles_employee_id_idx" ON "user_profiles"("employee_id");
    END IF;
END $$;
