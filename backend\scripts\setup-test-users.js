/**
 * Setup Test Users for RBAC Testing
 * 
 * This script configures the proper roles for RBAC testing:
 * - <EMAIL>: ADMIN role
 * - <EMAIL>: USER role
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function setupTestUsers() {
    console.log('🔧 Setting up test users for RBAC testing...\n');

    try {
        // Get all auth users
        const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();
        
        if (authError) {
            console.error('❌ Error fetching auth users:', authError.message);
            return;
        }

        // Find the users
        const adminUser = authUsers.users.find(user => user.email === '<EMAIL>');
        const regularUser = authUsers.users.find(user => user.email === '<EMAIL>');

        if (!adminUser) {
            console.error('❌ <NAME_EMAIL> not found');
            return;
        }

        if (!regularUser) {
            console.error('❌ <NAME_EMAIL> not found');
            return;
        }

        console.log('👥 Found users:');
        console.log(`   ADMIN: ${adminUser.email} (${adminUser.id})`);
        console.log(`   USER:  ${regularUser.email} (${regularUser.id})`);

        // Set admin user role
        console.log('\n🔧 Setting up ADMIN user...');
        const { data: adminProfile, error: adminError } = await supabaseAdmin
            .from('user_profiles')
            .update({
                role: 'ADMIN',
                updated_at: new Date().toISOString()
            })
            .eq('id', adminUser.id)
            .select()
            .single();

        if (adminError) {
            console.error('❌ Error updating admin profile:', adminError.message);
            return;
        }

        console.log('✅ Admin user configured:');
        console.log(`   Email: ${adminUser.email}`);
        console.log(`   Role: ${adminProfile.role}`);
        console.log(`   Active: ${adminProfile.is_active}`);

        // Set regular user role
        console.log('\n🔧 Setting up USER...');
        const { data: userProfile, error: userError } = await supabaseAdmin
            .from('user_profiles')
            .update({
                role: 'USER',
                updated_at: new Date().toISOString()
            })
            .eq('id', regularUser.id)
            .select()
            .single();

        if (userError) {
            console.error('❌ Error updating user profile:', userError.message);
            return;
        }

        console.log('✅ Regular user configured:');
        console.log(`   Email: ${regularUser.email}`);
        console.log(`   Role: ${userProfile.role}`);
        console.log(`   Active: ${userProfile.is_active}`);

        console.log('\n🎯 RBAC Test Setup Complete!');
        console.log('============================');
        console.log('✅ ADMIN: <EMAIL> (Role: ADMIN)');
        console.log('✅ USER:  <EMAIL> (Role: USER)');

        console.log('\n📋 Testing Instructions:');
        console.log('========================');
        console.log('\n1️⃣ Test ADMIN User:');
        console.log('   • Log in as: <EMAIL>');
        console.log('   • Should have access to admin endpoints');
        console.log('   • Should be able to create/modify employees');
        console.log('   • Should see all data (no RLS filtering)');

        console.log('\n2️⃣ Test NON-ADMIN User:');
        console.log('   • Log out and log in as: <EMAIL>');
        console.log('   • Should be DENIED admin endpoints (403 Forbidden)');
        console.log('   • Should be DENIED employee creation (403 Forbidden)');
        console.log('   • Should see limited data (RLS filtering)');

        console.log('\n🔧 Browser Console Test Script:');
        console.log('Copy the enhanced NON-ADMIN test script from:');
        console.log('backend/scripts/nonadmin-test-script.js');

    } catch (error) {
        console.error('❌ Setup failed:', error.message);
    }
}

setupTestUsers().catch(console.error);
