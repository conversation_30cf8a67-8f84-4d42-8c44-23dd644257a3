-- Fix Auth Hook and Helper Functions
-- Execute this SQL in Supabase Dashboard → SQL Editor

-- 1. Fix the custom access token hook function (JSON parsing issue)
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event JSONB)
RETURNS JSONB
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    claims JSONB;
    user_role TEXT;
    is_active BOOLEAN;
    employee_id INTEGER;
    user_uuid UUID;
BEGIN
    -- Safely extract user_id from event
    BEGIN
        user_uuid := (event->>'user_id')::UUID;
    EXCEPTION WHEN OTHERS THEN
        -- If user_id extraction fails, return event unchanged
        RETURN event;
    END;

    -- Get the claims from the event
    claims := event->'claims';
    
    -- Ensure claims is a valid JSONB object
    IF claims IS NULL THEN
        claims := '{}'::JSONB;
    END IF;

    -- Fetch user role, status, and employee_id from user_profiles
    SELECT role, is_active, employee_id 
    INTO user_role, is_active, employee_id
    FROM public.user_profiles 
    WHERE id = user_uuid;

    -- Set custom claims in the JWT
    IF user_role IS NOT NULL THEN
        claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
            'user_role', user_role,
            'is_active', COALESCE(is_active, true),
            'employee_id', employee_id
        ));
    ELSE
        -- Default claims for users without a profile
        claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
            'user_role', 'USER',
            'is_active', true,
            'employee_id', null
        ));
    END IF;

    -- Update the event with the new claims
    event := jsonb_set(event, '{claims}', claims);
    
    RETURN event;
EXCEPTION WHEN OTHERS THEN
    -- If anything fails, return the original event to prevent auth failure
    RETURN event;
END;
$$;

-- 2. Fix helper functions (UUID parameter type issue)
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM public.user_profiles
    WHERE id = user_id;
    
    RETURN COALESCE(user_role, 'USER');
END;
$$;

CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM public.user_profiles
    WHERE id = user_id;
    
    RETURN user_role IN ('ADMIN', 'SUPER_ADMIN');
END;
$$;

CREATE OR REPLACE FUNCTION public.is_manager_or_above(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM public.user_profiles
    WHERE id = user_id;
    
    RETURN user_role IN ('MANAGER', 'ADMIN', 'SUPER_ADMIN');
END;
$$;

CREATE OR REPLACE FUNCTION public.get_user_employee_id(user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    emp_id INTEGER;
BEGIN
    SELECT employee_id INTO emp_id
    FROM public.user_profiles
    WHERE id = user_id;
    
    RETURN emp_id;
END;
$$;

-- 3. Grant proper permissions
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO service_role;

GRANT EXECUTE ON FUNCTION public.get_user_role(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_manager_or_above(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_employee_id(UUID) TO authenticated;

GRANT SELECT ON public.user_profiles TO supabase_auth_admin;
GRANT SELECT ON public.user_profiles TO service_role;
