{"version": 3, "file": "supabaseAuth.js", "sourceRoot": "", "sources": ["../../src/middleware/supabaseAuth.ts"], "names": [], "mappings": "AAEA,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAYjD;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,KAAK,EAC5C,GAAY,EACZ,GAAa,EACb,IAAkB,EACF,EAAE;IAClB,IAAI,CAAC;QACJ,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,uBAAuB;gBAC9B,IAAI,EAAE,UAAU;gBAChB,OAAO,EACN,0EAA0E;aAC3E,CAAC,CAAC;YACH,OAAO;QACR,CAAC;QAED,iCAAiC;QACjC,MAAM,EACL,IAAI,EAAE,EAAC,IAAI,EAAC,EACZ,KAAK,GACL,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE5C,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CACZ,wBAAwB,EACxB,KAAK,EAAE,OAAO,IAAI,eAAe,CACjC,CAAC;YACF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,0BAA0B;gBACjC,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,sDAAsD;aAC/D,CAAC,CAAC;YACH,OAAO;QACR,CAAC;QAED,6DAA6D;QAC7D,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,oBAAoB;gBAC3B,IAAI,EAAE,oBAAoB;gBAC1B,OAAO,EAAE,8DAA8D;aACvE,CAAC,CAAC;YACH,OAAO;QACR,CAAC;QAED,mDAAmD;QACnD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;QAErB,qDAAqD;QACrD,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAEhE,IAAI,EAAE,CAAC;IACR,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACrB,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACtD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACpB,KAAK,EAAE,oCAAoC;YAC3C,IAAI,EAAE,oBAAoB;YAC1B,OAAO,EAAE,8CAA8C;SACvD,CAAC,CAAC;IACJ,CAAC;AACF,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,YAAsB,EAAE,EAAE;IACrD,OAAO,KAAK,EACX,GAAY,EACZ,GAAa,EACb,IAAkB,EACF,EAAE;QAClB,IAAI,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACpB,KAAK,EAAE,yBAAyB;oBAChC,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,oDAAoD;iBAC7D,CAAC,CAAC;gBACH,OAAO;YACR,CAAC;YAED,0DAA0D;YAC1D,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC;YAC1D,MAAM,QAAQ,GAAG,YAAY,EAAE,SAAS,CAAC;YACzC,MAAM,QAAQ,GAAG,YAAY,EAAE,SAAS,CAAC;YAEzC,yDAAyD;YACzD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACpB,KAAK,EAAE,0BAA0B;oBACjC,IAAI,EAAE,gBAAgB;oBACtB,OAAO,EACN,qEAAqE;iBACtE,CAAC,CAAC;gBACH,OAAO;YACR,CAAC;YAED,0BAA0B;YAC1B,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;gBACxB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACpB,KAAK,EAAE,kBAAkB;oBACzB,IAAI,EAAE,kBAAkB;oBACxB,OAAO,EACN,qEAAqE;iBACtE,CAAC,CAAC;gBACH,OAAO;YACR,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACpB,KAAK,EAAE,0BAA0B;oBACjC,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,kCAAkC,YAAY,CAAC,IAAI,CAC3D,IAAI,CACJ,gBAAgB,QAAQ,EAAE;iBAC3B,CAAC,CAAC;gBACH,OAAO;YACR,CAAC;YAED,+BAA+B;YAC/B,OAAO,CAAC,GAAG,CACV,sBAAsB,GAAG,CAAC,IAAI,CAAC,KAAK,WAAW,QAAQ,mBAAmB,CAC1E,CAAC;YAEF,IAAI,EAAE,CAAC;QACR,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACrB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,mCAAmC;gBAC1C,IAAI,EAAE,qBAAqB;aAC3B,CAAC,CAAC;QACJ,CAAC;IACF,CAAC,CAAC;AACH,CAAC,CAAC"}