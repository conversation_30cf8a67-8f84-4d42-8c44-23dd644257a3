# Database Configuration
# Set USE_SUPABASE to 'true' to use Supabase, 'false' to use local PostgreSQL
USE_SUPABASE=true

# Database URL - This will be used by Prisma
# For local development with Docker:
# DATABASE_URL=postgresql://postgres:postgres@localhost:5432/car_service_db?schema=public

# For Supabase (uncomment and update when switching to Supabase):
DATABASE_URL=postgresql://postgres.abylqjnpaegeqwktcukn:<EMAIL>:5432/postgres



# Supabase Configuration - EMERGENCY SECURITY IMPLEMENTATION
SUPABASE_URL=https://abylqjnpaegeqwktcukn.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzIxMzQ1MywiZXhwIjoyMDYyNzg5NDUzfQ.yLrGESZvVC6ISrqlcKeR3uvfRqdWPcZqYqLLZkjphU8
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzIxMzQ1MywiZXhwIjoyMDYyNzg5NDUzfQ.yLrGESZvVC6ISrqlcKeR3uvfRqdWPcZqYqLLZkjphU8

# Server configuration
PORT=3001
NODE_ENV=development

# CORS configuration
FRONTEND_URL=http://localhost:3000,http://localhost:9002,https://9000-firebase-studio-1746987210702.cluster-c3a7z3wnwzapkx3rfr5kz62dac.cloudworkstations.dev,https://6000-firebase-studio-1746987210702.cluster-c3a7z3wnwzapkx3rfr5kz62dac.cloudworkstations.dev,https://6000-firebase-studio-1747070456093.cluster-3gc7bglotjgwuxlqpiut7yyqt4.cloudworkstations.dev

# Third-party API keys
# OpenSky Network API doesn't require an API key for basic access
# For higher rate limits, you can register at https://opensky-network.org/

# JWT Secret (for authentication)
JWT_SECRET=JJxeCe52j/9tIaQLW9guDu+pBpSRp//c4PXnj7mV/oyTUdwSBGIOfmpKEAGPY3hA3cBkcu2o2xbw/FiHIKtFUw==
# Logging
LOG_LEVEL=info