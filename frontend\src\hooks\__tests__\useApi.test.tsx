import {renderHook, act} from '@testing-library/react';
import {useApi, usePaginatedApi} from '../useApi';
import {withRetry} from '@/lib/utils/apiUtils';

// Mock dependencies
jest.mock('@/lib/utils/apiUtils', () => ({
	withRetry: jest.fn((fn) => fn()),
}));

describe('useApi', () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('should return initial state correctly', () => {
		const fetchFn = jest.fn().mockResolvedValue({data: 'test'});

		const {result} = renderHook(() => useApi(fetchFn, {autoFetch: false}));

		expect(result.current.data).toBeNull();
		expect(result.current.isLoading).toBe(false);
		expect(result.current.error).toBeNull();
		expect(typeof result.current.refetch).toBe('function');
	});

	it('should fetch data on mount when autoFetch is true', async () => {
		const mockData = {data: 'test'};
		const fetchFn = jest.fn().mockResolvedValue(mockData);

		const {result} = renderHook(() => useApi(fetchFn));

		expect(result.current.isLoading).toBe(true);

		// Wait for the async operation to complete
		await act(async () => {
			await new Promise((resolve) => setTimeout(resolve, 0));
		});

		expect(fetchFn).toHaveBeenCalledTimes(1);
		expect(withRetry).toHaveBeenCalled();
		expect(result.current.data).toEqual(mockData);
		expect(result.current.isLoading).toBe(false);
		expect(result.current.error).toBeNull();
	});

	it('should handle errors correctly', async () => {
		const error = new Error('Test error');
		const fetchFn = jest.fn().mockRejectedValue(error);

		const {result} = renderHook(() => useApi(fetchFn));

		expect(result.current.isLoading).toBe(true);

		// Wait for the async operation to complete
		await act(async () => {
			await new Promise((resolve) => setTimeout(resolve, 0));
		});

		expect(fetchFn).toHaveBeenCalledTimes(1);
		expect(result.current.data).toBeNull();
		expect(result.current.isLoading).toBe(false);
		expect(result.current.error).toBe('Test error');
	});

	it('should refetch data when refetch is called', async () => {
		const mockData = {data: 'test'};
		const fetchFn = jest.fn().mockResolvedValue(mockData);

		const {result} = renderHook(() => useApi(fetchFn));

		// Wait for initial fetch to complete
		await act(async () => {
			await new Promise((resolve) => setTimeout(resolve, 0));
		});

		expect(fetchFn).toHaveBeenCalledTimes(1);

		// Call refetch
		await act(async () => {
			result.current.refetch();
			// Wait for refetch to complete
			await new Promise((resolve) => setTimeout(resolve, 0));
		});

		expect(fetchFn).toHaveBeenCalledTimes(2);
		expect(result.current.data).toEqual(mockData);
	});

	it('should call onSuccess callback when fetch is successful', async () => {
		const mockData = {data: 'test'};
		const fetchFn = jest.fn().mockResolvedValue(mockData);
		const onSuccess = jest.fn();

		renderHook(() => useApi(fetchFn, {onSuccess}));

		// Wait for the async operation to complete
		await act(async () => {
			await new Promise((resolve) => setTimeout(resolve, 0));
		});

		expect(onSuccess).toHaveBeenCalledWith(mockData);
	});

	it('should call onError callback when fetch fails', async () => {
		const error = new Error('Test error');
		const fetchFn = jest.fn().mockRejectedValue(error);
		const onError = jest.fn();

		renderHook(() => useApi(fetchFn, {onError}));

		// Wait for the async operation to complete
		await act(async () => {
			await new Promise((resolve) => setTimeout(resolve, 0));
		});

		expect(onError).toHaveBeenCalledWith(error);
	});
});

describe('usePaginatedApi', () => {
	it('should handle pagination correctly', async () => {
		const mockResponse = {
			data: ['item1', 'item2'],
			pagination: {
				page: 1,
				limit: 10,
				total: 20,
				totalPages: 2,
			},
		};

		const fetchFn = jest.fn().mockResolvedValue(mockResponse);

		const {result} = renderHook(() => usePaginatedApi(fetchFn));

		// Wait for the async operation to complete
		await act(async () => {
			await new Promise((resolve) => setTimeout(resolve, 0));
		});

		expect(fetchFn).toHaveBeenCalledWith(1, 10);
		expect(result.current.data).toEqual(mockResponse.data);
		expect(result.current.page).toBe(1);
		expect(result.current.totalPages).toBe(2);
		expect(result.current.totalItems).toBe(20);
		expect(result.current.hasNextPage).toBe(true);
		expect(result.current.hasPrevPage).toBe(false);
	});

	it('should navigate to next page correctly', async () => {
		const mockResponse1 = {
			data: ['item1', 'item2'],
			pagination: {
				page: 1,
				limit: 10,
				total: 20,
				totalPages: 2,
			},
		};

		const mockResponse2 = {
			data: ['item3', 'item4'],
			pagination: {
				page: 2,
				limit: 10,
				total: 20,
				totalPages: 2,
			},
		};

		const fetchFn = jest
			.fn()
			.mockResolvedValueOnce(mockResponse1)
			.mockResolvedValueOnce(mockResponse2);

		const {result} = renderHook(() => usePaginatedApi(fetchFn));

		// Wait for initial fetch to complete
		await act(async () => {
			await new Promise((resolve) => setTimeout(resolve, 0));
		});

		// Navigate to next page
		await act(async () => {
			result.current.nextPage();
			// Wait for the next page fetch to complete
			await new Promise((resolve) => setTimeout(resolve, 0));
		});

		expect(fetchFn).toHaveBeenCalledTimes(2);
		expect(fetchFn).toHaveBeenLastCalledWith(2, 10);
		expect(result.current.data).toEqual(mockResponse2.data);
		expect(result.current.page).toBe(2);
		expect(result.current.hasNextPage).toBe(false);
		expect(result.current.hasPrevPage).toBe(true);
	});
});
