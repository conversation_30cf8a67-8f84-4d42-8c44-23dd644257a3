'use client';

import React, {useState} from 'react';
import {useRouter} from 'next/navigation';
import EmployeeForm from '@/components/employees/EmployeeForm';
import {addEmployee} from '@/lib/store';
import {EmployeeFormData} from '@/lib/schemas/employeeSchemas';
import type {Employee} from '@/lib/types'; // For the return type of addEmployee
import {PageHeader} from '@/components/ui/PageHeader';
import {UsersRound} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';

const AddEmployeePage = () => {
	const router = useRouter();
	const {toast} = useToast();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleSubmit = async (data: EmployeeFormData) => {
		setIsLoading(true);
		setError(null);
		try {
			// The addEmployee function in store.ts expects data that aligns with EmployeeFormData
			// after schema transformation (e.g. skills to array).
			// The ID, createdAt, updatedAt etc. are handled by the backend and store logic.
			const newEmployee = await addEmployee(data);
			toast({
				title: 'Employee Added',
				description: `${
					newEmployee.name || newEmployee.fullName
				} has been successfully added.`,
				variant: 'default',
			});
			router.push('/employees');
		} catch (err: any) {
			console.error('Failed to add employee:', err);
			const errorMessage =
				err.response?.data?.error ||
				err.message ||
				'An unexpected error occurred.';
			setError(errorMessage);
			toast({
				title: 'Error Adding Employee',
				description: errorMessage,
				variant: 'destructive',
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className='container mx-auto py-8 space-y-8'>
			<PageHeader
				title='Add New Employee'
				description='Enter the details for the new employee.'
				icon={UsersRound}
			/>
			{error && (
				<div
					className='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4'
					role='alert'>
					<strong className='font-bold'>Error: </strong>
					<span className='block sm:inline'>{error}</span>
				</div>
			)}
			<EmployeeForm
				onSubmit={handleSubmit}
				isEditing={false}
				isLoading={isLoading}
			/>
		</div>
	);
};

export default AddEmployeePage;
