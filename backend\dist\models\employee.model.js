import prisma from './index.js';
import { PrismaClientKnownRequestError } from '../generated/prisma/runtime/library.js';
export const createEmployee = async (data) => {
    try {
        // Prisma.EmployeeCreateInput already expects status to be PrismaEmployeeStatus if not nullable
        // If data.status is a string from controller, it should be cast like data.status as PrismaEmployeeStatus
        // The controller already does this cast.
        return await prisma.employee.create({
            data,
            include: {
                statusHistory: { orderBy: { changedAt: 'desc' } },
                assignedVehicle: true,
                serviceRecords: { orderBy: { date: 'desc' } },
                assignedTasks: { orderBy: { dateTime: 'desc' } }
            }
        });
    }
    catch (error) {
        console.error('Error creating employee:', error);
        if (error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2002') {
            if (error.meta?.target?.includes('employeeId')) {
                throw new Error(`Employee with Employee ID ${data.employeeId} already exists.`);
            }
            // Check if the error is related to assignedVehicle if data.assignedVehicle is used
            if (data.assignedVehicle && error.meta?.target?.includes('assignedVehicleId_unique')) { // Assuming a unique constraint name
                throw new Error(`Vehicle is already assigned to another employee.`);
            }
            throw new Error('A unique constraint failed while creating the employee.');
        }
        if (error instanceof PrismaClientKnownRequestError && error.code === 'P2003') {
            // Check if the error is related to assignedVehicle if data.assignedVehicle is used
            if (data.assignedVehicle && error.meta?.field_name?.includes('assignedVehicleId')) {
                throw new Error(`Vehicle not found for assignment.`);
            }
        }
        return null;
    }
};
export const getAllEmployees = async () => {
    try {
        return await prisma.employee.findMany({
            include: {
                statusHistory: { orderBy: { changedAt: 'desc' } },
                assignedVehicle: true,
                serviceRecords: { orderBy: { date: 'desc' } },
                assignedTasks: { orderBy: { dateTime: 'desc' } }
            },
            orderBy: { name: 'asc' }
        });
    }
    catch (error) {
        console.error('Error fetching all employees:', error);
        return [];
    }
};
export const getEmployeeById = async (id) => {
    try {
        return await prisma.employee.findUnique({
            where: { id },
            include: {
                statusHistory: { orderBy: { changedAt: 'desc' } },
                assignedVehicle: true,
                serviceRecords: { orderBy: { date: 'desc' } },
                assignedTasks: { orderBy: { dateTime: 'desc' } }
            }
        });
    }
    catch (error) {
        console.error(`Error fetching employee with ID ${id}:`, error);
        return null;
    }
};
export const updateEmployee = async (id, data, statusChangeReason) => {
    try {
        let updatedEmployee = null;
        await prisma.$transaction(async (tx) => {
            if (data.status && typeof data.status === 'string') {
                const currentEmployee = await tx.employee.findUnique({ where: { id }, select: { status: true } });
                if (currentEmployee && currentEmployee.status !== data.status) {
                    await tx.employeeStatusEntry.create({
                        data: {
                            employeeId: id,
                            status: data.status, // Ensured by controller
                            reason: statusChangeReason || "Status updated",
                        }
                    });
                }
            }
            // The 'data' payload from controller already correctly handles 'assignedVehicle' relation
            // or 'assignedVehicleId: null' if it's a scalar.
            // If 'assignedVehicleId' is passed for connect/disconnect, controller maps it to 'assignedVehicle' input.
            updatedEmployee = await tx.employee.update({
                where: { id },
                data: data,
                include: {
                    statusHistory: { orderBy: { changedAt: 'desc' } },
                    assignedVehicle: true,
                    serviceRecords: { orderBy: { date: 'desc' } },
                    assignedTasks: { orderBy: { dateTime: 'desc' } }
                }
            });
        });
        return updatedEmployee;
    }
    catch (error) {
        console.error(`Error updating employee with ID ${id}:`, error);
        if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
            return null;
        }
        if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
            if (error.meta?.target?.includes('employeeId') && data.employeeId) {
                throw new Error(`Another employee with Employee ID ${data.employeeId} already exists.`);
            }
            // Check if the error is related to assignedVehicle if data.assignedVehicle is used for connect
            if (data.assignedVehicle && error.meta?.target?.includes('assignedVehicleId_unique')) { // Assuming constraint name
                throw new Error(`Vehicle is already assigned to another employee.`);
            }
        }
        if (error instanceof PrismaClientKnownRequestError && error.code === 'P2003') {
            // Check if the error is related to assignedVehicle if data.assignedVehicle is used for connect
            if (data.assignedVehicle && error.meta?.field_name?.includes('assignedVehicleId')) {
                throw new Error(`Vehicle not found for assignment.`);
            }
        }
        return null;
    }
};
export const deleteEmployee = async (id) => {
    try {
        return await prisma.$transaction(async (tx) => {
            const employeeToDelete = await tx.employee.findUnique({ where: { id } });
            if (!employeeToDelete)
                return null;
            await tx.employeeStatusEntry.deleteMany({ where: { employeeId: id } });
            await tx.employee.delete({ where: { id } });
            return employeeToDelete;
        });
    }
    catch (error) {
        console.error(`Error deleting employee with ID ${id}:`, error);
        if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
            return null;
        }
        return null;
    }
};
//# sourceMappingURL=employee.model.js.map