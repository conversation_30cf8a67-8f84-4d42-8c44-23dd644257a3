'use client';

import React from 'react';
import type {EnrichedServiceRecord} from '@/lib/types';
import {Card, CardContent} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import {cn} from '@/lib/utils';

/**
 * Props for the SummaryStatCard component
 */
interface SummaryStatCardProps {
	/** Value to display (typically a number) */
	value: number | string;
	/** Label to display below the value */
	label: string;
	/** Additional CSS class for the card background */
	className?: string;
	/** CSS class for the label text color */
	textColor?: string;
	/** Optional column span for the card */
	colSpan?: 'col-span-1' | 'col-span-2' | 'col-span-3';
}

/**
 * A card component for displaying a summary statistic
 */
function SummaryStatCard({
	value,
	label,
	className,
	textColor = 'text-muted-foreground',
	colSpan,
}: SummaryStatCardProps) {
	return (
		<Card className={cn('overflow-hidden', className, colSpan)}>
			<CardContent className='p-4 text-center'>
				<p className='text-2xl font-semibold text-card-foreground'>{value}</p>
				<p className={cn('text-sm', textColor)}>{label}</p>
			</CardContent>
		</Card>
	);
}

/**
 * Props for the ServiceHistorySummary component
 */
interface ServiceHistorySummaryProps {
	/** Array of service records to display summary statistics for */
	records: EnrichedServiceRecord[];
	/** Whether this summary is for a vehicle-specific view */
	vehicleSpecific?: boolean;
	/** Additional CSS class names */
	className?: string;
}

/**
 * A component that displays summary statistics for service history records
 *
 * @example
 * ```tsx
 * <ServiceHistorySummary records={filteredRecords} vehicleSpecific={false} />
 * ```
 */
export function ServiceHistorySummary({
	records,
	vehicleSpecific = false,
	className,
}: ServiceHistorySummaryProps) {
	// Calculate statistics
	const totalRecords = records.length;
	const totalCost = records.reduce(
		(sum, record) => sum + (Number(record.cost) || 0),
		0
	);

	// Get date range
	const dates = records.map((record) => new Date(record.date).getTime());
	const oldestDate = dates.length ? new Date(Math.min(...dates)) : null;
	const newestDate = dates.length ? new Date(Math.max(...dates)) : null;

	// Get service type counts
	const serviceTypeCounts = records.reduce((acc, record) => {
		record.servicePerformed.forEach((service) => {
			acc[service] = (acc[service] || 0) + 1;
		});
		return acc;
	}, {} as Record<string, number>);

	// Get top services
	const topServices = Object.entries(serviceTypeCounts)
		.sort((a, b) => b[1] - a[1])
		.slice(0, 3);

	// Calculate odometer range for vehicle-specific view
	const odometerRange =
		vehicleSpecific && records.length > 0
			? Math.max(...records.map((r) => r.odometer)) -
			  Math.min(...records.map((r) => r.odometer))
			: 0;

	// Count unique vehicles for general view
	const uniqueVehicleCount =
		!vehicleSpecific && records.length > 0
			? new Set(records.map((r) => r.vehicleId)).size
			: 0;

	return (
		<div
			className={cn(
				'mt-6 grid grid-cols-2 sm:grid-cols-3 gap-4 summary-grid',
				className
			)}>
			{/* Total Services Card */}
			<SummaryStatCard
				value={totalRecords}
				label='Total Services'
				className='border-primary/10'
			/>

			{/* Total Cost Card */}
			<SummaryStatCard
				value={`$${totalCost.toFixed(2)}`}
				label='Total Cost'
				className='border-primary/10'
			/>

			{/* Vehicles Serviced Card (only for general view) */}
			{!vehicleSpecific && records.length > 0 && (
				<SummaryStatCard
					value={uniqueVehicleCount}
					label='Vehicles Serviced'
					className='border-primary/10'
				/>
			)}

			{/* Odometer Range Card (only for vehicle-specific view) */}
			{vehicleSpecific && records.length > 0 && (
				<SummaryStatCard
					value={odometerRange.toLocaleString()}
					label='Odometer Range Covered'
					className='border-primary/10'
				/>
			)}

			{/* Date Range Card */}
			{records.length > 0 && (
				<SummaryStatCard
					value={`${oldestDate?.toLocaleDateString()} - ${newestDate?.toLocaleDateString()}`}
					label='Date Range'
					className='border-primary/10'
					colSpan='col-span-2 sm:col-span-3'
				/>
			)}

			{/* Top Services Card */}
			{topServices.length > 0 && (
				<Card className='overflow-hidden col-span-2 sm:col-span-3 border-primary/10'>
					<CardContent className='p-4'>
						<h3 className='text-sm font-semibold mb-2 text-card-foreground'>
							Top Services
						</h3>
						<div className='flex flex-wrap gap-2'>
							{topServices.map(([service, count]) => (
								<Badge
									key={service}
									variant='secondary'
									className='text-xs px-2 py-1'
									aria-label={`${service}: ${count} services`}>
									{service} ({count})
								</Badge>
							))}
						</div>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
