import { Router } from 'express';
import * as serviceRecordController from '../controllers/serviceRecord.controller.js';
import { validate } from '../middleware/validation.js';
import { authenticateSupabaseUser, } from '../middleware/supabaseAuth.js';
import { serviceRecordIdParamSchema } from '../schemas/serviceRecord.schema.js';
import logger from '../utils/logger.js';
import { z } from 'zod';
const router = Router();
/**
 * @openapi
 * /servicerecords:
 *   get:
 *     tags: [Service Records]
 *     summary: Retrieve all service records across all vehicles
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter records by start date (ISO format)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter records by end date (ISO format)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Limit the number of records returned
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *         description: Offset for pagination
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [date, odometer, cost]
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort order (ascending or descending)
 *     responses:
 *       200:
 *         description: A list of all service records.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ServiceRecord'
 *       500:
 *         description: Server error.
 */
// 🚨 EMERGENCY SECURITY: All direct service record routes require authentication
router.get('/', authenticateSupabaseUser, serviceRecordController.getAllServiceRecordsDirect);
/**
 * @openapi
 * /servicerecords/enriched:
 *   get:
 *     tags: [Service Records]
 *     summary: Retrieve all service records enriched with vehicle and employee information
 *     responses:
 *       200:
 *         description: A list of enriched service records.
 *       500:
 *         description: Server error.
 */
// Define a custom schema for the 'enriched' special parameter
const enrichedParamSchema = z.object({
    id: z.literal('enriched'),
});
// IMPORTANT: The order of routes matters in Express!
// More specific routes (like '/enriched') must come BEFORE generic routes (like '/:id')
router.get('/enriched', authenticateSupabaseUser, serviceRecordController.getEnrichedServiceRecords);
/**
 * @openapi
 * /servicerecords/{id}:
 *   get:
 *     tags: [Service Records]
 *     summary: Retrieve a specific service record by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The service record ID
 *     responses:
 *       200:
 *         description: The service record.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ServiceRecord'
 *       404:
 *         description: Service record not found.
 *       500:
 *         description: Server error.
 */
router.get('/:id', authenticateSupabaseUser, validate(serviceRecordIdParamSchema, 'params'), serviceRecordController.getServiceRecordByIdDirect);
// Log all requests to this router
router.use((req, res, next) => {
    logger.info(`Direct service record request: ${req.method} ${req.originalUrl}`, {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
    });
    next();
});
export default router;
//# sourceMappingURL=directServiceRecord.routes.js.map