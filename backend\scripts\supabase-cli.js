/**
 * Supabase CLI Helper Script
 *
 * This script provides a convenient interface for working with Supabase CLI
 * in conjunction with Prisma. It helps manage the connection between your
 * local Prisma schema and your Supabase PostgreSQL database.
 *
 * Usage:
 *   node scripts/supabase-cli.js <command>
 *
 * Commands:
 *   login       - Login to Supabase CLI
 *   link        - Link to an existing Supabase project
 *   db:pull     - Pull the database schema from Supabase
 *   db:push     - Push the Prisma schema to Supabase
 *   db:migrate  - Run Prisma migrations on Supabase
 *   db:seed     - Seed the Supabase database
 *   config      - Show current configuration
 */

import fs from 'fs';
import path from 'path';
import {fileURLToPath} from 'url';
import {execSync} from 'child_process';
import readline from 'readline';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const configPath = path.join(rootDir, 'supabase', 'config.json');

// Create readline interface for user input
const rl = readline.createInterface({
	input: process.stdin,
	output: process.stdout,
});

// Helper function to execute shell commands
function execCommand(command) {
	try {
		return execSync(command, {stdio: 'inherit', cwd: rootDir});
	} catch (error) {
		console.error(`Error executing command: ${command}`);
		console.error(error.message);
		process.exit(1);
	}
}

// Helper function to read config
function readConfig() {
	try {
		if (fs.existsSync(configPath)) {
			const configData = fs.readFileSync(configPath, 'utf8');
			return JSON.parse(configData);
		}
		return null;
	} catch (error) {
		console.error('Error reading config file:', error.message);
		return null;
	}
}

// Helper function to write config
function writeConfig(config) {
	try {
		const configDir = path.dirname(configPath);
		if (!fs.existsSync(configDir)) {
			fs.mkdirSync(configDir, {recursive: true});
		}
		fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
		console.log('Configuration saved successfully.');
	} catch (error) {
		console.error('Error writing config file:', error.message);
	}
}

// Helper function to update .env file
function updateEnvFile(dbUrl) {
	try {
		const envPath = path.join(rootDir, '.env');
		let envContent = '';

		if (fs.existsSync(envPath)) {
			envContent = fs.readFileSync(envPath, 'utf8');
		}

		// Update DATABASE_URL
		if (envContent.includes('DATABASE_URL=')) {
			envContent = envContent.replace(
				/DATABASE_URL=.*(\r?\n|$)/g,
				`DATABASE_URL=${dbUrl}$1`
			);
		} else {
			envContent += `\nDATABASE_URL=${dbUrl}\n`;
		}

		// Update USE_SUPABASE flag
		if (envContent.includes('USE_SUPABASE=')) {
			envContent = envContent.replace(
				/USE_SUPABASE=.*(\r?\n|$)/g,
				'USE_SUPABASE=true$1'
			);
		} else {
			envContent += `USE_SUPABASE=true\n`;
		}

		fs.writeFileSync(envPath, envContent);
		console.log('.env file updated with Supabase database URL.');
	} catch (error) {
		console.error('Error updating .env file:', error.message);
	}
}

// Login to Supabase
function login() {
	console.log('Logging in to Supabase...');
	execCommand('npx supabase login');
}

// Link to an existing Supabase project
function linkProject() {
	rl.question('Enter your Supabase project ID: ', (projectId) => {
		if (!projectId) {
			console.error('Project ID cannot be empty');
			rl.close();
			process.exit(1);
		}

		console.log(`Linking to Supabase project: ${projectId}`);

		rl.question('Enter your database password: ', (password) => {
			if (!password) {
				console.error('Password cannot be empty');
				rl.close();
				process.exit(1);
			}

			rl.question('Enter your service role key: ', (serviceRoleKey) => {
				if (!serviceRoleKey) {
					console.error('Service role key cannot be empty');
					rl.close();
					process.exit(1);
				}

				const config = {
					project_id: projectId,
					api: {
						http_url: `https://${projectId}.supabase.co`,
						db_url: `postgresql://postgres:${password}@db.${projectId}.supabase.co:5432/postgres`,
						service_role_key: serviceRoleKey,
					},
				};

				writeConfig(config);
				updateEnvFile(config.api.db_url);

				// Set environment variables for Supabase CLI
				process.env.SUPABASE_PROJECT_ID = projectId;
				process.env.SUPABASE_ACCESS_TOKEN = serviceRoleKey;

				console.log('Project linked successfully!');
				console.log(
					'You can now use other commands like db:pull, db:push, etc.'
				);

				rl.close();
			});
		});
	});
}

// Pull database schema from Supabase
function pullDatabase() {
	const config = readConfig();
	if (!config) {
		console.error('No configuration found. Please run "link" command first.');
		process.exit(1);
	}

	console.log('Pulling database schema from Supabase...');

	// Set environment variables for Prisma
	process.env.DATABASE_URL = config.api.db_url;

	// Use Prisma to introspect the database
	execCommand('npx prisma db pull');
	console.log('Database schema pulled successfully!');
}

// Push Prisma schema to Supabase
function pushDatabase() {
	const config = readConfig();
	if (!config) {
		console.error('No configuration found. Please run "link" command first.');
		process.exit(1);
	}

	console.log('Pushing Prisma schema to Supabase...');

	// Set environment variables for Prisma
	process.env.DATABASE_URL = config.api.db_url;

	// Use Prisma to push the schema
	execCommand('npx prisma db push');
	console.log('Schema pushed successfully!');
}

// Run Prisma migrations on Supabase
function migrateDatabase() {
	const config = readConfig();
	if (!config) {
		console.error('No configuration found. Please run "link" command first.');
		process.exit(1);
	}

	console.log('Running migrations on Supabase database...');

	// Set environment variables for Prisma
	process.env.DATABASE_URL = config.api.db_url;

	// Use Prisma to run migrations
	execCommand('npx prisma migrate deploy');
	console.log('Migrations completed successfully!');
}

// Seed the Supabase database
function seedDatabase() {
	const config = readConfig();
	if (!config) {
		console.error('No configuration found. Please run "link" command first.');
		process.exit(1);
	}

	console.log('Seeding Supabase database...');

	// Set environment variables for Prisma
	process.env.DATABASE_URL = config.api.db_url;

	// Use Prisma to seed the database
	execCommand('npx prisma db seed');
	console.log('Database seeded successfully!');
}

// Show current configuration
function showConfig() {
	const config = readConfig();
	if (!config) {
		console.log('No configuration found. Please run "link" command first.');
		return;
	}

	// Hide sensitive information
	const sanitizedConfig = {
		...config,
		api: {
			...config.api,
			db_url: config.api.db_url.replace(/postgres:.*@/, 'postgres:****@'),
			service_role_key: '****',
		},
	};

	console.log('Current Supabase Configuration:');
	console.log(JSON.stringify(sanitizedConfig, null, 2));
}

// Parse command line arguments
const command = process.argv[2];

switch (command) {
	case 'login':
		login();
		break;
	case 'link':
		linkProject();
		break;
	case 'db:pull':
		pullDatabase();
		break;
	case 'db:push':
		pushDatabase();
		break;
	case 'db:migrate':
		migrateDatabase();
		break;
	case 'db:seed':
		seedDatabase();
		break;
	case 'config':
		showConfig();
		break;
	default:
		console.log('Usage: node scripts/supabase-cli.js <command>');
		console.log('Commands:');
		console.log('  login       - Login to Supabase CLI');
		console.log('  link        - Link to an existing Supabase project');
		console.log('  db:pull     - Pull the database schema from Supabase');
		console.log('  db:push     - Push the Prisma schema to Supabase');
		console.log('  db:migrate  - Run Prisma migrations on Supabase');
		console.log('  db:seed     - Seed the Supabase database');
		console.log('  config      - Show current configuration');
		break;
}
