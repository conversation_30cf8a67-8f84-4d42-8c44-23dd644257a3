/**
 * Middleware to sanitize task response data before it's sent to the frontend
 * Ensures that array fields are always arrays, even if they're null or undefined
 */

import {Request, Response, NextFunction} from 'express';

// Import logger if available, otherwise use console
let logger: any;
try {
	// Try to import the application logger
	const {default: appLogger} = require('../utils/logger');
	logger = appLogger;
} catch (error) {
	// Fallback to console if logger is not available
	logger = {
		warn: console.warn,
		error: console.error,
	};
}

/**
 * Sanitizes a single task object to ensure all array fields are arrays
 * @param task The task object to sanitize
 * @param requestInfo Additional request information for logging
 * @returns The sanitized task object
 */
const sanitizeTaskObject = (
	task: any,
	requestInfo: {path: string; taskId?: string} = {path: 'unknown'}
): any => {
	if (!task) return task;

	// Create a copy of the task to avoid modifying the original
	const sanitizedTask = {...task};
	const taskId = task.id || requestInfo.taskId || 'unknown';

	// Ensure assignedEmployees is always an array
	if (
		!sanitizedTask.assignedEmployees ||
		!Array.isArray(sanitizedTask.assignedEmployees)
	) {
		const originalValue =
			sanitizedTask.assignedEmployees === null
				? 'null'
				: typeof sanitizedTask.assignedEmployees;

		logger.warn(
			`TaskResponseSanitizer: Original value for assignedEmployees was ${originalValue}, sanitized to []. ` +
				`[TaskID: ${taskId}, Path: ${requestInfo.path}]`
		);
		sanitizedTask.assignedEmployees = [];
	}

	// Ensure requiredSkills is always an array
	if (
		!sanitizedTask.requiredSkills ||
		!Array.isArray(sanitizedTask.requiredSkills)
	) {
		const originalValue =
			sanitizedTask.requiredSkills === null
				? 'null'
				: typeof sanitizedTask.requiredSkills;

		logger.warn(
			`TaskResponseSanitizer: Original value for requiredSkills was ${originalValue}, sanitized to []. ` +
				`[TaskID: ${taskId}, Path: ${requestInfo.path}]`
		);
		sanitizedTask.requiredSkills = [];
	}

	// Ensure subTasks is always an array
	if (!sanitizedTask.subTasks || !Array.isArray(sanitizedTask.subTasks)) {
		const originalValue =
			sanitizedTask.subTasks === null ? 'null' : typeof sanitizedTask.subTasks;

		logger.warn(
			`TaskResponseSanitizer: Original value for subTasks was ${originalValue}, sanitized to []. ` +
				`[TaskID: ${taskId}, Path: ${requestInfo.path}]`
		);
		sanitizedTask.subTasks = [];
	}

	// Ensure statusHistory is always an array
	if (
		!sanitizedTask.statusHistory ||
		!Array.isArray(sanitizedTask.statusHistory)
	) {
		const originalValue =
			sanitizedTask.statusHistory === null
				? 'null'
				: typeof sanitizedTask.statusHistory;

		logger.warn(
			`TaskResponseSanitizer: Original value for statusHistory was ${originalValue}, sanitized to []. ` +
				`[TaskID: ${taskId}, Path: ${requestInfo.path}]`
		);
		sanitizedTask.statusHistory = [];
	}

	return sanitizedTask;
};

/**
 * Intercepts response data to ensure all task objects have proper array fields
 * This prevents "findIndex is not a function" errors in the frontend
 */
export const sanitizeTaskResponse = (
	req: Request,
	res: Response,
	next: NextFunction
): void => {
	// Store the original res.json method
	const originalJson = res.json;

	// Create request info object for logging
	const requestInfo = {
		path: req.path,
		taskId: req.params.id || 'unknown',
	};

	// Override res.json to sanitize the response data
	res.json = function (data: any): Response {
		try {
			// If the response is an array of tasks
			if (Array.isArray(data)) {
				data = data.map((item) => sanitizeTaskObject(item, requestInfo));
			}
			// If the response is a single task
			else if (
				data &&
				typeof data === 'object' &&
				(data.description || data.assignedEmployees)
			) {
				data = sanitizeTaskObject(data, requestInfo);
			}
		} catch (error) {
			logger.error('Error in task response sanitizer middleware:', error);
			// Continue with the original data if sanitization fails
		}

		// Call the original json method with the sanitized data
		return originalJson.call(this, data);
	};

	next();
};
