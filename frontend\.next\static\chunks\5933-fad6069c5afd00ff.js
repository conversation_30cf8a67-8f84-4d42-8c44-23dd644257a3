"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5933],{16051:(e,r,t)=>{t.d(r,{FH:()=>f,CZ:()=>h,O5:()=>o});var a=function(e){return e.NETWORK_ERROR="network_error",e.TIMEOUT="timeout",e.SERVER_ERROR="server_error",e.RATE_LIMIT="rate_limit",e.CLIENT_ERROR="client_error",e.VALIDATION_ERROR="validation_error",e.AUTHENTICATION_ERROR="authentication_error",e.AUTHORIZATION_ERROR="authorization_error",e.NOT_FOUND="not_found",e.PARSING_ERROR="parsing_error",e.UNKNOWN="unknown",e}({});class s extends Error{static create(e,r,t){return new s(e,{status:r,validationErrors:null==t?void 0:t.validationErrors,receivedData:null==t?void 0:t.receivedData,details:null==t?void 0:t.details,errorType:null==t?void 0:t.errorType})}determineErrorType(){return this.status>=500?"server_error":429===this.status?"rate_limit":401===this.status?"authentication_error":403===this.status?"authorization_error":400===this.status&&this.isValidationError()?"validation_error":this.status>=400&&this.status<500?"client_error":"unknown"}isRetryable(){return"server_error"===this.errorType||"network_error"===this.errorType||"timeout"===this.errorType||"rate_limit"===this.errorType}isValidationError(){return 400===this.status&&Array.isArray(this.validationErrors)&&this.validationErrors.length>0}getFormattedMessage(){if(this.isValidationError()){let e=this.validationErrors.map(e=>"".concat(e.path,": ").concat(e.message)).join("; ");return"Validation failed: ".concat(e)}switch(this.errorType){case"network_error":return"Network error: Unable to connect to the server. Please check your internet connection.";case"timeout":return"Request timed out. The server is taking too long to respond.";case"rate_limit":return"Too many requests. Please try again later.";case"authentication_error":return"Authentication required. Please log in and try again.";case"authorization_error":return"You do not have permission to perform this action.";case"not_found":return"Resource not found: ".concat(this.endpoint||"The requested resource"," could not be found.");case"parsing_error":return"Could not parse the server response. Please try again or contact support.";case"server_error":return"Server error (".concat(this.status,"): ").concat(this.message,". Please try again later.");default:return this.message}}getTechnicalDetails(){let e=["Status: ".concat(this.status),"Type: ".concat(this.errorType),"Message: ".concat(this.message)];return this.details&&e.push("Details: ".concat(JSON.stringify(this.details))),this.validationErrors&&e.push("Validation Errors: ".concat(JSON.stringify(this.validationErrors))),e.join("\n")}constructor(e,r){super(e),this.name="ApiError",this.status=r.status,this.endpoint=r.endpoint,this.validationErrors=r.validationErrors,this.receivedData=r.receivedData,this.details=r.details,this.errorType=r.errorType||this.determineErrorType(),this.retryable=this.isRetryable(),Object.setPrototypeOf(this,s.prototype)}}let n=null;function o(e){n=e}"localhost"!==window.location.hostname&&window.location.hostname;let i=e=>new Promise(r=>setTimeout(r,e)),l=(e,r)=>Math.min(r*Math.pow(2,e),3e4);async function u(e){let r,t;if(204===e.status)return null;if(e.ok)try{return await e.json()}catch(r){throw console.error("Failed to parse successful API response:",{status:e.status,statusText:e.statusText,url:e.url,error:r instanceof Error?r.message:"Unknown error"}),new s("Failed to parse successful response: ".concat(r instanceof Error?r.message:"Unknown error"),{status:e.status,endpoint:e.url,errorType:a.PARSING_ERROR})}let n=function(e){if(e>=500)return a.SERVER_ERROR;if(429===e)return a.RATE_LIMIT;if(401===e)return a.AUTHENTICATION_ERROR;if(403===e)return a.AUTHORIZATION_ERROR;if(404===e)return a.NOT_FOUND;else if(e>=400&&e<500)return a.CLIENT_ERROR;else return a.UNKNOWN}(e.status),o="HTTP error ".concat(e.status);try{if(o=(null==(r=await e.json())?void 0:r.message)||o,t=null==r?void 0:r.details,400===e.status&&(null==r?void 0:r.status)==="error"&&(null==r?void 0:r.message)==="Validation failed"){n=a.VALIDATION_ERROR;let t=r.errors;throw console.error("API validation errors:",{endpoint:e.url,status:e.status,errors:t,receivedData:r.receivedData}),new s(r.message,{status:e.status,endpoint:e.url,errorType:n,validationErrors:t,receivedData:r.receivedData})}}catch(r){try{if(r instanceof s)throw r;{let r=await e.text();r&&(o=r)}}catch(r){if(r instanceof s)throw r;o=e.statusText||o}}throw console.error("API error (".concat(e.status,"):"),{endpoint:e.url,status:e.status,message:o,details:t,errorType:n}),new s(o,{status:e.status,endpoint:e.url,details:t,errorType:n})}async function c(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=function(){let e={"Content-Type":"application/json"};return n&&(e.Authorization="Bearer ".concat(n)),{headers:e,retries:3,retryDelay:1e3,timeout:1e4}}(),o={...t,...r,headers:{...t.headers,...r.headers}},{retries:c=3,retryDelay:d=1e3,timeout:h=1e4,skipRetryLogging:f=!1}=o,g=null;for(let r=0;r<=c;r++)try{let r=new AbortController,t=setTimeout(()=>{r.abort()},h),a={...o,signal:r.signal},s=e.startsWith("http")?e:"".concat("http://localhost:3001/api").concat(e),n=await fetch(s,a);return clearTimeout(t),await u(n)}catch(t){if(g=t,function(e){if(e instanceof s){if(e.status>=400&&e.status<500)return 429===e.status?a.RATE_LIMIT:a.CLIENT_ERROR;if(e.status>=500)return a.SERVER_ERROR}return e instanceof TypeError&&e.message.includes("network")?a.NETWORK_ERROR:e instanceof DOMException&&"AbortError"===e.name?a.TIMEOUT:a.UNKNOWN}(t)===a.CLIENT_ERROR||r===c)throw t;let e=l(r,d);f||console.warn("API request failed (attempt ".concat(r+1,"/").concat(c+1,"), retrying in ").concat(e/1e3,"s:"),t),await i(e)}throw g||Error("Failed to fetch after retries")}async function d(e,r,t){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s={...a,method:e};return"GET"!==e&&t&&(s.body=JSON.stringify(t)),c(r,s)}function h(e,r){if(e&&"object"==typeof e){if("data"in e&&"status"in e)return e.data;Array.isArray(e)||"status"in e||"message"in e||!("errors"in e)}return e}let f={get:(e,r)=>d("GET",e,void 0,r),post:(e,r,t)=>d("POST",e,r,t),put:(e,r,t)=>d("PUT",e,r,t),patch:(e,r,t)=>d("PATCH",e,r,t),delete:(e,r)=>d("DELETE",e,void 0,r)}},40283:(e,r,t)=>{t.d(r,{OJ:()=>u,Z2:()=>c});var a=t(95155),s=t(12115),n=t(86829),o=t(16051),i=t(52582);let l=(0,s.createContext)(void 0);function u(e){var r;let{children:t}=e,u=(0,n.A)();return(0,s.useEffect)(()=>{var e;let r=(null==(e=u.session)?void 0:e.access_token)||null;(0,o.O5)(r),(0,i.O5)(r)},[null==(r=u.session)?void 0:r.access_token]),(0,a.jsx)(l.Provider,{value:u,children:t})}function c(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e}},55365:(e,r,t)=>{t.d(r,{Fc:()=>l,TN:()=>c,XL:()=>u});var a=t(95155),s=t(12115),n=t(74466),o=t(59434);let i=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=s.forwardRef((e,r)=>{let{className:t,variant:s,...n}=e;return(0,a.jsx)("div",{ref:r,role:"alert",className:(0,o.cn)(i({variant:s}),t),...n})});l.displayName="Alert";let u=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h5",{ref:r,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",t),...s})});u.displayName="AlertTitle";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",t),...s})});c.displayName="AlertDescription"},66695:(e,r,t)=>{t.d(r,{BT:()=>u,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i,wL:()=>d});var a=t(95155),s=t(12115),n=t(59434);let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});o.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...s})});i.displayName="CardHeader";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});l.displayName="CardTitle";let u=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...s})});u.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...s})});c.displayName="CardContent";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...s})});d.displayName="CardFooter"},86829:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(12115),s=t(10851);let n="https://abylqjnpaegeqwktcukn.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o";if(!n||!o)throw Error("Missing Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env.local file");let i=(0,s.UU)(n,o);function l(){let[e,r]=(0,a.useState)({user:null,session:null,loading:!0,error:null}),t=(0,a.useCallback)(()=>{r(e=>({...e,error:null}))},[]),s=(0,a.useCallback)(async(e,t)=>{r(e=>({...e,loading:!0,error:null}));try{let{data:a,error:s}=await i.auth.signInWithPassword({email:e,password:t});if(s)return r(e=>({...e,loading:!1,error:s.message})),{error:s};return r(e=>({...e,loading:!1})),{error:null}}catch(t){let e=t.message||"An unexpected error occurred during sign in";return r(r=>({...r,loading:!1,error:e})),{error:{message:e}}}},[]),n=(0,a.useCallback)(async(e,t,a)=>{r(e=>({...e,loading:!0,error:null}));try{let{data:s,error:n}=await i.auth.signUp({email:e,password:t,options:{data:a||{}}});if(n)return r(e=>({...e,loading:!1,error:n.message})),{error:n};return r(e=>({...e,loading:!1})),{error:null}}catch(t){let e=t.message||"An unexpected error occurred during sign up";return r(r=>({...r,loading:!1,error:e})),{error:{message:e}}}},[]),o=(0,a.useCallback)(async()=>{r(e=>({...e,loading:!0,error:null}));try{let{error:e}=await i.auth.signOut();if(e)return r(r=>({...r,loading:!1,error:e.message})),{error:e};return{error:null}}catch(t){let e=t.message||"An unexpected error occurred during sign out";return r(r=>({...r,loading:!1,error:e})),{error:{message:e}}}},[]),l=(0,a.useCallback)(async e=>{r(e=>({...e,loading:!0,error:null}));try{let{error:t}=await i.auth.resetPasswordForEmail(e,{redirectTo:"".concat(window.location.origin,"/reset-password")});if(t)return r(e=>({...e,loading:!1,error:t.message})),{error:t};return r(e=>({...e,loading:!1})),{error:null}}catch(t){let e=t.message||"An unexpected error occurred during password reset";return r(r=>({...r,loading:!1,error:e})),{error:{message:e}}}},[]);return(0,a.useEffect)(()=>{let e=!0;(async()=>{try{let{data:{session:t},error:a}=await i.auth.getSession();e&&(a?(console.error("Error getting initial session:",a),r(e=>({...e,loading:!1,error:a.message}))):r(e=>{var r;return{...e,session:t,user:null!=(r=null==t?void 0:t.user)?r:null,loading:!1}}))}catch(t){e&&(console.error("Error in getInitialSession:",t),r(e=>({...e,loading:!1,error:t.message||"Failed to initialize authentication"})))}})();let{data:{subscription:t}}=i.auth.onAuthStateChange(async(t,a)=>{if(e){var s;console.log("\uD83D\uDD10 Auth state changed:",t,null==a||null==(s=a.user)?void 0:s.email),r(e=>{var r;return{...e,session:a,user:null!=(r=null==a?void 0:a.user)?r:null,loading:!1}}),a&&"SIGNED_IN"===t&&r(e=>({...e,error:null}))}});return()=>{e=!1,null==t||t.unsubscribe()}},[]),{...e,signIn:s,signUp:n,signOut:o,resetPassword:l,clearError:t}}}}]);