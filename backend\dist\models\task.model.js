import prisma from './index.js';
import { PrismaClientKnownRequestError } from '../generated/prisma/runtime/library.js';
// The 'data' parameter (Prisma.TaskCreateInput) is prepared by the controller.
// It should correctly structure relational links and enum types.
export const createTask = async (data) => {
    try {
        // The controller (processTaskData) is responsible for formatting `data` correctly,
        // including `statusHistory`, `status`, and `priority` with correct enum types.
        return await prisma.task.create({
            data, // Directly use the data prepared by the controller
            include: {
                assignedEmployees: true,
                vehicle: true,
                subTasks: true,
                statusHistory: { orderBy: { changedAt: 'desc' } },
            },
        });
    }
    catch (error) {
        console.error('Error creating task:', error);
        if (error instanceof PrismaClientKnownRequestError && error.code === 'P2003') {
            const fieldName = error.meta?.field_name;
            if (fieldName?.toLowerCase().includes('vehicleid')) {
                throw new Error(`Vehicle not found for the provided vehicleId.`);
            }
            // Updated to check for the actual foreign key constraint names if possible, or a more generic check
            if (fieldName?.toLowerCase().includes('employeeid') || fieldName?.toLowerCase().includes('task')) { // General check
                throw new Error(`One or more assigned employees not found.`);
            }
        }
        return null;
    }
};
export const getAllTasks = async () => {
    try {
        return await prisma.task.findMany({
            include: {
                assignedEmployees: true,
                vehicle: true,
                subTasks: true,
                statusHistory: { orderBy: { changedAt: 'desc' } },
            },
            orderBy: { dateTime: 'desc' },
        });
    }
    catch (error) {
        console.error('Error fetching all tasks:', error);
        return [];
    }
};
export const getTaskById = async (id) => {
    try {
        return await prisma.task.findUnique({
            where: { id },
            include: {
                assignedEmployees: true,
                vehicle: true,
                subTasks: true,
                statusHistory: { orderBy: { changedAt: 'desc' } },
            },
        });
    }
    catch (error) {
        console.error(`Error fetching task with ID ${id}:`, error);
        return null;
    }
};
export const updateTask = async (id, data, statusChangeReason) => {
    try {
        let updatedTask = null;
        const taskDataForUpdate = { ...data };
        // Controller should ensure correct enum types. These casts are defensive.
        if (typeof data.status === 'string') {
            taskDataForUpdate.status = data.status;
        }
        if (typeof data.priority === 'string') {
            taskDataForUpdate.priority = data.priority;
        }
        await prisma.$transaction(async (tx) => {
            if (taskDataForUpdate.status) { // Check if status is part of the update
                const currentTask = await tx.task.findUnique({ where: { id }, select: { status: true } });
                const newStatusEnumValue = taskDataForUpdate.status; // Already cast or should be from controller
                if (currentTask && currentTask.status !== newStatusEnumValue) {
                    const newStatusEntry = {
                        status: newStatusEnumValue,
                        reason: statusChangeReason || "Status updated",
                    };
                    // Always create a new status history entry, do not try to merge with existing pending creates
                    taskDataForUpdate.statusHistory = {
                        create: [newStatusEntry]
                    };
                }
            }
            updatedTask = await tx.task.update({
                where: { id },
                data: taskDataForUpdate,
                include: {
                    assignedEmployees: true,
                    vehicle: true,
                    subTasks: true,
                    statusHistory: { orderBy: { changedAt: 'desc' } }
                }
            });
        });
        return updatedTask;
    }
    catch (error) {
        console.error(`Error updating task with ID ${id}:`, error);
        if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
            return null;
        }
        if (error instanceof PrismaClientKnownRequestError && error.code === 'P2003') {
            const fieldName = error.meta?.field_name;
            if (fieldName?.toLowerCase().includes('vehicleid')) {
                throw new Error(`Vehicle not found for update.`);
            }
            if (fieldName?.toLowerCase().includes('employeeid') || fieldName?.toLowerCase().includes('task')) {
                throw new Error(`One or more assigned employees not found for update.`);
            }
        }
        return null;
    }
};
export const deleteTask = async (id) => {
    try {
        return await prisma.$transaction(async (tx) => {
            const taskToDelete = await tx.task.findUnique({ where: { id } });
            if (!taskToDelete)
                return null;
            await tx.subTask.deleteMany({ where: { taskId: id } });
            await tx.taskStatusEntry.deleteMany({ where: { taskId: id } });
            // Note: Dissociating employees is handled by Prisma schema if relations are set up correctly (e.g. onDelete: SetNull or Cascade)
            // or might need explicit handling if it's a many-to-many relation table.
            // Current schema implies Task.assignedEmployees is a list of relations, Prisma handles the join table.
            await tx.task.delete({ where: { id } });
            return taskToDelete;
        });
    }
    catch (error) {
        console.error(`Error deleting task with ID ${id}:`, error);
        if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
            return null;
        }
        return null;
    }
};
//# sourceMappingURL=task.model.js.map