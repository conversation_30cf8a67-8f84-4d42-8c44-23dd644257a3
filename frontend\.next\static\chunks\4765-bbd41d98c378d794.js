"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4765],{6560:(e,a,s)=>{s.d(a,{r:()=>i});var t=s(95155),r=s(12115),l=s(30285),d=s(50172),n=s(59434);let i=r.forwardRef((e,a)=>{let{actionType:s="primary",icon:r,isLoading:i=!1,loadingText:c,className:o,children:m,disabled:f,asChild:x=!1,...u}=e,{variant:p,className:N}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[s];return(0,t.jsx)(l.$,{ref:a,variant:p,className:(0,n.cn)(N,o),disabled:i||f,asChild:x,...u,children:i?(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),c||m]}):(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",r&&(0,t.jsx)("span",{className:"mr-2",children:r}),m]})})});i.displayName="ActionButton"},26126:(e,a,s)=>{s.d(a,{E:()=>n});var t=s(95155);s(12115);var r=s(74466),l=s(59434);let d=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:a,variant:s,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)(d({variant:s}),a),...r})}},55365:(e,a,s)=>{s.d(a,{Fc:()=>i,TN:()=>o,XL:()=>c});var t=s(95155),r=s(12115),l=s(74466),d=s(59434);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),i=r.forwardRef((e,a)=>{let{className:s,variant:r,...l}=e;return(0,t.jsx)("div",{ref:a,role:"alert",className:(0,d.cn)(n({variant:r}),s),...l})});i.displayName="Alert";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("h5",{ref:a,className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",s),...r})});c.displayName="AlertTitle";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",s),...r})});o.displayName="AlertDescription"},66695:(e,a,s)=>{s.d(a,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>d,aR:()=>n,wL:()=>m});var t=s(95155),r=s(12115),l=s(59434);let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});d.displayName="Card";let n=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});n.displayName="CardHeader";let i=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});i.displayName="CardTitle";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("p-6 pt-0",s),...r})});o.displayName="CardContent";let m=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})});m.displayName="CardFooter"},68856:(e,a,s)=>{s.d(a,{E:()=>l});var t=s(95155),r=s(59434);function l(e){let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",a),...s})}},77023:(e,a,s)=>{s.d(a,{gO:()=>p,jt:()=>x});var t=s(95155);s(12115);var r=s(50172),l=s(11133),d=s(59434),n=s(68856),i=s(55365),c=s(6560);let o={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},m={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function f(e){let{size:a="md",className:s,text:l,fullPage:n=!1}=e;return(0,t.jsx)("div",{className:(0,d.cn)("flex items-center justify-center",n&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(r.A,{className:(0,d.cn)("animate-spin text-primary",o[a])}),l&&(0,t.jsx)("span",{className:(0,d.cn)("mt-2 text-muted-foreground",m[a]),children:l})]})})}function x(e){let{variant:a="default",count:s=1,className:r,testId:l="loading-skeleton"}=e;return"card"===a?(0,t.jsx)("div",{className:(0,d.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",r),"data-testid":l,children:Array(s).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,t.jsx)(n.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(n.E,{className:"h-7 w-3/4 mb-1"}),(0,t.jsx)(n.E,{className:"h-4 w-1/2 mb-3"}),(0,t.jsx)(n.E,{className:"h-px w-full my-3"}),(0,t.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,t.jsx)(n.E,{className:"h-5 w-2/3"})]},a))})]})]},a))}):"table"===a?(0,t.jsxs)("div",{className:(0,d.cn)("space-y-3",r),"data-testid":l,children:[(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,a)=>(0,t.jsx)(n.E,{className:"h-8 flex-1"},a))}),Array(s).fill(0).map((e,a)=>(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,a)=>(0,t.jsx)(n.E,{className:"h-6 flex-1"},a))},a))]}):"list"===a?(0,t.jsx)("div",{className:(0,d.cn)("space-y-3",r),"data-testid":l,children:Array(s).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(n.E,{className:"h-12 w-12 rounded-full"}),(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsx)(n.E,{className:"h-4 w-1/3"}),(0,t.jsx)(n.E,{className:"h-4 w-full"})]})]},a))}):"stats"===a?(0,t.jsx)("div",{className:(0,d.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",r),"data-testid":l,children:Array(s).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(n.E,{className:"h-5 w-1/3"}),(0,t.jsx)(n.E,{className:"h-5 w-5 rounded-full"})]}),(0,t.jsx)(n.E,{className:"h-8 w-1/2 mt-3"}),(0,t.jsx)(n.E,{className:"h-4 w-2/3 mt-2"})]},a))}):(0,t.jsx)("div",{className:(0,d.cn)("space-y-2",r),"data-testid":l,children:Array(s).fill(0).map((e,a)=>(0,t.jsx)(n.E,{className:"w-full h-5"},a))})}function u(e){let{message:a,onRetry:s,className:n}=e;return(0,t.jsxs)(i.Fc,{variant:"destructive",className:(0,d.cn)("my-4",n),children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)(i.XL,{children:"Error"}),(0,t.jsx)(i.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:a}),s&&(0,t.jsx)(c.r,{actionType:"tertiary",size:"sm",onClick:s,icon:(0,t.jsx)(r.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function p(e){let{isLoading:a,error:s,data:r,onRetry:l,children:n,loadingComponent:i,errorComponent:c,emptyComponent:o,className:m}=e;return a?i||(0,t.jsx)(f,{className:m,text:"Loading..."}):s?c||(0,t.jsx)(u,{message:s,onRetry:l,className:m}):!r||Array.isArray(r)&&0===r.length?o||(0,t.jsx)("div",{className:(0,d.cn)("text-center py-8",m),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:m,children:n(r)})}},87481:(e,a,s)=>{s.d(a,{dj:()=>f});var t=s(12115);let r=0,l=new Map,d=e=>{if(l.has(e))return;let a=setTimeout(()=>{l.delete(e),o({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,a)},n=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=a;return s?d(s):e.toasts.forEach(e=>{d(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},i=[],c={toasts:[]};function o(e){c=n(c,e),i.forEach(e=>{e(c)})}function m(e){let{...a}=e,s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),t=()=>o({type:"DISMISS_TOAST",toastId:s});return o({type:"ADD_TOAST",toast:{...a,id:s,open:!0,onOpenChange:e=>{e||t()}}}),{id:s,dismiss:t,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function f(){let[e,a]=t.useState(c);return t.useEffect(()=>(i.push(a),()=>{let e=i.indexOf(a);e>-1&&i.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>o({type:"DISMISS_TOAST",toastId:e})}}},90010:(e,a,s)=>{s.d(a,{$v:()=>N,EO:()=>f,Lt:()=>i,Rx:()=>g,Zr:()=>h,ck:()=>u,r7:()=>p,tv:()=>c,wd:()=>x});var t=s(95155),r=s(12115),l=s(17649),d=s(59434),n=s(30285);let i=l.bL,c=l.l9,o=l.ZL,m=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.hJ,{className:(0,d.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r,ref:a})});m.displayName=l.hJ.displayName;let f=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsxs)(o,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(l.UC,{ref:a,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...r})]})});f.displayName=l.UC.displayName;let x=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...s})};x.displayName="AlertDialogHeader";let u=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s})};u.displayName="AlertDialogFooter";let p=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.hE,{ref:a,className:(0,d.cn)("text-lg font-semibold",s),...r})});p.displayName=l.hE.displayName;let N=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.VY,{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",s),...r})});N.displayName=l.VY.displayName;let g=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.rc,{ref:a,className:(0,d.cn)((0,n.r)(),s),...r})});g.displayName=l.rc.displayName;let h=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.ZD,{ref:a,className:(0,d.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",s),...r})});h.displayName=l.ZD.displayName},95647:(e,a,s)=>{s.d(a,{z:()=>r});var t=s(95155);function r(e){let{title:a,description:s,icon:r,children:l}=e;return(0,t.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,t.jsx)(r,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:a})]}),s&&(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:s})]}),l&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:l})]})}s(12115)}}]);