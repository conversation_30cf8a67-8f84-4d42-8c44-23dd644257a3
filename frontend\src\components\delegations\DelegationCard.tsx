'use client';

import Image from 'next/image';
import Link from 'next/link';
import {
	<PERSON>,
	<PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	CardHeader,
	CardTitle,
	CardDescription,
} from '@/components/ui/card';
import {ActionButton} from '@/components/ui/action-button';
import type {Delegation} from '@/lib/types';
import {
	ArrowRight,
	Users,
	MapPin,
	CalendarDays,
	Plane,
	Briefcase,
	Info,
} from 'lucide-react';
import {Badge} from '@/components/ui/badge';
import {Separator} from '@/components/ui/separator';
import {cn} from '@/lib/utils';
import {format} from 'date-fns';
import {formatDelegationStatusForDisplay} from '@/lib/utils/formattingUtils';

interface DelegationCardProps {
	delegation: Delegation;
}

const getStatusColor = (status: Delegation['status']) => {
	switch (status) {
		case 'Planned':
			return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';
		case 'Confirmed':
			return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
		case 'In_Progress':
			return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
		case 'Completed':
			return 'bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20';
		case 'Cancelled':
			return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
		case 'No_details':
		default:
			return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
	}
};

export default function DelegationCard({delegation}: DelegationCardProps) {
	const formatDate = (dateString: string) =>
		format(new Date(dateString), 'MMM d, yyyy');

	return (
		<Card className='overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60'>
			<CardHeader className='p-0 relative'>
				<div className='aspect-[16/10] w-full relative'>
					<Image
						src={
							delegation.imageUrl ||
							`https://picsum.photos/seed/${delegation.id}/400/250`
						}
						alt={delegation.eventName}
						layout='fill'
						objectFit='cover'
						className='bg-muted'
						data-ai-hint='conference meeting'
						priority={true}
					/>
				</div>
				<Badge
					className={cn(
						'absolute top-2 right-2 text-xs py-1 px-2 font-semibold',
						getStatusColor(delegation.status)
					)}>
					{formatDelegationStatusForDisplay(delegation.status)}
				</Badge>
			</CardHeader>
			<CardContent className='p-5 flex-grow flex flex-col'>
				<CardTitle className='text-xl font-semibold mb-1 text-primary'>
					{delegation.eventName}
				</CardTitle>
				<CardDescription className='text-sm text-muted-foreground mb-3 flex items-center'>
					<MapPin className='h-4 w-4 mr-1.5 flex-shrink-0 text-accent' />
					{delegation.location}
				</CardDescription>

				<Separator className='my-3 bg-border/50' />

				<div className='space-y-2.5 text-sm text-foreground flex-grow'>
					<div className='flex items-center'>
						<CalendarDays className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
						<div>
							<span className='text-muted-foreground'>Duration: </span>
							<strong className='font-semibold'>
								{formatDate(delegation.durationFrom)} -{' '}
								{formatDate(delegation.durationTo)}
							</strong>
						</div>
					</div>
					<div className='flex items-center'>
						<Users className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
						<div>
							<span className='text-muted-foreground'>Delegates: </span>
							<strong className='font-semibold'>
								{delegation.delegates.length}
							</strong>
						</div>
					</div>
					{(delegation.flightArrivalDetails ||
						delegation.flightDepartureDetails) && (
						<div className='flex items-center'>
							<Plane className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
							<span className='font-semibold text-muted-foreground'>
								Flight details logged
							</span>
						</div>
					)}
				</div>

				{delegation.notes && (
					<p
						className='mt-3 text-xs text-muted-foreground line-clamp-2 pt-2 border-t border-dashed border-border/50'
						title={delegation.notes}>
						<Info size={12} className='inline mr-1 text-accent' />
						{delegation.notes}
					</p>
				)}
			</CardContent>
			<CardFooter className='p-4 border-t border-border/60 bg-muted/20'>
				<ActionButton
					actionType='tertiary'
					className='w-full'
					icon={<ArrowRight className='h-4 w-4' />}
					asChild>
					<Link href={`/delegations/${delegation.id}`}>View Details</Link>
				</ActionButton>
			</CardFooter>
		</Card>
	);
}
