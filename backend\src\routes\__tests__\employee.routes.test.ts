import request from 'supertest';
import app from '../../app.js'; // The Express app
import prisma from '../../utils/prisma.js'; // Prisma client
import type {Employee, Vehicle} from '../../generated/prisma/index.js';

// Ensure Prisma client disconnects after all tests are done
afterAll(async () => {
	await prisma.$disconnect();
});

// Clean up relevant tables before each test run in this suite
beforeEach(async () => {
	// Delete records in a way that respects foreign key constraints or model delete logic
	// Assuming Task and ServiceRecord might be related to Employee and need to be cleared if not cascaded
	// Employee model's delete logic handles status entries and task disassociation.
	await prisma.serviceRecord.deleteMany({});
	await prisma.task.deleteMany({});
	await prisma.employeeStatusEntry.deleteMany({});
	await prisma.employee.deleteMany({});
	await prisma.vehicle.deleteMany({}); // Clean vehicles if drivers are assigned to them
});

describe('Employee API Routes', () => {
	let createdEmployeeId: number;
	let createdVehicleId: number;

	const newEmployeeData = {
		name: 'Test Employee',
		role: 'mechanic',
		employeeId: `EMP-${Date.now()}`, // Business key, ensure unique
		contactInfo: '<EMAIL>',
		position: 'Senior Mechanic',
		department: 'Maintenance',
		hireDate: new Date().toISOString(),
		status: 'Active',
		skills: ['Engine Repair', 'Diagnostics'],
	};

	const newDriverData = {
		name: 'Test Driver',
		role: 'driver',
		employeeId: `DRV-${Date.now()}`,
		contactInfo: '<EMAIL>',
		position: 'Delivery Driver',
		department: 'Logistics',
		hireDate: new Date().toISOString(),
		status: 'Active',
		availability: 'On Shift',
		currentLocation: 'Main Garage',
		workingHours: 'Mon-Fri 9am-4pm',
		assignedVehicleId: null as number | null, // Will be set after vehicle creation
	};

	it('should create a new employee (mechanic) via POST /api/employees', async () => {
		const response = await request(app)
			.post('/api/employees')
			.send(newEmployeeData);

		expect(response.statusCode).toBe(201);
		expect(response.body).toHaveProperty('id');
		expect(response.body.name).toBe(newEmployeeData.name);
		expect(response.body.employeeId).toBe(newEmployeeData.employeeId);
		expect(response.body.role).toBe('mechanic');
		createdEmployeeId = response.body.id; // Save for later tests
	});

	it('should create a new employee (driver) and assign a vehicle via POST /api/employees', async () => {
		// First, create a vehicle to assign
		const vehicleRes = await request(app)
			.post('/api/vehicles')
			.send({
				make: 'TestTruck',
				model: 'Runner',
				year: 2022,
				vin: `TRUCKDRIVERVIN${Date.now()}`,
				licensePlate: 'DRVTRK',
				ownerName: 'Company',
				ownerContact: '<EMAIL>',
			});
		expect(vehicleRes.statusCode).toBe(201);
		createdVehicleId = vehicleRes.body.id;

		const driverDataWithVehicle = {
			...newDriverData,
			assignedVehicleId: createdVehicleId,
		};
		const response = await request(app)
			.post('/api/employees')
			.send(driverDataWithVehicle);

		expect(response.statusCode).toBe(201);
		expect(response.body).toHaveProperty('id');
		expect(response.body.name).toBe(driverDataWithVehicle.name);
		expect(response.body.employeeId).toBe(driverDataWithVehicle.employeeId);
		expect(response.body.role).toBe('driver');
		expect(response.body.assignedVehicleId).toBe(createdVehicleId);
		expect(response.body.assignedVehicle).toBeDefined();
		expect(response.body.assignedVehicle.make).toBe('TestTruck');
	});

	it('should not create an employee with a duplicate Employee ID (business key)', async () => {
		// Attempt to create again with the same employeeId (business key)
		const response = await request(app)
			.post('/api/employees')
			.send(newEmployeeData);

		expect(response.statusCode).toBe(409); // Conflict
		expect(response.body.message).toContain('already exists');
	});

	it('should get all employees via GET /api/employees', async () => {
		// Ensure at least one employee exists (created in previous test)
		const response = await request(app).get('/api/employees');
		expect(response.statusCode).toBe(200);
		expect(Array.isArray(response.body)).toBe(true);
		expect(response.body.length).toBeGreaterThanOrEqual(1);
		expect(
			response.body.some((e: Employee) => e.id === createdEmployeeId)
		).toBe(true);
	});

	it('should get a specific employee by ID via GET /api/employees/:id', async () => {
		const response = await request(app).get(
			`/api/employees/${createdEmployeeId}`
		);
		expect(response.statusCode).toBe(200);
		expect(response.body).toHaveProperty('id', createdEmployeeId);
		expect(response.body.employeeId).toBe(newEmployeeData.employeeId);
	});

	it('should return 404 for a non-existent employee ID', async () => {
		const response = await request(app).get('/api/employees/999999'); // Assuming 999999 does not exist
		expect(response.statusCode).toBe(404);
	});

	it('should update an employee via PUT /api/employees/:id', async () => {
		const updates = {
			name: 'Updated Test Employee',
			position: 'Lead Mechanic',
			status: 'On Leave',
			statusChangeReason: 'Vacation',
		};
		const response = await request(app)
			.put(`/api/employees/${createdEmployeeId}`)
			.send(updates);

		expect(response.statusCode).toBe(200);
		expect(response.body.name).toBe(updates.name);
		expect(response.body.position).toBe(updates.position);
		expect(response.body.status).toBe(updates.status);
		expect(response.body.statusHistory.length).toBeGreaterThanOrEqual(2); // Initial + Update
		expect(response.body.statusHistory[0].status).toBe(updates.status);
		expect(response.body.statusHistory[0].reason).toBe(
			updates.statusChangeReason
		);
	});

	it('should not update an employee to an existing Employee ID', async () => {
		const anotherEmpData = {
			...newEmployeeData,
			name: 'Another Employee',
			employeeId: `EMP-${Date.now() + 1000}`,
		};
		const anotherEmpRes = await request(app)
			.post('/api/employees')
			.send(anotherEmpData);
		expect(anotherEmpRes.statusCode).toBe(201);

		const updates = {employeeId: newEmployeeData.employeeId}; // Try to update to the first employee's ID

		const response = await request(app)
			.put(`/api/employees/${anotherEmpRes.body.id}`)
			.send(updates);

		expect(response.statusCode).toBe(409);
		expect(response.body.message).toContain('already exists');
	});

	it('should delete an employee via DELETE /api/employees/:id', async () => {
		const response = await request(app).delete(
			`/api/employees/${createdEmployeeId}`
		);
		expect(response.statusCode).toBe(200);
		expect(response.body.message).toBe('Employee deleted successfully');

		// Verify it's actually deleted
		const getResponse = await request(app).get(
			`/api/employees/${createdEmployeeId}`
		);
		expect(getResponse.statusCode).toBe(404);
	});
});
