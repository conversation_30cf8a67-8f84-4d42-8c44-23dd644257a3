'use client';

import React from 'react';
import type { EnrichedServiceRecord } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Props for the ServiceHistoryTable component
 */
interface ServiceHistoryTableProps {
  /** Array of service records to display */
  records: EnrichedServiceRecord[];
  /** Whether to show vehicle information column */
  showVehicleInfo?: boolean;
  /** Current sort field */
  sortField?: string;
  /** Current sort direction */
  sortDirection?: 'asc' | 'desc';
  /** Callback function when a column is sorted */
  onSort?: (field: string) => void;
  /** Additional CSS class names */
  className?: string;
}

/**
 * A component that displays service history records in a table with sortable columns
 * 
 * @example
 * ```tsx
 * <ServiceHistoryTable
 *   records={paginatedRecords}
 *   showVehicleInfo={true}
 *   sortField="date"
 *   sortDirection="desc"
 *   onSort={handleSort}
 * />
 * ```
 */
export function ServiceHistoryTable({
  records,
  showVehicleInfo = true,
  sortField,
  sortDirection,
  onSort,
  className,
}: ServiceHistoryTableProps) {
  // Handle sort click
  const handleSort = (field: string) => {
    if (onSort) {
      onSort(field);
    }
  };

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    
    return sortDirection === 'asc' 
      ? <ChevronUp className="inline-block h-4 w-4 ml-1" aria-hidden="true" /> 
      : <ChevronDown className="inline-block h-4 w-4 ml-1" aria-hidden="true" />;
  };

  return (
    <Table id="service-history-table" className={className}>
      <TableHeader>
        <TableRow>
          <TableHead 
            onClick={() => handleSort('date')} 
            className={cn(onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'date' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('date');
              }
            }}
            aria-label="Sort by date"
          >
            Date {renderSortIndicator('date')}
          </TableHead>
          
          {showVehicleInfo && (
            <TableHead 
              onClick={() => handleSort('vehicleMake')} 
              className={cn(onSort ? 'cursor-pointer' : '')}
              aria-sort={sortField === 'vehicleMake' ? sortDirection : undefined}
              role="columnheader"
              tabIndex={onSort ? 0 : undefined}
              onKeyDown={(e) => {
                if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                  e.preventDefault();
                  handleSort('vehicleMake');
                }
              }}
              aria-label="Sort by vehicle"
            >
              Vehicle {renderSortIndicator('vehicleMake')}
            </TableHead>
          )}
          
          <TableHead 
            onClick={() => handleSort('servicePerformed')} 
            className={cn(onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'servicePerformed' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('servicePerformed');
              }
            }}
            aria-label="Sort by services performed"
          >
            Service(s) {renderSortIndicator('servicePerformed')}
          </TableHead>
          
          <TableHead 
            onClick={() => handleSort('odometer')} 
            className={cn(onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'odometer' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('odometer');
              }
            }}
            aria-label="Sort by odometer reading"
          >
            Odometer {renderSortIndicator('odometer')}
          </TableHead>
          
          <TableHead 
            onClick={() => handleSort('cost')} 
            className={cn('text-right', onSort ? 'cursor-pointer' : '')}
            aria-sort={sortField === 'cost' ? sortDirection : undefined}
            role="columnheader"
            tabIndex={onSort ? 0 : undefined}
            onKeyDown={(e) => {
              if (onSort && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                handleSort('cost');
              }
            }}
            aria-label="Sort by cost"
          >
            Cost {renderSortIndicator('cost')}
          </TableHead>
          
          <TableHead className="print-notes-col">Notes</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {records.map((record) => (
          <TableRow key={record.id}>
            <TableCell>
              {new Date(record.date).toLocaleDateString()}
            </TableCell>
            
            {showVehicleInfo && (
              <TableCell>
                {record.vehicleMake} {record.vehicleModel} ({record.vehicleYear})
                {record.vehiclePlateNumber && (
                  <span className="block text-xs text-muted-foreground">
                    {record.vehiclePlateNumber}
                  </span>
                )}
              </TableCell>
            )}
            
            <TableCell
              className="max-w-xs truncate print-service-col"
              title={record.servicePerformed.join(', ')}
            >
              {record.servicePerformed.join(', ')}
            </TableCell>
            
            <TableCell>{record.odometer.toLocaleString()}</TableCell>
            
            <TableCell className="text-right">
              {record.cost ? `$${Number(record.cost).toFixed(2)}` : '-'}
            </TableCell>
            
            <TableCell
              className="max-w-xs truncate print-notes-col"
              title={record.notes}
            >
              {record.notes || '-'}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
