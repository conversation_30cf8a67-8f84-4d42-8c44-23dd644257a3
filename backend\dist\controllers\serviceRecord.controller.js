import * as serviceRecordModel from '../models/serviceRecord.model.js';
import { emitServiceRecordChange, SOCKET_EVENTS, } from '../services/socketService.js';
import logger from '../utils/logger.js';
import prisma from '../models/index.js';
export const createServiceRecord = async (req, res) => {
    try {
        const vehicleId = parseInt(req.params.vehicleId, 10);
        if (isNaN(vehicleId)) {
            res.status(400).json({ message: 'Invalid vehicle ID format' });
            return;
        }
        const validatedBody = req.body;
        const { employeeId: bodyEmployeeId, date, odometer, servicePerformed, notes, cost, } = validatedBody;
        // Prisma.ServiceRecordCreateInput expects 'vehicle' and 'employee' relations
        const createData = {
            date: new Date(date),
            odometer: Number(odometer),
            servicePerformed,
            notes,
            cost: cost ? Number(cost) : undefined,
            vehicle: { connect: { id: vehicleId } }, // Correct: use relation 'vehicle'
        };
        if (bodyEmployeeId) {
            // Correct: use relation 'employee'
            createData.employee = { connect: { id: Number(bodyEmployeeId) } };
        }
        const newServiceRecord = await serviceRecordModel.createServiceRecord(createData);
        if (newServiceRecord) {
            emitServiceRecordChange(SOCKET_EVENTS.SERVICE_RECORD_CREATED, newServiceRecord);
            emitServiceRecordChange(SOCKET_EVENTS.VEHICLE_UPDATED, {
                id: vehicleId,
                needsRefresh: true,
            });
            res.status(201).json(newServiceRecord);
        }
        else {
            res.status(400).json({ message: 'Could not create service record.' });
        }
    }
    catch (error) {
        if (error.message.includes('not found')) {
            res.status(404).json({ message: error.message });
        }
        else {
            res
                .status(500)
                .json({ message: 'Error creating service record', error: error.message });
        }
    }
};
export const getAllServiceRecordsForVehicle = async (req, res) => {
    try {
        const vehicleId = parseInt(req.params.vehicleId, 10);
        if (isNaN(vehicleId)) {
            res.status(400).json({ message: 'Invalid vehicle ID format' });
            return;
        }
        const records = await serviceRecordModel.getAllServiceRecords(vehicleId);
        res.status(200).json(records);
    }
    catch (error) {
        res
            .status(500)
            .json({ message: 'Error fetching service records', error: error.message });
    }
};
export const getServiceRecordById = async (req, res) => {
    try {
        const { vehicleId, id } = req.params;
        const vehicleIdNum = parseInt(vehicleId, 10);
        if (isNaN(vehicleIdNum)) {
            res.status(400).json({ message: 'Invalid vehicle ID format' });
            return;
        }
        const record = await serviceRecordModel.getServiceRecordById(id);
        if (record && record.vehicleId === vehicleIdNum) {
            res.status(200).json(record);
        }
        else {
            res.status(404).json({
                message: 'Service record not found or does not belong to this vehicle',
            });
        }
    }
    catch (error) {
        res
            .status(500)
            .json({ message: 'Error fetching service record', error: error.message });
    }
};
export const updateServiceRecord = async (req, res) => {
    try {
        const { vehicleId, id } = req.params;
        const vehicleIdNum = parseInt(vehicleId, 10);
        if (isNaN(vehicleIdNum)) {
            res.status(400).json({ message: 'Invalid vehicle ID format' });
            return;
        }
        const existingRecord = await serviceRecordModel.getServiceRecordById(id);
        if (!existingRecord || existingRecord.vehicleId !== vehicleIdNum) {
            res.status(404).json({
                message: 'Service record not found or does not belong to this vehicle for update.',
            });
            return;
        }
        const serviceRecordDataFromRequest = req.body;
        // Prisma.ServiceRecordUpdateInput for base fields
        const updateData = {
            ...serviceRecordDataFromRequest,
        };
        if (serviceRecordDataFromRequest.date) {
            updateData.date = new Date(serviceRecordDataFromRequest.date);
        }
        // Handle 'employee' relation for update
        if (serviceRecordDataFromRequest.employeeId) {
            updateData.employee = {
                connect: { id: Number(serviceRecordDataFromRequest.employeeId) },
            };
            // If `employeeId` is purely a relational FK shadow field in Prisma's eyes for `ServiceRecordUpdateInput`
            // and not meant to be set directly when `employee` relation is used, it should be removed.
            // However, if `employeeId` IS a scalar field you can set on `ServiceRecordUpdateInput`, then this delete is not needed.
            // Assuming the error "Property 'employeeId' does not exist on type 'ServiceRecordUpdateInput'" means it should be done via relation.
            if ('employeeId' in updateData)
                delete updateData.employeeId;
        }
        else if (serviceRecordDataFromRequest.hasOwnProperty('employeeId') &&
            serviceRecordDataFromRequest.employeeId === null) {
            updateData.employee = { disconnect: true };
            // If 'employeeId' scalar is not part of ServiceRecordUpdateInput for this operation, this isn't needed.
            // The error "Property 'employeeId' does not exist" suggests not to set it directly.
            // updateData.employeeId = null; // This line might be problematic if `employeeId` isn't a direct field.
            if ('employeeId' in updateData)
                delete updateData.employeeId;
        }
        const updatedRecord = await serviceRecordModel.updateServiceRecord(id, updateData);
        if (updatedRecord) {
            emitServiceRecordChange(SOCKET_EVENTS.SERVICE_RECORD_UPDATED, updatedRecord);
            emitServiceRecordChange(SOCKET_EVENTS.VEHICLE_UPDATED, {
                id: vehicleIdNum,
                needsRefresh: true,
            });
            res.status(200).json(updatedRecord);
        }
        else {
            res
                .status(404)
                .json({ message: 'Service record not found or could not be updated.' });
        }
    }
    catch (error) {
        if (error.message.includes('not found')) {
            res.status(404).json({ message: error.message });
        }
        else {
            res
                .status(500)
                .json({ message: 'Error updating service record', error: error.message });
        }
    }
};
export const deleteServiceRecord = async (req, res) => {
    try {
        const { vehicleId, id } = req.params;
        const vehicleIdNum = parseInt(vehicleId, 10);
        if (isNaN(vehicleIdNum)) {
            res.status(400).json({ message: 'Invalid vehicle ID format' });
            return;
        }
        const existingRecord = await serviceRecordModel.getServiceRecordById(id);
        if (!existingRecord || existingRecord.vehicleId !== vehicleIdNum) {
            res.status(404).json({
                message: 'Service record not found or does not belong to this vehicle for deletion.',
            });
            return;
        }
        const deletedRecord = await serviceRecordModel.deleteServiceRecord(id);
        if (deletedRecord) {
            emitServiceRecordChange(SOCKET_EVENTS.SERVICE_RECORD_DELETED, {
                id: deletedRecord.id,
                vehicleId: vehicleIdNum,
            });
            emitServiceRecordChange(SOCKET_EVENTS.VEHICLE_UPDATED, {
                id: vehicleIdNum,
                needsRefresh: true,
            });
            res.status(200).json({
                message: 'Service record deleted successfully',
                record: deletedRecord,
            });
        }
        else {
            res
                .status(404)
                .json({ message: 'Service record not found or could not be deleted' });
        }
    }
    catch (error) {
        res
            .status(500)
            .json({ message: 'Error deleting service record', error: error.message });
    }
};
/**
 * Get all service records without requiring a vehicle ID
 * This is used for the direct /api/servicerecords endpoint
 */
export const getAllServiceRecordsDirect = async (req, res) => {
    try {
        logger.info('Fetching all service records directly', {
            query: req.query,
            ip: req.ip,
            userAgent: req.headers['user-agent'],
        });
        // Get query parameters for filtering
        const { startDate, endDate, limit, offset, sortBy, sortOrder } = req.query;
        // Convert query parameters to appropriate types if needed
        const queryParams = {};
        // Apply filters if provided
        if (startDate && typeof startDate === 'string') {
            try {
                queryParams.startDate = new Date(startDate);
                // Check if date is valid
                if (isNaN(queryParams.startDate.getTime())) {
                    throw new Error('Invalid startDate format');
                }
            }
            catch (err) {
                logger.warn(`Invalid startDate parameter: ${startDate}`);
                // Don't add invalid date to query params
            }
        }
        if (endDate && typeof endDate === 'string') {
            try {
                queryParams.endDate = new Date(endDate);
                // Check if date is valid
                if (isNaN(queryParams.endDate.getTime())) {
                    throw new Error('Invalid endDate format');
                }
            }
            catch (err) {
                logger.warn(`Invalid endDate parameter: ${endDate}`);
                // Don't add invalid date to query params
            }
        }
        if (limit && typeof limit === 'string') {
            const limitNum = parseInt(limit, 10);
            if (!isNaN(limitNum) && limitNum > 0) {
                queryParams.limit = limitNum;
            }
            else {
                logger.warn(`Invalid limit parameter: ${limit}`);
            }
        }
        if (offset && typeof offset === 'string') {
            const offsetNum = parseInt(offset, 10);
            if (!isNaN(offsetNum) && offsetNum >= 0) {
                queryParams.offset = offsetNum;
            }
            else {
                logger.warn(`Invalid offset parameter: ${offset}`);
            }
        }
        if (sortBy && typeof sortBy === 'string') {
            // Validate sortBy against allowed fields
            const allowedSortFields = ['date', 'odometer', 'cost'];
            if (allowedSortFields.includes(sortBy)) {
                queryParams.sortBy = sortBy;
            }
            else {
                logger.warn(`Invalid sortBy parameter: ${sortBy}`);
            }
        }
        if (sortOrder && typeof sortOrder === 'string') {
            // Validate sortOrder
            const allowedSortOrders = ['asc', 'desc'];
            if (allowedSortOrders.includes(sortOrder.toLowerCase())) {
                queryParams.sortOrder = sortOrder.toLowerCase();
            }
            else {
                logger.warn(`Invalid sortOrder parameter: ${sortOrder}`);
            }
        }
        // Call the model function without a vehicleId
        const records = await serviceRecordModel.getAllServiceRecords();
        // Ensure records is always an array
        const safeRecords = Array.isArray(records) ? records : [];
        logger.debug('Service records retrieved successfully', {
            count: safeRecords.length,
            query: req.query,
        });
        // Always return an array, even if empty
        res.status(200).json(safeRecords);
    }
    catch (error) {
        logger.error('Error fetching all service records directly', {
            error: error.message,
            stack: error.stack,
            query: req.query,
        });
        // Return an empty array instead of an error to prevent frontend from getting stuck
        res.status(200).json([]);
    }
};
/**
 * Get a specific service record by ID without requiring a vehicle ID
 * This is used for the direct /api/servicerecords/:id endpoint
 */
export const getServiceRecordByIdDirect = async (req, res) => {
    try {
        const { id } = req.params;
        logger.info(`Fetching service record by ID directly: ${id}`);
        const record = await serviceRecordModel.getServiceRecordById(id);
        if (record) {
            res.status(200).json(record);
        }
        else {
            logger.warn(`Service record not found: ${id}`);
            res.status(404).json({
                status: 'error',
                message: 'Service record not found',
            });
        }
    }
    catch (error) {
        logger.error(`Error fetching service record by ID directly: ${req.params.id}`, {
            error: error.message,
            stack: error.stack,
        });
        res.status(500).json({
            status: 'error',
            message: 'Error fetching service record',
            error: error.message,
        });
    }
};
/**
 * Get all service records enriched with vehicle and employee information
 * This is used for the /api/servicerecords/enriched endpoint
 */
export const getEnrichedServiceRecords = async (req, res) => {
    try {
        logger.info('Fetching enriched service records', {
            query: req.query,
            ip: req.ip,
            userAgent: req.headers['user-agent'],
        });
        // Get all service records
        const serviceRecords = await serviceRecordModel.getAllServiceRecords();
        // If no service records, return empty array
        if (!serviceRecords ||
            !Array.isArray(serviceRecords) ||
            serviceRecords.length === 0) {
            logger.info('No service records found for enrichment');
            res.status(200).json([]);
            return;
        }
        // Get all vehicles and employees in a single query to avoid N+1 problem
        const vehicleIds = [
            ...new Set(serviceRecords.map((record) => record.vehicleId)),
        ];
        const employeeIds = [
            ...new Set(serviceRecords
                .filter((record) => record.employeeId)
                .map((record) => record.employeeId)),
        ];
        // Fetch vehicles and employees in parallel
        const [vehicles, employees] = await Promise.all([
            prisma.vehicle.findMany({
                where: {
                    id: {
                        in: vehicleIds,
                    },
                },
            }),
            prisma.employee.findMany({
                where: {
                    id: {
                        in: employeeIds.filter(Boolean),
                    },
                },
            }),
        ]);
        // Create lookup maps for faster access
        const vehicleMap = new Map(vehicles.map((vehicle) => [vehicle.id, vehicle]));
        const employeeMap = new Map(employees.map((employee) => [employee.id, employee]));
        // Enrich service records with vehicle and employee information
        const enrichedRecords = serviceRecords.map((record) => {
            const vehicle = vehicleMap.get(record.vehicleId);
            const employee = record.employeeId
                ? employeeMap.get(record.employeeId)
                : null;
            return {
                ...record,
                vehicleMake: vehicle?.make || 'Unknown',
                vehicleModel: vehicle?.model || 'Unknown',
                vehicleYear: vehicle?.year || 0,
                vehiclePlateNumber: vehicle?.licensePlate || 'Unknown',
                employeeName: employee?.name || null,
                employeeFullName: employee?.fullName || null,
                employeeRole: employee?.role || null,
            };
        });
        logger.debug('Enriched service records successfully', {
            count: enrichedRecords.length,
            query: req.query,
        });
        // Return the enriched records
        res.status(200).json(enrichedRecords);
    }
    catch (error) {
        logger.error('Error fetching enriched service records', {
            error: error.message,
            stack: error.stack,
            query: req.query,
        });
        // Return an empty array instead of an error to prevent frontend from getting stuck
        res.status(200).json([]);
    }
};
//# sourceMappingURL=serviceRecord.controller.js.map