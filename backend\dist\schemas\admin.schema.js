/**
 * Admin <PERSON>as
 *
 * This module defines the schemas for admin-related data structures.
 */
import { z } from 'zod';
/**
 * Log level enum
 */
export const LogLevelEnum = z.enum(['ERROR', 'WARNING', 'INFO']);
/**
 * Schema for error log query parameters
 */
export const errorLogQuerySchema = z.object({
    page: z.coerce.number().int().positive().default(1),
    limit: z.coerce.number().int().positive().max(100).default(10),
    level: LogLevelEnum.optional(),
});
/**
 * Schema for health component status
 */
export const healthComponentStatusSchema = z.object({
    status: z.string(),
    type: z.string().optional(),
    url: z.string().optional(),
    error: z.any().nullable().optional(),
});
/**
 * Schema for health response
 */
export const healthResponseSchema = z.object({
    status: z.string(),
    message: z.string(),
    components: z.object({
        database: healthComponentStatusSchema,
        supabase: healthComponentStatusSchema.optional(),
    }),
    config: z.object({
        useSupabase: z.boolean(),
        supabaseConfigured: z.boolean(),
        connectionMode: z.string().optional(),
    }),
    timestamp: z.string(),
    version: z.string(),
    uptime: z.number().optional(),
});
/**
 * Schema for cache hit rate
 */
export const cacheHitRateSchema = z.object({
    indexHitRate: z.number(),
    tableHitRate: z.number(),
});
/**
 * Schema for performance metrics
 */
export const performanceMetricsSchema = z.object({
    cacheHitRate: cacheHitRateSchema,
    connectionCount: z.number(),
    activeQueries: z.number(),
    avgQueryTime: z.number(),
    timestamp: z.string(),
});
/**
 * Schema for error log entry
 */
export const errorLogEntrySchema = z.object({
    id: z.string(),
    timestamp: z.string(),
    level: LogLevelEnum,
    message: z.string(),
    details: z.record(z.any()).optional(),
    source: z.string().optional(),
});
/**
 * Schema for pagination metadata
 */
export const paginationSchema = z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
});
/**
 * Schema for paginated response
 */
export const paginatedResponseSchema = z.object({
    data: z.array(errorLogEntrySchema),
    pagination: paginationSchema,
});
//# sourceMappingURL=admin.schema.js.map