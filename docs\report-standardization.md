# WorkHub Report Standardization

This document outlines the standardized approach for report pages in the WorkHub application, including print and download functionality.

## Report Actions Component

The `ReportActions` component provides standardized print and download functionality for all report pages. It includes:

- Print button (using `window.print()`)
- Download dropdown with PDF and CSV options
- Consistent styling and positioning

### Usage

```tsx
<ReportActions
  reportContentId="#report-content"  // ID of the element containing the report content
  tableId="#data-table"              // ID of the table element for CSV export (optional)
  fileName="report-name"             // Base name for downloaded files (without extension)
  enableCsv={true}                   // Whether to enable CSV export option
  csvData={{ headers: [], data: [] }} // Raw data for CSV export (alternative to tableId)
/>
```

### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `reportContentId` | `string` | Yes | ID or selector of the element containing the report content |
| `tableId` | `string` | No | ID or selector of the table element for CSV export |
| `fileName` | `string` | Yes | Base name for downloaded files (without extension) |
| `enableCsv` | `boolean` | No | Whether to enable CSV export option (default: false) |
| `csvData` | `{ headers: string[], data: any[][] }` | No | Raw data for CSV export (alternative to tableId) |
| `className` | `string` | No | Additional CSS class names |

## File Utility Functions

The file utility module (`src/lib/utils/fileUtils.ts`) provides functions for generating and downloading files:

### `downloadFile(blob: Blob, fileName: string): void`

Generic function to trigger a browser download for a given Blob and filename.

### `generatePdfFromHtml(elementIdOrSelector: string, fileName: string, options?: PdfOptions): Promise<void>`

Generates a PDF from an HTML element and triggers download.

#### PdfOptions

```typescript
interface PdfOptions {
  orientation?: 'portrait' | 'landscape';
  unit?: string;
  format?: string;
  scale?: number;
  applyPrintStyles?: boolean;
}
```

### `generateCsvFromData(data: any[][], headers: string[], fileName: string): void`

Generates a CSV file from data and triggers download.

### `extractTableDataForCsv(tableElementIdOrSelector: string): { headers: string[]; data: any[][] }`

Extracts data from an HTML table for CSV export.

## Implementation Guidelines

### 1. Report Content Structure

Each report page should follow this structure:

```tsx
<div className="max-w-5xl mx-auto bg-white p-2 sm:p-4">
  {/* Report Actions */}
  <div className="text-right mb-4 no-print">
    <ReportActions
      reportContentId="#report-content"
      tableId="#data-table"
      fileName="report-name"
      enableCsv={true}
    />
  </div>

  {/* Report Content */}
  <div id="report-content" className="report-content">
    <header className="text-center mb-8 pb-4 border-b-2 border-gray-300">
      <h1 className="text-3xl font-bold">Report Title</h1>
      <p className="text-md text-gray-600">Report Subtitle</p>
    </header>

    {/* Report Data */}
    <section>
      {/* Report content goes here */}
      <table id="data-table">
        {/* Table content goes here */}
      </table>
    </section>

    <footer className="mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500">
      <p>Report generated on: {new Date().toLocaleDateString()}</p>
      <p>WorkHub</p>
    </footer>
  </div>
</div>
```

### 2. Print Styling

The application includes global print styles in `src/app/globals.css` that handle:

- Hiding non-printable elements (navigation, buttons, etc.)
- Ensuring content expands to fill the page
- Adjusting font colors for printing
- Handling page breaks appropriately

### 3. Testing

All file utility functions are tested in `src/lib/utils/__tests__/fileUtils.test.ts`.

## Dependencies

The report functionality relies on the following libraries:

- `jspdf`: For PDF generation
- `html2canvas`: For converting HTML to canvas for PDF generation
- `papaparse`: For CSV generation

## Examples

### Vehicle Report

```tsx
<ReportActions
  reportContentId="#vehicle-report-content"
  tableId="#service-history-table"
  fileName={`vehicle-report-${vehicle.make}-${vehicle.model}`}
  enableCsv={vehicle.serviceHistory.length > 0}
/>
```

### Tasks Report

```tsx
<ReportActions
  reportContentId="#task-report-content"
  tableId="#tasks-table"
  fileName={`tasks-report-${new Date().toISOString().split('T')[0]}`}
  enableCsv={filteredTasks.length > 0}
/>
```
