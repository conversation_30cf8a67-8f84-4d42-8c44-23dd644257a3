/**
 * Flight API Service
 *
 * This service provides functions to interact with the flight-related API endpoints.
 */

import {fetchData} from '../apiService';

// Re-export the API_BASE_URL for direct fetch calls
// This ensures consistency with the apiService
import {API_BASE_URL} from '../apiService';

// Define the flight data interface
export interface FlightData {
	icao24: string;
	callsign: string;
	departureAirport?: string;
	arrivalAirport?: string;
	departureTime?: number;
	arrivalTime?: number;
	lastSeen?: number;
	onGround?: boolean;
	latitude?: number;
	longitude?: number;
	altitude?: number;
	velocity?: number;
	heading?: number;
}

// Enhanced response type for flight search
export interface FlightSearchResponse {
	flights?: FlightData[];
	message?: string;
	details?: {
		possibleReasons?: string[];
		apiInfo?: string;
		searchParams?: {
			callsign: string;
			date: string;
		};
	};
	error?: string;
	timestamp?: string;
}

/**
 * Search for flights by callsign and date
 *
 * @param callsign The flight callsign to search for (e.g., "SWR123")
 * @param date The date to search for flights (YYYY-MM-DD)
 * @returns Promise with flight data or enhanced error response
 */
export const searchFlightsByCallsignAndDate = async (
	callsign: string,
	date: string // Expected format: "YYYY-MM-DD"
): Promise<FlightData[]> => {
	if (!date) {
		console.error('Search date is required for historical flight search.');
		throw new Error('Search date is required');
	}

	// Check if date is in the future
	const searchDate = new Date(`${date}T00:00:00.000Z`);
	const currentDate = new Date();
	if (searchDate > currentDate) {
		console.warn(`Search for future date rejected: ${date}`);
		throw new Error(
			`OpenSky API does not provide data for future dates. The date ${date} is in the future.`
		);
	}

	const response = await fetchData<FlightSearchResponse>(
		`/flights/search?callsign=${encodeURIComponent(callsign)}&date=${date}`
	);

	// Handle the enhanced response format
	if (Array.isArray(response)) {
		return response; // Original array response
	} else if (response.flights) {
		return response.flights; // New format with flights array
	} else {
		// If we have an error message but no flights, throw an error with the details
		if (response.message) {
			const error = new Error(response.message);
			// @ts-ignore - Add details to the error object
			error.details = response.details;
			throw error;
		}
		return []; // Fallback to empty array
	}
};

/**
 * Get flights by airport (arrivals or departures)
 *
 * @param airport ICAO code of the airport (e.g., "EDDF" for Frankfurt)
 * @param begin Start time in seconds since epoch (Unix timestamp)
 * @param end End time in seconds since epoch (Unix timestamp)
 * @param type Type of flights to retrieve (arrival or departure)
 * @returns Promise with flight data
 */
export const getFlightsByAirport = async (
	airport: string,
	begin: number,
	end: number,
	type: 'arrival' | 'departure' = 'arrival'
): Promise<FlightData[]> => {
	return fetchData(
		`/flights/airport?airport=${encodeURIComponent(
			airport
		)}&begin=${begin}&end=${end}&type=${type}`
	);
};

/**
 * Get flights by time interval
 *
 * @param begin Start time in seconds since epoch (Unix timestamp)
 * @param end End time in seconds since epoch (Unix timestamp)
 * @returns Promise with flight data
 */
export const getFlightsByTimeInterval = async (
	begin: number,
	end: number
): Promise<FlightData[]> => {
	return fetchData(`/flights/interval?begin=${begin}&end=${end}`);
};
