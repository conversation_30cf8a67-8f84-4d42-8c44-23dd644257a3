import { z } from 'zod';
const DelegationStatusEnum = z.enum([
    'Planned',
    'Confirmed',
    'In_Progress',
    'Completed',
    'Cancelled',
    'No_details',
]);
const DelegateSchema = z.object({
    id: z.string().uuid().optional(),
    name: z.string().min(1, 'Delegate name is required'),
    title: z.string().min(1, 'Delegate title is required'),
    notes: z.string().optional().nullable(),
});
const FlightDetailsSchema = z.preprocess(
// First preprocess to handle the entire object being null/undefined
(val) => {
    // If the value is null or undefined, return null
    if (val === null || val === undefined) {
        return null;
    }
    // If it's an object, check if all required fields are empty
    if (typeof val === 'object') {
        const obj = val;
        const isEmpty = (!obj.flightNumber || obj.flightNumber.trim() === '') &&
            (!obj.dateTime || obj.dateTime.trim() === '') &&
            (!obj.airport || obj.airport.trim() === '');
        if (isEmpty) {
            return null;
        }
    }
    // Otherwise return the value as is
    return val;
}, z
    .object({
    id: z.string().uuid().optional(),
    flightNumber: z.string().min(1, 'Flight number is required'),
    dateTime: z
        .string()
        .refine((val) => {
        try {
            // Check if it's a valid date
            const date = new Date(val);
            return !isNaN(date.getTime());
        }
        catch (e) {
            return false;
        }
    }, {
        message: 'Invalid date/time format for flight',
    })
        .transform((val) => {
        // Ensure consistent ISO format
        try {
            return new Date(val).toISOString();
        }
        catch (e) {
            return val; // If transformation fails, return original value for validation to fail
        }
    }),
    airport: z.string().min(1, 'Airport is required'),
    terminal: z.string().optional().nullable(),
    notes: z.string().optional().nullable(),
})
    .nullable()
    .optional());
/**
 * @openapi
 * components:
 *   schemas:
 *     DelegateInput:
 *       type: object
 *       required:
 *         - name
 *         - title
 *       properties:
 *         name:
 *           type: string
 *         title:
 *           type: string
 *         notes:
 *           type: string
 *           nullable: true
 *     FlightDetailsInput:
 *       type: object
 *       required:
 *         - flightNumber
 *         - dateTime
 *         - airport
 *       properties:
 *         flightNumber:
 *           type: string
 *         dateTime:
 *           type: string
 *           format: date-time
 *         airport:
 *           type: string
 *         terminal:
 *           type: string
 *           nullable: true
 *         notes:
 *           type: string
 *           nullable: true
 *     DelegationCreateInput:
 *       type: object
 *       required:
 *         - eventName
 *         - location
 *         - durationFrom
 *         - durationTo
 *       properties:
 *         eventName:
 *           type: string
 *         location:
 *           type: string
 *         durationFrom:
 *           type: string
 *           format: date-time
 *         durationTo:
 *           type: string
 *           format: date-time
 *         invitationFrom:
 *           type: string
 *           nullable: true
 *         invitationTo:
 *           type: string
 *           nullable: true
 *         notes:
 *           type: string
 *           nullable: true
 *         imageUrl:
 *           type: string
 *           format: url
 *           nullable: true
 *         status:
 *           $ref: '#/components/schemas/DelegationStatusEnum'
 *         delegates:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/DelegateInput'
 *         flightArrivalDetails:
 *           $ref: '#/components/schemas/FlightDetailsInput'
 *           nullable: true
 *         flightDepartureDetails:
 *           $ref: '#/components/schemas/FlightDetailsInput'
 *           nullable: true
 *     DelegationUpdateInput:
 *       type: object
 *       properties:
 *         eventName:
 *           type: string
 *         location:
 *           type: string
 *         durationFrom:
 *           type: string
 *           format: date-time
 *         durationTo:
 *           type: string
 *           format: date-time
 *         invitationFrom:
 *           type: string
 *           nullable: true
 *         invitationTo:
 *           type: string
 *           nullable: true
 *         notes:
 *           type: string
 *           nullable: true
 *         imageUrl:
 *           type: string
 *           format: url
 *           nullable: true
 *         status:
 *           $ref: '#/components/schemas/DelegationStatusEnum'
 *         statusChangeReason:
 *           type: string
 *           description: Reason for status change if status is updated.
 *           nullable: true
 *         delegates:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/DelegateInput'
 *           description: To update delegates, provide the full new list. Existing delegates not in the list will be removed.
 *         flightArrivalDetails:
 *           $ref: '#/components/schemas/FlightDetailsInput'
 *           nullable: true
 *           description: To remove, pass null. To update/create, pass the object.
 *         flightDepartureDetails:
 *           $ref: '#/components/schemas/FlightDetailsInput'
 *           nullable: true
 *           description: To remove, pass null. To update/create, pass the object.
 *     DelegationStatusEnum:
 *       type: string
 *       enum: [Planned, Confirmed, In_Progress, Completed, Cancelled, No_details]
 *     Delegation:
 *       allOf:
 *         - $ref: '#/components/schemas/DelegationCreateInput'
 *         - type: object
 *           properties:
 *             id:
 *               type: string
 *               format: uuid
 *             createdAt:
 *               type: string
 *               format: date-time
 *             updatedAt:
 *               type: string
 *               format: date-time
 *             statusHistory:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/DelegationStatusEntry'
 *     DelegationStatusEntry:
 *       type: object
 *       properties:
 *         id: { type: string, format: uuid }
 *         status: { $ref: '#/components/schemas/DelegationStatusEnum' }
 *         changedAt: { type: string, format: date-time }
 *         reason: { type: string, nullable: true }
 */
const delegationBaseObjectSchema = z.object({
    eventName: z.string().min(1, 'Event name is required'),
    location: z.string().min(1, 'Location is required'),
    durationFrom: z.string().datetime({ message: 'Invalid start date format' }),
    durationTo: z.string().datetime({ message: 'Invalid end date format' }),
    invitationFrom: z.string().optional().nullable(),
    invitationTo: z.string().optional().nullable(),
    notes: z.string().optional().nullable(),
    imageUrl: z
        .string()
        .url('Invalid image URL')
        .optional()
        .nullable()
        .or(z.literal('')),
    status: DelegationStatusEnum.default('Planned'),
    delegates: z
        .array(DelegateSchema.omit({ id: true }))
        .optional()
        .default([]),
    flightArrivalDetails: FlightDetailsSchema.nullable().optional(),
    flightDepartureDetails: FlightDetailsSchema.nullable().optional(),
});
export const delegationCreateSchema = delegationBaseObjectSchema.refine((data) => new Date(data.durationFrom) <= new Date(data.durationTo), {
    message: 'End date cannot be earlier than start date',
    path: ['durationTo'],
});
export const delegationUpdateSchema = delegationBaseObjectSchema
    .partial()
    .extend({
    statusChangeReason: z.string().optional().nullable(),
})
    .refine((data) => {
    if (data.durationFrom && data.durationTo) {
        return new Date(data.durationFrom) <= new Date(data.durationTo);
    }
    return true;
}, {
    message: 'End date cannot be earlier than start date if both are provided',
    path: ['durationTo'],
});
export const delegationIdSchema = z.object({
    id: z.string().uuid('Invalid Delegation ID format (must be UUID)'),
});
//# sourceMappingURL=delegation.schema.js.map