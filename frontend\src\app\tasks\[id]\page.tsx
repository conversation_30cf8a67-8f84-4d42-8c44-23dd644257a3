'use client';

import {useEffect, useState, useCallback} from 'react';
import {usePara<PERSON>, useRouter} from 'next/navigation';
import Link from 'next/link';
import {ActionButton} from '@/components/ui/action-button';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
	CardFooter,
} from '@/components/ui/card';
import {Separator} from '@/components/ui/separator';
import {Avatar, AvatarFallback} from '@/components/ui/avatar';
import {Button} from '@/components/ui/button';
import {
	ArrowLeft,
	Edit,
	Trash2,
	ClipboardList,
	MapPin,
	Clock,
	AlertTriangle,
	User,
	Users,
	Car,
	CalendarDays,
	UserPlus,
	UserMinus,
	CheckCircle,
	XCircle,
	ExternalLink,
	Wrench,
	RefreshCw,
} from 'lucide-react';
import type {Task, Employee, Vehicle, TaskStatus} from '@/lib/types';
import {
	getTaskById,
	deleteTask as storeDeleteTask,
	getEmployees,
	getEmployeeById,
	getVehicleById,
	assignTaskToEmployee,
	unassignTaskFromEmployee,
} from '@/lib/store';
import {PageHeader} from '@/components/ui/PageHeader';
import {useToast} from '@/hooks/use-toast';
import {Badge} from '@/components/ui/badge';
import {cn} from '@/lib/utils';
import {format, parseISO} from 'date-fns';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {Label} from '@/components/ui/label';
import {
	DataLoader,
	SkeletonLoader,
	ErrorDisplay,
} from '@/components/ui/loading';

const getStatusColor = (status: Task['status']) => {
	switch (status) {
		case 'Pending':
			return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30';
		case 'Assigned':
			return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30';
		case 'In Progress':
			return 'bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800/30';
		case 'Completed':
			return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30';
		case 'Cancelled':
			return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30';
		default:
			return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30';
	}
};

const getPriorityColor = (priority: Task['priority']) => {
	switch (priority) {
		case 'Low':
			return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30';
		case 'Medium':
			return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30';
		case 'High':
			return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30';
		default:
			return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30';
	}
};

const formatDate = (dateString: string | undefined, includeTime = false) => {
	if (!dateString) return 'N/A';
	try {
		return format(
			parseISO(dateString),
			includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy'
		);
	} catch (e) {
		return 'Invalid Date';
	}
};

interface KeyValueItemProps {
	icon: React.ElementType;
	label: string;
	value?: string | number | null;
	children?: React.ReactNode;
	className?: string;
}

function KeyValueItem({
	icon: Icon,
	label,
	value,
	children,
	className,
}: KeyValueItemProps) {
	if (!value && !children) return null;
	return (
		<div className={cn('flex items-start gap-3', className)}>
			<Icon className='h-4 w-4 text-muted-foreground mt-1 shrink-0' />
			<div className='space-y-1'>
				<p className='text-sm text-muted-foreground'>{label}</p>
				{value && (
					<p className='text-base font-medium text-foreground'>{value}</p>
				)}
				{children}
			</div>
		</div>
	);
}

interface AssignedEmployeeListProps {
	assignedEmployees: Employee[];
	onUnassign?: (employeeId: number) => void;
	isTaskCompleted?: boolean;
}

function AssignedEmployeeList({
	assignedEmployees,
	onUnassign,
	isTaskCompleted = false,
}: AssignedEmployeeListProps) {
	if (assignedEmployees.length === 0) return null;

	return (
		<div className='space-y-3'>
			<div className='flex items-center gap-2'>
				<Users className='h-4 w-4 text-muted-foreground' />
				<span className='text-sm text-muted-foreground'>Assigned To</span>
			</div>
			<div className='flex flex-wrap gap-3'>
				{assignedEmployees.map((employee) => (
					<div
						key={employee.id}
						className='flex items-center gap-2 p-2 rounded-lg border bg-muted/30'>
						<Avatar className='h-8 w-8'>
							<AvatarFallback className='text-xs font-medium'>
								{employee.fullName
									.split(' ')
									.map((n) => n[0])
									.join('')
									.toUpperCase()
									.slice(0, 2)}
							</AvatarFallback>
						</Avatar>
						<div className='flex flex-col'>
							<Link
								href={`/employees/${employee.id}`}
								className='text-sm font-medium hover:text-primary'>
								{employee.fullName}
							</Link>
							<span className='text-xs text-muted-foreground capitalize'>
								{employee.role.replace('_', ' ')}
							</span>
						</div>
						{!isTaskCompleted && onUnassign && (
							<Button
								variant='ghost'
								size='sm'
								onClick={() => onUnassign(employee.id)}
								className='h-6 w-6 p-0 text-muted-foreground hover:text-destructive'>
								<UserMinus className='h-3 w-3' />
							</Button>
						)}
					</div>
				))}
			</div>
		</div>
	);
}

export default function TaskDetailPage() {
	const params = useParams();
	const router = useRouter();
	const {toast} = useToast();
	const [task, setTask] = useState<Task | null>(null);
	const [assignedEmployees, setAssignedEmployees] = useState<Employee[]>([]);
	const [assignedVehicle, setAssignedVehicle] = useState<
		Vehicle | null | undefined
	>(undefined);
	const [availableEmployees, setAvailableEmployees] = useState<Employee[]>([]);
	const [selectedAssigneeId, setSelectedAssigneeId] = useState<string>('');
	const [isLoadingData, setIsLoadingData] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const taskId = params.id as string;

	const fetchTaskData = useCallback(async () => {
		if (taskId) {
			setIsLoadingData(true);
			setError(null);
			try {
				const foundTask = await getTaskById(taskId);
				if (foundTask) {
					setTask(foundTask);

					// Fetch assigned employees
					if (foundTask.assignedTo && foundTask.assignedTo.length > 0) {
						const employees = await Promise.all(
							foundTask.assignedTo.map((empId) =>
								getEmployeeById(Number(empId))
							)
						);
						setAssignedEmployees(
							employees.filter((emp): emp is Employee => emp !== undefined)
						);
					} else {
						setAssignedEmployees([]);
					}

					if (foundTask.vehicleId) {
						const vehicle = await getVehicleById(Number(foundTask.vehicleId));
						setAssignedVehicle(vehicle);
					} else {
						setAssignedVehicle(null);
					}

					const allEmployees = await getEmployees();
					setAvailableEmployees(
						allEmployees.filter(
							(e) =>
								e.status === 'Active' &&
								!foundTask.assignedTo?.includes(String(e.id))
						)
					);
				} else {
					setError('Task not found.');
					toast({
						title: 'Error',
						description: 'Task not found.',
						variant: 'destructive',
					});
				}
			} catch (err) {
				console.error('Error fetching task data:', err);
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to load task data.';
				setError(errorMessage);
				toast({
					title: 'Error',
					description: errorMessage,
					variant: 'destructive',
				});
			} finally {
				setIsLoadingData(false);
			}
		}
	}, [taskId, toast]);

	useEffect(() => {
		fetchTaskData();
	}, [fetchTaskData]);

	const handleDeleteTask = async () => {
		if (task) {
			try {
				await storeDeleteTask(task.id);
				toast({
					title: 'Task Deleted',
					description: `Task "${task.description.substring(
						0,
						30
					)}..." has been deleted.`,
				});
				router.push('/tasks');
			} catch (error) {
				console.error('Error deleting task:', error);
				toast({
					title: 'Error',
					description: 'Failed to delete task. Please try again.',
					variant: 'destructive',
				});
			}
		}
	};

	const handleAssign = async () => {
		if (task && selectedAssigneeId) {
			try {
				const result = await assignTaskToEmployee(task.id, selectedAssigneeId);
				if (result.task && result.employee) {
					toast({
						title: 'Task Assigned',
						description: `Task assigned to employee ${result.employee.fullName}.`,
					});
					await fetchTaskData();
					setSelectedAssigneeId('');
				} else {
					toast({
						title: 'Error',
						description: 'Failed to assign task.',
						variant: 'destructive',
					});
				}
			} catch (error) {
				console.error('Error assigning task:', error);
				toast({
					title: 'Error',
					description: 'Failed to assign task. Please try again.',
					variant: 'destructive',
				});
			}
		}
	};

	const handleUnassign = async (employeeId?: number) => {
		if (task) {
			try {
				const result = await unassignTaskFromEmployee(
					task.id,
					employeeId ? String(employeeId) : undefined
				);
				if (result?.task) {
					toast({
						title: 'Task Unassigned',
						description: employeeId
							? 'Employee unassigned from task.'
							: 'All employees unassigned from task.',
					});
					await fetchTaskData();
				} else {
					toast({
						title: 'Error',
						description: 'Failed to unassign task.',
						variant: 'destructive',
					});
				}
			} catch (error) {
				console.error('Error unassigning task:', error);
				toast({
					title: 'Error',
					description: 'Failed to unassign task. Please try again.',
					variant: 'destructive',
				});
			}
		}
	};

	const isTaskCompleted =
		task?.status === ('Completed' as TaskStatus) ||
		task?.status === ('Cancelled' as TaskStatus);

	return (
		<div className='space-y-6'>
			<DataLoader
				isLoading={isLoadingData}
				error={error}
				data={task}
				onRetry={fetchTaskData}
				loadingComponent={
					<div className='space-y-6'>
						<PageHeader title='Loading Task...' icon={ClipboardList} />
						<SkeletonLoader variant='card' count={1} />
						<div className='grid lg:grid-cols-3 gap-6'>
							<SkeletonLoader
								variant='card'
								count={1}
								className='lg:col-span-2'
							/>
							<SkeletonLoader variant='card' count={1} />
						</div>
					</div>
				}
				emptyComponent={
					<div className='text-center py-10'>
						<PageHeader title='Task Not Found' icon={AlertTriangle} />
						<p className='mb-4'>The requested task could not be found.</p>
						<Button
							onClick={() => router.push('/tasks')}
							variant='outline'
							className='gap-2'>
							<ArrowLeft className='h-4 w-4' />
							Back to Tasks
						</Button>
					</div>
				}>
				{(loadedTask) => (
					<>
						{/* Page Header with Action Buttons */}
						<PageHeader
							title={loadedTask.description}
							icon={ClipboardList}
							description='Manage details and assignment for this task.'>
							<div className='flex gap-2 items-center flex-wrap'>
								<Button
									variant='outline'
									onClick={() => router.push('/tasks')}
									className='gap-2'>
									<ArrowLeft className='h-4 w-4' />
									Back to List
								</Button>
								<Button variant='default' asChild className='gap-2'>
									<Link href={`/tasks/${loadedTask.id}/edit`}>
										<Edit className='h-4 w-4' />
										Edit
									</Link>
								</Button>
								<AlertDialog>
									<AlertDialogTrigger asChild>
										<Button variant='destructive' className='gap-2'>
											<Trash2 className='h-4 w-4' />
											Delete Task
										</Button>
									</AlertDialogTrigger>
									<AlertDialogContent>
										<AlertDialogHeader>
											<AlertDialogTitle>Are you sure?</AlertDialogTitle>
											<AlertDialogDescription>
												This action cannot be undone. This will permanently
												delete the task.
											</AlertDialogDescription>
										</AlertDialogHeader>
										<AlertDialogFooter>
											<AlertDialogCancel>Cancel</AlertDialogCancel>
											<AlertDialogAction
												onClick={handleDeleteTask}
												className='bg-destructive hover:bg-destructive/90'>
												Delete
											</AlertDialogAction>
										</AlertDialogFooter>
									</AlertDialogContent>
								</AlertDialog>
							</div>
						</PageHeader>

						{/* Main Content Grid */}
						<div className='grid lg:grid-cols-3 gap-6'>
							{/* Main Task Details Card */}
							<Card className='lg:col-span-2 shadow-sm'>
								<CardHeader className='border-b'>
									<div className='flex justify-between items-start gap-4'>
										<div className='space-y-1'>
											<CardTitle className='text-2xl font-bold leading-tight'>
												{loadedTask.description}
											</CardTitle>
											<CardDescription>
												Detailed overview of the task
											</CardDescription>
										</div>
										<div className='flex flex-col gap-2'>
											<Badge
												className={cn(
													'justify-center',
													getStatusColor(loadedTask.status)
												)}>
												{loadedTask.status}
											</Badge>
											<Badge
												variant='outline'
												className={cn(
													'justify-center',
													getPriorityColor(loadedTask.priority)
												)}>
												{loadedTask.priority} Priority
											</Badge>
										</div>
									</div>
								</CardHeader>

								<CardContent className='p-6 space-y-6'>
									{/* Key Information Grid */}
									<div className='grid md:grid-cols-2 gap-4'>
										<KeyValueItem
											icon={MapPin}
											label='Location'
											value={loadedTask.location}
										/>
										<KeyValueItem
											icon={CalendarDays}
											label='Start Date & Time'
											value={formatDate(loadedTask.dateTime, true)}
										/>
										{loadedTask.deadline && (
											<KeyValueItem
												icon={Clock}
												label='Deadline'
												value={formatDate(loadedTask.deadline, true)}
											/>
										)}
										<KeyValueItem
											icon={Clock}
											label='Estimated Duration'
											value={`${loadedTask.estimatedDuration} minutes`}
										/>
									</div>

									{/* Required Skills */}
									{loadedTask.requiredSkills &&
										loadedTask.requiredSkills.length > 0 && (
											<KeyValueItem icon={Wrench} label='Required Skills'>
												<div className='flex flex-wrap gap-1'>
													{loadedTask.requiredSkills.map((skill) => (
														<Badge
															key={skill}
															variant='secondary'
															className='text-xs'>
															{skill}
														</Badge>
													))}
												</div>
											</KeyValueItem>
										)}

									{/* Associated Vehicle */}
									{assignedVehicle && (
										<KeyValueItem icon={Car} label='Associated Vehicle'>
											<Link
												href={`/vehicles/${assignedVehicle.id}`}
												className='text-primary hover:underline flex items-center gap-1'>
												{assignedVehicle.make} {assignedVehicle.model} (
												{assignedVehicle.year})
												<ExternalLink className='h-3 w-3' />
											</Link>
										</KeyValueItem>
									)}

									{/* Assigned Employees - NEW SECTION */}
									{assignedEmployees.length > 0 && (
										<AssignedEmployeeList
											assignedEmployees={assignedEmployees}
											onUnassign={handleUnassign}
											isTaskCompleted={isTaskCompleted}
										/>
									)}

									{/* Notes */}
									{loadedTask.notes && (
										<KeyValueItem
											icon={ClipboardList}
											label='Notes'
											className='md:col-span-2'>
											<p className='text-base font-medium whitespace-pre-wrap'>
												{loadedTask.notes}
											</p>
										</KeyValueItem>
									)}

									{/* Sub-Tasks */}
									{loadedTask.subTasks && loadedTask.subTasks.length > 0 && (
										<div className='space-y-3'>
											<Separator />
											<div>
												<h3 className='text-lg font-semibold mb-3 flex items-center gap-2'>
													<ClipboardList className='h-4 w-4' />
													Sub-Tasks
												</h3>
												<ul className='space-y-2'>
													{loadedTask.subTasks.map((sub) => (
														<li
															key={sub.id}
															className={cn(
																'flex items-center gap-2 text-sm',
																sub.completed &&
																	'line-through text-muted-foreground'
															)}>
															{sub.completed ? (
																<CheckCircle className='h-4 w-4 text-green-500' />
															) : (
																<div className='h-4 w-4 border rounded-sm' />
															)}
															{sub.title}
														</li>
													))}
												</ul>
											</div>
										</div>
									)}
								</CardContent>

								<CardFooter className='border-t text-xs text-muted-foreground'>
									Created: {formatDate(loadedTask.createdAt, true)} | Updated:{' '}
									{formatDate(loadedTask.updatedAt, true)}
								</CardFooter>
							</Card>

							{/* Task Assignment Card */}
							<Card className='shadow-sm h-fit'>
								<CardHeader>
									<CardTitle className='flex items-center gap-2'>
										<Users className='h-5 w-5 text-primary' />
										Task Assignment
									</CardTitle>
								</CardHeader>
								<CardContent className='space-y-4'>
									{/* Current Assignment Status */}
									{assignedEmployees.length > 0 ? (
										<div className='space-y-4'>
											<div className='space-y-3'>
												<p className='text-sm text-muted-foreground'>
													Currently assigned to {assignedEmployees.length}{' '}
													employee(s):
												</p>
												{assignedEmployees.map((employee) => (
													<div
														key={employee.id}
														className='flex items-center justify-between p-3 rounded-lg border bg-muted/30'>
														<div className='flex items-center gap-2'>
															<Avatar className='h-8 w-8'>
																<AvatarFallback className='text-xs'>
																	{employee.fullName
																		.split(' ')
																		.map((n) => n[0])
																		.join('')
																		.toUpperCase()
																		.slice(0, 2)}
																</AvatarFallback>
															</Avatar>
															<div>
																<Link
																	href={`/employees/${employee.id}`}
																	className='font-medium hover:text-primary text-sm'>
																	{employee.fullName}
																</Link>
																<p className='text-xs text-muted-foreground capitalize'>
																	{employee.role.replace('_', ' ')}
																</p>
															</div>
														</div>
														{!isTaskCompleted && (
															<Button
																variant='ghost'
																size='sm'
																onClick={() => handleUnassign(employee.id)}
																className='gap-1'>
																<UserMinus className='h-4 w-4' />
																Unassign
															</Button>
														)}
													</div>
												))}
											</div>
											{!isTaskCompleted && assignedEmployees.length > 1 && (
												<Button
													variant='outline'
													onClick={() => handleUnassign()}
													className='w-full gap-2'>
													<UserMinus className='h-4 w-4' />
													Unassign All
												</Button>
											)}
										</div>
									) : (
										!isTaskCompleted && (
											<div className='space-y-4'>
												<p className='text-sm text-muted-foreground'>
													This task is currently unassigned.
												</p>
												<div className='space-y-3'>
													<Label
														htmlFor='assignee-select'
														className='text-sm font-medium'>
														Select Employee to Assign
													</Label>
													<Select
														value={selectedAssigneeId}
														onValueChange={setSelectedAssigneeId}>
														<SelectTrigger>
															<SelectValue placeholder='Select an employee' />
														</SelectTrigger>
														<SelectContent>
															{availableEmployees.length > 0 ? (
																availableEmployees.map((e) => (
																	<SelectItem key={e.id} value={String(e.id)}>
																		{e.fullName} (
																		{e.role.charAt(0).toUpperCase() +
																			e.role.slice(1).replace('_', ' ')}
																		)
																	</SelectItem>
																))
															) : (
																<div className='p-2 text-sm text-muted-foreground'>
																	No available employees.
																</div>
															)}
														</SelectContent>
													</Select>
													<Button
														onClick={handleAssign}
														disabled={!selectedAssigneeId}
														className='w-full gap-2'>
														<UserPlus className='h-4 w-4' />
														Assign to Selected Employee
													</Button>
												</div>
											</div>
										)
									)}

									{/* Completed/Cancelled Status */}
									{loadedTask.status === 'Completed' && (
										<div className='flex items-center gap-2 p-3 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'>
											<CheckCircle className='h-4 w-4 text-green-600' />
											<p className='text-sm text-green-700 dark:text-green-400'>
												Task completed
												{assignedEmployees.length > 0
													? ` by ${assignedEmployees
															.map((e) => e.fullName)
															.join(', ')}`
													: ''}
												.
											</p>
										</div>
									)}
									{loadedTask.status === 'Cancelled' && (
										<div className='flex items-center gap-2 p-3 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'>
											<XCircle className='h-4 w-4 text-red-600' />
											<p className='text-sm text-red-700 dark:text-red-400'>
												Task cancelled.
											</p>
										</div>
									)}
								</CardContent>
							</Card>
						</div>
					</>
				)}
			</DataLoader>
		</div>
	);
}
