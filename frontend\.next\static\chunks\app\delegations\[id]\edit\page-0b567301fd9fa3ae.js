(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9977],{11133:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(40157).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},31949:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(40157).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},36615:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var i=a(95155),n=a(12115),l=a(35695),s=a(77762),r=a(2730),o=a(95647),d=a(57082),c=a(31949),u=a(12543),p=a(87481),h=a(77023),y=a(15080),g=a(6560);function v(){let e=(0,l.useRouter)(),t=(0,l.useParams)(),{toast:a}=(0,p.dj)(),[v,f]=(0,n.useState)(null),[m,x]=(0,n.useState)(!0),[k,j]=(0,n.useState)(null),[A,E]=(0,n.useState)(!1),D=t.id,N=(0,n.useCallback)(async()=>{x(!0),j(null);try{if(D){let e=await (0,r.getDelegationById)(D);e?f(e):(j("Delegation not found"),a({title:"Error",description:"Delegation not found.",variant:"destructive"}))}}catch(t){console.error("Error fetching delegation:",t);let e=t instanceof Error?t.message:"Failed to load delegation";j(e),a({title:"Error",description:e,variant:"destructive"})}finally{x(!1)}},[D,a]);(0,n.useEffect)(()=>{N()},[N]);let b=async t=>{if(D){E(!0),j(null);try{await (0,r.updateDelegation)(D,t),a({title:"Delegation Updated",description:'The delegation "'.concat(t.eventName,'" has been successfully updated.'),variant:"default"}),e.push("/delegations/".concat(D))}catch(t){console.error("Error updating delegation:",t);let e="Failed to update delegation";if(t.validationErrors&&Array.isArray(t.validationErrors)){let a=t.validationErrors.map(e=>"".concat(e.path,": ").concat(e.message)).join(", ");e="Validation failed: ".concat(a)}else t.message&&(e=t.message);j(e),a({title:"Error",description:e,variant:"destructive"})}finally{E(!1)}}};return(0,i.jsx)(y.A,{children:(0,i.jsx)(h.gO,{isLoading:m,error:k,data:v,onRetry:N,loadingComponent:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(o.z,{title:"Loading Delegation...",icon:d.A}),(0,i.jsx)(h.jt,{variant:"card",count:1})," "]}),emptyComponent:(0,i.jsxs)("div",{className:"space-y-6 text-center",children:[(0,i.jsx)(o.z,{title:"Delegation Not Found",icon:c.A}),(0,i.jsx)("p",{children:"The requested delegation could not be found."}),(0,i.jsx)(g.r,{actionType:"primary",onClick:()=>e.push("/delegations"),icon:(0,i.jsx)(u.A,{className:"h-4 w-4"}),children:"Back to Delegations"})]}),children:e=>(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(o.z,{title:"Edit Delegation: ".concat(e.eventName),description:"Modify the details for this delegation or event.",icon:d.A}),(0,i.jsx)(s.A,{onSubmit:b,initialData:e,isEditing:!0,isSubmitting:A})]})})})}},67554:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(40157).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},98733:(e,t,a)=>{Promise.resolve().then(a.bind(a,36615))}},e=>{var t=t=>e(e.s=t);e.O(0,[5769,8360,832,2688,2512,1859,4066,8241,5813,8162,2730,536,7762,8441,1684,7358],()=>t(98733)),_N_E=e.O()}]);