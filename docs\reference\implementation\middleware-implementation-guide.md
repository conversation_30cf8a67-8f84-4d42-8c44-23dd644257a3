# Middleware Implementation Guide

## Overview

This document provides detailed guidance on implementing and using middleware in the Car Life Tracker application, with a specific focus on data sanitization middleware. It serves as a reference for developers working on similar middleware implementations.

## Middleware Architecture

The application uses Express middleware for cross-cutting concerns such as:

1. **Data Validation**: Ensuring incoming data meets schema requirements
2. **Data Sanitization**: Ensuring data consistency and preventing type errors
3. **Authentication**: Verifying user identity and permissions
4. **Logging**: Recording request and response information
5. **Error Handling**: Catching and processing errors

Middleware can be applied at different levels:
- **Application-level**: Applied to all routes (`app.use(middleware)`)
- **Router-level**: Applied to specific route groups (`router.use(middleware)`)
- **Route-level**: Applied to specific routes (`router.get('/path', middleware, handler)`)

## Data Sanitization Middleware

### Purpose

Data sanitization middleware ensures that data structures are consistent and match expected types, preventing runtime errors in both backend and frontend code.

### Implementation Pattern

The implementation follows a consistent pattern:

1. **Import Dependencies**: Express types and logging utilities
2. **Define Sanitization Logic**: Functions to transform data into expected formats
3. **Export Middleware Function**: Function that integrates with Express middleware chain

### Example: Task Data Sanitization

#### 1. Request Data Sanitization (taskDataSanitizer.ts)

This middleware sanitizes incoming request data before it reaches route handlers:

```typescript
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

export const sanitizeTaskData = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Only process if there's a body
    if (req.body) {
      const requestPath = req.path;
      const taskId = req.params.id || 'unknown';
      
      // Sanitize specific fields
      if (!req.body.assignedEmployeeIds || !Array.isArray(req.body.assignedEmployeeIds)) {
        logger.warn(`TaskDataSanitizer: Sanitized assignedEmployeeIds [TaskID: ${taskId}]`);
        req.body.assignedEmployeeIds = [];
      }
      
      // Additional field sanitization...
    }
    
    next();
  } catch (error) {
    logger.error('Error in task data sanitizer middleware:', error);
    next();
  }
};
```

#### 2. Response Data Sanitization (taskResponseSanitizer.ts)

This middleware sanitizes outgoing response data before it's sent to the client:

```typescript
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

// Helper function to sanitize a single task object
const sanitizeTaskObject = (task: any, requestInfo: { path: string; taskId?: string }): any => {
  if (!task) return task;
  
  // Create a sanitized copy
  const sanitizedTask = { ...task };
  
  // Sanitize specific fields
  if (!sanitizedTask.assignedEmployees || !Array.isArray(sanitizedTask.assignedEmployees)) {
    logger.warn(`TaskResponseSanitizer: Sanitized assignedEmployees [TaskID: ${task.id || 'unknown'}]`);
    sanitizedTask.assignedEmployees = [];
  }
  
  // Additional field sanitization...
  
  return sanitizedTask;
};

export const sanitizeTaskResponse = (req: Request, res: Response, next: NextFunction): void => {
  // Store the original res.json method
  const originalJson = res.json;
  
  // Create request info for logging
  const requestInfo = {
    path: req.path,
    taskId: req.params.id || 'unknown'
  };

  // Override res.json to sanitize the response data
  res.json = function(data: any): Response {
    try {
      // Sanitize array of tasks
      if (Array.isArray(data)) {
        data = data.map(item => sanitizeTaskObject(item, requestInfo));
      } 
      // Sanitize single task
      else if (data && typeof data === 'object' && (data.description || data.assignedEmployees)) {
        data = sanitizeTaskObject(data, requestInfo);
      }
    } catch (error) {
      logger.error('Error in task response sanitizer middleware:', error);
    }

    // Call the original json method with sanitized data
    return originalJson.call(this, data);
  };

  next();
};
```

### Integration with Routes

Middleware is applied to routes in the route configuration file:

```typescript
// backend/src/routes/task.routes.ts
import { Router } from 'express';
import * as taskController from '../controllers/task.controller.js';
import { validate } from '../middleware/validation.js';
import { taskCreateSchema, taskUpdateSchema } from '../schemas/task.schema.js';
import { sanitizeTaskData } from '../middleware/taskDataSanitizer.js';
import { sanitizeTaskResponse } from '../middleware/taskResponseSanitizer.js';

const router = Router();

// Apply middleware to all task routes
router.use(sanitizeTaskData);
router.use(sanitizeTaskResponse);

// Define routes
router.get('/', taskController.getAllTasks);
router.post('/', validate(taskCreateSchema), taskController.createTask);
// Additional routes...

export default router;
```

## Logging Strategy

The middleware includes detailed logging to help identify and diagnose issues:

1. **Warning Level**: Used for data sanitization events (non-critical but noteworthy)
2. **Error Level**: Used for middleware execution errors

Log messages include:
- **Middleware Name**: Identifies which middleware generated the log
- **Field Name**: Identifies which field was sanitized
- **Original Value**: Records the type or value before sanitization
- **Context Information**: Includes request path, task ID, etc.

Example log message:
```
WARN: TaskDataSanitizer: Original value for assignedEmployeeIds was null, sanitized to []. [TaskID: 123, Path: /api/tasks]
```

## Testing Middleware

### Unit Testing

Unit tests for middleware should verify:
1. **Sanitization Logic**: Test that fields are properly sanitized
2. **Error Handling**: Test that errors are caught and don't block the request
3. **Logging**: Test that appropriate log messages are generated

Example test:
```typescript
describe('taskDataSanitizer', () => {
  it('should convert null assignedEmployeeIds to empty array', () => {
    const req = { body: { assignedEmployeeIds: null } };
    const res = {};
    const next = jest.fn();
    
    sanitizeTaskData(req, res, next);
    
    expect(req.body.assignedEmployeeIds).toEqual([]);
    expect(next).toHaveBeenCalled();
  });
});
```

### Integration Testing

Integration tests should verify:
1. **Middleware Chain**: Test that middleware executes in the correct order
2. **End-to-End Behavior**: Test that requests and responses are properly sanitized

## Best Practices

1. **Keep Middleware Focused**: Each middleware should have a single responsibility
2. **Error Handling**: Always include try/catch blocks to prevent middleware from crashing the application
3. **Logging**: Include detailed logging for debugging and monitoring
4. **Performance**: Be mindful of performance impact, especially for response sanitization
5. **Testing**: Thoroughly test middleware in isolation and as part of the request chain

## Troubleshooting

Common issues and solutions:

1. **Middleware Not Executing**: Ensure middleware is properly registered and in the correct order
2. **Sanitization Not Working**: Check that the middleware is targeting the correct fields and data structures
3. **Performance Issues**: Consider optimizing sanitization logic or applying middleware selectively

## Conclusion

Data sanitization middleware is a critical component for ensuring data consistency and preventing runtime errors. By following the patterns and practices outlined in this guide, you can implement robust middleware that enhances application reliability.

---

*Document created: [Current Date]*  
*Last updated: [Current Date]*  
*Author: Development Team*