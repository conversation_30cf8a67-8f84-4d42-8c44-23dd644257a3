(()=>{var e={};e.id=6683,e.ids=[6683],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24665:(e,t,r)=>{Promise.resolve().then(r.bind(r,92489))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35137:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},41449:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>o});var s=r(65239),i=r(48088),a=r(88170),l=r.n(a),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let o={children:["",{children:["vehicles",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,91255)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\edit\\[id]\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/vehicles/edit/[id]/page",pathname:"/vehicles/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},48041:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(60687);function i({title:e,description:t,icon:r,children:i}){return(0,s.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,s.jsx)(r,{className:"h-8 w-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),t&&(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:t})]}),i&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:i})]})}r(43210)},54041:(e,t,r)=>{Promise.resolve().then(r.bind(r,91255))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55817:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var s=r(60687),i=r(4780);function a({className:e,...t}){return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...t})}},91255:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\vehicles\\\\edit\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\edit\\[id]\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},92489:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(43210),a=r(16189),l=r(95950),n=r(28840),d=r(48041),o=r(35137),c=r(24920),m=r(29867),p=r(85726),h=r(29523);let u=()=>{let e=(0,a.useRouter)(),t=(0,a.useParams)(),{toast:r}=(0,m.dj)(),u=t.id?Number(t.id):null,[x,j]=(0,i.useState)(null),[v,g]=(0,i.useState)(!1),[f,y]=(0,i.useState)(!0),[N,w]=(0,i.useState)(null),b=(0,i.useCallback)(async()=>{if(!u){w("No vehicle ID provided."),y(!1);return}y(!0);try{let e=await (0,n.getVehicleById)(u);e?j(e):w("Vehicle not found.")}catch(e){console.error("Failed to fetch vehicle:",e),w(e.message||"Failed to load vehicle data.")}finally{y(!1)}},[u]);(0,i.useEffect)(()=>{b()},[b]);let k=async t=>{if(!u)return void w("Cannot update vehicle without an ID.");g(!0),w(null);try{let s={...t,initialOdometer:void 0===t.initialOdometer?x?.initialOdometer||0:t.initialOdometer};await (0,n.updateVehicle)(u,s),r({title:"Vehicle Updated",description:`${t.make} ${t.model} has been successfully updated.`,variant:"default"}),e.push("/vehicles")}catch(e){console.error("Failed to update vehicle:",e),w(e.message||"An unexpected error occurred. Please try again."),r({title:"Error Updating Vehicle",description:e.message||"Could not update the vehicle. Please check the details and try again.",variant:"destructive"})}finally{g(!1)}};return f?(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,s.jsx)(d.z,{title:"Edit Vehicle",description:"Loading vehicle details...",icon:o.A}),(0,s.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,s.jsx)(p.E,{className:"h-10 w-1/3"}),(0,s.jsx)(p.E,{className:"h-12 w-full"}),(0,s.jsx)(p.E,{className:"h-12 w-full"}),(0,s.jsx)(p.E,{className:"h-12 w-full"}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-6",children:[(0,s.jsx)(p.E,{className:"h-10 w-24"}),(0,s.jsx)(p.E,{className:"h-10 w-24"})]})]})]}):N&&!x?(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8 text-center",children:[(0,s.jsx)(d.z,{title:"Error",description:N,icon:c.A}),(0,s.jsx)(h.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]}):(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,s.jsx)(d.z,{title:"Edit Vehicle",description:`Update details for ${x?.make||"vehicle"} ${x?.model||""}`,icon:o.A}),N&&x&&(0,s.jsxs)("p",{className:"text-red-500 bg-red-100 p-3 rounded-md",children:["Error: ",N]}),x&&(0,s.jsx)(l.A,{onSubmit:k,initialData:x,isEditing:!0,isLoading:v})]})}},94735:e=>{"use strict";e.exports=require("events")},95950:(e,t,r)=>{"use strict";r.d(t,{A:()=>j});var s=r(60687),i=r(43210),a=r(16189),l=r(27605),n=r(63442),d=r(45880);let o=d.Ik({make:d.Yj().min(1,"Make is required"),model:d.Yj().min(1,"Model is required"),year:d.au.number().min(1900,"Year must be 1900 or later").max(new Date().getFullYear()+1,`Year cannot be more than ${new Date().getFullYear()+1}`),vin:d.Yj().min(1,"VIN is required").regex(/^[A-HJ-NPR-Z0-9]{17}$/,"VIN must be a valid 17-character format (only capital letters A-H, J-N, P-R, Z and numbers 0-9)"),licensePlate:d.Yj().min(1,"License plate is required"),ownerName:d.Yj().min(1,"Owner name is required"),ownerContact:d.Yj().min(1,"Owner contact is required"),color:d.Yj().optional(),initialOdometer:d.au.number().min(0,"Odometer reading cannot be negative").optional(),imageUrl:d.Yj().url("Invalid image URL").optional().or(d.eu(""))});var c=r(29523),m=r(89667),p=r(80013),h=r(44493),u=r(29867),x=r(55817);let j=({onSubmit:e,initialData:t={},isEditing:r=!1,submitButtonText:d=r?"Save Changes":"Create Vehicle",isLoading:j=!1})=>{let v=(0,a.useRouter)(),{toast:g}=(0,u.dj)(),{register:f,handleSubmit:y,formState:{errors:N,isSubmitting:w},reset:b,setValue:k}=(0,l.mN)({resolver:(0,n.u)(o),defaultValues:{make:t?.make||"",model:t?.model||"",year:t?.year||new Date().getFullYear(),vin:t?.vin||"",licensePlate:t?.licensePlate||"",ownerName:t?.ownerName||"",ownerContact:t?.ownerContact||"",color:t?.color||"",initialOdometer:t?.initialOdometer??0,imageUrl:t?.imageUrl||""}});(0,i.useEffect)(()=>{t&&(k("make",t.make||""),k("model",t.model||""),k("year",t.year||new Date().getFullYear()),k("vin",t.vin||""),k("licensePlate",t.licensePlate||""),k("ownerName",t.ownerName||""),k("ownerContact",t.ownerContact||""),k("color",t.color||""),k("initialOdometer",t.initialOdometer??0),k("imageUrl",t.imageUrl||""))},[t,k]);let C=async t=>{await e(t),g({title:r?"Vehicle Updated":"Vehicle Added",description:`${t.make} ${t.model} has been successfully ${r?"updated":"added"}.`,variant:"default"})};return(0,s.jsxs)(h.Zp,{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)(h.aR,{children:[(0,s.jsx)(h.ZB,{children:r?"Edit Vehicle":"Add New Vehicle"}),(0,s.jsx)(h.BT,{children:r?"Update the details of the vehicle.":"Enter the details for the new vehicle."})]}),(0,s.jsxs)("form",{onSubmit:y(C),children:[(0,s.jsxs)(h.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"make",children:"Make"}),(0,s.jsx)(m.p,{id:"make",...f("make"),placeholder:"e.g., Toyota"}),N.make&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:N.make.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"model",children:"Model"}),(0,s.jsx)(m.p,{id:"model",...f("model"),placeholder:"e.g., Camry"}),N.model&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:N.model.message})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"year",children:"Year"}),(0,s.jsx)(m.p,{id:"year",type:"number",...f("year"),placeholder:"e.g., 2023"}),N.year&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:N.year.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"vin",children:"VIN"}),(0,s.jsx)(m.p,{id:"vin",...f("vin"),placeholder:"Vehicle Identification Number"}),N.vin&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:N.vin.message}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"VIN must be exactly 17 characters, using capital letters A-H, J-N, P-R, Z and numbers 0-9."})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"licensePlate",children:"License Plate"}),(0,s.jsx)(m.p,{id:"licensePlate",...f("licensePlate"),placeholder:"e.g., ABC-123"}),N.licensePlate&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:N.licensePlate.message})]}),(0,s.jsx)("hr",{className:"my-6"}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"ownerName",children:"Owner Name"}),(0,s.jsx)(m.p,{id:"ownerName",...f("ownerName"),placeholder:"e.g., John Doe"}),N.ownerName&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:N.ownerName.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"ownerContact",children:"Owner Contact (Email/Phone)"}),(0,s.jsx)(m.p,{id:"ownerContact",...f("ownerContact"),placeholder:"e.g., <EMAIL> or 555-1234"}),N.ownerContact&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:N.ownerContact.message})]}),(0,s.jsx)("hr",{className:"my-6"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"color",children:"Color (Optional)"}),(0,s.jsx)(m.p,{id:"color",...f("color"),placeholder:"e.g., Blue"}),N.color&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:N.color.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"initialOdometer",children:"Initial Odometer (Optional)"}),(0,s.jsx)(m.p,{id:"initialOdometer",type:"number",...f("initialOdometer"),placeholder:"e.g., 100"}),N.initialOdometer&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:N.initialOdometer.message})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"imageUrl",children:"Image URL (Optional)"}),(0,s.jsx)(m.p,{id:"imageUrl",...f("imageUrl"),placeholder:"https://example.com/image.png"}),N.imageUrl&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:N.imageUrl.message})]})]}),(0,s.jsxs)(h.wL,{className:"flex justify-end space-x-3 pt-6",children:[(0,s.jsxs)(c.$,{type:"button",variant:"outline",onClick:()=>v.back(),disabled:w||j,children:[(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),(0,s.jsx)(c.$,{type:"submit",disabled:w||j,children:w||j?"Processing...":d})]})]})]})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3744,1658,5880,3442,8141,3983],()=>r(41449));module.exports=s})();