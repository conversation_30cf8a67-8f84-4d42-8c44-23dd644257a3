/**
 * Admin Routes
 *
 * This module defines the routes for admin functionality, including:
 * - Health status
 * - Performance metrics
 * - Error logs
 */

import {Router} from 'express';
import * as adminController from '../controllers/admin.controller.js';
import {validate} from '../middleware/validation.js';
import {errorLogQuerySchema} from '../schemas/admin.schema.js';
import {
	authenticateSupabaseUser,
	requireRole,
} from '../middleware/supabaseAuth.js';

const router = Router();

// 🚨 EMERGENCY SECURITY: All admin routes require authentication and admin role
router.use(authenticateSupabaseUser);
router.use(requireRole(['ADMIN', 'SUPER_ADMIN']));

/**
 * @openapi
 * /admin/health:
 *   get:
 *     tags: [Admin]
 *     summary: Get system health status
 *     description: Returns the health status of the system and its components
 *     responses:
 *       200:
 *         description: System is healthy
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthResponse'
 *       503:
 *         description: System is unhealthy
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthResponse'
 */
router.get('/health', adminController.getHealthStatus);

/**
 * @openapi
 * /admin/diagnostics:
 *   get:
 *     tags: [Admin]
 *     summary: Get comprehensive system diagnostics
 *     description: Returns comprehensive diagnostics including health, metrics, and system info
 *     responses:
 *       200:
 *         description: Diagnostics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DiagnosticsResponse'
 *       500:
 *         description: Server error
 */
router.get('/diagnostics', adminController.getDiagnostics);

/**
 * @openapi
 * /admin/performance:
 *   get:
 *     tags: [Admin]
 *     summary: Get database performance metrics
 *     description: Returns performance metrics for the database
 *     responses:
 *       200:
 *         description: Performance metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PerformanceMetrics'
 *       500:
 *         description: Server error
 */
router.get('/performance', adminController.getPerformanceMetrics);

/**
 * @openapi
 * /admin/errors:
 *   get:
 *     tags: [Admin]
 *     summary: Get system error logs
 *     description: Returns paginated error logs with optional filtering
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [ERROR, WARNING, INFO]
 *         description: Filter by log level
 *     responses:
 *       200:
 *         description: Error logs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginatedErrorLogResponse'
 *       400:
 *         description: Invalid query parameters
 *       500:
 *         description: Server error
 */
router.get(
	'/errors',
	validate(errorLogQuerySchema, 'query'),
	adminController.getErrorLogs
);

export default router;
