"use strict";exports.id=4550,exports.ids=[4550],exports.modules={6211:(e,r,a)=>{a.d(r,{A0:()=>c,BF:()=>o,Hj:()=>n,XI:()=>i,nA:()=>h,nd:()=>d});var s=a(60687),t=a(43210),l=a(4780);let i=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:a,className:(0,l.cn)("w-full caption-bottom text-sm",e),...r})}));i.displayName="Table";let c=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("thead",{ref:a,className:(0,l.cn)("[&_tr]:border-b",e),...r}));c.displayName="TableHeader";let o=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("tbody",{ref:a,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...r}));o.displayName="TableBody",t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("tfoot",{ref:a,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...r})).displayName="TableFooter";let n=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("tr",{ref:a,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...r}));n.displayName="TableRow";let d=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("th",{ref:a,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...r}));d.displayName="TableHead";let h=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("td",{ref:a,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...r}));h.displayName="TableCell",t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("caption",{ref:a,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",e),...r})).displayName="TableCaption"},14583:(e,r,a)=>{a.d(r,{$o:()=>f});var s=a(60687),t=a(43210),l=a(4780),i=a(43967),c=a(74158),o=a(69795),n=a(29523);let d=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,l.cn)("flex justify-center",e),...r}));d.displayName="Pagination";let h=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("ul",{ref:a,className:(0,l.cn)("flex flex-row items-center gap-1",e),...r}));h.displayName="PaginationContent";let m=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("li",{ref:a,className:(0,l.cn)("",e),...r}));m.displayName="PaginationItem";let x=t.forwardRef(({className:e,isActive:r,...a},t)=>(0,s.jsx)(n.$,{ref:t,"aria-current":r?"page":void 0,variant:r?"outline":"ghost",size:"icon",className:(0,l.cn)("h-9 w-9",e),...a}));x.displayName="PaginationLink";let u=t.forwardRef(({className:e,...r},a)=>(0,s.jsxs)(n.$,{ref:a,variant:"ghost",size:"icon",className:(0,l.cn)("h-9 w-9 gap-1",e),...r,children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Previous page"})]}));u.displayName="PaginationPrevious";let v=t.forwardRef(({className:e,...r},a)=>(0,s.jsxs)(n.$,{ref:a,variant:"ghost",size:"icon",className:(0,l.cn)("h-9 w-9 gap-1",e),...r,children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Next page"})]}));v.displayName="PaginationNext";let p=t.forwardRef(({className:e,...r},a)=>(0,s.jsxs)("span",{ref:a,"aria-hidden":!0,className:(0,l.cn)("flex h-9 w-9 items-center justify-center",e),...r,children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"More pages"})]}));function f({currentPage:e,totalPages:r,onPageChange:a,className:t}){let l=(()=>{let a=[];a.push(1);let s=Math.max(2,e-1),t=Math.min(r-1,e+1);s>2&&a.push("ellipsis1");for(let e=s;e<=t;e++)a.push(e);return t<r-1&&a.push("ellipsis2"),r>1&&a.push(r),a})();return r<=1?null:(0,s.jsx)(d,{className:t,children:(0,s.jsxs)(h,{children:[(0,s.jsx)(m,{children:(0,s.jsx)(u,{onClick:()=>a(e-1),disabled:1===e,"aria-disabled":1===e?"true":void 0,"aria-label":"Go to previous page"})}),l.map((r,t)=>"ellipsis1"===r||"ellipsis2"===r?(0,s.jsx)(m,{children:(0,s.jsx)(p,{})},`ellipsis-${t}`):(0,s.jsx)(m,{children:(0,s.jsx)(x,{onClick:()=>a(r),isActive:e===r,"aria-label":`Go to page ${r}`,children:r})},`page-${r}`)),(0,s.jsx)(m,{children:(0,s.jsx)(v,{onClick:()=>a(e+1),disabled:e===r,"aria-disabled":e===r?"true":void 0,"aria-label":"Go to next page"})})]})})}p.displayName="PaginationEllipsis"},24847:(e,r,a)=>{a.d(r,{R:()=>b});var s=a(60687),t=a(43210),l=a(44493),i=a(91821),c=a(29523),o=a(14975),n=a(99196),d=a(6211),h=a(89743),m=a(61662),x=a(4780);function u({records:e,showVehicleInfo:r=!0,sortField:a,sortDirection:t,onSort:l,className:i}){let c=e=>{l&&l(e)},o=e=>a!==e?null:"asc"===t?(0,s.jsx)(h.A,{className:"inline-block h-4 w-4 ml-1","aria-hidden":"true"}):(0,s.jsx)(m.A,{className:"inline-block h-4 w-4 ml-1","aria-hidden":"true"});return(0,s.jsxs)(d.XI,{id:"service-history-table",className:i,children:[(0,s.jsx)(d.A0,{children:(0,s.jsxs)(d.Hj,{children:[(0,s.jsxs)(d.nd,{onClick:()=>c("date"),className:(0,x.cn)(l?"cursor-pointer":""),"aria-sort":"date"===a?t:void 0,role:"columnheader",tabIndex:l?0:void 0,onKeyDown:e=>{l&&("Enter"===e.key||" "===e.key)&&(e.preventDefault(),c("date"))},"aria-label":"Sort by date",children:["Date ",o("date")]}),r&&(0,s.jsxs)(d.nd,{onClick:()=>c("vehicleMake"),className:(0,x.cn)(l?"cursor-pointer":""),"aria-sort":"vehicleMake"===a?t:void 0,role:"columnheader",tabIndex:l?0:void 0,onKeyDown:e=>{l&&("Enter"===e.key||" "===e.key)&&(e.preventDefault(),c("vehicleMake"))},"aria-label":"Sort by vehicle",children:["Vehicle ",o("vehicleMake")]}),(0,s.jsxs)(d.nd,{onClick:()=>c("servicePerformed"),className:(0,x.cn)(l?"cursor-pointer":""),"aria-sort":"servicePerformed"===a?t:void 0,role:"columnheader",tabIndex:l?0:void 0,onKeyDown:e=>{l&&("Enter"===e.key||" "===e.key)&&(e.preventDefault(),c("servicePerformed"))},"aria-label":"Sort by services performed",children:["Service(s) ",o("servicePerformed")]}),(0,s.jsxs)(d.nd,{onClick:()=>c("odometer"),className:(0,x.cn)(l?"cursor-pointer":""),"aria-sort":"odometer"===a?t:void 0,role:"columnheader",tabIndex:l?0:void 0,onKeyDown:e=>{l&&("Enter"===e.key||" "===e.key)&&(e.preventDefault(),c("odometer"))},"aria-label":"Sort by odometer reading",children:["Odometer ",o("odometer")]}),(0,s.jsxs)(d.nd,{onClick:()=>c("cost"),className:(0,x.cn)("text-right",l?"cursor-pointer":""),"aria-sort":"cost"===a?t:void 0,role:"columnheader",tabIndex:l?0:void 0,onKeyDown:e=>{l&&("Enter"===e.key||" "===e.key)&&(e.preventDefault(),c("cost"))},"aria-label":"Sort by cost",children:["Cost ",o("cost")]}),(0,s.jsx)(d.nd,{className:"print-notes-col",children:"Notes"})]})}),(0,s.jsx)(d.BF,{children:e.map(e=>(0,s.jsxs)(d.Hj,{children:[(0,s.jsx)(d.nA,{children:new Date(e.date).toLocaleDateString()}),r&&(0,s.jsxs)(d.nA,{children:[e.vehicleMake," ",e.vehicleModel," (",e.vehicleYear,")",e.vehiclePlateNumber&&(0,s.jsx)("span",{className:"block text-xs text-muted-foreground",children:e.vehiclePlateNumber})]}),(0,s.jsx)(d.nA,{className:"max-w-xs truncate print-service-col",title:e.servicePerformed.join(", "),children:e.servicePerformed.join(", ")}),(0,s.jsx)(d.nA,{children:e.odometer.toLocaleString()}),(0,s.jsx)(d.nA,{className:"text-right",children:e.cost?`$${Number(e.cost).toFixed(2)}`:"-"}),(0,s.jsx)(d.nA,{className:"max-w-xs truncate print-notes-col",title:e.notes,children:e.notes||"-"})]},e.id))})]})}var v=a(96834);function p({value:e,label:r,className:a,textColor:t="text-muted-foreground",colSpan:i}){return(0,s.jsx)(l.Zp,{className:(0,x.cn)("overflow-hidden",a,i),children:(0,s.jsxs)(l.Wu,{className:"p-4 text-center",children:[(0,s.jsx)("p",{className:"text-2xl font-semibold text-card-foreground",children:e}),(0,s.jsx)("p",{className:(0,x.cn)("text-sm",t),children:r})]})})}function f({records:e,vehicleSpecific:r=!1,className:a}){let t=e.length,i=e.reduce((e,r)=>e+(Number(r.cost)||0),0),c=e.map(e=>new Date(e.date).getTime()),o=c.length?new Date(Math.min(...c)):null,n=c.length?new Date(Math.max(...c)):null,d=Object.entries(e.reduce((e,r)=>(r.servicePerformed.forEach(r=>{e[r]=(e[r]||0)+1}),e),{})).sort((e,r)=>r[1]-e[1]).slice(0,3),h=r&&e.length>0?Math.max(...e.map(e=>e.odometer))-Math.min(...e.map(e=>e.odometer)):0,m=!r&&e.length>0?new Set(e.map(e=>e.vehicleId)).size:0;return(0,s.jsxs)("div",{className:(0,x.cn)("mt-6 grid grid-cols-2 sm:grid-cols-3 gap-4 summary-grid",a),children:[(0,s.jsx)(p,{value:t,label:"Total Services",className:"border-primary/10"}),(0,s.jsx)(p,{value:`$${i.toFixed(2)}`,label:"Total Cost",className:"border-primary/10"}),!r&&e.length>0&&(0,s.jsx)(p,{value:m,label:"Vehicles Serviced",className:"border-primary/10"}),r&&e.length>0&&(0,s.jsx)(p,{value:h.toLocaleString(),label:"Odometer Range Covered",className:"border-primary/10"}),e.length>0&&(0,s.jsx)(p,{value:`${o?.toLocaleDateString()} - ${n?.toLocaleDateString()}`,label:"Date Range",className:"border-primary/10",colSpan:"col-span-2 sm:col-span-3"}),d.length>0&&(0,s.jsx)(l.Zp,{className:"overflow-hidden col-span-2 sm:col-span-3 border-primary/10",children:(0,s.jsxs)(l.Wu,{className:"p-4",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold mb-2 text-card-foreground",children:"Top Services"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:d.map(([e,r])=>(0,s.jsxs)(v.E,{variant:"secondary",className:"text-xs px-2 py-1","aria-label":`${e}: ${r} services`,children:[e," (",r,")"]},e))})]})})]})}var j=a(14583),N=a(52027);function b({records:e,isLoading:r,error:a,onRetry:d,showVehicleInfo:h=!0,vehicleSpecific:m=!1,className:x}){let[v,p]=(0,t.useState)("date"),[b,g]=(0,t.useState)("desc"),[y,w]=(0,t.useState)(1),[k]=(0,t.useState)(10),M=(0,t.useMemo)(()=>[...e].sort((e,r)=>{let a,s;switch(v){case"date":a=new Date(e.date).getTime(),s=new Date(r.date).getTime();break;case"cost":a=Number(e.cost)||0,s=Number(r.cost)||0;break;case"odometer":a=e.odometer,s=r.odometer;break;case"servicePerformed":a=e.servicePerformed.join(", ").toLowerCase(),s=r.servicePerformed.join(", ").toLowerCase();break;case"vehicleMake":a=`${e.vehicleMake} ${e.vehicleModel}`.toLowerCase(),s=`${r.vehicleMake} ${r.vehicleModel}`.toLowerCase();break;default:a=e[v],s=r[v]}return a<s?"asc"===b?-1:1:a>s?"asc"===b?1:-1:0}),[e,v,b]),C=(0,t.useCallback)(e=>{v===e?g("asc"===b?"desc":"asc"):(p(e),g("asc"))},[v,b]),S=y*k,P=S-k,A=(0,t.useMemo)(()=>M.slice(P,S),[M,P,S]),$=(0,t.useMemo)(()=>Math.ceil(M.length/k),[M.length,k]),D=(0,t.useCallback)(e=>{w(e)},[]);return r?(0,s.jsx)("div",{className:"space-y-4","data-testid":"loading-skeleton",children:(0,s.jsx)(N.jt,{variant:"table",count:5})}):a?(0,s.jsxs)(i.Fc,{variant:"destructive",className:"mb-6",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)(i.XL,{children:"Error"}),(0,s.jsx)(i.TN,{children:a}),(0,s.jsx)(c.$,{variant:"outline",size:"sm",onClick:d,className:"mt-2","aria-label":"Try loading service records again",children:"Try Again"})]}):0===e.length?(0,s.jsxs)(i.Fc,{variant:"default",className:"mb-6",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)(i.XL,{children:"No Service Records"}),(0,s.jsx)(i.TN,{children:m?"No service records available for this vehicle.":"No service records match your current filters. Try adjusting your search or filter criteria."})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(f,{records:M,vehicleSpecific:m}),(0,s.jsx)(l.Zp,{className:"shadow-md card-print",children:(0,s.jsx)(l.Wu,{className:"p-0",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)(u,{records:A,showVehicleInfo:h,sortField:v,sortDirection:b,onSort:C})})})}),M.length>k&&(0,s.jsx)("div",{className:"flex justify-center mt-4 no-print",children:(0,s.jsx)(j.$o,{currentPage:y,totalPages:$,onPageChange:D})})]})}},48041:(e,r,a)=>{a.d(r,{z:()=>t});var s=a(60687);function t({title:e,description:r,icon:a,children:t}){return(0,s.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[a&&(0,s.jsx)(a,{className:"h-8 w-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),r&&(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:r})]}),t&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:t})]})}a(43210)},57697:(e,r,a)=>{a.d(r,{Lv:()=>t,_X:()=>i});var s=a(82046);async function t(e){try{let r=await s.FH.get("/servicerecords/enriched",e);return(0,s.CZ)(r,"/servicerecords/enriched")||[]}catch(e){throw console.error("Error fetching enriched service records:",e),e}}async function l(e,r){try{let a=`/vehicles/${e}/servicerecords`,t=await s.FH.get(a,r);return(0,s.CZ)(t,a)||[]}catch(r){throw console.error(`Error fetching service records for vehicle ${e}:`,r),r}}async function i(e,r){try{try{return(await t(r)).filter(r=>Number(r.vehicleId)===e)}catch(i){console.warn("Falling back to manual enrichment for vehicle service records",i);let s=await l(e,r),t=await Promise.resolve().then(a.bind(a,28840)).then(r=>r.getVehicleById(e));if(!t)throw Error(`Vehicle with ID ${e} not found`);return s.map(e=>({...e,vehicleId:String(e.vehicleId),vehicleMake:t.make,vehicleModel:t.model,vehicleYear:t.year,vehiclePlateNumber:t.licensePlate||void 0}))}}catch(r){throw console.error(`Error fetching enriched service records for vehicle ${e}:`,r),r}}},77368:(e,r,a)=>{a.d(r,{A:()=>s});let s=(0,a(82614).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},95758:(e,r,a)=>{a.d(r,{A:()=>n});var s=a(60687),t=a(43210),l=a(91821),i=a(29523),c=a(77368);class o extends t.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e,errorInfo:null}}componentDidCatch(e,r){console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",r.componentStack),this.setState({errorInfo:r})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,s.jsxs)(l.Fc,{variant:"destructive",className:"my-4",children:[(0,s.jsx)(l.XL,{className:"text-lg font-semibold",children:"Something went wrong"}),(0,s.jsxs)(l.TN,{className:"mt-2",children:[(0,s.jsx)("p",{className:"mb-2",children:this.state.error?.message||"An unexpected error occurred"}),this.state.error?.stack&&(0,s.jsxs)("details",{className:"mt-2 text-xs",children:[(0,s.jsx)("summary",{children:"Error details"}),(0,s.jsx)("pre",{className:"mt-2 whitespace-pre-wrap overflow-auto max-h-[200px] p-2 bg-slate-100 dark:bg-slate-900 rounded",children:this.state.error.stack})]}),(0,s.jsxs)(i.$,{variant:"outline",size:"sm",onClick:this.handleRetry,className:"mt-4",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]}):this.props.children}}let n=o},99196:(e,r,a)=>{a.d(r,{A:()=>s});let s=(0,a(82614).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};