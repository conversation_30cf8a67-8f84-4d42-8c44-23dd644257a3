/**
 * Response Adapter Utilities
 * 
 * This module provides utilities for adapting API responses to a consistent format.
 * It helps handle different response structures from the backend and provides a
 * consistent interface for the frontend components.
 */

/**
 * Standard API response wrapper
 */
export interface ApiResponse<T> {
  /** Response status: 'success' or 'error' */
  status: 'success' | 'error';
  /** Response data (only present for success responses) */
  data?: T;
  /** Error message (only present for error responses) */
  message?: string;
  /** Error details (only present for error responses) */
  error?: string | Record<string, any>;
  /** Pagination information (only present for paginated responses) */
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Adapts an API response to a consistent format
 * 
 * This function handles different response structures:
 * 1. Standard wrapped responses: { status: 'success', data: {...} }
 * 2. Direct responses: {...} (without a wrapper)
 * 3. Error responses: { status: 'error', message: '...' }
 * 
 * @param response The API response to adapt
 * @returns The adapted response data
 */
export function adaptApiResponse<T>(response: any): T {
  // If the response is null or undefined, return it as is
  if (response == null) {
    return response as T;
  }

  // If the response has a status and data property, it's already in the standard format
  if (response && typeof response === 'object' && 'status' in response) {
    if (response.status === 'success' && 'data' in response) {
      // Return the data property for success responses
      return response.data as T;
    } else if (response.status === 'error') {
      // For error responses, throw an error with the message
      throw new Error(response.message || 'Unknown error');
    }
  }

  // If the response doesn't match the standard format, return it as is
  // This handles direct responses without a wrapper
  return response as T;
}

/**
 * Wraps a function that returns a Promise to adapt its response
 * 
 * @param fn The function to wrap
 * @returns A new function that adapts the response
 */
export function withResponseAdapter<T, Args extends any[]>(
  fn: (...args: Args) => Promise<any>
): (...args: Args) => Promise<T> {
  return async (...args: Args): Promise<T> => {
    const response = await fn(...args);
    return adaptApiResponse<T>(response);
  };
}
