(()=>{var e={};e.id=8147,e.ids=[8147],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5389:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=r(65239),a=r(48088),l=r(88170),i=r.n(l),d=r(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(s,n);let o={children:["",{children:["tasks",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,88989)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/tasks/page",pathname:"/tasks",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},8760:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15036:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},15079:(e,s,r)=>{"use strict";r.d(s,{bq:()=>x,eb:()=>f,gC:()=>p,l6:()=>c,yv:()=>m});var t=r(60687),a=r(43210),l=r(22670),i=r(61662),d=r(89743),n=r(58450),o=r(4780);let c=l.bL;l.YJ;let m=l.WT,x=a.forwardRef(({className:e,children:s,...r},a)=>(0,t.jsxs)(l.l9,{ref:a,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[s,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=l.l9.displayName;let u=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)(l.PP,{ref:r,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(d.A,{className:"h-4 w-4"})}));u.displayName=l.PP.displayName;let h=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)(l.wn,{ref:r,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}));h.displayName=l.wn.displayName;let p=a.forwardRef(({className:e,children:s,position:r="popper",...a},i)=>(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:i,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...a,children:[(0,t.jsx)(u,{}),(0,t.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,t.jsx)(h,{})]})}));p.displayName=l.UC.displayName,a.forwardRef(({className:e,...s},r)=>(0,t.jsx)(l.JU,{ref:r,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.JU.displayName;let f=a.forwardRef(({className:e,children:s,...r},a)=>(0,t.jsxs)(l.q7,{ref:a,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(n.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:s})]}));f.displayName=l.q7.displayName,a.forwardRef(({className:e,...s},r)=>(0,t.jsx)(l.wv,{ref:r,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26398:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31447:(e,s,r)=>{Promise.resolve().then(r.bind(r,97032))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35265:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},35950:(e,s,r)=>{"use strict";r.d(s,{w:()=>d});var t=r(60687),a=r(43210),l=r(62369),i=r(4780);let d=a.forwardRef(({className:e,orientation:s="horizontal",decorative:r=!0,...a},d)=>(0,t.jsx)(l.b,{ref:d,decorative:r,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...a}));d.displayName=l.b.displayName},36644:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},41936:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},48041:(e,s,r)=>{"use strict";r.d(s,{z:()=>a});var t=r(60687);function a({title:e,description:s,icon:r,children:a}){return(0,t.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,t.jsx)(r,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),s&&(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:s})]}),a&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:a})]})}r(43210)},48206:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},52027:(e,s,r)=>{"use strict";r.d(s,{gO:()=>p,jt:()=>u});var t=r(60687);r(43210);var a=r(11516),l=r(72963),i=r(4780),d=r(85726),n=r(91821),o=r(68752);let c={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},m={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function x({size:e="md",className:s,text:r,fullPage:l=!1}){return(0,t.jsx)("div",{className:(0,i.cn)("flex items-center justify-center",l&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(a.A,{className:(0,i.cn)("animate-spin text-primary",c[e])}),r&&(0,t.jsx)("span",{className:(0,i.cn)("mt-2 text-muted-foreground",m[e]),children:r})]})})}function u({variant:e="default",count:s=1,className:r,testId:a="loading-skeleton"}){return"card"===e?(0,t.jsx)("div",{className:(0,i.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",r),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,t.jsx)(d.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(d.E,{className:"h-7 w-3/4 mb-1"}),(0,t.jsx)(d.E,{className:"h-4 w-1/2 mb-3"}),(0,t.jsx)(d.E,{className:"h-px w-full my-3"}),(0,t.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,t.jsx)(d.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===e?(0,t.jsxs)("div",{className:(0,i.cn)("space-y-3",r),"data-testid":a,children:[(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,t.jsx)(d.E,{className:"h-8 flex-1"},s))}),Array(s).fill(0).map((e,s)=>(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,t.jsx)(d.E,{className:"h-6 flex-1"},s))},s))]}):"list"===e?(0,t.jsx)("div",{className:(0,i.cn)("space-y-3",r),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(d.E,{className:"h-12 w-12 rounded-full"}),(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsx)(d.E,{className:"h-4 w-1/3"}),(0,t.jsx)(d.E,{className:"h-4 w-full"})]})]},s))}):"stats"===e?(0,t.jsx)("div",{className:(0,i.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",r),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(d.E,{className:"h-5 w-1/3"}),(0,t.jsx)(d.E,{className:"h-5 w-5 rounded-full"})]}),(0,t.jsx)(d.E,{className:"h-8 w-1/2 mt-3"}),(0,t.jsx)(d.E,{className:"h-4 w-2/3 mt-2"})]},s))}):(0,t.jsx)("div",{className:(0,i.cn)("space-y-2",r),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,t.jsx)(d.E,{className:"w-full h-5"},s))})}function h({message:e,onRetry:s,className:r}){return(0,t.jsxs)(n.Fc,{variant:"destructive",className:(0,i.cn)("my-4",r),children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)(n.XL,{children:"Error"}),(0,t.jsx)(n.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:e}),s&&(0,t.jsx)(o.r,{actionType:"tertiary",size:"sm",onClick:s,icon:(0,t.jsx)(a.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function p({isLoading:e,error:s,data:r,onRetry:a,children:l,loadingComponent:d,errorComponent:n,emptyComponent:o,className:c}){return e?d||(0,t.jsx)(x,{className:c,text:"Loading..."}):s?n||(0,t.jsx)(h,{message:s,onRetry:a,className:c}):!r||Array.isArray(r)&&0===r.length?o||(0,t.jsx)("div",{className:(0,i.cn)("text-center py-8",c),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:c,children:l(r)})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60368:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},60477:(e,s,r)=>{"use strict";r.d(s,{l8:()=>l,pj:()=>n,xb:()=>i});var t=r(45880),a=r(65594);let l=t.k5(["Pending","Assigned","In Progress","Completed","Cancelled"]),i=t.k5(["Low","Medium","High"]),d=t.Ik({id:t.Yj().uuid().optional(),title:t.Yj().min(1,"Subtask title cannot be empty"),completed:t.zM().default(!1)}),n=t.Ik({id:t.Yj().uuid().optional(),description:t.Yj().min(1,"Task description is required"),location:t.Yj().min(1,"Location is required"),dateTime:t.Yj().min(1,"Start date & time is required").refine(e=>(0,a.isValidDateString)(e),{message:"Please enter a valid date and time in YYYY-MM-DD HH:MM format"}),estimatedDuration:t.au.number().int().min(1,"Estimated duration must be at least 1 minute"),requiredSkills:t.YO(t.Yj()).optional().default([]),priority:i.default("Medium"),deadline:t.Yj().refine(e=>""===e||(0,a.isValidDateString)(e),{message:"Please enter a valid deadline date and time in YYYY-MM-DD HH:MM format"}).optional().transform(e=>""===e?void 0:e),status:l.default("Pending"),assignedEmployeeIds:t.YO(t.ai().int().positive("Employee ID must be a positive integer.")).optional().default([]),subTasks:t.YO(d).optional().default([]),notes:t.Yj().optional().or(t.eu("")),vehicleId:t.ai().int().positive("Vehicle ID must be a positive integer.").nullable().optional(),statusChangeReason:t.Yj().optional()}).superRefine((e,s)=>{if(e.dateTime&&e.deadline){let r=new Date(e.dateTime);new Date(e.deadline)<r&&s.addIssue({code:t.eq.custom,message:"Deadline cannot be earlier than the start date & time",path:["deadline"]})}})},62369:(e,s,r)=>{"use strict";r.d(s,{b:()=>o});var t=r(43210),a=r(14163),l=r(60687),i="horizontal",d=["horizontal","vertical"],n=t.forwardRef((e,s)=>{var r;let{decorative:t,orientation:n=i,...o}=e,c=(r=n,d.includes(r))?n:i;return(0,l.jsx)(a.sG.div,{"data-orientation":c,...t?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:s})});n.displayName="Separator";var o=n},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68752:(e,s,r)=>{"use strict";r.d(s,{r:()=>o});var t=r(60687),a=r(43210),l=r.n(a),i=r(29523),d=r(11516),n=r(4780);let o=l().forwardRef(({actionType:e="primary",icon:s,isLoading:r=!1,loadingText:a,className:l,children:o,disabled:c,asChild:m=!1,...x},u)=>{let{variant:h,className:p}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[e];return(0,t.jsx)(i.$,{ref:u,variant:h,className:(0,n.cn)(p,l),disabled:r||c,asChild:m,...x,children:r?(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),a||o]}):(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",s&&(0,t.jsx)("span",{className:"mr-2",children:s}),o]})})});o.displayName="ActionButton"},69981:(e,s,r)=>{"use strict";r.d(s,{M:()=>o});var t=r(60687);r(43210);var a=r(85814),l=r.n(a),i=r(36644),d=r(60368),n=r(68752);function o({href:e,getReportUrl:s,isList:r=!1,className:a}){if(!e&&!s)return console.error("ViewReportButton requires either href or getReportUrl prop"),null;let o=r?"View List Report":"View Report";return e?(0,t.jsx)(n.r,{actionType:"secondary",asChild:!0,icon:(0,t.jsx)(i.A,{className:"h-4 w-4"}),className:a,children:(0,t.jsxs)(l(),{href:e,target:"_blank",rel:"noopener noreferrer",children:[o,(0,t.jsx)(d.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"}),(0,t.jsx)("span",{className:"sr-only",children:"(opens in new tab)"})]})}):(0,t.jsxs)(n.r,{actionType:"secondary",onClick:()=>{if(s){let e=s();window.open(e,"_blank","noopener,noreferrer")}},icon:(0,t.jsx)(i.A,{className:"h-4 w-4"}),className:a,children:[o,(0,t.jsx)(d.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"})]})}},74075:e=>{"use strict";e.exports=require("zlib")},77368:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85726:(e,s,r)=>{"use strict";r.d(s,{E:()=>l});var t=r(60687),a=r(4780);function l({className:e,...s}){return(0,t.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...s})}},88989:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},92876:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},94495:(e,s,r)=>{Promise.resolve().then(r.bind(r,88989))},94735:e=>{"use strict";e.exports=require("events")},95758:(e,s,r)=>{"use strict";r.d(s,{A:()=>o});var t=r(60687),a=r(43210),l=r(91821),i=r(29523),d=r(77368);class n extends a.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e,errorInfo:null}}componentDidCatch(e,s){console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",s.componentStack),this.setState({errorInfo:s})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,t.jsxs)(l.Fc,{variant:"destructive",className:"my-4",children:[(0,t.jsx)(l.XL,{className:"text-lg font-semibold",children:"Something went wrong"}),(0,t.jsxs)(l.TN,{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-2",children:this.state.error?.message||"An unexpected error occurred"}),this.state.error?.stack&&(0,t.jsxs)("details",{className:"mt-2 text-xs",children:[(0,t.jsx)("summary",{children:"Error details"}),(0,t.jsx)("pre",{className:"mt-2 whitespace-pre-wrap overflow-auto max-h-[200px] p-2 bg-slate-100 dark:bg-slate-900 rounded",children:this.state.error.stack})]}),(0,t.jsxs)(i.$,{variant:"outline",size:"sm",onClick:this.handleRetry,className:"mt-4",children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]}):this.props.children}}let o=n},97032:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>I});var t=r(60687),a=r(43210),l=r(85814),i=r.n(l),d=r(16189);let n=(0,r(82614).A)("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);var o=r(35265),c=r(41936),m=r(28840),x=r(48041),u=r(89667),h=r(44493),p=r(68752),f=r(58595),g=r(48206),y=r(26398),j=r(92876),b=r(15036),v=r(8760),N=r(96834),k=r(35950),w=r(4780),A=r(76869),C=r(58261);let M=e=>{switch(e){case"Pending":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Assigned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";case"In Progress":return"bg-indigo-500/20 text-indigo-700 border-indigo-500/30 dark:text-indigo-400 dark:bg-indigo-500/10 dark:border-indigo-500/20";case"Completed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},E=e=>{switch(e){case"Low":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Medium":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"High":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},S=e=>{if(!e)return"N/A";try{return(0,A.GP)((0,C.H)(e),"MMM d, yyyy HH:mm")}catch(e){return"Invalid Date"}};function q({task:e}){let[s,r]=(0,a.useState)(void 0),l=s?.fullName,d=s?.role==="driver"?f.A:g.A;return(0,t.jsxs)(h.Zp,{className:"overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60",children:[(0,t.jsxs)(h.aR,{className:"p-5",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start gap-2",children:[(0,t.jsx)(h.ZB,{className:"text-lg font-semibold text-primary line-clamp-2",title:e.description,children:e.description}),(0,t.jsxs)("div",{className:"flex flex-col items-end gap-1 shrink-0",children:[(0,t.jsx)(N.E,{className:(0,w.cn)("text-xs py-1 px-2 font-semibold",M(e.status)),children:e.status}),(0,t.jsxs)(N.E,{className:(0,w.cn)("text-xs py-1 px-2 font-semibold",E(e.priority)),children:[e.priority," Priority"]})]})]}),(0,t.jsxs)(h.BT,{className:"text-sm text-muted-foreground flex items-center pt-1",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-1.5 flex-shrink-0 text-accent"}),e.location]})]}),(0,t.jsxs)(h.Wu,{className:"p-5 flex-grow flex flex-col",children:[(0,t.jsx)(k.w,{className:"my-3 bg-border/50"}),(0,t.jsxs)("div",{className:"space-y-2.5 text-sm text-foreground flex-grow",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(j.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Start: "}),(0,t.jsx)("strong",{className:"font-semibold",children:S(e.dateTime)})]})]}),e.deadline&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Deadline: "}),(0,t.jsx)("strong",{className:"font-semibold",children:S(e.deadline)})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Duration: "}),(0,t.jsxs)("strong",{className:"font-semibold",children:[e.estimatedDuration," mins"]})]})]}),l&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Assigned to: "}),(0,t.jsxs)("strong",{className:"font-semibold",children:[l," (",s?.role?s.role.charAt(0).toUpperCase()+s.role.slice(1).replace("_"," "):"Employee",")"]})]})]}),!l&&"Completed"!==e.status&&"Cancelled"!==e.status&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"mr-2.5 h-4 w-4 text-destructive flex-shrink-0"}),(0,t.jsx)("strong",{className:"font-semibold text-destructive",children:"Unassigned"})]})]}),e.notes&&(0,t.jsx)("p",{className:"mt-3 text-xs text-muted-foreground line-clamp-2 pt-2 border-t border-dashed border-border/50",title:e.notes,children:e.notes})]}),(0,t.jsx)(h.wL,{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,t.jsx)(p.r,{actionType:"tertiary",className:"w-full",icon:(0,t.jsx)(v.A,{className:"h-4 w-4"}),asChild:!0,children:(0,t.jsx)(i(),{href:`/tasks/${e.id}`,children:"View Details"})})})]})}var T=r(15079),P=r(60477),R=r(80013),Y=r(52027),D=r(95758),_=r(69981);function z(){return(0,t.jsxs)("div",{className:"overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60 rounded-lg",children:[(0,t.jsxs)("div",{className:"p-5 flex-grow flex flex-col",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsx)(Y.jt,{variant:"default",count:1,className:"h-7 w-3/5 mb-1 bg-muted/50"}),(0,t.jsx)(Y.jt,{variant:"default",count:1,className:"h-5 w-1/4 mb-1 bg-muted/50 rounded-full"})]}),(0,t.jsx)(Y.jt,{variant:"default",count:1,className:"h-4 w-1/2 mb-3 bg-muted/50"}),(0,t.jsx)(Y.jt,{variant:"default",count:1,className:"h-px w-full my-3 bg-border/50"}),(0,t.jsx)("div",{className:"space-y-2.5 flex-grow",children:[void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(Y.jt,{variant:"default",count:1,className:"mr-2.5 h-5 w-5 rounded-full bg-muted/50"}),(0,t.jsx)(Y.jt,{variant:"default",count:1,className:"h-5 w-2/3 bg-muted/50"})]},s))})]}),(0,t.jsx)("div",{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,t.jsx)(Y.jt,{variant:"default",count:1,className:"h-10 w-full bg-muted/50"})})]})}let L=()=>{(0,d.useRouter)();let[e,s]=(0,a.useState)([]),[r,l]=(0,a.useState)([]),[f,g]=(0,a.useState)(!0),[y,j]=(0,a.useState)(null),[b,v]=(0,a.useState)(""),[N,k]=(0,a.useState)("all"),[w,A]=(0,a.useState)("all"),[C,M]=(0,a.useState)("all"),[E,S]=(0,a.useState)([]),D=(0,a.useCallback)(async()=>{g(!0),j(null);try{let[e,r]=await Promise.all([(0,m.getTasks)(),(0,m.getEmployees)()]);if(Array.isArray(e)?(e.sort((e,s)=>new Date(s.dateTime).getTime()-new Date(e.dateTime).getTime()),s(e)):(console.error("getTasks did not return an array:",e),s([])),Array.isArray(r)){let e=r.map(e=>({id:String(e.id),name:e.fullName||e.name,role:e.role}));S(e)}else console.error("getEmployees did not return an array:",r),S([])}catch(e){console.error("Error fetching data:",e),j(e instanceof Error?e.message:"Failed to load data"),s([]),S([])}finally{g(!1)}},[]);(0,a.useEffect)(()=>{D()},[D]),(0,a.useEffect)(()=>{let s=[...e],r=b.toLowerCase();"all"!==N&&(s=s.filter(e=>e.status===N)),"all"!==w&&(s=s.filter(e=>e.priority===w)),"all"!==C&&(s=s.filter(e=>e.assignedEmployeeId&&e.assignedEmployeeId===C||"unassigned"===C&&!e.assignedEmployeeId)),r&&(s=s.filter(e=>{let s=e.assignedEmployeeId?E.find(s=>s.id===String(e.assignedEmployeeId)):null;return e.description.toLowerCase().includes(r)||e.location.toLowerCase().includes(r)||e.notes&&e.notes.toLowerCase().includes(r)||s&&s.name.toLowerCase().includes(r)})),l(s)},[b,e,N,w,C,E]);let L=b||"all"!==N||"all"!==w||"all"!==C;return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)(x.z,{title:"Manage Tasks",description:"Oversee all tasks, assignments, and progress.",icon:n,children:(0,t.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,t.jsx)(p.r,{actionType:"primary",icon:(0,t.jsx)(o.A,{className:"h-4 w-4"}),asChild:!0,children:(0,t.jsx)(i(),{href:"/tasks/add",children:"Add New Task"})}),(0,t.jsx)(_.M,{getReportUrl:()=>{let e=new URLSearchParams({searchTerm:b,status:N,priority:w,employee:C}).toString();return`/tasks/report?${e}`},isList:!0})]})}),(0,t.jsx)(h.Zp,{className:"mb-6 p-4 shadow",children:(0,t.jsx)(h.Wu,{className:"pt-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end",children:[(0,t.jsxs)("div",{className:"relative lg:col-span-1",children:[(0,t.jsx)(R.J,{htmlFor:"search-tasks",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Search Tasks"}),(0,t.jsx)(c.A,{className:"absolute left-3 top-[calc(50%_-_0.5rem_+_12px)] h-5 w-5 text-muted-foreground"}),(0,t.jsx)(u.p,{id:"search-tasks",type:"text",placeholder:"Description, location, assignee...",value:b,onChange:e=>v(e.target.value),className:"pl-10 w-full"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(R.J,{htmlFor:"status-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Status"}),(0,t.jsxs)(T.l6,{value:N,onValueChange:k,children:[(0,t.jsx)(T.bq,{id:"status-filter",children:(0,t.jsx)(T.yv,{placeholder:"All Statuses"})}),(0,t.jsxs)(T.gC,{children:[(0,t.jsx)(T.eb,{value:"all",children:"All Statuses"}),P.l8.options.map(e=>(0,t.jsx)(T.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(R.J,{htmlFor:"priority-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Priority"}),(0,t.jsxs)(T.l6,{value:w,onValueChange:A,children:[(0,t.jsx)(T.bq,{id:"priority-filter",children:(0,t.jsx)(T.yv,{placeholder:"All Priorities"})}),(0,t.jsxs)(T.gC,{children:[(0,t.jsx)(T.eb,{value:"all",children:"All Priorities"}),P.xb.options.map(e=>(0,t.jsx)(T.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(R.J,{htmlFor:"employee-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Employee"}),(0,t.jsxs)(T.l6,{value:C,onValueChange:M,children:[(0,t.jsx)(T.bq,{id:"employee-filter",children:(0,t.jsx)(T.yv,{placeholder:"All Employees"})}),(0,t.jsxs)(T.gC,{children:[(0,t.jsx)(T.eb,{value:"all",children:"All Employees"}),E.map(e=>(0,t.jsxs)(T.eb,{value:e.id,children:[e.name," (",e.role.charAt(0).toUpperCase()+e.role.slice(1).replace("_"," "),")"]},e.id)),(0,t.jsx)(T.eb,{value:"unassigned",children:"Unassigned"})]})]})]})]})})}),(0,t.jsx)(Y.gO,{isLoading:f,error:y,data:r,onRetry:D,loadingComponent:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[void 0,void 0,void 0].map((e,s)=>(0,t.jsx)(z,{},s))}),emptyComponent:(0,t.jsxs)("div",{className:"text-center py-12 bg-card rounded-lg shadow-md",children:[(0,t.jsx)(n,{className:"mx-auto h-16 w-16 text-muted-foreground mb-6"}),(0,t.jsx)("h3",{className:"text-2xl font-semibold text-foreground mb-2",children:L?"No Tasks Match Your Filters":"No Tasks Created Yet"}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2 mb-6 max-w-md mx-auto",children:L?"Try adjusting your search or filter criteria.":"It looks like you haven't created any tasks yet. Get started by adding one."}),!L&&(0,t.jsx)(p.r,{actionType:"primary",size:"lg",icon:(0,t.jsx)(o.A,{className:"h-4 w-4"}),asChild:!0,children:(0,t.jsx)(i(),{href:"/tasks/add",children:"Create Your First Task"})})]}),children:e=>(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-8",children:e.map(e=>(0,t.jsx)(q,{task:e},e.id))})})]})};function I(){return(0,t.jsx)(D.A,{children:(0,t.jsx)(L,{})})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,3744,1658,5880,2729,8141,3983],()=>r(5389));module.exports=t})();