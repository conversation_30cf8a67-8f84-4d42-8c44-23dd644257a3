/**
 * Phase 3: RBAC Infrastructure Testing
 * 
 * This script tests the RBAC infrastructure components that can be verified
 * without requiring an active user session.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Test results tracking
let testResults = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    testDetails: []
};

/**
 * Main testing function
 */
async function testRBACInfrastructure() {
    console.log('🧪 Phase 3: RBAC Infrastructure Testing');
    console.log('=======================================\n');

    try {
        // Test 1: User Profiles Table Structure
        await runTest('User Profiles Table Structure', testUserProfilesTable);

        // Test 2: UserRole Enum Values
        await runTest('UserRole Enum Values', testUserRoleEnum);

        // Test 3: Auth Hook Function Exists
        await runTest('Auth Hook Function Exists', testAuthHookFunction);

        // Test 4: RLS Policies Exist
        await runTest('RLS Policies Exist', testRLSPolicies);

        // Test 5: User Profile Data Integrity
        await runTest('User Profile Data Integrity', testUserProfileIntegrity);

        // Test 6: Employee Linking Capability
        await runTest('Employee Linking Capability', testEmployeeLinking);

        // Test 7: Migration Completeness
        await runTest('Migration Completeness', testMigrationCompleteness);

        // Generate test report
        generateTestReport();

    } catch (error) {
        console.error('❌ Infrastructure testing failed:', error.message);
        process.exit(1);
    }
}

/**
 * Test runner wrapper
 */
async function runTest(testName, testFunction) {
    testResults.totalTests++;
    console.log(`🔍 Running Test: ${testName}`);
    console.log('='.repeat(50));

    try {
        const result = await testFunction();
        if (result.success) {
            testResults.passedTests++;
            console.log(`✅ PASSED: ${testName}`);
            if (result.details) {
                console.log(`   Details: ${result.details}`);
            }
        } else {
            testResults.failedTests++;
            console.log(`❌ FAILED: ${testName}`);
            console.log(`   Reason: ${result.reason}`);
        }
        
        testResults.testDetails.push({
            name: testName,
            success: result.success,
            reason: result.reason || 'Test passed',
            details: result.details || null
        });

    } catch (error) {
        testResults.failedTests++;
        console.log(`❌ FAILED: ${testName}`);
        console.log(`   Error: ${error.message}`);
        
        testResults.testDetails.push({
            name: testName,
            success: false,
            reason: error.message,
            details: null
        });
    }

    console.log(''); // Empty line for readability
}

/**
 * Test 1: User Profiles Table Structure
 */
async function testUserProfilesTable() {
    try {
        // Test table accessibility and structure
        const { data, error } = await supabaseAdmin
            .from('user_profiles')
            .select('id, role, employee_id, is_active, created_at, updated_at')
            .limit(1);

        if (error) {
            return {
                success: false,
                reason: `Cannot access user_profiles table: ${error.message}`
            };
        }

        return {
            success: true,
            details: 'user_profiles table has correct structure and is accessible'
        };

    } catch (error) {
        return {
            success: false,
            reason: `Table structure test failed: ${error.message}`
        };
    }
}

/**
 * Test 2: UserRole Enum Values
 */
async function testUserRoleEnum() {
    const testRoles = ['USER', 'MANAGER', 'ADMIN', 'SUPER_ADMIN', 'READONLY'];
    let validRoles = [];

    for (const role of testRoles) {
        try {
            // Try to insert a test record with each role
            const testId = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            
            const { error } = await supabaseAdmin
                .from('user_profiles')
                .insert({
                    id: testId,
                    role: role,
                    is_active: true
                });

            if (!error) {
                validRoles.push(role);
                
                // Clean up the test record
                await supabaseAdmin
                    .from('user_profiles')
                    .delete()
                    .eq('id', testId);
            }
        } catch (e) {
            // Expected for invalid enum values
        }
    }

    if (validRoles.length === testRoles.length) {
        return {
            success: true,
            details: `All UserRole enum values are valid: ${validRoles.join(', ')}`
        };
    } else {
        return {
            success: false,
            reason: `Some UserRole enum values are invalid. Valid: ${validRoles.join(', ')}`
        };
    }
}

/**
 * Test 3: Auth Hook Function Exists
 */
async function testAuthHookFunction() {
    try {
        // Check if the function exists by querying the database
        const { data, error } = await supabaseAdmin
            .from('pg_proc')
            .select('proname')
            .eq('proname', 'custom_access_token_hook')
            .limit(1);

        if (error) {
            return {
                success: false,
                reason: `Cannot query database functions: ${error.message}`
            };
        }

        if (!data || data.length === 0) {
            return {
                success: false,
                reason: 'custom_access_token_hook function does not exist'
            };
        }

        return {
            success: true,
            details: 'custom_access_token_hook function exists in database'
        };

    } catch (error) {
        return {
            success: false,
            reason: `Auth hook function test failed: ${error.message}`
        };
    }
}

/**
 * Test 4: RLS Policies Exist
 */
async function testRLSPolicies() {
    try {
        // Check if RLS is enabled on user_profiles table
        const { data, error } = await supabaseAdmin
            .from('pg_tables')
            .select('tablename, rowsecurity')
            .eq('tablename', 'user_profiles')
            .eq('schemaname', 'public');

        if (error) {
            return {
                success: false,
                reason: `Cannot check RLS status: ${error.message}`
            };
        }

        if (!data || data.length === 0) {
            return {
                success: false,
                reason: 'user_profiles table not found'
            };
        }

        const table = data[0];
        if (!table.rowsecurity) {
            return {
                success: false,
                reason: 'RLS is not enabled on user_profiles table'
            };
        }

        return {
            success: true,
            details: 'RLS is enabled on user_profiles table'
        };

    } catch (error) {
        return {
            success: false,
            reason: `RLS policies test failed: ${error.message}`
        };
    }
}

/**
 * Test 5: User Profile Data Integrity
 */
async function testUserProfileIntegrity() {
    try {
        // Check for any data integrity issues
        const { data: profiles, error } = await supabaseAdmin
            .from('user_profiles')
            .select('id, role, employee_id, is_active');

        if (error) {
            return {
                success: false,
                reason: `Cannot access user profiles: ${error.message}`
            };
        }

        // Check for invalid data
        const issues = [];
        
        profiles.forEach(profile => {
            if (!profile.id) {
                issues.push('Profile with missing ID');
            }
            if (!profile.role) {
                issues.push(`Profile ${profile.id} has missing role`);
            }
            if (typeof profile.is_active !== 'boolean') {
                issues.push(`Profile ${profile.id} has invalid is_active value`);
            }
        });

        if (issues.length > 0) {
            return {
                success: false,
                reason: `Data integrity issues: ${issues.join(', ')}`
            };
        }

        return {
            success: true,
            details: `${profiles.length} user profiles have valid data integrity`
        };

    } catch (error) {
        return {
            success: false,
            reason: `Data integrity test failed: ${error.message}`
        };
    }
}

/**
 * Test 6: Employee Linking Capability
 */
async function testEmployeeLinking() {
    try {
        // Check if employee linking is working
        const { data: linkedProfiles, error } = await supabaseAdmin
            .from('user_profiles')
            .select(`
                id,
                employee_id,
                Employee:employee_id (
                    id,
                    name,
                    contactEmail
                )
            `)
            .not('employee_id', 'is', null);

        if (error) {
            return {
                success: false,
                reason: `Cannot test employee linking: ${error.message}`
            };
        }

        return {
            success: true,
            details: `Employee linking is functional. ${linkedProfiles.length} profiles linked to employees`
        };

    } catch (error) {
        return {
            success: false,
            reason: `Employee linking test failed: ${error.message}`
        };
    }
}

/**
 * Test 7: Migration Completeness
 */
async function testMigrationCompleteness() {
    try {
        // Check if all auth users have corresponding profiles
        const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();
        
        if (authError) {
            return {
                success: false,
                reason: `Cannot access auth users: ${authError.message}`
            };
        }

        const { data: profiles, error: profileError } = await supabaseAdmin
            .from('user_profiles')
            .select('id');

        if (profileError) {
            return {
                success: false,
                reason: `Cannot access user profiles: ${profileError.message}`
            };
        }

        const authUserIds = authUsers.users.map(user => user.id);
        const profileIds = profiles.map(profile => profile.id);
        
        const missingProfiles = authUserIds.filter(id => !profileIds.includes(id));
        
        if (missingProfiles.length > 0) {
            return {
                success: false,
                reason: `${missingProfiles.length} auth users missing profiles: ${missingProfiles.slice(0, 3).join(', ')}${missingProfiles.length > 3 ? '...' : ''}`
            };
        }

        return {
            success: true,
            details: `Migration complete: ${authUserIds.length} auth users have corresponding profiles`
        };

    } catch (error) {
        return {
            success: false,
            reason: `Migration completeness test failed: ${error.message}`
        };
    }
}

/**
 * Generate comprehensive test report
 */
function generateTestReport() {
    console.log('\n📊 Phase 3 Infrastructure Testing Results');
    console.log('=========================================');
    console.log(`Total Tests: ${testResults.totalTests}`);
    console.log(`✅ Passed: ${testResults.passedTests}`);
    console.log(`❌ Failed: ${testResults.failedTests}`);
    console.log(`Success Rate: ${((testResults.passedTests / testResults.totalTests) * 100).toFixed(1)}%`);

    if (testResults.failedTests > 0) {
        console.log('\n⚠️  Failed Tests:');
        testResults.testDetails
            .filter(test => !test.success)
            .forEach((test, index) => {
                console.log(`   ${index + 1}. ${test.name}: ${test.reason}`);
            });
    }

    console.log('\n🎯 Infrastructure Status:');
    if (testResults.failedTests === 0) {
        console.log('✅ RBAC infrastructure is fully functional');
        console.log('✅ Ready for user session testing');
        console.log('✅ Ready to proceed to Phase 4 (System Cleanup)');
    } else {
        console.log('❌ Infrastructure issues detected');
        console.log('⚠️  Please fix infrastructure issues before proceeding');
    }

    console.log('\n📋 Next Steps:');
    console.log('1. Sign in through your frontend application');
    console.log('2. Run: node scripts/test-auth-hook.js');
    console.log('3. Test protected API endpoints manually');
    console.log('4. Verify role-based access in the UI');
}

// Run infrastructure tests
testRBACInfrastructure().catch(console.error);
