'use client';

import React, {useState, useEffect} from 'react';
import {useRouter} from 'next/navigation';
import {useForm, SubmitHandler} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {Vehicle} from '@/lib/types';
import {VehicleFormData, VehicleFormSchema} from '@/lib/schemas/vehicleSchemas';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {Textarea} from '@/components/ui/textarea';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardFooter,
	CardDescription,
} from '@/components/ui/card';
import {useToast} from '@/hooks/use-toast';
import {Save, ArrowLeft} from 'lucide-react';

interface VehicleFormProps {
	onSubmit: (data: VehicleFormData) => Promise<void>;
	initialData?: Partial<Vehicle>;
	isEditing?: boolean;
	submitButtonText?: string;
	isLoading?: boolean;
}

const VehicleForm: React.FC<VehicleFormProps> = ({
	onSubmit,
	initialData = {},
	isEditing = false,
	submitButtonText = isEditing ? 'Save Changes' : 'Create Vehicle',
	isLoading = false,
}) => {
	const router = useRouter();
	const {toast} = useToast();
	const {
		register,
		handleSubmit,
		formState: {errors, isSubmitting},
		reset,
		setValue,
	} = useForm<VehicleFormData>({
		resolver: zodResolver(VehicleFormSchema),
		defaultValues: {
			make: initialData?.make || '',
			model: initialData?.model || '',
			year: initialData?.year || new Date().getFullYear(),
			vin: initialData?.vin || '',
			licensePlate: initialData?.licensePlate || '',
			ownerName: initialData?.ownerName || '',
			ownerContact: initialData?.ownerContact || '',
			color: initialData?.color || '',
			initialOdometer: initialData?.initialOdometer ?? 0,
			imageUrl: initialData?.imageUrl || '',
		},
	});

	useEffect(() => {
		if (initialData) {
			setValue('make', initialData.make || '');
			setValue('model', initialData.model || '');
			setValue('year', initialData.year || new Date().getFullYear());
			setValue('vin', initialData.vin || '');
			setValue('licensePlate', initialData.licensePlate || '');
			setValue('ownerName', initialData.ownerName || '');
			setValue('ownerContact', initialData.ownerContact || '');
			setValue('color', initialData.color || '');
			setValue('initialOdometer', initialData.initialOdometer ?? 0);
			setValue('imageUrl', initialData.imageUrl || '');
		}
	}, [initialData, setValue]);

	const processSubmit: SubmitHandler<VehicleFormData> = async (data) => {
		await onSubmit(data);
		toast({
			title: isEditing ? 'Vehicle Updated' : 'Vehicle Added',
			description: `${data.make} ${data.model} has been successfully ${
				isEditing ? 'updated' : 'added'
			}.`,
			variant: 'default',
		});
	};

	return (
		<Card className='max-w-2xl mx-auto'>
			<CardHeader>
				<CardTitle>{isEditing ? 'Edit Vehicle' : 'Add New Vehicle'}</CardTitle>
				<CardDescription>
					{isEditing
						? 'Update the details of the vehicle.'
						: 'Enter the details for the new vehicle.'}
				</CardDescription>
			</CardHeader>
			<form onSubmit={handleSubmit(processSubmit)}>
				<CardContent className='space-y-6'>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						<div>
							<Label htmlFor='make'>Make</Label>
							<Input
								id='make'
								{...register('make')}
								placeholder='e.g., Toyota'
							/>
							{errors.make && (
								<p className='text-sm text-red-500 mt-1'>
									{errors.make.message}
								</p>
							)}
						</div>
						<div>
							<Label htmlFor='model'>Model</Label>
							<Input
								id='model'
								{...register('model')}
								placeholder='e.g., Camry'
							/>
							{errors.model && (
								<p className='text-sm text-red-500 mt-1'>
									{errors.model.message}
								</p>
							)}
						</div>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						<div>
							<Label htmlFor='year'>Year</Label>
							<Input
								id='year'
								type='number'
								{...register('year')}
								placeholder='e.g., 2023'
							/>
							{errors.year && (
								<p className='text-sm text-red-500 mt-1'>
									{errors.year.message}
								</p>
							)}
						</div>
						<div>
							<Label htmlFor='vin'>VIN</Label>
							<Input
								id='vin'
								{...register('vin')}
								placeholder='Vehicle Identification Number'
							/>
							{errors.vin && (
								<p className='text-sm text-red-500 mt-1'>
									{errors.vin.message}
								</p>
							)}
							<p className='text-xs text-gray-500 mt-1'>
								VIN must be exactly 17 characters, using capital letters A-H,
								J-N, P-R, Z and numbers 0-9.
							</p>
						</div>
					</div>

					<div>
						<Label htmlFor='licensePlate'>License Plate</Label>
						<Input
							id='licensePlate'
							{...register('licensePlate')}
							placeholder='e.g., ABC-123'
						/>
						{errors.licensePlate && (
							<p className='text-sm text-red-500 mt-1'>
								{errors.licensePlate.message}
							</p>
						)}
					</div>

					<hr className='my-6' />

					<div>
						<Label htmlFor='ownerName'>Owner Name</Label>
						<Input
							id='ownerName'
							{...register('ownerName')}
							placeholder='e.g., John Doe'
						/>
						{errors.ownerName && (
							<p className='text-sm text-red-500 mt-1'>
								{errors.ownerName.message}
							</p>
						)}
					</div>

					<div>
						<Label htmlFor='ownerContact'>Owner Contact (Email/Phone)</Label>
						<Input
							id='ownerContact'
							{...register('ownerContact')}
							placeholder='e.g., <EMAIL> or 555-1234'
						/>
						{errors.ownerContact && (
							<p className='text-sm text-red-500 mt-1'>
								{errors.ownerContact.message}
							</p>
						)}
					</div>

					<hr className='my-6' />

					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						<div>
							<Label htmlFor='color'>Color (Optional)</Label>
							<Input
								id='color'
								{...register('color')}
								placeholder='e.g., Blue'
							/>
							{errors.color && (
								<p className='text-sm text-red-500 mt-1'>
									{errors.color.message}
								</p>
							)}
						</div>
						<div>
							<Label htmlFor='initialOdometer'>
								Initial Odometer (Optional)
							</Label>
							<Input
								id='initialOdometer'
								type='number'
								{...register('initialOdometer')}
								placeholder='e.g., 100'
							/>
							{errors.initialOdometer && (
								<p className='text-sm text-red-500 mt-1'>
									{errors.initialOdometer.message}
								</p>
							)}
						</div>
					</div>

					<div>
						<Label htmlFor='imageUrl'>Image URL (Optional)</Label>
						<Input
							id='imageUrl'
							{...register('imageUrl')}
							placeholder='https://example.com/image.png'
						/>
						{errors.imageUrl && (
							<p className='text-sm text-red-500 mt-1'>
								{errors.imageUrl.message}
							</p>
						)}
					</div>
				</CardContent>
				<CardFooter className='flex justify-end space-x-3 pt-6'>
					<Button
						type='button'
						variant='outline'
						onClick={() => router.back()}
						disabled={isSubmitting || isLoading}>
						<ArrowLeft className='mr-2 h-4 w-4' />
						Cancel
					</Button>
					<Button type='submit' disabled={isSubmitting || isLoading}>
						{isSubmitting || isLoading ? 'Processing...' : submitButtonText}
					</Button>
				</CardFooter>
			</form>
		</Card>
	);
};

export default VehicleForm;
