import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ActionButton } from '../action-button';
import { PlusCircle } from 'lucide-react';

describe('ActionButton', () => {
  it('renders with primary styling by default', () => {
    render(<ActionButton>Primary Button</ActionButton>);
    
    const button = screen.getByRole('button', { name: /primary button/i });
    expect(button).toBeInTheDocument();
    // Check for primary styling classes
    expect(button.className).toContain('bg-primary');
  });

  it('renders with secondary styling when actionType is secondary', () => {
    render(<ActionButton actionType="secondary">Secondary Button</ActionButton>);
    
    const button = screen.getByRole('button', { name: /secondary button/i });
    expect(button).toBeInTheDocument();
    // Check for secondary styling classes
    expect(button.className).toContain('bg-secondary');
  });

  it('renders with tertiary styling when actionType is tertiary', () => {
    render(<ActionButton actionType="tertiary">Tertiary Button</ActionButton>);
    
    const button = screen.getByRole('button', { name: /tertiary button/i });
    expect(button).toBeInTheDocument();
    // Check for tertiary styling classes
    expect(button.className).toContain('border-input');
  });

  it('renders with danger styling when actionType is danger', () => {
    render(<ActionButton actionType="danger">Danger Button</ActionButton>);
    
    const button = screen.getByRole('button', { name: /danger button/i });
    expect(button).toBeInTheDocument();
    // Check for danger styling classes
    expect(button.className).toContain('bg-destructive');
  });

  it('renders with an icon when provided', () => {
    render(
      <ActionButton icon={<PlusCircle data-testid="plus-icon" />}>
        Button with Icon
      </ActionButton>
    );
    
    expect(screen.getByTestId('plus-icon')).toBeInTheDocument();
    expect(screen.getByText('Button with Icon')).toBeInTheDocument();
  });

  it('renders in loading state when isLoading is true', () => {
    render(
      <ActionButton isLoading loadingText="Loading...">
        Submit
      </ActionButton>
    );
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(screen.queryByText('Submit')).not.toBeInTheDocument();
    // Check for spinner
    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('uses children as loading text when loadingText is not provided', () => {
    render(
      <ActionButton isLoading>
        Submit
      </ActionButton>
    );
    
    expect(screen.getByText('Submit')).toBeInTheDocument();
    // Check for spinner
    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('is disabled when isLoading is true', () => {
    render(
      <ActionButton isLoading>
        Submit
      </ActionButton>
    );
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('calls onClick handler when clicked', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();
    
    render(
      <ActionButton onClick={handleClick}>
        Click Me
      </ActionButton>
    );
    
    const button = screen.getByRole('button', { name: /click me/i });
    await user.click(button);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('does not call onClick handler when disabled', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();
    
    render(
      <ActionButton onClick={handleClick} disabled>
        Click Me
      </ActionButton>
    );
    
    const button = screen.getByRole('button', { name: /click me/i });
    await user.click(button);
    
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('renders as a child component when asChild is true', () => {
    render(
      <ActionButton asChild>
        <a href="/test">Link Button</a>
      </ActionButton>
    );
    
    const link = screen.getByRole('link', { name: /link button/i });
    expect(link).toBeInTheDocument();
    expect(link.getAttribute('href')).toBe('/test');
  });
});
