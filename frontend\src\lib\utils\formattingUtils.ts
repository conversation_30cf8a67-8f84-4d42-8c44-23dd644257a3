
import type { DelegationStatus, TaskStatus, EmployeeStatus } from '@/lib/types';

export function formatDelegationStatusForDisplay(status: DelegationStatus): string {
  switch (status) {
    case 'In_Progress':
      return 'In Progress';
    case 'No_details':
      return 'No Details';
    default:
      // For statuses like 'Planned', 'Confirmed', 'Completed', 'Cancelled'
      // which are single words, no transformation is needed beyond what might be
      // handled by CSS (e.g., text-transform: capitalize).
      // If they also had underscores, they'd be handled here.
      return status;
  }
}

// Placeholder for other status formatters if needed in the future
export function formatTaskStatusForDisplay(status: TaskStatus): string {
  // Example: transform 'In_Progress' to 'In Progress' if TaskStatus uses underscores
  return status.replace('_', ' ');
}

export function formatEmployeeStatusForDisplay(status: EmployeeStatus): string {
  // Example: transform 'On_Leave' to 'On Leave' if EmployeeStatus uses underscores
  return status.replace('_', ' ');
}
