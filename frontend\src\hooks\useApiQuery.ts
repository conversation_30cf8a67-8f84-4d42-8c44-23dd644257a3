'use client';

import { useState, useEffect, useCallback } from 'react';
import { ApiError } from '@/lib/types/api';

interface UseApiQueryOptions<T> {
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  enabled?: boolean;
  initialData?: T;
  refetchInterval?: number | false;
  refetchOnWindowFocus?: boolean;
  retry?: number | boolean;
  retryDelay?: number | ((attempt: number) => number);
}

interface UseApiQueryResult<T> {
  data: T | undefined;
  error: Error | null;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  refetch: () => Promise<void>;
  reset: () => void;
}

/**
 * Custom hook for API data fetching with loading, error, and retry functionality
 * 
 * @param queryFn - Function that returns a promise with the data
 * @param options - Configuration options
 * @returns Query result object
 */
function useApiQuery<T>(
  queryFn: () => Promise<T>,
  options: UseApiQueryOptions<T> = {}
): UseApiQueryResult<T> {
  const {
    onSuccess,
    onError,
    enabled = true,
    initialData,
    refetchInterval = false,
    refetchOnWindowFocus = true,
    retry = 0,
    retryDelay = 1000,
  } = options;

  const [data, setData] = useState<T | undefined>(initialData);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(enabled);
  const [retryCount, setRetryCount] = useState<number>(0);

  // Calculate retry delay based on attempt number
  const getRetryDelay = useCallback(
    (attempt: number): number => {
      if (typeof retryDelay === 'function') {
        return retryDelay(attempt);
      }
      // Exponential backoff with jitter
      const baseDelay = typeof retryDelay === 'number' ? retryDelay : 1000;
      const exponentialDelay = baseDelay * Math.pow(2, attempt);
      const jitter = Math.random() * 0.5 + 0.5; // 0.5-1.5 multiplier
      return Math.min(exponentialDelay * jitter, 30000); // Cap at 30 seconds
    },
    [retryDelay]
  );

  // Fetch data function
  const fetchData = useCallback(async () => {
    if (!enabled) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await queryFn();
      setData(result);
      setRetryCount(0);
      if (onSuccess) onSuccess(result);
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      
      // Log error details
      if (error instanceof ApiError) {
        console.error(`API Error (${error.status}):`, error.message);
        if (error.validationErrors) {
          console.error('Validation errors:', error.validationErrors);
        }
      } else {
        console.error('Query error:', error);
      }

      // Handle retries
      const maxRetries = typeof retry === 'boolean' ? (retry ? 3 : 0) : retry;
      if (retryCount < maxRetries) {
        const shouldRetry = !(error instanceof ApiError && error.status >= 400 && error.status < 500);
        
        if (shouldRetry) {
          const delay = getRetryDelay(retryCount);
          console.warn(`Retrying query (attempt ${retryCount + 1}/${maxRetries}) after ${delay}ms`);
          
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
            fetchData();
          }, delay);
          return;
        }
      }
      
      if (onError) onError(error);
    } finally {
      setIsLoading(false);
    }
  }, [enabled, queryFn, onSuccess, onError, retry, retryCount, getRetryDelay]);

  // Manual refetch function
  const refetch = useCallback(async () => {
    setRetryCount(0);
    await fetchData();
  }, [fetchData]);

  // Reset function
  const reset = useCallback(() => {
    setData(initialData);
    setError(null);
    setIsLoading(false);
    setRetryCount(0);
  }, [initialData]);

  // Initial fetch and refetch interval
  useEffect(() => {
    if (enabled) {
      fetchData();
    }

    // Set up refetch interval if specified
    let intervalId: NodeJS.Timeout | undefined;
    if (enabled && refetchInterval && typeof refetchInterval === 'number') {
      intervalId = setInterval(fetchData, refetchInterval);
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [enabled, fetchData, refetchInterval]);

  // Refetch on window focus
  useEffect(() => {
    if (!refetchOnWindowFocus || typeof window === 'undefined') return;

    const handleFocus = () => {
      if (enabled) fetchData();
    };

    window.addEventListener('focus', handleFocus);
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [enabled, fetchData, refetchOnWindowFocus]);

  return {
    data,
    error,
    isLoading,
    isError: !!error,
    isSuccess: !!data && !error,
    refetch,
    reset,
  };
}

export default useApiQuery;
