"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8162],{21876:(e,r,t)=>{t.r(r),t.d(r,{formatDateForApi:()=>l,formatDateForDisplay:()=>i,formatDateForInput:()=>s,isDateAfter:()=>f,isValidDateString:()=>h,isValidIsoDateString:()=>u,parseDateFromApi:()=>d});var o=t(83343),n=t(44861),a=t(73168),c=t(25399);let s=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"datetime-local";if(!e)return"";try{let t="string"==typeof e?(0,o.H)(e):e;if(!(0,n.f)(t))return console.warn("Invalid date for input formatting:",e),"";if("date"===r)return(0,a.GP)(t,"yyyy-MM-dd");return(0,a.GP)(t,"yyyy-MM-dd'T'HH:mm")}catch(e){return console.warn("Error formatting date for input:",e),""}},i=function(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"N/A";try{let t="string"==typeof e?(0,o.H)(e):e;if(!(0,n.f)(t))return"Invalid Date";return(0,a.GP)(t,r?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return console.warn("Error formatting date for display:",e),"Invalid Date"}},l=e=>{if(!e)return"";try{if((0,c.$)(e))return e.toISOString();let r=new Date(e);if(!(0,n.f)(r))return console.warn("Invalid date for API formatting:",e),"";return r.toISOString()}catch(e){return console.warn("Error formatting date for API:",e),""}},d=e=>{if(!e)return null;try{let r=(0,o.H)(e);return(0,n.f)(r)?r:null}catch(e){return console.warn("Error parsing date from API:",e),null}},u=e=>{if(!e)return!1;try{let r=(0,o.H)(e);return(0,n.f)(r)}catch(e){return!1}},h=e=>{if(!e)return!1;try{let r=new Date(e);return(0,n.f)(r)}catch(e){return!1}},f=(e,r)=>{if(!e||!r)return!1;try{let t="string"==typeof e?(0,o.H)(e):e,a="string"==typeof r?(0,o.H)(r):r;if(!(0,n.f)(t)||!(0,n.f)(a))return!1;return t>a}catch(e){return console.warn("Error comparing dates:",e),!1}}},30285:(e,r,t)=>{t.d(r,{$:()=>l,r:()=>i});var o=t(95155),n=t(12115),a=t(99708),c=t(74466),s=t(59434);let i=(0,c.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef((e,r)=>{let{className:t,variant:n,size:c,asChild:l=!1,...d}=e,u=l?a.DX:"button";return(0,o.jsx)(u,{className:(0,s.cn)(i({variant:n,size:c,className:t})),ref:r,...d})});l.displayName="Button"},52582:(e,r,t)=>{t.d(r,{Fd:()=>i,Fv:()=>w,Lv:()=>j,O5:()=>a,Oc:()=>p,Q3:()=>h,Qu:()=>P,UT:()=>T,VH:()=>N,Y$:()=>E,Y3:()=>M,Yv:()=>g,a9:()=>S,bV:()=>b,bt:()=>O,dC:()=>y,ih:()=>f,kw:()=>l,lC:()=>D,mh:()=>U,nz:()=>d,sI:()=>A,vq:()=>I,x1:()=>k,xY:()=>m,xn:()=>u,y_:()=>v});var o=t(21876);let n=null;function a(e){n=e}let c="localhost"!==window.location.hostname&&"127.0.0.1"!==window.location.hostname,s="http://localhost:3001/api";async function i(e,r){let t="".concat(s).concat(e),o={"Content-Type":"application/json",...null==r?void 0:r.headers};n&&(o.Authorization="Bearer ".concat(n));let a={...r,headers:o};try{if(console.debug("API Request: ".concat((null==a?void 0:a.method)||"GET"," ").concat(t)),null==a?void 0:a.body)try{console.debug("Request payload:",JSON.parse(a.body))}catch(e){console.debug("Request payload (non-JSON):",a.body)}let e=await fetch(t,a);if(!e.ok){var c,i;let r;try{r=await e.json()}catch(t){r={message:e.statusText||"HTTP error ".concat(e.status)}}if(console.error("API Error ".concat(e.status," at ").concat(t,":"),r),(null==r?void 0:r.status)==="error"&&(null==r?void 0:r.message)==="Validation failed"){console.error("Validation errors:",r.errors);let o="Validation failed";if(r.errors&&Array.isArray(r.errors)){let e=r.errors.map(e=>"".concat(e.path,": ").concat(e.message)).join("; ");e&&(o+=": ".concat(e))}let n=Error("API request to ".concat(t," failed with status ").concat(e.status,": ").concat(o));throw n.validationErrors=r.errors,n.receivedData=r.receivedData,n.status=e.status,n}if((null==r?void 0:r.errors)&&Array.isArray(r.errors)){console.error("Validation errors:",r.errors);let o=Error("API request to ".concat(t," failed with status ").concat(e.status,": ").concat(r.message||"Validation failed"));throw o.validationErrors=r.errors,o}if(null==r?void 0:r.details){let e=Error((null==r||null==(i=r.error)?void 0:i.message)||(null==r?void 0:r.message)||"Unknown server error");throw e.details=r.details,e}let o=(null==r||null==(c=r.error)?void 0:c.message)||(null==r?void 0:r.message)||"Unknown server error";throw console.error("API Error details: ".concat(o)),Error("API request to ".concat(t," failed with status ").concat(e.status,": ").concat(o))}if(204===e.status)return null;let r=await e.json();return console.debug("API Response from ".concat(t,":"),r),r}catch(o){if(o.message.startsWith("API request to"))throw console.error("API Error Propagated:",o.message),o;let e="Network error when trying to fetch ".concat(t,". The server might be down, unreachable, or there could be a CORS issue. Original error: ").concat(o.message||"Unknown fetch error");throw console.error("Fetch setup/network error details:",e,"Options:",r,"Original error object:",o),Error(e)}}console.log("[API Service] Using API base URL: ".concat(s)),console.log("[API Service] Environment: ".concat(c?"Docker":"Local"));let l=async()=>i("/vehicles"),d=async e=>i("/vehicles/".concat(e)),u=async e=>i("/vehicles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),h=async(e,r)=>i("/vehicles/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),f=async e=>i("/vehicles/".concat(e),{method:"DELETE"}),y=async(e,r)=>i("/vehicles/".concat(e,"/servicerecords"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),v=async e=>i("/vehicles/".concat(e,"/servicerecords")),m=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let r=new URLSearchParams;e.vehicleId&&r.append("vehicleId",e.vehicleId),e.startDate&&r.append("startDate",e.startDate),e.endDate&&r.append("endDate",e.endDate),e.limit&&r.append("limit",e.limit.toString()),e.offset&&r.append("offset",e.offset.toString()),e.sortBy&&r.append("sortBy",e.sortBy),e.sortOrder&&r.append("sortOrder",e.sortOrder);let t="/servicerecords".concat(r.toString()?"?".concat(r.toString()):"");console.info("Fetching service records",{url:t,params:e});let o=0;for(;o<=3;){let e=new AbortController,r=setTimeout(()=>e.abort(),1e4);try{let n=await fetch("".concat(s).concat(t),{signal:e.signal});if(clearTimeout(r),!n.ok&&400===n.status){let e=await n.json();throw console.warn("400 Bad Request error (not retrying)",{url:t,status:n.status,errorData:e}),Error(e.message||"Bad Request")}if(!n.ok&&n.status>=500&&o<3){console.warn("Server error (".concat(n.status,"), retrying..."),{url:t,attempt:o+1,status:n.status}),await new Promise(e=>setTimeout(e,1e3*(o+1))),o++;continue}if(!n.ok){let e=await n.json().catch(()=>({}));throw Error(e.message||"HTTP error ".concat(n.status))}let a=await n.json();if(!Array.isArray(a))throw console.error("Expected array but received:",a),Error("Invalid response format: expected an array of service records");return console.debug("Service records fetched successfully",{count:a.length}),a}catch(e){if(clearTimeout(r),e instanceof Error&&"AbortError"===e.name){if(console.warn("Request timed out after ".concat(1e4,"ms, retrying..."),{url:t,attempt:o+1}),o<3){o++;continue}throw Error("Request timed out after ".concat(3," attempts"))}if(e instanceof Error&&e.message.includes("Bad Request"))throw e;if(o<3)console.warn("Fetch attempt ".concat(o+1," failed, retrying..."),{url:t,error:e instanceof Error?e.message:"Unknown error"}),await new Promise(e=>setTimeout(e,1e3*(o+1))),o++;else throw console.error("All ".concat(3," retry attempts failed"),{url:t,error:e instanceof Error?e.message:"Unknown error"}),e}}throw Error("Failed to fetch service records after retries")}catch(r){return console.error("Service records fetch exception",{error:r instanceof Error?r.message:"Unknown error",params:e}),[]}},g=async(e,r)=>i("/vehicles/".concat(e,"/servicerecords/").concat(r),{method:"DELETE"}),p=async()=>i("/employees"),w=async e=>i("/employees/".concat(e)),b=async e=>{let r={...e};return r.hireDate&&(r.hireDate=(0,o.formatDateForApi)(r.hireDate)),""===r.contactEmail&&(r.contactEmail=null),""===r.contactPhone&&(r.contactPhone=null),""===r.contactMobile&&(r.contactMobile=null),i("/employees",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})},E=async(e,r)=>{let t={...r};return t.hireDate&&(t.hireDate=(0,o.formatDateForApi)(t.hireDate)),""===t.contactEmail&&(t.contactEmail=null),""===t.contactPhone&&(t.contactPhone=null),""===t.contactMobile&&(t.contactMobile=null),i("/employees/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})},P=async e=>i("/employees/".concat(e),{method:"DELETE"}),k=async()=>i("/tasks"),S=async e=>i("/tasks/".concat(e)),T=async e=>i("/tasks",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),D=async(e,r)=>i("/tasks/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),I=async e=>i("/tasks/".concat(e),{method:"DELETE"}),A=async()=>i("/delegations"),O=async e=>i("/delegations/".concat(e)),N=async e=>i("/delegations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),U=async(e,r)=>i("/delegations/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),M=async e=>i("/delegations/".concat(e),{method:"DELETE"}),j=async()=>{try{console.info("Starting to fetch enriched service records");let e=[],r=[];try{let t=await Promise.allSettled([l(),m()]);"fulfilled"===t[0].status?(e=Array.isArray(t[0].value)?t[0].value:[],console.debug("Successfully fetched ".concat(e.length," vehicles"))):console.error("Failed to fetch vehicles:",t[0].reason),"fulfilled"===t[1].status?(r=Array.isArray(t[1].value)?t[1].value:[],console.debug("Successfully fetched ".concat(r.length," service records"))):console.error("Failed to fetch service records:",t[1].reason)}catch(e){console.error("Error in parallel fetch operations:",e)}if(0===e.length&&0===r.length)throw Error("Failed to fetch both vehicles and service records");let t=new Map;e.forEach(e=>{e&&"number"==typeof e.id&&t.set(e.id,e)});let o=r.map(e=>{if(!e||void 0===e.vehicleId)return console.warn("Invalid service record found:",e),{id:(null==e?void 0:e.id)||"unknown",vehicleId:"unknown",date:(null==e?void 0:e.date)||new Date().toISOString(),odometer:(null==e?void 0:e.odometer)||0,servicePerformed:(null==e?void 0:e.servicePerformed)||["Unknown"],vehicleMake:"Unknown",vehicleModel:"Unknown",vehicleYear:0,vehiclePlateNumber:"Unknown"};let r=Number(e.vehicleId),o=t.get(r);return o?{...e,vehicleId:String(o.id),vehicleMake:o.make||"Unknown",vehicleModel:o.model||"Unknown",vehicleYear:o.year||0,vehiclePlateNumber:o.licensePlate||"Unknown"}:(console.warn("Vehicle with ID ".concat(e.vehicleId," not found for service record ").concat(e.id)),{...e,vehicleId:String(e.vehicleId),vehicleMake:"Unknown",vehicleModel:"Unknown",vehicleYear:0,vehiclePlateNumber:"Unknown"})}).sort((e,r)=>{try{let t=new Date(e.date).getTime(),o=new Date(r.date).getTime();if(isNaN(t)||isNaN(o))return 0;let n=o-t;if(0!==n)return n;let a="number"==typeof e.odometer?e.odometer:0;return("number"==typeof r.odometer?r.odometer:0)-a}catch(e){return console.error("Error sorting records:",e),0}});return console.info("Returning ".concat(o.length," enriched service records")),o}catch(e){return console.error("Error fetching enriched service records:",e),[]}}},59434:(e,r,t)=>{t.d(r,{cn:()=>a});var o=t(52596),n=t(39688);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,o.$)(r))}}}]);