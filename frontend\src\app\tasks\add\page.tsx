'use client';

import TaskForm from '@/components/tasks/TaskForm';
import {addTask as storeAddTask} from '@/lib/store'; // No change needed here for storeAddTask signature itself
import {useRouter} from 'next/navigation';
import type {TaskFormData} from '@/lib/schemas/taskSchemas';
import {PageHeader} from '@/components/ui/PageHeader';
import {ListPlus} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';

export default function AddTaskPage() {
	const router = useRouter();
	const {toast} = useToast();

	const handleSubmit = async (data: TaskFormData) => {
		try {
			// storeAddTask now handles assignment using assignedDriverId or assignedEmployeeId internally from data
			const newTask = await storeAddTask(data);

			toast({
				title: 'Task Added',
				description: `The task "${data.description.substring(
					0,
					30
				)}..." has been successfully created.`,
				variant: 'default',
			});
			router.push('/tasks');
		} catch (error) {
			console.error('Error adding task:', error);
			toast({
				title: 'Error',
				description: 'Failed to add task. Please try again.',
				variant: 'destructive',
			});
		}
	};

	return (
		<div className='space-y-6'>
			<PageHeader
				title='Add New Task'
				description='Enter the details for the new task or job.'
				icon={ListPlus}
			/>
			<TaskForm onSubmit={handleSubmit} isEditing={false} />
		</div>
	);
}
