import {Request, Response, NextFunction} from 'express';
import HttpError from '../utils/HttpError.js';

const errorHandler = (
	err: Error | HttpError,
	req: Request,
	res: Response,
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	next: NextFunction // Must be defined for Express to recognize it as an error handler
): void => {
	console.error('Error encountered:');
	console.error('Message:', err.message);
	if (err.stack) {
		console.error('Stack:', err.stack);
	}

	if (err instanceof HttpError) {
		res.status(err.status).json({
			status: 'error',
			statusCode: err.status,
			message: err.message,
		});
	} else {
		// Generic error
		res.status(500).json({
			status: 'error',
			statusCode: 500,
			message: 'An unexpected internal server error occurred.',
			// Optionally include err.message in development, but not in production for security
			// error: process.env.NODE_ENV === 'development' ? err.message : undefined,
		});
	}
};

export default errorHandler;
