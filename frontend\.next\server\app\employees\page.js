(()=>{var e={};e.id=8708,e.ids=[8708],e.modules={1814:(e,r,s)=>{"use strict";s.d(r,{O2:()=>l,Q:()=>i,gT:()=>n});var t=s(45880),a=s(74880);let l=t.k5(["Active","On Leave","Terminated","Inactive"]),i=t.k5(["driver","mechanic","administrator","office_staff","manager","service_advisor","technician","other"]),n=t.Ik({name:t.Yj().min(1,"Name is required"),fullName:t.Yj().min(1,"Full name is required").optional(),employeeId:t.Yj().min(1,"Employee ID (unique business ID) is required"),position:t.Yj().min(1,"Position/Title is required"),department:t.Yj().min(1,"Department is required"),contactInfo:t.Yj().min(1,"Primary contact (email or phone) is required").refine(e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)||/^[\d\s()+-]{7,20}$/.test(e),"Must be a valid email or phone number."),contactEmail:t.Yj().email("Invalid email address").optional().nullable().or(t.eu("")),contactPhone:t.Yj().optional().nullable().or(t.eu("")),contactMobile:t.Yj().optional().nullable().or(t.eu("")),hireDate:t.Yj().refine(e=>e&&!isNaN(Date.parse(e)),{message:"Invalid hire date"}),status:l.default("Active"),role:i.default("other"),availability:a.X.optional().nullable(),currentLocation:t.Yj().optional().or(t.eu("")),workingHours:t.Yj().optional().or(t.eu("")),assignedVehicleId:t.ai().int().positive().nullable().optional(),skills:t.YO(t.Yj()).optional().default([]),shiftSchedule:t.Yj().optional().or(t.eu("")),generalAssignments:t.YO(t.Yj()).optional().default([]),notes:t.Yj().optional().or(t.eu("")),profileImageUrl:t.Yj().url("Invalid URL for profile image").optional().or(t.eu("")),statusChangeReason:t.Yj().optional().nullable()})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15079:(e,r,s)=>{"use strict";s.d(r,{bq:()=>u,eb:()=>f,gC:()=>h,l6:()=>c,yv:()=>m});var t=s(60687),a=s(43210),l=s(22670),i=s(61662),n=s(89743),o=s(58450),d=s(4780);let c=l.bL;l.YJ;let m=l.WT,u=a.forwardRef(({className:e,children:r,...s},a)=>(0,t.jsxs)(l.l9,{ref:a,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[r,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=l.l9.displayName;let x=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(l.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})}));x.displayName=l.PP.displayName;let p=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(l.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}));p.displayName=l.wn.displayName;let h=a.forwardRef(({className:e,children:r,position:s="popper",...a},i)=>(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...a,children:[(0,t.jsx)(x,{}),(0,t.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,t.jsx)(p,{})]})}));h.displayName=l.UC.displayName,a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(l.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...r})).displayName=l.JU.displayName;let f=a.forwardRef(({className:e,children:r,...s},a)=>(0,t.jsxs)(l.q7,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(o.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:r})]}));f.displayName=l.q7.displayName,a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(l.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...r})).displayName=l.wv.displayName},17612:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21724:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},21820:e=>{"use strict";e.exports=require("os")},25915:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("CircleUserRound",[["path",{d:"M18 20a6 6 0 0 0-12 0",key:"1qehca"}],["circle",{cx:"12",cy:"10",r:"4",key:"1h16sb"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},26398:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28498:(e,r,s)=>{Promise.resolve().then(s.bind(s,58518))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},48973:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=s(65239),a=s(48088),l=s(88170),i=s.n(l),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(r,o);let d={children:["",{children:["employees",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,58518)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/employees/page",pathname:"/employees",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},52322:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>J});var t=s(60687),a=s(43210),l=s(85814),i=s.n(l),n=s(28399),o=s(77368),d=s(35265),c=s(41936),m=s(28840),u=s(50933),x=s(73992);let p=({children:e})=>{let[r,s]=(0,a.useState)([]),[l,i]=(0,a.useState)(!0),[n,o]=(0,a.useState)(null),d=(0,a.useCallback)(async()=>{l&&i(!0);try{try{console.info("Fetching enriched employees");let e=await (0,m.getEnrichedEmployees)();s(e),o(null);return}catch(r){console.warn("Failed to fetch enriched employees, falling back to regular endpoint:",r);let e=await (0,m.getEmployees)();s(e),o(null)}}catch(e){console.error("Error fetching employees:",e),o("Failed to fetch employees. Please try again.")}finally{i(!1)}},[l]),{isRefreshing:c,refresh:p,isConnected:h,socketTriggered:f}=(0,u.a)(d,[x.A.EMPLOYEE_CREATED,x.A.EMPLOYEE_UPDATED,x.A.EMPLOYEE_DELETED,x.A.REFRESH_EMPLOYEES],{interval:3e4,enabled:!0,immediate:!0,enableSocket:!0,enablePolling:!0,onError:e=>{console.error("Socket refresh error:",e)}});(0,a.useEffect)(()=>{f||d()},[d,f]);let g=async e=>{try{await (0,m.deleteEmployee)(e),s(r=>r.filter(r=>r.id!==e))}catch(e){throw console.error("Error deleting employee:",e),o("Failed to delete employee: "+(e.message||"Unknown error")),e}};return(0,t.jsx)(t.Fragment,{children:e({employees:r,loading:l,error:n,handleDelete:g,fetchEmployees:p,isRefreshing:c,isConnected:h,socketTriggered:f})})};var h=s(48041),f=s(89667),g=s(85726),y=s(30474),j=s(44493),b=s(68752),v=s(25915),N=s(17612),w=s(27247),k=s(53678),A=s(21724),E=s(26398),C=s(24920),S=s(29333),q=s(8760),P=s(96834),_=s(35950),M=s(4780),R=s(76869),L=s(58261);let D=e=>{switch(e){case"Active":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"On Leave":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Terminated":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},Y=e=>{if(!e)return"";switch(e){case"On_Shift":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Off_Shift":default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20";case"On_Break":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Busy":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20"}};function T({employee:e}){let r="assignedVehicleDetails"in e;return(0,t.jsxs)(j.Zp,{className:"overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60",children:[(0,t.jsx)(j.aR,{className:"p-5",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"relative w-12 h-12 rounded-full overflow-hidden bg-muted flex items-center justify-center ring-2 ring-primary/30",children:e.profileImageUrl?(0,t.jsx)(y.default,{src:e.profileImageUrl,alt:e.fullName||e.name,layout:"fill",objectFit:"cover","data-ai-hint":"employee photo"}):(0,t.jsx)(v.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(j.ZB,{className:"text-xl font-semibold text-primary",children:e.fullName||e.name}),(0,t.jsxs)(j.BT,{className:"text-sm text-muted-foreground",children:[e.position," (",e.role?e.role.charAt(0).toUpperCase()+e.role.slice(1).replace("_"," "):"N/A",")"]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col items-end gap-1",children:[(0,t.jsx)(P.E,{className:(0,M.cn)("text-xs py-1 px-2 font-semibold",D(e.status)),children:e.status}),"driver"===e.role&&e.availability&&(0,t.jsx)(P.E,{className:(0,M.cn)("text-xs py-1 px-2 font-semibold",Y(e.availability)),children:e.availability?.replace("_"," ")})]})]})}),(0,t.jsxs)(j.Wu,{className:"p-5 flex-grow flex flex-col",children:[(0,t.jsx)(_.w,{className:"my-3 bg-border/50"}),(0,t.jsxs)("div",{className:"space-y-2.5 text-sm text-foreground flex-grow",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Department: "}),(0,t.jsx)("strong",{className:"font-semibold",children:e.department})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(w.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Email: "}),(0,t.jsx)("strong",{className:"font-semibold",children:e.contactEmail})]})]}),e.contactMobile&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(k.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Mobile: "}),(0,t.jsx)("strong",{className:"font-semibold",children:e.contactMobile})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(A.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Hire Date: "}),(0,t.jsx)("strong",{className:"font-semibold",children:(0,R.GP)((0,L.H)(e.hireDate),"MMM d, yyyy")})]})]}),"driver"===e.role&&(0,t.jsxs)(t.Fragment,{children:[e.currentLocation&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(E.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Location: "}),(0,t.jsx)("strong",{className:"font-semibold",children:e.currentLocation})]})]}),e.assignedVehicleId&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(C.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Vehicle: "}),(0,t.jsx)("strong",{className:"font-semibold",children:r&&e.assignedVehicleDetails?(0,t.jsxs)(t.Fragment,{children:[e.assignedVehicleDetails.make," ",e.assignedVehicleDetails.model," (",e.assignedVehicleDetails.year,")",e.assignedVehicleDetails.licensePlate&&(0,t.jsxs)("span",{className:"ml-1 text-xs text-muted-foreground",children:["[",e.assignedVehicleDetails.licensePlate,"]"]}),e.assignedVehicleDetails.color&&(0,t.jsxs)("span",{className:"ml-1 text-xs text-muted-foreground",children:["• ",e.assignedVehicleDetails.color]})]}):(0,t.jsxs)(t.Fragment,{children:["Vehicle ID: ",e.assignedVehicleId]})})]})]})]}),e.skills&&e.skills.length>0&&(0,t.jsxs)("div",{className:"flex items-start pt-1",children:[(0,t.jsx)(S.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Skills: "}),(0,t.jsx)("p",{className:"text-xs font-semibold leading-tight",children:e.skills.join(", ")})]})]})]})]}),(0,t.jsx)(j.wL,{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,t.jsx)(b.r,{actionType:"tertiary",className:"w-full",icon:(0,t.jsx)(q.A,{className:"h-4 w-4"}),asChild:!0,children:(0,t.jsx)(i(),{href:`/employees/${e.id}`,children:"View Details"})})})]})}var F=s(80013),V=s(15079),O=s(1814),I=s(29867),z=s(52027),U=s(95758);function G(){return(0,t.jsxs)("div",{className:"overflow-hidden shadow-lg flex flex-col h-full bg-card border-border/60 rounded-lg",children:[(0,t.jsxs)("div",{className:"p-5 flex-grow flex flex-col",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)(g.E,{className:"h-8 w-3/5 bg-muted/50"}),(0,t.jsx)(g.E,{className:"h-5 w-1/4 bg-muted/50 rounded-full"})]}),(0,t.jsx)(g.E,{className:"h-4 w-1/2 mb-1 bg-muted/50"}),(0,t.jsx)(g.E,{className:"h-4 w-1/3 mb-3 bg-muted/50"}),(0,t.jsx)(g.E,{className:"h-px w-full my-3 bg-border/50"}),(0,t.jsx)("div",{className:"space-y-2.5 flex-grow",children:[void 0,void 0,void 0].map((e,r)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.E,{className:"mr-2.5 h-5 w-5 rounded-full bg-muted/50"}),(0,t.jsx)(g.E,{className:"h-5 w-2/3 bg-muted/50"})]},r))})]}),(0,t.jsx)("div",{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,t.jsx)(g.E,{className:"h-10 w-full bg-muted/50"})})]})}let B=()=>{let[e,r]=(0,a.useState)([]),[s,l]=(0,a.useState)([]),[m,u]=(0,a.useState)(""),[x,g]=(0,a.useState)("all"),[y,v]=(0,a.useState)("all"),[N,w]=(0,a.useState)("all"),{toast:k}=(0,I.dj)(),A=Array.from(new Set(e.map(e=>e.department))).sort();return(0,a.useEffect)(()=>{let r=[...e],s=m.toLowerCase();"all"!==x&&(r=r.filter(e=>e.status===x)),"all"!==y&&(r=r.filter(e=>e.department===y)),"all"!==N&&(r=r.filter(e=>e.role===N)),s&&(r=r.filter(e=>(e.name||e.fullName||"").toLowerCase().includes(s)||(e.position||"").toLowerCase().includes(s)||(e.department||"").toLowerCase().includes(s)||(e.role||"").toLowerCase().includes(s)||(e.contactEmail||"").toLowerCase().includes(s)||e.skills&&e.skills.some(e=>(e||"").toLowerCase().includes(s))||"driver"===e.role&&e.availability&&(e.availability||"").toLowerCase().includes(s)||"driver"===e.role&&e.currentLocation&&(e.currentLocation||"").toLowerCase().includes(s))),l(r)},[m,e,x,y,N]),(0,t.jsx)(p,{children:({employees:e,loading:l,error:p,fetchEmployees:E,isRefreshing:C,isConnected:S,socketTriggered:q})=>{(0,a.useEffect)(()=>{l||p||r([...e].sort((e,r)=>(e.name||e.fullName||"").localeCompare(r.name||r.fullName||"")))},[e,l,p]);let P=async()=>{try{await E(),k({title:"Refresh Complete",description:"Employee list has been updated."})}catch(e){console.error("Error refreshing employees:",e),k({title:"Refresh Failed",description:"Could not update employee list. Please try again.",variant:"destructive"})}},_=m||"all"!==x||"all"!==y||"all"!==N;return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)(h.z,{title:"Manage Employees",description:"Oversee employee profiles, roles, status, and assignments.",icon:n.A,children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(b.r,{actionType:"tertiary",onClick:P,isLoading:l||C,icon:(0,t.jsx)(o.A,{className:`h-4 w-4 ${C||q?"animate-spin":""}`}),loadingText:C?"Updating...":"Refresh",children:!(C||q)&&"Refresh"}),(0,t.jsx)(b.r,{actionType:"primary",icon:(0,t.jsx)(d.A,{className:"h-4 w-4"}),asChild:!0,children:(0,t.jsx)(i(),{href:"/employees/new",children:"Add New Employee"})})]})}),(0,t.jsxs)(j.Zp,{className:"mb-6 p-4 shadow relative",children:[(C||q)&&!l&&(0,t.jsxs)("div",{className:"absolute right-4 top-4 text-xs flex items-center text-muted-foreground",children:[(0,t.jsx)(o.A,{className:"h-3 w-3 mr-1 animate-spin"}),q?"Real-time update...":"Refreshing..."]}),!l&&(0,t.jsxs)("div",{className:"absolute right-24 top-4 text-xs inline-flex items-center sm:right-24",children:[(0,t.jsx)("div",{className:`h-2 w-2 rounded-full mr-2 ${S?"bg-green-500":"bg-gray-400"}`}),(0,t.jsx)("span",{className:"text-muted-foreground text-xs",children:S?"Real-time active":"Real-time inactive"})]}),(0,t.jsx)(j.Wu,{className:"pt-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end",children:[(0,t.jsxs)("div",{className:"relative lg:col-span-1",children:[(0,t.jsx)(F.J,{htmlFor:"search-employees",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Search Employees"}),(0,t.jsx)(c.A,{className:"absolute left-3 top-[calc(50%_-_0.5rem_+_12px)] h-5 w-5 text-muted-foreground"}),(0,t.jsx)(f.p,{id:"search-employees",type:"text",placeholder:"Name, Role, Dept, Skill...",value:m,onChange:e=>u(e.target.value),className:"pl-10 w-full"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(F.J,{htmlFor:"status-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Status"}),(0,t.jsxs)(V.l6,{value:x,onValueChange:g,children:[(0,t.jsx)(V.bq,{id:"status-filter",children:(0,t.jsx)(V.yv,{placeholder:"All Statuses"})}),(0,t.jsxs)(V.gC,{children:[(0,t.jsx)(V.eb,{value:"all",children:"All Statuses"}),O.O2.options.map(e=>(0,t.jsx)(V.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(F.J,{htmlFor:"department-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Department"}),(0,t.jsxs)(V.l6,{value:y,onValueChange:v,children:[(0,t.jsx)(V.bq,{id:"department-filter",children:(0,t.jsx)(V.yv,{placeholder:"All Departments"})}),(0,t.jsxs)(V.gC,{children:[(0,t.jsx)(V.eb,{value:"all",children:"All Departments"}),A.map(e=>(0,t.jsx)(V.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(F.J,{htmlFor:"role-filter",className:"block text-sm font-medium text-muted-foreground mb-1",children:"Filter by Role"}),(0,t.jsxs)(V.l6,{value:N,onValueChange:w,children:[(0,t.jsx)(V.bq,{id:"role-filter",children:(0,t.jsx)(V.yv,{placeholder:"All Roles"})}),(0,t.jsxs)(V.gC,{children:[(0,t.jsx)(V.eb,{value:"all",children:"All Roles"}),O.Q.options.map(e=>(0,t.jsx)(V.eb,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1).replace("_"," ")},e))]})]})]})]})})]}),(0,t.jsx)(z.gO,{isLoading:l,error:p,data:s,onRetry:E,loadingComponent:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[void 0,void 0,void 0].map((e,r)=>(0,t.jsx)(G,{},r))}),emptyComponent:(0,t.jsxs)("div",{className:"text-center py-12 bg-card rounded-lg shadow-md",children:[(0,t.jsx)(n.A,{className:"mx-auto h-16 w-16 text-muted-foreground mb-6"}),(0,t.jsx)("h3",{className:"text-2xl font-semibold text-foreground mb-2",children:_?"No Employees Match Your Filters":"No Employees Registered Yet"}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2 mb-6 max-w-md mx-auto",children:_?"Try adjusting your search or filter criteria.":"Get started by adding an employee."}),!_&&(0,t.jsx)(b.r,{actionType:"primary",size:"lg",icon:(0,t.jsx)(d.A,{className:"h-4 w-4"}),asChild:!0,children:(0,t.jsx)(i(),{href:"/employees/add",children:"Add Your First Employee"})})]}),children:e=>(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,t.jsx)(T,{employee:e},e.id))})})]})}})};function J(){return(0,t.jsx)(U.A,{children:(0,t.jsx)(B,{})})}},53678:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58518:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\employees\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74880:(e,r,s)=>{"use strict";s.d(r,{X:()=>t});let t=s(45880).k5(["On_Shift","Off_Shift","On_Break","Busy"])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},91706:(e,r,s)=>{Promise.resolve().then(s.bind(s,52322))},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,3744,1658,5880,2729,474,8464,8141,3983,7982],()=>s(48973));module.exports=t})();