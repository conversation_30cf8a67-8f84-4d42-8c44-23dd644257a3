import { z } from 'zod';
const vehicleBaseSchema = {
    make: z
        .string({
        required_error: 'Vehicle make is required',
        invalid_type_error: 'Vehicle make must be a string',
    })
        .min(1, 'Make cannot be empty'),
    model: z
        .string({
        required_error: 'Vehicle model is required',
        invalid_type_error: 'Vehicle model must be a string',
    })
        .min(1, 'Model cannot be empty'),
    year: z
        .number({
        required_error: 'Vehicle year is required',
        invalid_type_error: 'Vehicle year must be a number',
    })
        .int('Year must be an integer')
        .min(1900, 'Year must be at least 1900')
        .max(new Date().getFullYear() + 1, `Year cannot be greater than ${new Date().getFullYear() + 1}`),
    vin: z
        .string({
        required_error: 'VIN is required',
        invalid_type_error: 'VIN must be a string',
    })
        .min(1, 'VIN cannot be empty')
        .regex(/^[A-HJ-NPR-Z0-9]{17}$/, 'VIN must be a valid 17-character VIN format'),
    licensePlate: z
        .string({
        required_error: 'License plate is required',
        invalid_type_error: 'License plate must be a string',
    })
        .min(1, 'License plate cannot be empty'),
    ownerName: z
        .string({
        required_error: 'Owner name is required',
        invalid_type_error: 'Owner name must be a string',
    })
        .min(1, 'Owner name cannot be empty'),
    ownerContact: z
        .string({
        required_error: 'Owner contact is required',
        invalid_type_error: 'Owner contact must be a string',
    })
        .min(1, 'Owner contact cannot be empty')
        .refine((val) => {
        const phoneRegex = /^[\d\s\+\-\(\)]{7,20}$/;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return phoneRegex.test(val) || emailRegex.test(val);
    }, 'Owner contact must be a valid phone number or email address'),
    color: z.string().optional().nullable(),
    initialOdometer: z.number().int().min(0, 'Odometer cannot be negative').optional().nullable(),
    imageUrl: z.string().url('Invalid image URL').optional().nullable().or(z.literal('')),
};
export const vehicleCreateSchema = z.object(vehicleBaseSchema);
export const vehicleUpdateSchema = z.object({
    ...Object.entries(vehicleBaseSchema).reduce((acc, [key, validator]) => ({
        ...acc,
        [key]: validator.optional(),
    }), {}),
});
export const vehicleIdSchema = z.object({
    id: z
        .string()
        .refine((val) => !isNaN(parseInt(val, 10)), {
        message: 'ID must be a valid number',
    })
        .transform((val) => parseInt(val, 10)),
});
//# sourceMappingURL=vehicle.schema.js.map