import * as employeeModel from '../models/employee.model.js';
import { EmployeeStatus as PrismaEmployeeStatus, } from '../generated/prisma/index.js';
import { emitEmployeeChange, SOCKET_EVENTS } from '../services/socketService.js';
import prisma from '../models/index.js';
import logger from '../utils/logger.js';
export const createEmployee = async (req, res) => {
    try {
        const validatedBody = req.body;
        const employeeData = {
            name: validatedBody.name,
            fullName: validatedBody.fullName || validatedBody.name,
            role: validatedBody.role,
            employeeId: validatedBody.employeeId,
            contactInfo: validatedBody.contactInfo,
            contactEmail: validatedBody.contactEmail,
            contactPhone: validatedBody.contactPhone,
            contactMobile: validatedBody.contactMobile,
            position: validatedBody.position,
            department: validatedBody.department,
            hireDate: validatedBody.hireDate
                ? new Date(validatedBody.hireDate)
                : null,
            status: validatedBody.status
                ? validatedBody.status
                : PrismaEmployeeStatus.Active, // Use imported Prisma enum
            availability: validatedBody.availability,
            currentLocation: validatedBody.currentLocation,
            workingHours: validatedBody.workingHours,
            skills: validatedBody.skills,
            shiftSchedule: validatedBody.shiftSchedule,
            generalAssignments: validatedBody.generalAssignments,
            notes: validatedBody.notes,
            profileImageUrl: validatedBody.profileImageUrl,
        };
        // Handle assignedVehicle relation
        if (validatedBody.assignedVehicleId !== null &&
            validatedBody.assignedVehicleId !== undefined) {
            employeeData.assignedVehicle = {
                connect: { id: Number(validatedBody.assignedVehicleId) },
            };
        }
        if (employeeData.status) {
            employeeData.statusHistory = {
                create: [
                    {
                        status: employeeData.status,
                        reason: 'Initial status set at creation',
                    },
                ],
            };
        }
        const newEmployee = await employeeModel.createEmployee(employeeData);
        if (newEmployee) {
            emitEmployeeChange(SOCKET_EVENTS.EMPLOYEE_CREATED, newEmployee);
            res.status(201).json(newEmployee);
        }
        else {
            res.status(400).json({
                message: 'Could not create employee, input may be invalid or another error occurred.',
            });
        }
    }
    catch (error) {
        if (error.message.includes('already exists') ||
            error.message.includes('not found')) {
            res.status(409).json({ message: error.message });
        }
        else {
            res
                .status(500)
                .json({ message: 'Error creating employee', error: error.message });
        }
    }
};
export const getAllEmployees = async (req, res) => {
    try {
        const employees = await employeeModel.getAllEmployees();
        res.status(200).json(employees);
    }
    catch (error) {
        res
            .status(500)
            .json({ message: 'Error fetching employees', error: error.message });
    }
};
export const getEmployeeById = async (req, res) => {
    try {
        const id = parseInt(req.params.id, 10);
        if (isNaN(id)) {
            res.status(400).json({ message: 'Invalid employee ID format' });
            return;
        }
        const employee = await employeeModel.getEmployeeById(id);
        if (employee) {
            res.status(200).json(employee);
        }
        else {
            res.status(404).json({ message: 'Employee not found' });
        }
    }
    catch (error) {
        res
            .status(500)
            .json({ message: 'Error fetching employee', error: error.message });
    }
};
export const updateEmployee = async (req, res) => {
    try {
        const id = parseInt(req.params.id, 10);
        if (isNaN(id)) {
            res.status(400).json({ message: 'Invalid employee ID format' });
            return;
        }
        const validatedBody = req.body;
        const { statusChangeReason, assignedVehicleId: rawAssignedVehicleId, ...updatePayloadFromSchema } = validatedBody;
        const employeeUpdateData = {
            ...updatePayloadFromSchema,
        };
        if (updatePayloadFromSchema.hireDate) {
            employeeUpdateData.hireDate = new Date(updatePayloadFromSchema.hireDate);
        }
        if (updatePayloadFromSchema.status) {
            employeeUpdateData.status =
                updatePayloadFromSchema.status; // Use imported Prisma enum
        }
        // Handle assignedVehicle relation
        if (validatedBody.hasOwnProperty('assignedVehicleId')) {
            // Check if assignedVehicleId was explicitly passed
            if (rawAssignedVehicleId === null) {
                employeeUpdateData.assignedVehicle = { disconnect: true };
            }
            else if (rawAssignedVehicleId !== undefined) {
                employeeUpdateData.assignedVehicle = {
                    connect: { id: Number(rawAssignedVehicleId) },
                };
            }
        }
        const updatedEmployee = await employeeModel.updateEmployee(id, employeeUpdateData, statusChangeReason);
        if (updatedEmployee) {
            emitEmployeeChange(SOCKET_EVENTS.EMPLOYEE_UPDATED, updatedEmployee);
            res.status(200).json(updatedEmployee);
        }
        else {
            const exists = await employeeModel.getEmployeeById(id);
            if (!exists) {
                res.status(404).json({ message: 'Employee not found to update' });
            }
            else {
                res.status(400).json({
                    message: 'Could not update employee, input may be invalid or another error occurred.',
                });
            }
        }
    }
    catch (error) {
        if (error.message.includes('already exists') ||
            error.message.includes('not found')) {
            res.status(409).json({ message: error.message });
        }
        else {
            res
                .status(500)
                .json({ message: 'Error updating employee', error: error.message });
        }
    }
};
export const deleteEmployee = async (req, res) => {
    try {
        const id = parseInt(req.params.id, 10);
        if (isNaN(id)) {
            res.status(400).json({ message: 'Invalid employee ID format' });
            return;
        }
        const deletedEmployee = await employeeModel.deleteEmployee(id);
        if (deletedEmployee) {
            emitEmployeeChange(SOCKET_EVENTS.EMPLOYEE_DELETED, { id });
            res.status(200).json({
                message: 'Employee deleted successfully',
                employee: deletedEmployee,
            });
        }
        else {
            res
                .status(404)
                .json({ message: 'Employee not found or could not be deleted' });
        }
    }
    catch (error) {
        res
            .status(500)
            .json({ message: 'Error deleting employee', error: error.message });
    }
};
/**
 * Get all employees enriched with vehicle information
 * This is used for the /api/employees/enriched endpoint
 */
export const getEnrichedEmployees = async (req, res) => {
    try {
        logger.info('Fetching enriched employees', {
            query: req.query,
            ip: req.ip,
            userAgent: req.headers['user-agent'],
        });
        // Get all employees
        const employees = await employeeModel.getAllEmployees();
        // If no employees, return empty array
        if (!employees || !Array.isArray(employees) || employees.length === 0) {
            logger.info('No employees found for enrichment');
            res.status(200).json([]);
            return;
        }
        // Get all vehicle IDs assigned to employees
        const vehicleIds = employees
            .filter((emp) => emp.assignedVehicleId !== null && emp.assignedVehicleId !== undefined)
            .map((emp) => emp.assignedVehicleId);
        // If no vehicles assigned, return employees as is
        if (vehicleIds.length === 0) {
            logger.info('No vehicles assigned to employees');
            res.status(200).json(employees);
            return;
        }
        // Fetch vehicles
        const vehicles = await prisma.vehicle.findMany({
            where: {
                id: {
                    in: vehicleIds,
                },
            },
        });
        // Create lookup map for faster access
        const vehicleMap = new Map(vehicles.map((vehicle) => [vehicle.id, vehicle]));
        // Enrich employees with vehicle information
        const enrichedEmployees = employees.map((employee) => {
            // If employee has no assigned vehicle, return as is
            if (!employee.assignedVehicleId) {
                return {
                    ...employee,
                    assignedVehicleDetails: null,
                };
            }
            const vehicle = vehicleMap.get(employee.assignedVehicleId);
            // If vehicle not found, return employee with null vehicle details
            if (!vehicle) {
                return {
                    ...employee,
                    assignedVehicleDetails: null,
                };
            }
            // Return employee with vehicle details
            return {
                ...employee,
                assignedVehicleDetails: {
                    id: vehicle.id,
                    make: vehicle.make,
                    model: vehicle.model,
                    year: vehicle.year,
                    licensePlate: vehicle.licensePlate,
                    color: vehicle.color,
                },
            };
        });
        logger.debug('Enriched employees successfully', {
            count: enrichedEmployees.length,
            query: req.query,
        });
        // Return the enriched employees
        res.status(200).json(enrichedEmployees);
    }
    catch (error) {
        logger.error('Error fetching enriched employees', {
            error: error.message,
            stack: error.stack,
            query: req.query,
        });
        // Return an empty array instead of an error to prevent frontend from getting stuck
        res.status(200).json([]);
    }
};
//# sourceMappingURL=employee.controller.js.map