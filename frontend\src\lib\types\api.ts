/**
 * API response and error types
 */

// Generic API response type
export interface ApiResponse<T> {
	status: 'success' | 'error';
	data?: T;
	message?: string;
	errors?: ApiValidationError[];
	receivedData?: any;
}

// Validation error type
export interface ApiValidationError {
	path: string;
	message: string;
	code?: string;
	received?: any;
}

/**
 * API Error Types for categorization and handling
 */
export enum ApiErrorType {
	// Network-related errors
	NETWORK_ERROR = 'network_error',
	TIMEOUT = 'timeout',

	// Server errors
	SERVER_ERROR = 'server_error',
	RATE_LIMIT = 'rate_limit',

	// Client errors
	CLIENT_ERROR = 'client_error',
	VALIDATION_ERROR = 'validation_error',
	AUTHENTICATION_ERROR = 'authentication_error',
	AUTHORIZATION_ERROR = 'authorization_error',
	NOT_FOUND = 'not_found',

	// Parsing errors
	PARSING_ERROR = 'parsing_error',

	// Other
	UNKNOWN = 'unknown',
}

/**
 * Enhanced API error with additional context and helper methods
 */
export class ApiError extends Error {
	status: number;
	endpoint?: string;
	validationErrors?: ApiValidationError[];
	receivedData?: any;
	details?: any;
	errorType: ApiErrorType;
	retryable: boolean;

	constructor(
		message: string,
		options: {
			status: number;
			endpoint?: string;
			validationErrors?: ApiValidationError[];
			receivedData?: any;
			details?: any;
			errorType?: ApiErrorType;
		}
	) {
		super(message);
		this.name = 'ApiError';
		this.status = options.status;
		this.endpoint = options.endpoint;
		this.validationErrors = options.validationErrors;
		this.receivedData = options.receivedData;
		this.details = options.details;

		// Determine error type based on status code if not provided
		this.errorType = options.errorType || this.determineErrorType();

		// Determine if error is retryable
		this.retryable = this.isRetryable();

		// Ensure instanceof works correctly
		Object.setPrototypeOf(this, ApiError.prototype);
	}

	// Legacy constructor support for backward compatibility
	static create(
		message: string,
		status: number,
		options?: {
			validationErrors?: ApiValidationError[];
			receivedData?: any;
			details?: any;
			errorType?: ApiErrorType;
		}
	): ApiError {
		return new ApiError(message, {
			status,
			validationErrors: options?.validationErrors,
			receivedData: options?.receivedData,
			details: options?.details,
			errorType: options?.errorType,
		});
	}

	/**
	 * Determine error type based on status code
	 */
	private determineErrorType(): ApiErrorType {
		if (this.status >= 500) {
			return ApiErrorType.SERVER_ERROR;
		}

		if (this.status === 429) {
			return ApiErrorType.RATE_LIMIT;
		}

		if (this.status === 401) {
			return ApiErrorType.AUTHENTICATION_ERROR;
		}

		if (this.status === 403) {
			return ApiErrorType.AUTHORIZATION_ERROR;
		}

		if (this.status === 400 && this.isValidationError()) {
			return ApiErrorType.VALIDATION_ERROR;
		}

		if (this.status >= 400 && this.status < 500) {
			return ApiErrorType.CLIENT_ERROR;
		}

		return ApiErrorType.UNKNOWN;
	}

	/**
	 * Determine if error is retryable
	 */
	private isRetryable(): boolean {
		return (
			this.errorType === ApiErrorType.SERVER_ERROR ||
			this.errorType === ApiErrorType.NETWORK_ERROR ||
			this.errorType === ApiErrorType.TIMEOUT ||
			this.errorType === ApiErrorType.RATE_LIMIT
		);
	}

	/**
	 * Check if this is a validation error
	 */
	isValidationError(): boolean {
		return (
			this.status === 400 &&
			Array.isArray(this.validationErrors) &&
			this.validationErrors.length > 0
		);
	}

	/**
	 * Get a user-friendly formatted message for display
	 */
	getFormattedMessage(): string {
		if (this.isValidationError()) {
			const errorDetails = this.validationErrors!.map(
				(err) => `${err.path}: ${err.message}`
			).join('; ');

			return `Validation failed: ${errorDetails}`;
		}

		switch (this.errorType) {
			case ApiErrorType.NETWORK_ERROR:
				return 'Network error: Unable to connect to the server. Please check your internet connection.';

			case ApiErrorType.TIMEOUT:
				return 'Request timed out. The server is taking too long to respond.';

			case ApiErrorType.RATE_LIMIT:
				return 'Too many requests. Please try again later.';

			case ApiErrorType.AUTHENTICATION_ERROR:
				return 'Authentication required. Please log in and try again.';

			case ApiErrorType.AUTHORIZATION_ERROR:
				return 'You do not have permission to perform this action.';

			case ApiErrorType.NOT_FOUND:
				return `Resource not found: ${
					this.endpoint || 'The requested resource'
				} could not be found.`;

			case ApiErrorType.PARSING_ERROR:
				return 'Could not parse the server response. Please try again or contact support.';

			case ApiErrorType.SERVER_ERROR:
				return `Server error (${this.status}): ${this.message}. Please try again later.`;

			default:
				return this.message;
		}
	}

	/**
	 * Get technical details for logging
	 */
	getTechnicalDetails(): string {
		const details = [
			`Status: ${this.status}`,
			`Type: ${this.errorType}`,
			`Message: ${this.message}`,
		];

		if (this.details) {
			details.push(`Details: ${JSON.stringify(this.details)}`);
		}

		if (this.validationErrors) {
			details.push(
				`Validation Errors: ${JSON.stringify(this.validationErrors)}`
			);
		}

		return details.join('\n');
	}
}

// Request options type
export interface ApiRequestOptions extends RequestInit {
	retries?: number;
	retryDelay?: number;
	timeout?: number;
	skipRetryLogging?: boolean;
}

// Pagination parameters
export interface PaginationParams {
	page?: number;
	limit?: number;
	offset?: number;
}

// Sorting parameters
export interface SortingParams {
	sortBy?: string;
	sortOrder?: 'asc' | 'desc';
}

// Filter parameters (generic)
export interface FilterParams {
	[key: string]: string | number | boolean | undefined;
}

// Combined query parameters
export interface QueryParams
	extends PaginationParams,
		SortingParams,
		FilterParams {}

// Pagination response
export interface PaginationResponse {
	total: number;
	limit: number;
	offset: number;
	pages: number;
	currentPage: number;
}
