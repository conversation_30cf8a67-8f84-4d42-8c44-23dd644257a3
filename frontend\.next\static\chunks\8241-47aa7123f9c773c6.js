"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8241],{12543:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},15452:(e,t,n)=>{n.d(t,{G$:()=>J,Hs:()=>x,UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>er,hJ:()=>et,l9:()=>X});var r=n(12115),o=n(85185),a=n(6101),l=n(46081),i=n(61285),s=n(5845),u=n(19178),d=n(25519),c=n(34378),p=n(28905),f=n(63655),m=n(92293),g=n(93795),v=n(38168),y=n(99708),h=n(95155),N="Dialog",[D,x]=(0,l.A)(N),[O,R]=D(N),w=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:N});return(0,h.jsx)(O,{scope:t,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};w.displayName=N;var C="DialogTrigger",I=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=R(C,n),i=(0,a.s)(t,l.triggerRef);return(0,h.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":Z(l.open),...r,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});I.displayName=C;var b="DialogPortal",[M,j]=D(b,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,l=R(b,t);return(0,h.jsx)(M,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,h.jsx)(p.C,{present:n||l.open,children:(0,h.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};A.displayName=b;var E="DialogOverlay",k=r.forwardRef((e,t)=>{let n=j(E,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=R(E,e.__scopeDialog);return a.modal?(0,h.jsx)(p.C,{present:r||a.open,children:(0,h.jsx)(_,{...o,ref:t})}):null});k.displayName=E;var T=(0,y.TL)("DialogOverlay.RemoveScroll"),_=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(E,n);return(0,h.jsx)(g.A,{as:T,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(f.sG.div,{"data-state":Z(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),F="DialogContent",P=r.forwardRef((e,t)=>{let n=j(F,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=R(F,e.__scopeDialog);return(0,h.jsx)(p.C,{present:r||a.open,children:a.modal?(0,h.jsx)(U,{...o,ref:t}):(0,h.jsx)(L,{...o,ref:t})})});P.displayName=F;var U=r.forwardRef((e,t)=>{let n=R(F,e.__scopeDialog),l=r.useRef(null),i=(0,a.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,h.jsx)(S,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,t)=>{let n=R(F,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,h.jsx)(S,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),S=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=R(F,n),p=r.useRef(null),f=(0,a.s)(t,p);return(0,m.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,h.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Z(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(Y,{titleId:c.titleId}),(0,h.jsx)($,{contentRef:p,descriptionId:c.descriptionId})]})]})}),W="DialogTitle",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(W,n);return(0,h.jsx)(f.sG.h2,{id:o.titleId,...r,ref:t})});G.displayName=W;var q="DialogDescription",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(q,n);return(0,h.jsx)(f.sG.p,{id:o.descriptionId,...r,ref:t})});B.displayName=q;var H="DialogClose",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=R(H,n);return(0,h.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}V.displayName=H;var z="DialogTitleWarning",[J,K]=(0,l.q)(z,{contentName:F,titleName:W,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,n=K(z),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:n}=e,o=K("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(a))},[a,t,n]),null},Q=w,X=I,ee=A,et=k,en=P,er=G,eo=B,ea=V},28905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(12115),o=n(6101),a=n(52712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),s=r.useRef(null),u=r.useRef(e),d=r.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(s.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=s.current,n=u.current;if(n!==e){let r=d.current,o=i(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),u.current=e}},[e,p]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=i(s.current).includes(e.animationName);if(e.target===o&&r&&(p("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=i(s.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,l(e)},[])}}(t),s="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),u=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||l.isPresent?r.cloneElement(s,{ref:u}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},50286:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},77223:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}}]);