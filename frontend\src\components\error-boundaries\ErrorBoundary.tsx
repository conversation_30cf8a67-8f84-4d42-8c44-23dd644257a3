'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  title?: string;
  description?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  resetLabel?: string;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Generic Error Boundary component
 * Catches errors in its child component tree and displays a fallback UI
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true, 
      error 
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Update state with error info for detailed reporting
    this.setState({
      errorInfo
    });
    
    // Log the error
    console.error('Error caught by ErrorBoundary:', error);
    console.error('Component stack:', errorInfo.componentStack);
    
    // Call onError prop if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // In a production app, you would send this to a monitoring service
    // Example: errorReportingService.captureError(error, errorInfo);
  }

  handleRetry = (): void => {
    // Reset the error boundary state to trigger a re-render
    this.setState({ 
      hasError: false, 
      error: null,
      errorInfo: null
    });
  };

  render(): ReactNode {
    const { 
      title = 'Something went wrong', 
      description = 'An unexpected error occurred.',
      resetLabel = 'Try Again'
    } = this.props;

    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // Otherwise, use the default error UI
      return (
        <Alert variant="destructive" className="my-4">
          <AlertTriangle className="h-4 w-4 mr-2" />
          <AlertTitle className="text-lg font-semibold">{title}</AlertTitle>
          <AlertDescription className="mt-2">
            <p className="mb-2">
              {this.state.error?.message || description}
            </p>
            {process.env.NODE_ENV !== 'production' && this.state.errorInfo && (
              <details className="mt-2 text-xs">
                <summary>Error details</summary>
                <pre className="mt-2 whitespace-pre-wrap overflow-auto max-h-[200px] p-2 bg-slate-100 dark:bg-slate-900 rounded">
                  {this.state.error?.stack}
                  {'\n\nComponent Stack:\n'}
                  {this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={this.handleRetry}
              className="mt-4"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              {resetLabel}
            </Button>
          </AlertDescription>
        </Alert>
      );
    }

    // If there's no error, render the children
    return this.props.children;
  }
}

export default ErrorBoundary;
