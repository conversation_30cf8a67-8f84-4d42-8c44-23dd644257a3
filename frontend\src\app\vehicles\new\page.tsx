'use client';

import React, {useState} from 'react';
import {useRouter} from 'next/navigation';
import VehicleForm from '@/components/vehicles/VehicleForm';
import {addVehicle} from '@/lib/store';
import {VehicleFormData} from '@/lib/schemas/vehicleSchemas';
import type {Vehicle} from '@/lib/types';
import {PageHeader} from '@/components/ui/PageHeader';
import {Car} from 'lucide-react';
import {useToast} from '@/hooks/use-toast'; // Assuming you have a useToast hook

const AddVehiclePage = () => {
	const router = useRouter();
	const {toast} = useToast();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleSubmit = async (data: VehicleFormData) => {
		setIsLoading(true);
		setError(null);
		try {
			// Construct the object to strictly match the expected type for addVehicle
			const vehicleDataForStore: Omit<
				Vehicle,
				'id' | 'serviceHistory' | 'createdAt' | 'updatedAt'
			> = {
				make: data.make,
				model: data.model,
				year: data.year,
				vin: data.vin,
				licensePlate: data.licensePlate,
				ownerName: data.ownerName,
				ownerContact: data.ownerContact,
				initialOdometer:
					data.initialOdometer === undefined ? 0 : data.initialOdometer, // Now definitely a number
				// Optional fields from VehicleFormData that are also optional in Vehicle type
				color: data.color,
				imageUrl: data.imageUrl,
			};

			const newVehicle = await addVehicle(vehicleDataForStore);
			toast({
				title: 'Vehicle Added',
				description: `${newVehicle.make} ${newVehicle.model} has been successfully added.`,
				variant: 'default', // or "success"
			});
			router.push('/vehicles'); // Navigate to the list page after successful addition
		} catch (err: any) {
			console.error('Failed to add vehicle:', err);
			setError(
				err.message || 'An unexpected error occurred. Please try again.'
			);
			toast({
				title: 'Error Adding Vehicle',
				description:
					err.message ||
					'Could not add the vehicle. Please check the details and try again.',
				variant: 'destructive',
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className='container mx-auto py-8 space-y-8'>
			<PageHeader
				title='Add New Vehicle'
				description='Enter the details for your new vehicle.'
				icon={Car}
			/>
			{error && (
				<p className='text-red-500 bg-red-100 p-3 rounded-md'>Error: {error}</p>
			)}
			<VehicleForm
				onSubmit={handleSubmit}
				isEditing={false}
				isLoading={isLoading}
			/>
		</div>
	);
};

export default AddVehiclePage;
