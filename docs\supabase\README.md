# Supabase Integration Documentation

This directory contains documentation for integrating Supabase with the WorkHub
application.

## Table of Contents

1. [Integration Plan](integration-plan.md) - Detailed plan and current progress
2. [Getting Started](getting-started.md) - Step-by-step guide for setup
3. [CLI Commands](cli-commands.md) - Reference for all CLI commands
4. [Architecture](architecture.md) - Implementation details and design decisions

## Overview

Supabase is an open-source Firebase alternative providing PostgreSQL database,
authentication, APIs, and storage. This integration allows our application to
use Supabase's managed PostgreSQL database while maintaining compatibility with
our existing Prisma ORM.

## Quick Start

1. **Create a Supabase Project**:

   - Sign up at [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project credentials

2. **Link Your Project**:

   ```bash
   cd backend
   npm run supabase:login
   npm run supabase:link
   ```

3. **Sync Database & Start App**:
   ```bash
   npm run supabase:pull  # or supabase:push
   npm run db:supabase    # switch to Supabase
   npm run dev            # start application
   ```

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Prisma with Supabase Guide](https://supabase.com/docs/guides/integrations/prisma)

## Security Guidelines

- Never commit sensitive information (API keys, passwords)
- Add `supabase/config.json` to your `.gitignore` file
- Use environment variables for sensitive information
- Keep the service role key secure

## Maintenance

- Keep dependencies updated
- Synchronize schema changes between Prisma and Supabase
- Apply security updates promptly
- Test thoroughly when updating
