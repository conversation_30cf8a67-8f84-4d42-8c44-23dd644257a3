import { 
  getHealthStatus, 
  getPerformanceMetrics, 
  getRecentErrors,
  getMockHealthStatus,
  getMockPerformanceMetrics,
  getMockRecentErrors,
  HealthResponse,
  PerformanceMetrics,
  PaginatedResponse,
  ErrorLogEntry
} from '../adminService';
import { withRetry } from '../utils/apiUtils';
import { fetchData } from '../apiService';

// Mock dependencies
jest.mock('../apiService', () => ({
  fetchData: jest.fn(),
}));

jest.mock('../utils/apiUtils', () => ({
  withRetry: jest.fn((fn) => fn()),
}));

describe('Admin Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getHealthStatus', () => {
    it('should call fetchData with the correct endpoint', async () => {
      const mockResponse: HealthResponse = getMockHealthStatus();
      (fetchData as jest.Mock).mockResolvedValue(mockResponse);

      const result = await getHealthStatus();

      expect(withRetry).toHaveBeenCalled();
      expect(fetchData).toHaveBeenCalledWith('/admin/health');
      expect(result).toEqual(mockResponse);
    });

    it('should propagate errors from fetchData', async () => {
      const error = new Error('Network error');
      (fetchData as jest.Mock).mockRejectedValue(error);
      (withRetry as jest.Mock).mockImplementation((fn) => fn());

      await expect(getHealthStatus()).rejects.toThrow('Network error');
    });
  });

  describe('getPerformanceMetrics', () => {
    it('should call fetchData with the correct endpoint', async () => {
      const mockResponse: PerformanceMetrics = getMockPerformanceMetrics();
      (fetchData as jest.Mock).mockResolvedValue(mockResponse);

      const result = await getPerformanceMetrics();

      expect(withRetry).toHaveBeenCalled();
      expect(fetchData).toHaveBeenCalledWith('/admin/performance');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getRecentErrors', () => {
    it('should call fetchData with default parameters', async () => {
      const mockResponse: PaginatedResponse<ErrorLogEntry> = getMockRecentErrors();
      (fetchData as jest.Mock).mockResolvedValue(mockResponse);

      const result = await getRecentErrors();

      expect(withRetry).toHaveBeenCalled();
      expect(fetchData).toHaveBeenCalledWith('/admin/errors?page=1&limit=10');
      expect(result).toEqual(mockResponse);
    });

    it('should call fetchData with custom parameters', async () => {
      const mockResponse: PaginatedResponse<ErrorLogEntry> = getMockRecentErrors({
        page: 2,
        limit: 5,
        level: 'ERROR'
      });
      (fetchData as jest.Mock).mockResolvedValue(mockResponse);

      const result = await getRecentErrors({
        page: 2,
        limit: 5,
        level: 'ERROR'
      });

      expect(withRetry).toHaveBeenCalled();
      expect(fetchData).toHaveBeenCalledWith('/admin/errors?page=2&limit=5&level=ERROR');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Mock Functions', () => {
    it('getMockHealthStatus should return a valid HealthResponse', () => {
      const result = getMockHealthStatus();
      
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('components.database');
      expect(result).toHaveProperty('components.supabase');
      expect(result).toHaveProperty('config');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('version');
      expect(result).toHaveProperty('uptime');
    });

    it('getMockPerformanceMetrics should return a valid PerformanceMetrics', () => {
      const result = getMockPerformanceMetrics();
      
      expect(result).toHaveProperty('cacheHitRate.indexHitRate');
      expect(result).toHaveProperty('cacheHitRate.tableHitRate');
      expect(result).toHaveProperty('connectionCount');
      expect(result).toHaveProperty('activeQueries');
      expect(result).toHaveProperty('avgQueryTime');
      expect(result).toHaveProperty('timestamp');
    });

    it('getMockRecentErrors should return a valid paginated response', () => {
      const result = getMockRecentErrors();
      
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('pagination');
      expect(result.pagination).toHaveProperty('page');
      expect(result.pagination).toHaveProperty('limit');
      expect(result.pagination).toHaveProperty('total');
      expect(result.pagination).toHaveProperty('totalPages');
      expect(Array.isArray(result.data)).toBe(true);
    });

    it('getMockRecentErrors should filter by level when specified', () => {
      const result = getMockRecentErrors({ level: 'ERROR' });
      
      // All returned errors should have level ERROR
      expect(result.data.every(error => error.level === 'ERROR')).toBe(true);
    });
  });
});
