-- =====================================================
-- EMERGENCY SECURITY MIGRATION - Row Level Security
-- Version: 3.0 - Pure Supabase Authentication Strategy
-- Date: 2025-01-23
-- Purpose: Enable comprehensive RLS on all tables
-- =====================================================

-- CRITICAL: This migration implements emergency security measures
-- All anonymous access will be COMPLETELY BLOCKED after this migration

BEGIN;

-- =====================================================
-- STEP 1: REVOKE ALL ANONYMOUS ACCESS
-- =====================================================

-- Revoke all privileges from anonymous role on all tables
DO $$
DECLARE
    table_record RECORD;
BEGIN
    -- Loop through all tables in public schema
    FOR table_record IN
        SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    LOOP
        -- Revoke all privileges from anon role
        EXECUTE format('REVOKE ALL ON TABLE public.%I FROM anon', table_record.tablename);
        EXECUTE format('REVOKE ALL ON TABLE public.%I FROM public', table_record.tablename);

        RAISE NOTICE 'Revoked anonymous access from table: %', table_record.tablename;
    END LOOP;
END $$;

-- =====================================================
-- STEP 2: ENABLE RLS ON ALL EXISTING TABLES
-- =====================================================

-- Enable RLS on Employee table
ALTER TABLE public."Employee" ENABLE ROW LEVEL SECURITY;

-- Enable RLS on Vehicle table
ALTER TABLE public."Vehicle" ENABLE ROW LEVEL SECURITY;

-- Enable RLS on ServiceRecord table
ALTER TABLE public."ServiceRecord" ENABLE ROW LEVEL SECURITY;

-- Enable RLS on Task table
ALTER TABLE public."Task" ENABLE ROW LEVEL SECURITY;

-- Enable RLS on Delegation table
ALTER TABLE public."Delegation" ENABLE ROW LEVEL SECURITY;

-- Enable RLS on any other existing tables
DO $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN
        SELECT tablename FROM pg_tables
        WHERE schemaname = 'public'
        AND tablename NOT IN ('Employee', 'Vehicle', 'ServiceRecord', 'Task', 'Delegation')
    LOOP
        EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', table_record.tablename);
        RAISE NOTICE 'Enabled RLS on table: %', table_record.tablename;
    END LOOP;
END $$;

-- =====================================================
-- STEP 3: CREATE SECURITY HELPER FUNCTIONS
-- =====================================================

-- Function to get current user's role from user_profiles
CREATE OR REPLACE FUNCTION auth.get_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT role FROM public.user_profiles
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION auth.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        SELECT role IN ('ADMIN', 'SUPER_ADMIN')
        FROM public.user_profiles
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is manager or above
CREATE OR REPLACE FUNCTION auth.is_manager_or_above()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        SELECT role IN ('MANAGER', 'ADMIN', 'SUPER_ADMIN')
        FROM public.user_profiles
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's employee record
CREATE OR REPLACE FUNCTION auth.get_user_employee_id()
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT employee_id::INTEGER
        FROM public.user_profiles
        WHERE id = auth.uid()
        AND employee_id IS NOT NULL
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 4: CREATE RLS POLICIES FOR EMPLOYEE TABLE
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "employee_select_policy" ON public."Employee";
DROP POLICY IF EXISTS "employee_insert_policy" ON public."Employee";
DROP POLICY IF EXISTS "employee_update_policy" ON public."Employee";
DROP POLICY IF EXISTS "employee_delete_policy" ON public."Employee";

-- Employees can view their own record + managers can view their team + admins can view all
CREATE POLICY "employee_select_policy" ON public."Employee"
    FOR SELECT USING (
        -- User can see their own employee record
        id = auth.get_user_employee_id()
        OR
        -- Managers and above can see all employees
        auth.is_manager_or_above()
    );

-- Only admins can insert new employees
CREATE POLICY "employee_insert_policy" ON public."Employee"
    FOR INSERT WITH CHECK (auth.is_admin());

-- Employees can update their own basic info, managers can update their team, admins can update all
CREATE POLICY "employee_update_policy" ON public."Employee"
    FOR UPDATE USING (
        -- User can update their own basic info (non-sensitive fields)
        (id = auth.get_user_employee_id() AND auth.get_user_role() = 'USER')
        OR
        -- Managers and above can update employee records
        auth.is_manager_or_above()
    );

-- Only admins can delete employees
CREATE POLICY "employee_delete_policy" ON public."Employee"
    FOR DELETE USING (auth.is_admin());

-- =====================================================
-- STEP 5: CREATE RLS POLICIES FOR VEHICLE TABLE
-- =====================================================

DROP POLICY IF EXISTS "vehicle_select_policy" ON public."Vehicle";
DROP POLICY IF EXISTS "vehicle_insert_policy" ON public."Vehicle";
DROP POLICY IF EXISTS "vehicle_update_policy" ON public."Vehicle";
DROP POLICY IF EXISTS "vehicle_delete_policy" ON public."Vehicle";

-- All authenticated users can view vehicles (needed for service operations)
CREATE POLICY "vehicle_select_policy" ON public."Vehicle"
    FOR SELECT USING (auth.uid() IS NOT NULL);

-- Only managers and above can insert vehicles
CREATE POLICY "vehicle_insert_policy" ON public."Vehicle"
    FOR INSERT WITH CHECK (auth.is_manager_or_above());

-- Only managers and above can update vehicles
CREATE POLICY "vehicle_update_policy" ON public."Vehicle"
    FOR UPDATE USING (auth.is_manager_or_above());

-- Only admins can delete vehicles
CREATE POLICY "vehicle_delete_policy" ON public."Vehicle"
    FOR DELETE USING (auth.is_admin());

-- =====================================================
-- STEP 6: CREATE RLS POLICIES FOR SERVICE RECORD TABLE
-- =====================================================

DROP POLICY IF EXISTS "service_record_select_policy" ON public."ServiceRecord";
DROP POLICY IF EXISTS "service_record_insert_policy" ON public."ServiceRecord";
DROP POLICY IF EXISTS "service_record_update_policy" ON public."ServiceRecord";
DROP POLICY IF EXISTS "service_record_delete_policy" ON public."ServiceRecord";

-- Users can view service records they created + managers can view all
CREATE POLICY "service_record_select_policy" ON public."ServiceRecord"
    FOR SELECT USING (
        -- User can see service records they created
        "employeeId" = auth.get_user_employee_id()
        OR
        -- Managers and above can see all service records
        auth.is_manager_or_above()
    );

-- All authenticated users can create service records
CREATE POLICY "service_record_insert_policy" ON public."ServiceRecord"
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
        AND "employeeId" = auth.get_user_employee_id()
    );

-- Users can update their own service records, managers can update all
CREATE POLICY "service_record_update_policy" ON public."ServiceRecord"
    FOR UPDATE USING (
        "employeeId" = auth.get_user_employee_id()
        OR
        auth.is_manager_or_above()
    );

-- Only managers and above can delete service records
CREATE POLICY "service_record_delete_policy" ON public."ServiceRecord"
    FOR DELETE USING (auth.is_manager_or_above());

-- =====================================================
-- STEP 7: CREATE RLS POLICIES FOR TASK TABLE
-- =====================================================

DROP POLICY IF EXISTS "task_select_policy" ON public."Task";
DROP POLICY IF EXISTS "task_insert_policy" ON public."Task";
DROP POLICY IF EXISTS "task_update_policy" ON public."Task";
DROP POLICY IF EXISTS "task_delete_policy" ON public."Task";

-- Users can view tasks assigned to them + managers can view all
CREATE POLICY "task_select_policy" ON public."Task"
    FOR SELECT USING (
        -- User can see tasks assigned to them
        "assignedTo" = auth.get_user_employee_id()
        OR
        -- User can see tasks they created
        "createdBy" = auth.get_user_employee_id()
        OR
        -- Managers and above can see all tasks
        auth.is_manager_or_above()
    );

-- All authenticated users can create tasks
CREATE POLICY "task_insert_policy" ON public."Task"
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
        AND "createdBy" = auth.get_user_employee_id()
    );

-- Users can update tasks assigned to them, managers can update all
CREATE POLICY "task_update_policy" ON public."Task"
    FOR UPDATE USING (
        "assignedTo" = auth.get_user_employee_id()
        OR
        "createdBy" = auth.get_user_employee_id()
        OR
        auth.is_manager_or_above()
    );

-- Only managers and above can delete tasks
CREATE POLICY "task_delete_policy" ON public."Task"
    FOR DELETE USING (auth.is_manager_or_above());

-- =====================================================
-- STEP 8: CREATE RLS POLICIES FOR DELEGATION TABLE
-- =====================================================

DROP POLICY IF EXISTS "delegation_select_policy" ON public."Delegation";
DROP POLICY IF EXISTS "delegation_insert_policy" ON public."Delegation";
DROP POLICY IF EXISTS "delegation_update_policy" ON public."Delegation";
DROP POLICY IF EXISTS "delegation_delete_policy" ON public."Delegation";

-- Users can view delegations they created or are assigned to + managers can view all
CREATE POLICY "delegation_select_policy" ON public."Delegation"
    FOR SELECT USING (
        -- User can see delegations they created
        "employeeId" = auth.get_user_employee_id()
        OR
        -- Managers and above can see all delegations
        auth.is_manager_or_above()
    );

-- All authenticated users can create delegations
CREATE POLICY "delegation_insert_policy" ON public."Delegation"
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
        AND "employeeId" = auth.get_user_employee_id()
    );

-- Users can update their own delegations, managers can update all
CREATE POLICY "delegation_update_policy" ON public."Delegation"
    FOR UPDATE USING (
        "employeeId" = auth.get_user_employee_id()
        OR
        auth.is_manager_or_above()
    );

-- Only managers and above can delete delegations
CREATE POLICY "delegation_delete_policy" ON public."Delegation"
    FOR DELETE USING (auth.is_manager_or_above());

-- =====================================================
-- STEP 9: CREATE RLS POLICIES FOR USER_PROFILES TABLE
-- =====================================================

-- User profiles table policies (already created in previous migration)
-- Ensuring they exist and are correct

DROP POLICY IF EXISTS "user_profiles_select_own" ON public.user_profiles;
DROP POLICY IF EXISTS "user_profiles_update_own" ON public.user_profiles;
DROP POLICY IF EXISTS "user_profiles_select_admin" ON public.user_profiles;
DROP POLICY IF EXISTS "user_profiles_update_admin" ON public.user_profiles;
DROP POLICY IF EXISTS "user_profiles_insert_admin" ON public.user_profiles;

-- Users can view their own profile
CREATE POLICY "user_profiles_select_own" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile (limited fields)
CREATE POLICY "user_profiles_update_own" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Admins can view all profiles
CREATE POLICY "user_profiles_select_admin" ON public.user_profiles
    FOR SELECT USING (auth.is_admin());

-- Admins can update all profiles
CREATE POLICY "user_profiles_update_admin" ON public.user_profiles
    FOR UPDATE USING (auth.is_admin());

-- Admins can insert new profiles
CREATE POLICY "user_profiles_insert_admin" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.is_admin());

-- =====================================================
-- STEP 10: CREATE EMERGENCY ACCESS LOG POLICIES
-- =====================================================

-- Emergency access log policies (if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'emergency_access_log') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "emergency_log_select_admin" ON public.emergency_access_log;
        DROP POLICY IF EXISTS "emergency_log_insert_all" ON public.emergency_access_log;

        -- Only admins can view access logs
        CREATE POLICY "emergency_log_select_admin" ON public.emergency_access_log
            FOR SELECT USING (auth.is_admin());

        -- All authenticated users can insert logs (for audit trail)
        CREATE POLICY "emergency_log_insert_all" ON public.emergency_access_log
            FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

        RAISE NOTICE 'Created RLS policies for emergency_access_log table';
    END IF;
END $$;

-- =====================================================
-- STEP 11: GRANT NECESSARY PERMISSIONS TO AUTHENTICATED ROLE
-- =====================================================

-- Grant SELECT permissions to authenticated users on tables they need to access
GRANT SELECT ON public."Employee" TO authenticated;
GRANT SELECT ON public."Vehicle" TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public."ServiceRecord" TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public."Task" TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public."Delegation" TO authenticated;
GRANT SELECT, UPDATE ON public.user_profiles TO authenticated;

-- Grant permissions on emergency log if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'emergency_access_log') THEN
        GRANT SELECT, INSERT ON public.emergency_access_log TO authenticated;
    END IF;
END $$;

-- =====================================================
-- STEP 12: CREATE SECURITY AUDIT FUNCTION
-- =====================================================

-- Function to audit RLS status
CREATE OR REPLACE FUNCTION public.audit_rls_status()
RETURNS TABLE(
    table_name TEXT,
    rls_enabled BOOLEAN,
    policy_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.tablename::TEXT,
        CASE WHEN t.rowsecurity THEN true ELSE false END as rls_enabled,
        COALESCE(p.policy_count, 0) as policy_count
    FROM pg_tables t
    LEFT JOIN (
        SELECT
            schemaname,
            tablename,
            COUNT(*) as policy_count
        FROM pg_policies
        GROUP BY schemaname, tablename
    ) p ON t.schemaname = p.schemaname AND t.tablename = p.tablename
    WHERE t.schemaname = 'public'
    ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 13: FINAL SECURITY VERIFICATION
-- =====================================================

-- Verify RLS is enabled on all tables
DO $$
DECLARE
    table_record RECORD;
    rls_count INTEGER := 0;
    total_count INTEGER := 0;
BEGIN
    FOR table_record IN
        SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public'
    LOOP
        total_count := total_count + 1;
        IF table_record.rowsecurity THEN
            rls_count := rls_count + 1;
            RAISE NOTICE 'RLS ENABLED: %', table_record.tablename;
        ELSE
            RAISE WARNING 'RLS NOT ENABLED: %', table_record.tablename;
        END IF;
    END LOOP;

    RAISE NOTICE 'EMERGENCY RLS MIGRATION SUMMARY:';
    RAISE NOTICE 'Tables with RLS enabled: % / %', rls_count, total_count;

    IF rls_count = total_count THEN
        RAISE NOTICE '✅ ALL TABLES SECURED WITH RLS';
    ELSE
        RAISE WARNING '❌ SOME TABLES NOT SECURED - MANUAL INTERVENTION REQUIRED';
    END IF;
END $$;

-- Log the migration completion
INSERT INTO public.emergency_access_log (
    user_id,
    action,
    details,
    ip_address
) VALUES (
    NULL, -- System action
    'EMERGENCY_RLS_MIGRATION_COMPLETED',
    '{"version": "3.0", "tables_secured": "all", "anonymous_access": "revoked"}',
    '127.0.0.1'::INET
) ON CONFLICT DO NOTHING;

COMMIT;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================
-- 🚨 EMERGENCY SECURITY MIGRATION COMPLETED
-- ✅ Row Level Security enabled on all tables
-- ✅ Anonymous access completely revoked
-- ✅ Role-based access policies implemented
-- ✅ Security audit functions created
-- =====================================================
