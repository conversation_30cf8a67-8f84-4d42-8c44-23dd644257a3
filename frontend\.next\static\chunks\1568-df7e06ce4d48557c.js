"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1568],{12543:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28328:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},36521:(e,t,a)=>{a.d(t,{A:()=>f});var r=a(95155),l=a(12115),s=a(35695),i=a(62177),d=a(90221),n=a(55594);let o=n.Ik({make:n.Yj().min(1,"Make is required"),model:n.Yj().min(1,"Model is required"),year:n.au.number().min(1900,"Year must be 1900 or later").max(new Date().getFullYear()+1,"Year cannot be more than ".concat(new Date().getFullYear()+1)),vin:n.Yj().min(1,"VIN is required").regex(/^[A-HJ-NPR-Z0-9]{17}$/,"VIN must be a valid 17-character format (only capital letters A-H, J-N, P-R, Z and numbers 0-9)"),licensePlate:n.Yj().min(1,"License plate is required"),ownerName:n.Yj().min(1,"Owner name is required"),ownerContact:n.Yj().min(1,"Owner contact is required"),color:n.Yj().optional(),initialOdometer:n.au.number().min(0,"Odometer reading cannot be negative").optional(),imageUrl:n.Yj().url("Invalid image URL").optional().or(n.eu(""))});var c=a(30285),m=a(62523),x=a(85057),p=a(66695),u=a(87481),h=a(12543);let f=e=>{var t;let{onSubmit:a,initialData:n={},isEditing:f=!1,submitButtonText:g=f?"Save Changes":"Create Vehicle",isLoading:j=!1}=e,v=(0,s.useRouter)(),{toast:N}=(0,u.dj)(),{register:y,handleSubmit:w,formState:{errors:b,isSubmitting:O},reset:C,setValue:k}=(0,i.mN)({resolver:(0,d.u)(o),defaultValues:{make:(null==n?void 0:n.make)||"",model:(null==n?void 0:n.model)||"",year:(null==n?void 0:n.year)||new Date().getFullYear(),vin:(null==n?void 0:n.vin)||"",licensePlate:(null==n?void 0:n.licensePlate)||"",ownerName:(null==n?void 0:n.ownerName)||"",ownerContact:(null==n?void 0:n.ownerContact)||"",color:(null==n?void 0:n.color)||"",initialOdometer:null!=(t=null==n?void 0:n.initialOdometer)?t:0,imageUrl:(null==n?void 0:n.imageUrl)||""}});(0,l.useEffect)(()=>{if(n){var e;k("make",n.make||""),k("model",n.model||""),k("year",n.year||new Date().getFullYear()),k("vin",n.vin||""),k("licensePlate",n.licensePlate||""),k("ownerName",n.ownerName||""),k("ownerContact",n.ownerContact||""),k("color",n.color||""),k("initialOdometer",null!=(e=n.initialOdometer)?e:0),k("imageUrl",n.imageUrl||"")}},[n,k]);let A=async e=>{await a(e),N({title:f?"Vehicle Updated":"Vehicle Added",description:"".concat(e.make," ").concat(e.model," has been successfully ").concat(f?"updated":"added","."),variant:"default"})};return(0,r.jsxs)(p.Zp,{className:"max-w-2xl mx-auto",children:[(0,r.jsxs)(p.aR,{children:[(0,r.jsx)(p.ZB,{children:f?"Edit Vehicle":"Add New Vehicle"}),(0,r.jsx)(p.BT,{children:f?"Update the details of the vehicle.":"Enter the details for the new vehicle."})]}),(0,r.jsxs)("form",{onSubmit:w(A),children:[(0,r.jsxs)(p.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"make",children:"Make"}),(0,r.jsx)(m.p,{id:"make",...y("make"),placeholder:"e.g., Toyota"}),b.make&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:b.make.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"model",children:"Model"}),(0,r.jsx)(m.p,{id:"model",...y("model"),placeholder:"e.g., Camry"}),b.model&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:b.model.message})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"year",children:"Year"}),(0,r.jsx)(m.p,{id:"year",type:"number",...y("year"),placeholder:"e.g., 2023"}),b.year&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:b.year.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"vin",children:"VIN"}),(0,r.jsx)(m.p,{id:"vin",...y("vin"),placeholder:"Vehicle Identification Number"}),b.vin&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:b.vin.message}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"VIN must be exactly 17 characters, using capital letters A-H, J-N, P-R, Z and numbers 0-9."})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"licensePlate",children:"License Plate"}),(0,r.jsx)(m.p,{id:"licensePlate",...y("licensePlate"),placeholder:"e.g., ABC-123"}),b.licensePlate&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:b.licensePlate.message})]}),(0,r.jsx)("hr",{className:"my-6"}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"ownerName",children:"Owner Name"}),(0,r.jsx)(m.p,{id:"ownerName",...y("ownerName"),placeholder:"e.g., John Doe"}),b.ownerName&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:b.ownerName.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"ownerContact",children:"Owner Contact (Email/Phone)"}),(0,r.jsx)(m.p,{id:"ownerContact",...y("ownerContact"),placeholder:"e.g., <EMAIL> or 555-1234"}),b.ownerContact&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:b.ownerContact.message})]}),(0,r.jsx)("hr",{className:"my-6"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"color",children:"Color (Optional)"}),(0,r.jsx)(m.p,{id:"color",...y("color"),placeholder:"e.g., Blue"}),b.color&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:b.color.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"initialOdometer",children:"Initial Odometer (Optional)"}),(0,r.jsx)(m.p,{id:"initialOdometer",type:"number",...y("initialOdometer"),placeholder:"e.g., 100"}),b.initialOdometer&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:b.initialOdometer.message})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(x.J,{htmlFor:"imageUrl",children:"Image URL (Optional)"}),(0,r.jsx)(m.p,{id:"imageUrl",...y("imageUrl"),placeholder:"https://example.com/image.png"}),b.imageUrl&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:b.imageUrl.message})]})]}),(0,r.jsxs)(p.wL,{className:"flex justify-end space-x-3 pt-6",children:[(0,r.jsxs)(c.$,{type:"button",variant:"outline",onClick:()=>v.back(),disabled:O||j,children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),(0,r.jsx)(c.$,{type:"submit",disabled:O||j,children:O||j?"Processing...":g})]})]})]})}},62523:(e,t,a)=>{a.d(t,{p:()=>i});var r=a(95155),l=a(12115),s=a(59434);let i=l.forwardRef((e,t)=>{let{className:a,type:l,...i}=e;return(0,r.jsx)("input",{type:l,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...i})});i.displayName="Input"},66695:(e,t,a)=>{a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>d,wL:()=>m});var r=a(95155),l=a(12115),s=a(59434);let i=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...l})});i.displayName="Card";let d=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",a),...l})});d.displayName="CardHeader";let n=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",a),...l})});n.displayName="CardTitle";let o=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",a),...l})});o.displayName="CardDescription";let c=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",a),...l})});c.displayName="CardContent";let m=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",a),...l})});m.displayName="CardFooter"},85057:(e,t,a)=>{a.d(t,{J:()=>o});var r=a(95155),l=a(12115),s=a(40968),i=a(74466),d=a(59434);let n=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)(s.b,{ref:t,className:(0,d.cn)(n(),a),...l})});o.displayName=s.b.displayName},87481:(e,t,a)=>{a.d(t,{dj:()=>x});var r=a(12115);let l=0,s=new Map,i=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},d=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?i(a):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},n=[],o={toasts:[]};function c(e){o=d(o,e),n.forEach(e=>{e(o)})}function m(e){let{...t}=e,a=(l=(l+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||r()}}}),{id:a,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function x(){let[e,t]=r.useState(o);return r.useEffect(()=>(n.push(t),()=>{let e=n.indexOf(t);e>-1&&n.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}}}]);