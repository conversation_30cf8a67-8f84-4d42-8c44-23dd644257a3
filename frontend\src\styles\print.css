/**
 * Print-specific styles for WorkHub reports
 * This file contains additional print styles that can be imported by report pages
 * Updated with modern typography and spacing standards
 */

/* Print-specific styles for report pages */
@media print {
	/* Ensure all tables use the standardized print styling */
	.report-table {
		page-break-inside: auto !important;
		width: 100% !important;
		border-collapse: collapse !important;
		margin-bottom: 8mm !important; /* Increased spacing */
		font-size: 10pt !important; /* Body text size */
		border: 0.5pt solid #ddd !important; /* Lighter border */
		border-radius: 2mm !important; /* Subtle rounded corners */
		overflow: hidden !important; /* For border-radius to work with tables */
	}

	.report-table thead {
		display: table-header-group !important;
	}

	.report-table tr {
		page-break-inside: avoid !important;
	}

	.report-table th {
		background-color: #f8f8f8 !important; /* Subtle header background */
		font-weight: bold !important;
		font-size: 10pt !important; /* Match body text size */
		border-bottom: 1pt solid #999 !important; /* Slightly thicker bottom border */
		padding: 4mm 5mm !important; /* Consistent padding */
		text-align: left !important;
		line-height: 1.3 !important; /* Improved line height */
	}

	.report-table td {
		border: 0.5pt solid #ddd !important; /* Lighter borders */
		padding: 4mm 5mm !important; /* Consistent padding */
		font-size: 10pt !important; /* Body text size */
		word-break: break-word !important;
		white-space: normal !important;
		line-height: 1.3 !important; /* Improved line height */
	}

	/* Alternating row colors */
	.report-table tr:nth-child(even) {
		background-color: #f9f9f9 !important; /* Subtle alternating row color */
	}

	/* Print-specific summary section */
	.report-summary {
		margin: 8mm 0 8mm 0 !important; /* Consistent 8mm spacing */
		border: 0.5pt solid #ddd !important; /* Lighter border */
		padding: 4mm 5mm !important; /* Consistent padding */
		background-color: #f9f9f9 !important; /* Subtle background */
		page-break-inside: avoid !important;
		border-radius: 2mm !important; /* Subtle rounded corners */
	}

	/* Print-specific section styling */
	.report-section {
		margin-bottom: 8mm !important; /* Consistent 8mm spacing */
		page-break-inside: avoid !important;
		background-color: #f9f9f9 !important; /* Subtle section background */
		border: 0.5pt solid #ddd !important; /* Lighter border */
		padding: 4mm 5mm !important; /* Consistent padding */
		border-radius: 2mm !important; /* Subtle rounded corners */
	}

	/* Print-specific section heading */
	.report-section-heading {
		font-size: 12pt !important; /* Subheader size */
		font-weight: bold !important;
		margin-bottom: 4mm !important; /* Slightly more spacing */
		border-bottom: 0.5pt solid #ddd !important; /* Lighter border */
		padding-bottom: 2mm !important; /* Slightly more padding */
		color: #333 !important;
		line-height: 1.3 !important; /* Improved line height */
	}

	/* Modern print layout enhancements */
	.print-layout-container {
		display: flex !important;
		flex-direction: column !important;
		gap: 8mm !important; /* Consistent 8mm spacing between sections */
	}

	/* Print-optimized header with logo, title, and date */
	.print-header-modern {
		display: flex !important;
		flex-direction: row !important;
		justify-content: space-between !important;
		align-items: flex-start !important;
		width: 100% !important;
		margin-bottom: 8mm !important;
		border-bottom: 1pt solid #999 !important;
		padding-bottom: 4mm !important;
		page-break-inside: avoid !important;
		page-break-after: avoid !important;
	}

	.print-header-left {
		text-align: left !important;
		width: 25% !important;
		font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI',
			Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue',
			sans-serif !important;
	}

	.print-header-center {
		text-align: center !important;
		width: 50% !important;
		font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI',
			Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue',
			sans-serif !important;
	}

	.print-header-right {
		text-align: right !important;
		width: 25% !important;
		font-size: 8pt !important;
		color: #666 !important;
		font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI',
			Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue',
			sans-serif !important;
	}

	/* Compact filter summary for print */
	.print-filter-summary {
		display: flex !important;
		flex-wrap: wrap !important;
		gap: 5mm !important;
		margin-bottom: 8mm !important;
		font-size: 9pt !important;
		color: #444 !important;
		background-color: #f9f9f9 !important;
		border: 0.5pt solid #ddd !important;
		padding: 3mm 4mm !important;
		border-radius: 2mm !important;
	}

	.print-filter-item {
		display: flex !important;
		align-items: center !important;
		gap: 2mm !important;
	}

	.print-filter-label {
		font-weight: bold !important;
	}

	/* Optimized table column widths */
	.print-col-narrow {
		width: 10% !important;
	}

	.print-col-medium {
		width: 20% !important;
	}

	.print-col-wide {
		width: 30% !important;
	}

	.print-col-extra-wide {
		width: 40% !important;
	}
}
