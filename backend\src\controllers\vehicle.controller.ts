
import {Request, Response} from 'express';
import * as vehicleModel from '../models/vehicle.model.js';
import {Prisma} from '../generated/prisma/index.js'; 
import {emitVehicleChange, SOCKET_EVENTS} from '../services/socketService.js';

export const createVehicle = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const vehicleData: Prisma.VehicleCreateInput = req.body;
		const newVehicle = await vehicleModel.createVehicle(vehicleData);
		if (newVehicle) {
			emitVehicleChange(SOCKET_EVENTS.VEHICLE_CREATED, newVehicle);
			res.status(201).json(newVehicle);
		} else {
			res.status(400).json({
				message:
					'Could not create vehicle, input may be invalid or another error occurred.',
			});
		}
	} catch (error: any) {
		if (error.message.includes('already exists')) {
			res.status(409).json({message: error.message});
		} else {
			res
				.status(500)
				.json({message: 'Error creating vehicle', error: error.message});
		}
	}
};

export const getAllVehicles = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const vehicles = await vehicleModel.getAllVehicles();
		res.status(200).json(vehicles);
	} catch (error: any) {
		res
			.status(500)
			.json({message: 'Error fetching vehicles', error: error.message});
	}
};

export const getVehicleById = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const id = parseInt(req.params.id, 10);
		if (isNaN(id)) {
			res.status(400).json({message: 'Invalid vehicle ID format'});
			return;
		}
		const vehicle = await vehicleModel.getVehicleById(id);
		if (vehicle) {
			res.status(200).json(vehicle);
		} else {
			res.status(404).json({message: 'Vehicle not found'});
		}
	} catch (error: any) {
		res
			.status(500)
			.json({message: 'Error fetching vehicle', error: error.message});
	}
};

export const updateVehicle = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const id = parseInt(req.params.id, 10);
		if (isNaN(id)) {
			res.status(400).json({message: 'Invalid vehicle ID format'});
			return;
		}
		const vehicleData: Prisma.VehicleUpdateInput = req.body;
		const updatedVehicle = await vehicleModel.updateVehicle(id, vehicleData);
		if (updatedVehicle) {
			emitVehicleChange(SOCKET_EVENTS.VEHICLE_UPDATED, updatedVehicle);
			res.status(200).json(updatedVehicle);
		} else {
			const exists = await vehicleModel.getVehicleById(id);
			if (!exists) {
				res.status(404).json({message: 'Vehicle not found to update'});
			} else {
				res.status(400).json({
					message:
						'Could not update vehicle, input may be invalid or another error occurred.',
				});
			}
		}
	} catch (error: any) {
		if (error.message.includes('already exists')) {
			res.status(409).json({message: error.message});
		} else {
			res
				.status(500)
				.json({message: 'Error updating vehicle', error: error.message});
		}
	}
};

export const deleteVehicle = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const id = parseInt(req.params.id, 10);
		if (isNaN(id)) {
			res.status(400).json({message: 'Invalid vehicle ID format'});
			return;
		}
		const deletedVehicle = await vehicleModel.deleteVehicle(id);
		if (deletedVehicle) {
			emitVehicleChange(SOCKET_EVENTS.VEHICLE_DELETED, {id}); 
			res.status(200).json({
				message: 'Vehicle deleted successfully',
				vehicle: deletedVehicle,
			});
		} else {
			res
				.status(404)
				.json({message: 'Vehicle not found or could not be deleted'});
		}
	} catch (error: any) {
		res
			.status(500)
			.json({message: 'Error deleting vehicle', error: error.message});
	}
};