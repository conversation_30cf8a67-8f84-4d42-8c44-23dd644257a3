# WorkHub Report Standardization

This document outlines the standardized approach for report pages in the WorkHub
application, including print and download functionality.

## ReportActions Component

The `ReportActions` component provides standardized print and download
functionality for all report pages. It includes:

- Print button (icon-only, using `window.print()`)
- Download dropdown with PDF and CSV options
- Consistent styling and positioning with `actionType='secondary'` and icon
  sizing `h-4 w-4`

### Usage

```tsx
<ReportActions
	reportContentId='#report-content' // ID of the element containing the report content for PDF
	tableId='#data-table' // ID of the table element for CSV export (optional)
	fileName='report-name' // Base name for downloaded files (without extension)
	enableCsv={true} // Whether to enable CSV export option
	csvData={{headers: [], data: []}} // Raw data for CSV export (alternative to tableId)
/>
```

### Props

| Prop              | Type                                   | Required | Description                                                                                                                                                                                                |
| ----------------- | -------------------------------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `reportContentId` | `string`                               | Yes      | ID or selector of the HTML element containing the report content to be converted to PDF.                                                                                                                   |
| `tableId`         | `string`                               | No       | ID or selector of the HTML table element to extract data from for CSV export. If not provided and `csvData` is also not provided, CSV export might be disabled or rely on `reportContentId` if applicable. |
| `fileName`        | `string`                               | Yes      | Base name for the downloaded files (e.g., "vehicle-report-toyota-camry"). The extension (.pdf or .csv) will be appended automatically.                                                                     |
| `enableCsv`       | `boolean`                              | No       | Whether to enable the CSV export option in the download dropdown. Defaults to `false`.                                                                                                                     |
| `csvData`         | `{ headers: string[], data: any[][] }` | No       | Optional. Raw data to be used for CSV export, bypassing table extraction. `headers` is an array of strings for column titles, and `data` is an array of arrays representing rows.                          |
| `className`       | `string`                               | No       | Additional CSS class names to apply to the root div of the component.                                                                                                                                      |

## File Utility Functions (`src/lib/utils/fileUtils.ts`)

This module provides functions for generating and downloading files:

### `downloadFile(blob: Blob, fileName: string): void`

Generic function to trigger a browser download for a given Blob and filename.

### `generatePdfFromHtml(elementIdOrSelector: string, fileName: string, options?: PdfOptions): Promise<void>`

Generates a PDF from an HTML element and triggers download. It uses
`html2canvas` to capture the element and `jsPDF` to create the PDF. Handles
multi-page content.

#### PdfOptions

```typescript
interface PdfOptions {
	orientation?: 'portrait' | 'landscape';
	unit?: string; // 'mm', 'cm', 'in', 'px', 'pt', 'pc'
	format?: string | number[]; // 'a4', 'letter', or [width, height]
	scale?: number; // html2canvas scale factor
	applyPrintStyles?: boolean; // Temporarily apply print-friendly styles
	marginTop?: number;
	marginRight?: number;
	marginBottom?: number;
	marginLeft?: number;
}
```

### `generateCsvFromData(data: any[][], headers: string[], fileName: string): void`

Generates a CSV file from an array of arrays (data) and an array of headers,
then triggers download using `papaparse`.

### `extractTableDataForCsv(tableElementIdOrSelector: string): { headers: string[]; data: any[][] }`

Extracts data (headers and rows) from an HTML table element, preparing it for
CSV generation.

## Implementation Guidelines for Report Pages

1.  **Report Content Structure**:

    - Wrap the main printable/downloadable content of your report in a `div`
      with a unique ID (e.g., `id="vehicle-report-content"`). This ID will be
      passed to `reportContentId`.
    - If your report includes a primary data table that should be exportable to
      CSV, give it a unique ID (e.g., `id="service-history-table"`) and pass
      this to `tableId`.

2.  **Integrate `ReportActions`**:

    - Place the `<ReportActions ... />` component in a suitable location,
      typically at the top of the report page, outside the main content div.
    - Ensure it's part of the `.no-print` class if you don't want it appearing
      on printed output.

3.  **Print Styling**:
    - Utilize the print styles defined in `src/app/globals.css`.
    - Add the class `.no-print` to any elements that should be hidden during
      printing (e.g., navigation, sidebars, the `ReportActions` component
      itself).
    - Use `.report-content` class for the main printable area to ensure it's
      styled correctly for print.
    - Mark specific elements only for print with `.print-only`.
    - Use `.card-print` on Card components intended for print to ensure proper
      border and shadow removal.
    - For text wrapping in table cells, use utility classes like
      `.print-text-wrap` and specific column classes like
      `.print-description-col` (see `globals.css` for examples).

## Dependencies

The report functionality relies on:

- `jspdf`: For PDF generation.
- `html2canvas`: For converting HTML to canvas for PDF generation.
- `papaparse`: For CSV generation.

## Example (Vehicle Report Page - Simplified)

```tsx
// In /frontend/src/app/vehicles/[id]/report/page.tsx

// ... other imports
import {ReportActions} from '@/components/reports/ReportActions';
// ...

export default function VehicleReportPage() {
	// ... state and data fetching logic for `vehicle` ...

	if (!vehicle) {
		return <p>Loading or vehicle not found...</p>;
	}

	return (
		<div className='max-w-4xl mx-auto bg-white p-2 sm:p-4'>
			<div className='text-right mb-4 no-print'>
				<ReportActions
					reportContentId='#vehicle-report-content'
					tableId='#service-history-table' // Assuming this table exists in your report
					fileName={`vehicle-report-${vehicle.make}-${vehicle.model}`}
					enableCsv={vehicle.serviceHistory.length > 0}
				/>
			</div>

			<div id='vehicle-report-content' className='report-content'>
				<header className='text-center mb-8 pb-4 border-b-2 border-gray-300 report-header'>
					{/* ... report header content ... */}
				</header>

				<section className='mb-8 card-print p-4 border border-gray-200 rounded'>
					{/* ... vehicle details ... */}
				</section>

				<section className='card-print p-4 border border-gray-200 rounded'>
					<h2 className='text-2xl font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200'>
						Service History
					</h2>
					{vehicle.serviceHistory.length === 0 ? (
						<p>No service records.</p>
					) : (
						<table id='service-history-table' className='w-full text-sm'>
							{/* ... table headers and rows ... */}
						</table>
					)}
				</section>

				<footer className='mt-12 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500 report-footer'>
					{/* ... report footer ... */}
				</footer>
			</div>
		</div>
	);
}
```

## Standardized Navigation to Report Pages

All navigation to report pages should use the `ViewReportButton` component,
which encapsulates the standardized styling, behavior, and accessibility
features.

### ViewReportButton Component

The `ViewReportButton` component provides a standardized way to navigate to
report pages, handling both direct links and dynamic URLs with query parameters.

```tsx
import {ViewReportButton} from '@/components/reports/ViewReportButton';
```

#### Props

| Prop           | Type           | Required | Description                                                                                 |
| -------------- | -------------- | -------- | ------------------------------------------------------------------------------------------- |
| `href`         | `string`       | No\*     | Direct URL to the report page (for individual item reports)                                 |
| `getReportUrl` | `() => string` | No\*     | Function that returns the URL to the report page (for list reports with dynamic parameters) |
| `isList`       | `boolean`      | No       | Whether this button is for a list report (affects button text). Default: `false`            |
| `className`    | `string`       | No       | Additional CSS class names                                                                  |

\*Either `href` or `getReportUrl` must be provided.

### Usage Examples

#### 1. For Individual Item Reports (e.g., Vehicle Report, Delegation Report)

```tsx
<ViewReportButton href={`/vehicles/${vehicle.id}/report`} />
```

#### 2. For List Reports with Dynamic Query Parameters (e.g., Tasks Report, Delegations List Report)

```tsx
// Function to build dynamic URL with query parameters
const getTasksReportUrl = () => {
	const queryParams = new URLSearchParams({
		searchTerm,
		status: statusFilter,
		// ...other filters
	}).toString();

	return `/tasks/report?${queryParams}`;
};

// In JSX
<ViewReportButton getReportUrl={getTasksReportUrl} isList={true} />;
```

### Key Standardization Features

The `ViewReportButton` component automatically handles:

1. **Button Styling**: Uses `actionType='secondary'` for consistent appearance
2. **Icon**: Uses the `<FileText className='h-4 w-4' />` icon
3. **Button Text**:
   - Individual item reports: "View Report"
   - List reports: "View List Report" (when `isList={true}`)
4. **Target Window**: Opens reports in a new tab
5. **Accessibility**: Includes `rel="noopener noreferrer"`, visual indicator,
   and screen reader text for links that open in new tabs

This documentation should guide developers in using the new standardized
reporting features.
