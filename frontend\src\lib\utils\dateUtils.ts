import {format, formatISO, parse, parseISO, isValid, isDate} from 'date-fns';

/**
 * Formats a date for HTML datetime-local input
 * @param dateValue - ISO date string, Date object, or undefined
 * @returns Formatted string in the format required by datetime-local inputs (YYYY-MM-DDTHH:mm)
 */
export const formatDateForInput = (
	dateValue: string | Date | undefined,
	type: 'date' | 'datetime-local' = 'datetime-local'
): string => {
	if (!dateValue) return '';

	try {
		// Convert to Date object if it's a string
		const date =
			typeof dateValue === 'string' ? parseISO(dateValue) : dateValue;

		// Check if the date is valid
		if (!isValid(date)) {
			console.warn('Invalid date for input formatting:', dateValue);
			return '';
		}

		// Format based on input type
		if (type === 'date') {
			return format(date, 'yyyy-MM-dd');
		}

		// For datetime-local, format is 'yyyy-MM-ddTHH:mm'
		return format(date, "yyyy-MM-dd'T'HH:mm");
	} catch (error) {
		console.warn('Error formatting date for input:', error);
		return '';
	}
};

/**
 * Formats a date for display to users
 * @param dateValue - ISO date string, Date object, or undefined
 * @param includeTime - Whether to include time in the formatted string
 * @returns Formatted date string for display (e.g., "Jan 15, 2023" or "Jan 15, 2023, 14:30")
 */
export const formatDateForDisplay = (
	dateValue: string | Date | undefined,
	includeTime = false
): string => {
	if (!dateValue) return 'N/A';

	try {
		// Convert to Date object if it's a string
		const date =
			typeof dateValue === 'string' ? parseISO(dateValue) : dateValue;

		// Check if the date is valid
		if (!isValid(date)) {
			return 'Invalid Date';
		}

		// Format with or without time
		return format(date, includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy');
	} catch (error) {
		console.warn('Error formatting date for display:', error);
		return 'Invalid Date';
	}
};

/**
 * Formats a date for API submission (ISO 8601 UTC format)
 * @param dateValue - Date string from form input, Date object, or undefined
 * @returns ISO 8601 UTC formatted date string ending in 'Z' or empty string if input is invalid
 */
export const formatDateForApi = (
	dateValue: string | Date | undefined
): string => {
	if (!dateValue) return '';

	try {
		// If it's already a Date object, format it directly
		if (isDate(dateValue)) {
			// Use toISOString() to ensure UTC format ending in 'Z'
			return dateValue.toISOString();
		}

		// For string input (e.g., from datetime-local input)
		// Create a Date object from the string
		const date = new Date(dateValue);

		// Check if the date is valid
		if (!isValid(date)) {
			console.warn('Invalid date for API formatting:', dateValue);
			return '';
		}

		// Use toISOString() to ensure UTC format ending in 'Z'
		// This is required by the backend's Zod z.string().datetime() validation
		return date.toISOString();
	} catch (error) {
		console.warn('Error formatting date for API:', error);
		return '';
	}
};

/**
 * Parses a date string from API (ISO format) to a Date object
 * @param dateString - ISO date string from API
 * @returns Date object or null if parsing fails
 */
export const parseDateFromApi = (
	dateString: string | undefined
): Date | null => {
	if (!dateString) return null;

	try {
		const date = parseISO(dateString);
		return isValid(date) ? date : null;
	} catch (error) {
		console.warn('Error parsing date from API:', error);
		return null;
	}
};

/**
 * Validates if a string is a valid ISO date string
 * @param dateString - String to validate
 * @returns Boolean indicating if the string is a valid ISO date
 */
export const isValidIsoDateString = (
	dateString: string | undefined
): boolean => {
	if (!dateString) return false;

	try {
		const date = parseISO(dateString);
		return isValid(date);
	} catch (error) {
		return false;
	}
};

/**
 * Validates if a string can be parsed as a valid date
 * @param dateString - String to validate
 * @returns Boolean indicating if the string can be parsed as a date
 */
export const isValidDateString = (dateString: string | undefined): boolean => {
	if (!dateString) return false;

	try {
		const date = new Date(dateString);
		return isValid(date);
	} catch (error) {
		return false;
	}
};

/**
 * Checks if date1 is after date2
 * @param date1 - First date (the one being checked if it's after)
 * @param date2 - Second date (the reference date)
 * @returns Boolean indicating if date1 is after date2
 */
export const isDateAfter = (
	date1: Date | string | undefined,
	date2: Date | string | undefined
): boolean => {
	if (!date1 || !date2) return false;

	try {
		const parsedDate1 = typeof date1 === 'string' ? parseISO(date1) : date1;
		const parsedDate2 = typeof date2 === 'string' ? parseISO(date2) : date2;

		if (!isValid(parsedDate1) || !isValid(parsedDate2)) {
			return false;
		}

		return parsedDate1 > parsedDate2;
	} catch (error) {
		console.warn('Error comparing dates:', error);
		return false;
	}
};
