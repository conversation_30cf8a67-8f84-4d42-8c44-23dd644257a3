"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1773],{11773:(t,r,e)=>{e.d(r,{getEnrichedEmployees:()=>o});var s=e(16051);async function o(t){try{let r=await s.FH.get("/employees/enriched",t);return(0,s.CZ)(r,"/employees/enriched")||[]}catch(t){throw console.error("Error fetching enriched employees:",t),t}}},16051:(t,r,e)=>{e.d(r,{FH:()=>R,CZ:()=>d,O5:()=>i});var s=function(t){return t.NETWORK_ERROR="network_error",t.TIMEOUT="timeout",t.SERVER_ERROR="server_error",t.RATE_LIMIT="rate_limit",t.CLIENT_ERROR="client_error",t.VALIDATION_ERROR="validation_error",t.AUTHENTICATION_ERROR="authentication_error",t.AUTHORIZATION_ERROR="authorization_error",t.NOT_FOUND="not_found",t.PARSING_ERROR="parsing_error",t.UNKNOWN="unknown",t}({});class o extends Error{static create(t,r,e){return new o(t,{status:r,validationErrors:null==e?void 0:e.validationErrors,receivedData:null==e?void 0:e.receivedData,details:null==e?void 0:e.details,errorType:null==e?void 0:e.errorType})}determineErrorType(){return this.status>=500?"server_error":429===this.status?"rate_limit":401===this.status?"authentication_error":403===this.status?"authorization_error":400===this.status&&this.isValidationError()?"validation_error":this.status>=400&&this.status<500?"client_error":"unknown"}isRetryable(){return"server_error"===this.errorType||"network_error"===this.errorType||"timeout"===this.errorType||"rate_limit"===this.errorType}isValidationError(){return 400===this.status&&Array.isArray(this.validationErrors)&&this.validationErrors.length>0}getFormattedMessage(){if(this.isValidationError()){let t=this.validationErrors.map(t=>"".concat(t.path,": ").concat(t.message)).join("; ");return"Validation failed: ".concat(t)}switch(this.errorType){case"network_error":return"Network error: Unable to connect to the server. Please check your internet connection.";case"timeout":return"Request timed out. The server is taking too long to respond.";case"rate_limit":return"Too many requests. Please try again later.";case"authentication_error":return"Authentication required. Please log in and try again.";case"authorization_error":return"You do not have permission to perform this action.";case"not_found":return"Resource not found: ".concat(this.endpoint||"The requested resource"," could not be found.");case"parsing_error":return"Could not parse the server response. Please try again or contact support.";case"server_error":return"Server error (".concat(this.status,"): ").concat(this.message,". Please try again later.");default:return this.message}}getTechnicalDetails(){let t=["Status: ".concat(this.status),"Type: ".concat(this.errorType),"Message: ".concat(this.message)];return this.details&&t.push("Details: ".concat(JSON.stringify(this.details))),this.validationErrors&&t.push("Validation Errors: ".concat(JSON.stringify(this.validationErrors))),t.join("\n")}constructor(t,r){super(t),this.name="ApiError",this.status=r.status,this.endpoint=r.endpoint,this.validationErrors=r.validationErrors,this.receivedData=r.receivedData,this.details=r.details,this.errorType=r.errorType||this.determineErrorType(),this.retryable=this.isRetryable(),Object.setPrototypeOf(this,o.prototype)}}let a=null;function i(t){a=t}"localhost"!==window.location.hostname&&window.location.hostname;let n=t=>new Promise(r=>setTimeout(r,t)),u=(t,r)=>Math.min(r*Math.pow(2,t),3e4);async function c(t){let r,e;if(204===t.status)return null;if(t.ok)try{return await t.json()}catch(r){throw console.error("Failed to parse successful API response:",{status:t.status,statusText:t.statusText,url:t.url,error:r instanceof Error?r.message:"Unknown error"}),new o("Failed to parse successful response: ".concat(r instanceof Error?r.message:"Unknown error"),{status:t.status,endpoint:t.url,errorType:s.PARSING_ERROR})}let a=function(t){if(t>=500)return s.SERVER_ERROR;if(429===t)return s.RATE_LIMIT;if(401===t)return s.AUTHENTICATION_ERROR;if(403===t)return s.AUTHORIZATION_ERROR;if(404===t)return s.NOT_FOUND;else if(t>=400&&t<500)return s.CLIENT_ERROR;else return s.UNKNOWN}(t.status),i="HTTP error ".concat(t.status);try{if(i=(null==(r=await t.json())?void 0:r.message)||i,e=null==r?void 0:r.details,400===t.status&&(null==r?void 0:r.status)==="error"&&(null==r?void 0:r.message)==="Validation failed"){a=s.VALIDATION_ERROR;let e=r.errors;throw console.error("API validation errors:",{endpoint:t.url,status:t.status,errors:e,receivedData:r.receivedData}),new o(r.message,{status:t.status,endpoint:t.url,errorType:a,validationErrors:e,receivedData:r.receivedData})}}catch(r){try{if(r instanceof o)throw r;{let r=await t.text();r&&(i=r)}}catch(r){if(r instanceof o)throw r;i=t.statusText||i}}throw console.error("API error (".concat(t.status,"):"),{endpoint:t.url,status:t.status,message:i,details:e,errorType:a}),new o(i,{status:t.status,endpoint:t.url,details:e,errorType:a})}async function l(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},e=function(){let t={"Content-Type":"application/json"};return a&&(t.Authorization="Bearer ".concat(a)),{headers:t,retries:3,retryDelay:1e3,timeout:1e4}}(),i={...e,...r,headers:{...e.headers,...r.headers}},{retries:l=3,retryDelay:h=1e3,timeout:d=1e4,skipRetryLogging:R=!1}=i,E=null;for(let r=0;r<=l;r++)try{let r=new AbortController,e=setTimeout(()=>{r.abort()},d),s={...i,signal:r.signal},o=t.startsWith("http")?t:"".concat("http://localhost:3001/api").concat(t),a=await fetch(o,s);return clearTimeout(e),await c(a)}catch(e){if(E=e,function(t){if(t instanceof o){if(t.status>=400&&t.status<500)return 429===t.status?s.RATE_LIMIT:s.CLIENT_ERROR;if(t.status>=500)return s.SERVER_ERROR}return t instanceof TypeError&&t.message.includes("network")?s.NETWORK_ERROR:t instanceof DOMException&&"AbortError"===t.name?s.TIMEOUT:s.UNKNOWN}(e)===s.CLIENT_ERROR||r===l)throw e;let t=u(r,h);R||console.warn("API request failed (attempt ".concat(r+1,"/").concat(l+1,"), retrying in ").concat(t/1e3,"s:"),e),await n(t)}throw E||Error("Failed to fetch after retries")}async function h(t,r,e){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o={...s,method:t};return"GET"!==t&&e&&(o.body=JSON.stringify(e)),l(r,o)}function d(t,r){if(t&&"object"==typeof t){if("data"in t&&"status"in t)return t.data;Array.isArray(t)||"status"in t||"message"in t||!("errors"in t)}return t}let R={get:(t,r)=>h("GET",t,void 0,r),post:(t,r,e)=>h("POST",t,r,e),put:(t,r,e)=>h("PUT",t,r,e),patch:(t,r,e)=>h("PATCH",t,r,e),delete:(t,r)=>h("DELETE",t,void 0,r)}}}]);