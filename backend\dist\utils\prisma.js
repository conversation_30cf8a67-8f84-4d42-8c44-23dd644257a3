import { PrismaClient } from '../generated/prisma/index.js';
// Initialize Prisma Client
const prisma = new PrismaClient({
    log: [
        {
            emit: 'stdout',
            level: 'query',
        },
        {
            emit: 'stdout',
            level: 'info',
        },
        {
            emit: 'stdout',
            level: 'warn',
        },
        {
            emit: 'stdout',
            level: 'error',
        },
    ],
});
export default prisma;
//# sourceMappingURL=prisma.js.map