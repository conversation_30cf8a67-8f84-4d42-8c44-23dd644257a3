'use client';

import React, {useState, useEffect} from 'react';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {RefreshCw, Activity} from 'lucide-react';
import {ActionButton} from '@/components/ui/action-button';
import {
	PerformanceMetrics,
	getPerformanceMetrics,
	getMockPerformanceMetrics,
} from '@/lib/adminService';
import {Skeleton} from '@/components/ui/skeleton';
import {useApi} from '@/hooks/useApi';
import {LoadingError} from '@/components/ui/loading-states';
import {ErrorBoundary} from '@/components/ui/error-boundary';
import {
	ChartContainer,
	ChartTooltip,
	ChartLegend,
	ChartLegendContent,
	ChartTooltipContent,
} from '@/components/ui/chart';
import {LineChart, Line, XAxis, YAxis, CartesianGrid} from 'recharts';

const generateMockChartData = () => {
	const data = [];
	const now = new Date();

	for (let i = 24; i >= 0; i--) {
		const time = new Date(now.getTime() - i * 3600000);
		data.push({
			time: time.toISOString(),
			queryTime: Math.random() * 2 + 0.5,
			connections: Math.floor(Math.random() * 15) + 5,
			cacheHitRate: Math.random() * 10 + 90,
		});
	}

	return data;
};

export function PerformanceStats() {
	const {
		data: metrics,
		isLoading,
		error,
		refetch,
	} = useApi<PerformanceMetrics>(() => getPerformanceMetrics(), {
		maxRetries: 3,
		initialDelay: 500,
		deps: [Math.floor(Date.now() / 120000)],
	});

	const [chartData, setChartData] = useState<any[]>([]);
	const [refreshing, setRefreshing] = useState(false);

	useEffect(() => {
		if (metrics) {
			setChartData(generateMockChartData());
		}
	}, [metrics]);

	const handleRefresh = async () => {
		setRefreshing(true);
		await refetch();
		setRefreshing(false);
	};

	return (
		<ErrorBoundary>
			<Card className='shadow-md'>
				<CardHeader className='pb-2 p-5'>
					<CardTitle className='text-xl font-semibold text-primary'>
						Performance Statistics
					</CardTitle>
					<CardDescription>Database performance over time</CardDescription>
				</CardHeader>
				<CardContent className='p-5'>
					{isLoading || refreshing ? (
						<div className='space-y-3'>
							<Skeleton className='h-[200px] w-full' />
							<div className='grid grid-cols-3 gap-4'>
								<Skeleton className='h-16 w-full' />
								<Skeleton className='h-16 w-full' />
								<Skeleton className='h-16 w-full' />
							</div>
						</div>
					) : error ? (
						<LoadingError message={error} onRetry={refetch} />
					) : metrics && chartData.length > 0 ? (
						<>
							<div className='h-[200px] mt-4'>
								<ChartContainer
									config={{
										queryTime: {
											label: 'Avg Query Time (ms)',
											color: 'hsl(var(--chart-1))',
										},
										connections: {
											label: 'Connections',
											color: 'hsl(var(--chart-2))',
										},
									}}>
									<LineChart data={chartData} margin={{left: 12, right: 12}}>
										<ChartLegend content={<ChartLegendContent />} />
										<CartesianGrid vertical={false} />
										<XAxis
											dataKey='time'
											tickFormatter={(value) => {
												const date = new Date(value);
												return `${date.getHours()}:00`;
											}}
										/>
										<YAxis />
										<ChartTooltip content={<ChartTooltipContent />} />
										<Line
											type='monotone'
											dataKey='queryTime'
											stroke='var(--color-queryTime)'
											strokeWidth={2}
											dot={false}
										/>
										<Line
											type='monotone'
											dataKey='connections'
											stroke='var(--color-connections)'
											strokeWidth={2}
											dot={false}
										/>
									</LineChart>
								</ChartContainer>
							</div>

							<div className='grid grid-cols-3 gap-4 mt-6'>
								<div className='border rounded-md p-3 text-center'>
									<div className='text-2xl font-bold'>
										{metrics.avgQueryTime.toFixed(2)}ms
									</div>
									<div className='text-xs text-muted-foreground'>
										Current Query Time
									</div>
								</div>

								<div className='border rounded-md p-3 text-center'>
									<div className='text-2xl font-bold'>
										{metrics.connectionCount}
									</div>
									<div className='text-xs text-muted-foreground'>
										Current Connections
									</div>
								</div>

								<div className='border rounded-md p-3 text-center'>
									<div className='text-2xl font-bold'>
										{metrics.cacheHitRate.indexHitRate.toFixed(1)}%
									</div>
									<div className='text-xs text-muted-foreground'>
										Cache Hit Rate
									</div>
								</div>
							</div>

							{metrics.timestamp && (
								<div className='text-xs text-muted-foreground text-center mt-4'>
									Last updated: {new Date(metrics.timestamp).toLocaleString()}
								</div>
							)}
						</>
					) : (
						<div className='p-8 text-center text-muted-foreground'>
							<Activity className='mx-auto h-8 w-8 mb-2 text-muted-foreground/50' />
							<p>No performance data available</p>
						</div>
					)}
				</CardContent>
				<CardFooter className='p-5'>
					<ActionButton
						actionType='tertiary'
						size='sm'
						className='w-full'
						onClick={handleRefresh}
						isLoading={refreshing || isLoading}
						loadingText='Refreshing...'
						icon={<RefreshCw className='h-4 w-4' />}>
						Refresh Statistics
					</ActionButton>
				</CardFooter>
			</Card>
		</ErrorBoundary>
	);
}
