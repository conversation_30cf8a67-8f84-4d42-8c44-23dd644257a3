# Database Configuration Guide

This guide explains how to configure and switch between different database providers in the Car Service Tracking System.

## Supported Database Providers

The application supports two database providers:

1. **Local PostgreSQL** (default) - Uses the PostgreSQL database running in Docker
2. **Supabase PostgreSQL** - Uses Supabase's managed PostgreSQL service

Both providers use PostgreSQL as the underlying database engine, which ensures compatibility with our Prisma ORM setup.

## Configuration

The database configuration is controlled through environment variables in the `.env` file:

```
# Database Configuration
USE_SUPABASE=false                                           # Set to 'true' to use Supabase
DATABASE_URL=**************************************/car_service_db  # Database connection string
SUPABASE_URL=https://your-project-ref.supabase.co            # Supabase project URL
SUPABASE_KEY=your-supabase-anon-key                          # Supabase anon key
```

## Switching Between Database Providers

### Using the Command Line

We provide convenient npm scripts to switch between database providers:

```bash
# Switch to local PostgreSQL
npm run db:local

# Switch to Supabase PostgreSQL
npm run db:supabase

# Check current database configuration
npm run db:status
```

### Manual Configuration

You can also manually edit the `.env` file:

1. To use local PostgreSQL:
   ```
   USE_SUPABASE=false
   DATABASE_URL=**************************************/car_service_db
   ```

2. To use Supabase:
   ```
   USE_SUPABASE=true
   DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
   SUPABASE_URL=https://your-project-ref.supabase.co
   SUPABASE_KEY=your-supabase-anon-key
   ```

## Setting Up Supabase

### 1. Create a Supabase Account and Project

1. Sign up at [https://supabase.com](https://supabase.com)
2. Create a new project
3. Set a secure database password (save this password!)
4. Choose a region closest to your users
5. Wait for your project to be created (usually takes a few minutes)

### 2. Get Your Supabase Credentials

1. In the Supabase dashboard, go to Project Settings (gear icon) > API
2. Copy the "Project URL" (e.g., `https://abcdefghijklm.supabase.co`)
3. Copy the "anon" public API key
4. Go to Project Settings > Database
5. Copy the PostgreSQL connection string (URI format)

### 3. Run the Database Switching Script

```bash
npm run db:supabase
```

The script will prompt you for your Supabase project reference, database password, and anon key.

### 4. Run Migrations on Supabase

After switching to Supabase, run the Prisma migrations to set up your schema:

```bash
npm run db:migrate
```

### 5. Seed the Supabase Database (Optional)

If you want to populate your Supabase database with sample data:

```bash
npm run db:seed:force
```

## Using Docker with Supabase

When running the application with Docker, the database configuration is determined by the `.env` file. The Docker Compose setup has been modified to respect the `USE_SUPABASE` flag:

1. If `USE_SUPABASE=false`, the application will connect to the local PostgreSQL container
2. If `USE_SUPABASE=true`, the application will connect to Supabase and the local PostgreSQL container will still run but won't be used

To run the application with Docker using Supabase:

```bash
# First, switch to Supabase
npm run db:supabase

# Then run Docker Compose
docker-compose up -d
```

## Checking Database Connection Status

You can check the status of your database connections by accessing the health endpoint:

```
GET /api/health
```

This endpoint returns detailed information about the database connections, including:

- Overall service health status
- Database connection status (PostgreSQL via Prisma)
- Supabase connection status (if configured)
- Current database configuration

## Troubleshooting

### Connection Issues

If you're having trouble connecting to the database:

1. Check that your DATABASE_URL is correctly formatted
2. Ensure your database credentials are correct
3. If using Supabase, verify that your IP address is allowed in Supabase's network restrictions
4. Check the application logs for detailed error messages

### Migration Issues

If Prisma migrations fail:

1. Check that your DATABASE_URL is correctly pointing to the right database
2. Ensure your database password is correct
3. Try running `npx prisma migrate reset` to reset the database (caution: this will delete all data)

## Additional Resources

- [Prisma Documentation](https://www.prisma.io/docs/)
- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
