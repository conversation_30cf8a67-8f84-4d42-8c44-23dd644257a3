(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1602],{11284:(e,t,s)=>{"use strict";s.d(t,{a:()=>o});var r=s(12115),i=s(46750);let n={interval:3e4,enabled:!0,errorHandler:e=>console.error("Refresh error:",e),immediate:!1,retryLimit:3,retryDelay:2e3};class l{start(){null===this.timerId&&(this.timerId=window.setTimeout(()=>this.refreshCycle(),this.options.interval))}stop(){null!==this.timerId&&(window.clearTimeout(this.timerId),this.timerId=null)}async refresh(){if(!this.isRefreshing){this.isRefreshing=!0;try{await this.callback(),this.consecutiveFailures=0,this.lastSuccessTime=Date.now()}catch(e){this.consecutiveFailures++,this.options.errorHandler(e)}finally{this.isRefreshing=!1}}}updateOptions(e){let t=this.options.enabled;this.options={...this.options,...e},null!==this.timerId&&this.stop(),this.options.enabled&&(!t||e.interval)&&this.start()}async refreshCycle(){if(!this.isOnline||!this.isVisible||this.isRefreshing)return void this.reschedule();if(this.consecutiveFailures>=this.options.retryLimit){let e=Math.min(this.options.retryDelay*Math.pow(2,this.consecutiveFailures-1),6e4);this.timerId=window.setTimeout(()=>this.refreshCycle(),e);return}await this.refresh(),this.reschedule()}reschedule(){this.options.enabled&&(this.timerId=window.setTimeout(()=>this.refreshCycle(),this.options.interval))}setupBrowserEventListeners(){document.addEventListener("visibilitychange",()=>{this.isVisible="visible"===document.visibilityState,this.isVisible&&this.options.enabled&&Date.now()-this.lastSuccessTime>this.options.interval&&(this.stop(),this.refresh(),this.start())}),window.addEventListener("online",()=>{this.isOnline=!0,this.options.enabled&&(this.stop(),this.refresh(),this.start())}),window.addEventListener("offline",()=>{this.isOnline=!1}),this.isOnline=navigator.onLine,this.isVisible="visible"===document.visibilityState}dispose(){this.stop()}constructor(e,t={}){this.timerId=null,this.isRefreshing=!1,this.consecutiveFailures=0,this.lastSuccessTime=0,this.isVisible=!0,this.isOnline=!0,this.callback=e,this.options={...n,...t},this.setupBrowserEventListeners(),this.options.enabled&&this.options.immediate?this.refresh():this.options.enabled&&this.start()}}let a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new l(e,t)};function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{interval:n=6e4,enabled:l=!0,immediate:o=!0,socketUrl:c,enableSocket:d=!0,enablePolling:h=!0,onRefresh:u,onError:m}=s,[f,p]=(0,r.useState)(null),[x,v]=(0,r.useState)(!1),{isConnected:g,subscribe:y}=(0,i.F)({url:c,autoConnect:d&&l}),E=(0,r.useCallback)(async()=>{try{let t=await e();return p(new Date),null==u||u(),t}catch(e){throw null==m||m(e),e}},[e,u,m]),{isRefreshing:b,refresh:w}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{interval:s=3e4,enabled:i=!0,immediate:n=!1,onRefresh:l,onError:o}=t,[c,d]=(0,r.useState)(!1),h=(0,r.useRef)(null),u=(0,r.useCallback)(async()=>{d(!0);try{await e(),null==l||l()}catch(e){throw null==o||o(e),e}finally{d(!1)}},[e,l,o]);return(0,r.useEffect)(()=>(h.current=a(u,{interval:s,enabled:i,immediate:n,errorHandler:o}),()=>{h.current&&(h.current.dispose(),h.current=null)}),[]),(0,r.useEffect)(()=>{h.current&&h.current.updateOptions({interval:s,enabled:i})},[s,i]),{isRefreshing:c,refresh:(0,r.useCallback)(async()=>{h.current&&await h.current.refresh()},[])}}(async()=>{await E()},{interval:n,enabled:h&&l&&(!g||!d),immediate:o&&(!g||!d||!t.length),onError:m});return(0,r.useEffect)(()=>{if(!d||!l||!g||0===t.length)return;let e=t.map(e=>y(e,t=>{console.log("Socket event received: ".concat(e),t),v(!0),w().catch(console.error),setTimeout(()=>v(!1),1e3)}));return()=>{e.forEach(e=>e())}},[d,l,g,t,y,w]),{isRefreshing:b,socketTriggered:x,isConnected:g,lastUpdated:f,refresh:(0,r.useCallback)(async()=>await w(),[w])}}},22346:(e,t,s)=>{"use strict";s.d(t,{w:()=>a});var r=s(95155),i=s(12115),n=s(87489),l=s(59434);let a=i.forwardRef((e,t)=>{let{className:s,orientation:i="horizontal",decorative:a=!0,...o}=e;return(0,r.jsx)(n.b,{ref:t,decorative:a,orientation:i,className:(0,l.cn)("shrink-0 bg-border","horizontal"===i?"h-[1px] w-full":"h-full w-[1px]",s),...o})});a.displayName=n.b.displayName},46750:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,F:()=>l});var r=s(12115),i=s(14298);let n={VEHICLE_UPDATED:"vehicle:updated",VEHICLE_CREATED:"vehicle:created",VEHICLE_DELETED:"vehicle:deleted",EMPLOYEE_UPDATED:"employee:updated",EMPLOYEE_CREATED:"employee:created",EMPLOYEE_DELETED:"employee:deleted",REFRESH_VEHICLES:"refresh:vehicles",REFRESH_EMPLOYEES:"refresh:employees"};function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{url:t="http://localhost:3001/api".replace("/api","")||"http://localhost:4000",autoConnect:s=!0}=e,[n,l]=(0,r.useState)(!1),[a,o]=(0,r.useState)(null),c=(0,r.useRef)(null);(0,r.useEffect)(()=>{if(!c.current&&s)try{c.current=(0,i.io)(t,{reconnectionAttempts:5,reconnectionDelay:1e3,autoConnect:!0}),c.current.on("connect",()=>{var e;l(!0),o(null),console.log("Socket connected:",null==(e=c.current)?void 0:e.id)}),c.current.on("disconnect",e=>{l(!1),console.log("Socket disconnected:",e)}),c.current.on("connect_error",e=>{o(e),console.error("Socket connection error:",e)})}catch(e){o(e instanceof Error?e:Error("Unknown socket error")),console.error("Error initializing socket:",e)}return()=>{c.current&&(c.current.disconnect(),c.current=null,l(!1))}},[t,s]);let d=(0,r.useCallback)((e,t)=>c.current?(c.current.on(e,t),()=>{var s;null==(s=c.current)||s.off(e,t)}):()=>{},[]),h=(0,r.useCallback)(()=>{if(c.current)c.current.connected||c.current.connect();else try{c.current=(0,i.io)(t),c.current.connect()}catch(e){o(e instanceof Error?e:Error("Failed to connect"))}},[t]),u=(0,r.useCallback)(()=>{c.current&&c.current.disconnect()},[]);return{socket:c.current,isConnected:n,error:a,subscribe:d,connect:h,disconnect:u}}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var r=s(95155),i=s(12115),n=s(59434);let l=i.forwardRef((e,t)=>{let{className:s,type:i,...l}=e;return(0,r.jsx)("input",{type:i,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...l})});l.displayName="Input"},68774:(e,t,s)=>{Promise.resolve().then(s.bind(s,96950))},69321:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]])},87481:(e,t,s)=>{"use strict";s.d(t,{dj:()=>u});var r=s(12115);let i=0,n=new Map,l=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?l(s):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],c={toasts:[]};function d(e){c=a(c,e),o.forEach(e=>{e(c)})}function h(e){let{...t}=e,s=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||r()}}}),{id:s,dismiss:r,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function u(){let[e,t]=r.useState(c);return r.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:h,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},96950:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var r=s(95155),i=s(12115),n=s(6874),l=s.n(n),a=s(28328),o=s(34301),c=s(67554),d=s(75074),h=s(66766),u=s(66695),m=s(6560),f=s(69321),p=s(3235),x=s(19968),v=s(22346);function g(e){let{vehicle:t}=e,s=t.serviceHistory.length>0,i=null!==t.initialOdometer,n=s?Math.max(t.initialOdometer||0,...t.serviceHistory.map(e=>e.odometer)):t.initialOdometer||0;return(0,r.jsxs)(u.Zp,{className:"overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60",children:[(0,r.jsx)(u.aR,{className:"p-0 relative",children:(0,r.jsx)("div",{className:"aspect-[16/10] w-full relative",children:(0,r.jsx)(h.default,{src:t.imageUrl||"https://picsum.photos/seed/".concat(t.id,"/600/375"),alt:"".concat(t.make," ").concat(t.model),layout:"fill",objectFit:"cover",className:"bg-muted","data-ai-hint":"luxury car",priority:!0})})}),(0,r.jsxs)(u.Wu,{className:"p-5 flex-grow flex flex-col",children:[(0,r.jsxs)(u.ZB,{className:"text-xl font-semibold mb-1 text-primary",children:[t.make," ",t.model]}),(0,r.jsxs)(u.BT,{className:"text-sm text-muted-foreground mb-3",children:[t.year," ",t.color&&"• ".concat(t.color)," ",t.licensePlate&&"• Plate: ".concat(t.licensePlate)]}),(0,r.jsx)(v.w,{className:"my-3 bg-border/50"}),(0,r.jsxs)("div",{className:"space-y-2.5 text-sm text-foreground flex-grow",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Latest Odometer: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s||i?"".concat(n.toLocaleString()," miles"):"Not recorded"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Services Logged: "}),(0,r.jsx)("strong",{className:"font-semibold",children:t.serviceHistory.length})]})]})]})]}),(0,r.jsx)(u.wL,{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,r.jsx)(m.r,{actionType:"tertiary",className:"w-full",icon:(0,r.jsx)(x.A,{className:"h-4 w-4"}),asChild:!0,children:(0,r.jsx)(l(),{href:"/vehicles/".concat(t.id),children:"Manage Vehicle"})})})]})}var y=s(2730),E=s(11284),b=s(46750);let w=e=>{let{children:t}=e,[s,r]=(0,i.useState)([]),[n,l]=(0,i.useState)(!0),[a,o]=(0,i.useState)(null),c=(0,i.useCallback)(async()=>{n&&l(!0);try{let e=await (0,y.getVehicles)();r(e),o(null)}catch(e){console.error("Error fetching vehicles:",e),o("Failed to fetch vehicles. Please try again.")}finally{n&&l(!1)}},[n]),{isRefreshing:d,refresh:h,isConnected:u,socketTriggered:m}=(0,E.a)(c,[b.A.VEHICLE_CREATED,b.A.VEHICLE_UPDATED,b.A.VEHICLE_DELETED,b.A.REFRESH_VEHICLES],{interval:3e4,enabled:!0,immediate:!0,enableSocket:!0,enablePolling:!0,onError:e=>{console.error("Socket/Polling refresh error:",e)}});return(0,i.useEffect)(()=>{n&&!m&&c()},[c,n,m]),t({vehicles:s,loading:n,error:a,handleDelete:async e=>{if(window.confirm("Are you sure you want to delete this vehicle?"))try{await (0,y.deleteVehicle)(e),r(t=>t.filter(t=>t.id!==e))}catch(e){throw console.error("Error deleting vehicle:",e),o("Failed to delete vehicle. Please try again."),e}},fetchVehicles:h,isRefreshing:d||m,isConnected:u,socketTriggered:m})};var j=s(95647),N=s(62523),S=s(87481),T=s(77023),A=s(15080);let C=()=>{let[e,t]=(0,i.useState)([]),[s,n]=(0,i.useState)([]),[h,u]=(0,i.useState)(""),{toast:f}=(0,S.dj)();return(0,r.jsx)(w,{children:f=>{let{vehicles:p,loading:x,error:v,handleDelete:y,fetchVehicles:E,isRefreshing:b,isConnected:w,socketTriggered:S}=f;return(0,i.useEffect)(()=>{x||v||(t(p),n(p))},[p,x,v]),(0,i.useEffect)(()=>{let t=h.toLowerCase();n(e.filter(e=>e.make.toLowerCase().includes(t)||e.model.toLowerCase().includes(t)||e.year.toString().includes(t)||e.licensePlate&&e.licensePlate.toLowerCase().includes(t)||e.vin&&e.vin.toLowerCase().includes(t)||e.ownerName&&e.ownerName.toLowerCase().includes(t)))},[h,e]),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)(j.z,{title:"My Vehicle Fleet",description:"Manage, track, and gain insights into your vehicles.",icon:a.A,children:(0,r.jsx)("div",{className:"flex gap-2",children:(0,r.jsx)(m.r,{actionType:"primary",icon:(0,r.jsx)(o.A,{className:"h-4 w-4"}),asChild:!0,children:(0,r.jsx)(l(),{href:"/vehicles/new",children:"Add New Vehicle"})})})}),(0,r.jsxs)("div",{className:"mb-6 p-4 bg-card rounded-lg shadow relative",children:[(b||S)&&!x&&(0,r.jsxs)("div",{className:"absolute right-4 top-4 text-xs flex items-center text-muted-foreground",children:[(0,r.jsx)(c.A,{className:"h-3 w-3 mr-1 animate-spin"}),S?"Real-time update...":"Updating list..."]}),!x&&(0,r.jsxs)("div",{className:"absolute right-40 top-4 text-xs inline-flex items-center sm:right-24",children:[" ",(0,r.jsx)("div",{className:"h-2 w-2 rounded-full mr-2 ".concat(w?"bg-green-500":"bg-gray-400")}),(0,r.jsx)("span",{className:"text-muted-foreground text-xs",children:w?"Real-time active":"Real-time inactive"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground"}),(0,r.jsx)(N.p,{type:"text",placeholder:"Search vehicles (Make, Model, Year, VIN, Plate, Owner...)",value:h,onChange:e=>u(e.target.value),className:"pl-10 w-full"})]})]}),(0,r.jsx)(T.gO,{isLoading:x,error:v,data:s,onRetry:E,loadingComponent:(0,r.jsx)(T.jt,{variant:"card",count:3}),emptyComponent:(0,r.jsxs)("div",{className:"text-center py-12 bg-card rounded-lg shadow-md",children:[(0,r.jsx)(a.A,{className:"mx-auto h-16 w-16 text-muted-foreground mb-6"}),(0,r.jsx)("h3",{className:"text-2xl font-semibold text-foreground mb-2",children:h?"No Vehicles Match Your Search":"Your Garage is Empty!"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-2 mb-6 max-w-md mx-auto",children:h?"Try adjusting your search terms or add a new vehicle to your fleet.":"It looks like you haven't added any vehicles yet. Let's get your first one set up."}),!h&&(0,r.jsx)(m.r,{actionType:"primary",size:"lg",icon:(0,r.jsx)(o.A,{className:"h-4 w-4"}),asChild:!0,children:(0,r.jsx)(l(),{href:"/vehicles/new",children:"Add Your First Vehicle"})})]}),children:e=>(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,r.jsx)(g,{vehicle:e},e.id))})})]})}})};function k(){return(0,r.jsx)(A.A,{children:(0,r.jsx)(C,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5769,8360,832,7529,6766,3150,8162,2730,536,8441,1684,7358],()=>t(68774)),_N_E=e.O()}]);