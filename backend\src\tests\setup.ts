// This setup file helps configure Jest for ESM modules
import {
	jest,
	expect,
	test,
	describe,
	beforeEach,
	afterEach,
	beforeAll,
	afterAll,
} from '@jest/globals';

// Make Jest available globally for ESM modules
// @ts-ignore - extending global object
global.jest = jest;
// @ts-ignore - extending global object
global.expect = expect;
// @ts-ignore - extending global object
global.test = test;
// @ts-ignore - extending global object
global.describe = describe;
// @ts-ignore - extending global object
global.beforeEach = beforeEach;
// @ts-ignore - extending global object
global.afterEach = afterEach;
// @ts-ignore - extending global object
global.beforeAll = beforeAll;
// @ts-ignore - extending global object
global.afterAll = afterAll;

export {};
