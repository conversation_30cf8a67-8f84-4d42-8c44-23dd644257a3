/**
 * Delegation API service
 */
import { api, ApiRequestOptions } from './apiService';
import { Delegation, DelegationStatus, FlightDetails, Delegate } from '../types';
import { ApiResponse } from '../types/api';

// Delegation response types
export interface DelegationResponse extends ApiResponse<Delegation> {}
export interface DelegationsResponse extends ApiResponse<Delegation[]> {}

// Delegation create/update input types
export interface DelegationCreateInput {
  eventName: string;
  location: string;
  durationFrom: string; // ISO date string
  durationTo: string; // ISO date string
  invitationFrom?: string;
  invitationTo?: string;
  delegates: Omit<Delegate, 'id'>[];
  flightArrivalDetails?: Omit<FlightDetails, 'id'> | null;
  flightDepartureDetails?: Omit<FlightDetails, 'id'> | null;
  status: DelegationStatus;
  statusChangeReason?: string;
  notes?: string;
  imageUrl?: string;
}

export interface DelegationUpdateInput {
  eventName?: string;
  location?: string;
  durationFrom?: string; // ISO date string
  durationTo?: string; // ISO date string
  invitationFrom?: string;
  invitationTo?: string;
  delegates?: Delegate[];
  flightArrivalDetails?: FlightDetails | null;
  flightDepartureDetails?: FlightDetails | null;
  status?: DelegationStatus;
  statusChangeReason?: string;
  notes?: string;
  imageUrl?: string;
}

/**
 * Get all delegations
 */
export async function getDelegations(options?: ApiRequestOptions): Promise<Delegation[]> {
  const response = await api.get<DelegationsResponse>('/delegations', options);
  return response.data || [];
}

/**
 * Get a delegation by ID
 */
export async function getDelegationById(id: string, options?: ApiRequestOptions): Promise<Delegation | null> {
  try {
    const response = await api.get<DelegationResponse>(`/delegations/${id}`, options);
    return response.data || null;
  } catch (error) {
    // Return null for 404 errors, rethrow others
    if (error instanceof Error && 'status' in error && (error as any).status === 404) {
      console.warn(`Delegation with ID ${id} not found`);
      return null;
    }
    throw error;
  }
}

/**
 * Create a new delegation
 */
export async function createDelegation(
  data: DelegationCreateInput, 
  options?: ApiRequestOptions
): Promise<Delegation> {
  const response = await api.post<DelegationResponse>('/delegations', data, options);
  if (!response.data) {
    throw new Error('Failed to create delegation: No data returned from API');
  }
  return response.data;
}

/**
 * Update a delegation
 */
export async function updateDelegation(
  id: string, 
  data: DelegationUpdateInput, 
  options?: ApiRequestOptions
): Promise<Delegation> {
  const response = await api.put<DelegationResponse>(`/delegations/${id}`, data, options);
  if (!response.data) {
    throw new Error(`Failed to update delegation ${id}: No data returned from API`);
  }
  return response.data;
}

/**
 * Delete a delegation
 */
export async function deleteDelegation(id: string, options?: ApiRequestOptions): Promise<void> {
  await api.delete<ApiResponse<void>>(`/delegations/${id}`, options);
}

/**
 * Update delegation status
 */
export async function updateDelegationStatus(
  id: string, 
  status: DelegationStatus, 
  reason?: string, 
  options?: ApiRequestOptions
): Promise<Delegation> {
  const response = await api.patch<DelegationResponse>(
    `/delegations/${id}/status`, 
    { status, reason }, 
    options
  );
  if (!response.data) {
    throw new Error(`Failed to update delegation status for ${id}: No data returned from API`);
  }
  return response.data;
}
