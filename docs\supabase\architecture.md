# Supabase Integration Architecture

This document describes the architecture and implementation details of the Supabase integration in the Car Service Tracking System application.

## Table of Contents

1. [Overview](#overview)
2. [Architecture Components](#architecture-components)
3. [Database Service](#database-service)
4. [CLI Integration](#cli-integration)
5. [Environment Configuration](#environment-configuration)
6. [Security Considerations](#security-considerations)
7. [Design Decisions](#design-decisions)

## Overview

The Supabase integration architecture is designed to provide a seamless way to use Supabase's managed PostgreSQL database while maintaining compatibility with our existing Prisma ORM setup. The architecture allows for easy switching between local PostgreSQL and Supabase, and provides a unified interface for database operations.

## Architecture Components

The Supabase integration consists of the following components:

1. **Database Service**: A unified service for database access that supports both local PostgreSQL and Supabase.
2. **CLI Integration**: Custom scripts and npm commands for managing Supabase operations.
3. **Environment Configuration**: Environment variables and configuration files for Supabase credentials.
4. **Prisma Integration**: Continued use of Prisma ORM with Supabase's PostgreSQL database.

### Component Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                      Application                            │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                     Database Service                        │
│                                                             │
│  ┌─────────────────────┐        ┌────────────────────────┐  │
│  │     Prisma Client   │        │    Supabase Client     │  │
│  └─────────────────────┘        └────────────────────────┘  │
└───────────────────────────┬─────────────────────────────────┘
                            │
                            ▼
┌─────────────────────┐    ┌─────────────────────┐
│  Local PostgreSQL   │    │  Supabase PostgreSQL │
└─────────────────────┘    └─────────────────────┘
```

## Database Service

The database service (`database.service.ts`) is the core component of the Supabase integration. It provides a unified interface for database access, regardless of whether the application is using local PostgreSQL or Supabase.

### Key Features

- **Database Provider Detection**: Automatically detects which database provider to use based on environment variables.
- **Client Initialization**: Initializes the appropriate client (Prisma or Supabase) based on the detected provider.
- **Connection Testing**: Provides methods to test the connection to both database providers.
- **Health Check**: Exposes a health check endpoint that reports the status of the database connection.

### Implementation Details

The database service uses the `USE_SUPABASE` environment variable to determine which database provider to use. If `USE_SUPABASE` is set to `true`, it initializes the Supabase client and uses it for database operations. Otherwise, it uses the Prisma client.

```typescript
// Simplified example
const getDatabaseConfig = (): DatabaseConfig => {
  const useSupabase = process.env.USE_SUPABASE === 'true';
  const databaseUrl = process.env.DATABASE_URL || '';
  const supabaseUrl = useSupabase ? process.env.SUPABASE_URL : undefined;
  const supabaseKey = useSupabase ? process.env.SUPABASE_KEY : undefined;
  
  return {
    useSupabase,
    databaseUrl,
    supabaseUrl,
    supabaseKey,
  };
};
```

## CLI Integration

The CLI integration consists of custom scripts and npm commands for managing Supabase operations. The main component is the `supabase-cli.js` script, which provides a convenient interface for working with Supabase CLI in conjunction with Prisma.

### Key Features

- **Login**: Authenticates with Supabase.
- **Project Linking**: Links the local project to a Supabase project.
- **Schema Synchronization**: Pulls and pushes database schema between Prisma and Supabase.
- **Migration Management**: Runs Prisma migrations on Supabase.
- **Database Seeding**: Seeds the Supabase database with initial data.
- **Configuration Management**: Manages Supabase configuration securely.

### Implementation Details

The `supabase-cli.js` script uses the Supabase CLI and Prisma CLI to perform various operations. It stores Supabase credentials in a configuration file (`supabase/config.json`) and updates the `.env` file with the appropriate database URL.

```javascript
// Simplified example
function linkProject() {
  rl.question('Enter your Supabase project ID: ', (projectId) => {
    rl.question('Enter your database password: ', (password) => {
      rl.question('Enter your service role key: ', (serviceRoleKey) => {
        const config = {
          project_id: projectId,
          api: {
            http_url: `https://${projectId}.supabase.co`,
            db_url: `postgresql://postgres:${password}@db.${projectId}.supabase.co:5432/postgres`,
            service_role_key: serviceRoleKey
          }
        };
        
        writeConfig(config);
        updateEnvFile(config.api.db_url);
      });
    });
  });
}
```

## Environment Configuration

The environment configuration consists of environment variables and configuration files for Supabase credentials. The main components are the `.env` file and the `supabase/config.json` file.

### Key Environment Variables

- `USE_SUPABASE`: Whether to use Supabase (`true`) or local PostgreSQL (`false`).
- `DATABASE_URL`: The PostgreSQL connection string.
- `SUPABASE_URL`: The Supabase project URL.
- `SUPABASE_KEY`: The Supabase anon key.

### Configuration File

The `supabase/config.json` file stores Supabase credentials securely:

```json
{
  "project_id": "your-project-id",
  "api": {
    "http_url": "https://your-project-id.supabase.co",
    "db_url": "postgresql://postgres:<EMAIL>:5432/postgres",
    "service_role_key": "your-service-role-key"
  }
}
```

## Security Considerations

### Credential Management

- **Configuration File**: The `supabase/config.json` file contains sensitive information and should be added to `.gitignore` to prevent committing it to version control.
- **Environment Variables**: Sensitive information like database passwords and API keys should be stored in environment variables and not committed to version control.
- **Service Role Key**: The service role key has full access to your database and should be kept secure.

### Access Control

- **Row-Level Security**: Supabase provides row-level security (RLS) policies that can be used to control access to data.
- **API Keys**: Different API keys (anon, service role) have different levels of access to your database.

## Design Decisions

### Why Prisma + Supabase?

We chose to continue using Prisma as our ORM while leveraging Supabase's managed PostgreSQL database for several reasons:

1. **Existing Codebase**: Our application already uses Prisma, and rewriting all database access code would be time-consuming.
2. **Type Safety**: Prisma provides strong type safety and auto-completion in TypeScript.
3. **Migration Management**: Prisma has excellent migration management capabilities.
4. **Managed Database**: Supabase provides a managed PostgreSQL database that scales with our application.

### Why Custom CLI Scripts?

We created custom CLI scripts instead of using the Supabase CLI directly for several reasons:

1. **Integration with Prisma**: Our scripts integrate Supabase with Prisma, providing a seamless experience.
2. **Simplified Commands**: Our scripts provide simple npm commands for common operations.
3. **Secure Credential Management**: Our scripts handle Supabase credentials securely.
4. **Environment Configuration**: Our scripts update the `.env` file with the appropriate database URL.

### Why Database Switching?

We implemented database switching to allow developers to work with either local PostgreSQL or Supabase:

1. **Local Development**: Developers can work with a local PostgreSQL database for faster development.
2. **Testing**: Tests can be run against a local database for faster execution.
3. **Production**: The production environment can use Supabase for scalability and reliability.
4. **Flexibility**: Developers can choose the database that best suits their needs.
