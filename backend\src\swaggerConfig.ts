import swaggerJsdoc from 'swagger-jsdoc';

const options: swaggerJsdoc.Options = {
	definition: {
		openapi: '3.0.0',
		info: {
			title: 'Car Service Tracking API',
			version: '1.0.0',
			description:
				'API documentation for the Car Service Tracking System backend.',
		},
		servers: [
			{
				url: 'http://localhost:3001/api', // Adjust if your port/base path is different
				description: 'Development server',
			},
		],
		components: {
			// Future: Define securitySchemes like <PERSON><PERSON> Auth here
			// securitySchemes: {
			//   bearerAuth: {
			//     type: 'http',
			//     scheme: 'bearer',
			//     bearerFormat: 'JWT',
			//   }
			// }
		},
		// security: [
		//   {
		//     bearerAuth: []
		//   }
		// ]
	},
	// Path to the API docs (routes or controllers with JSDoc comments)
	apis: ['./src/routes/*.ts', './src/controllers/*.ts'], // Adjust as needed
};

const swaggerSpec = swaggerJsdoc(options);

export default swaggerSpec;
