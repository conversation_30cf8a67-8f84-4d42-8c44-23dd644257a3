import prisma from './index.js';
import { PrismaClientKnownRequestError } from '../generated/prisma/runtime/library.js';
export const createVehicle = async (data) => {
    try {
        return await prisma.vehicle.create({
            data,
            include: { serviceRecords: { orderBy: { date: 'desc' } }, tasks: true, assignedDriver: true }
        });
    }
    catch (error) {
        console.error('Error creating vehicle:', error);
        if (error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2002') {
            throw new Error(`Vehicle with VIN ${data.vin} already exists.`);
        }
        return null;
    }
};
export const getAllVehicles = async () => {
    try {
        return await prisma.vehicle.findMany({
            include: { serviceRecords: { orderBy: { date: 'desc' } }, tasks: true, assignedDriver: true },
            orderBy: { createdAt: 'desc' }
        });
    }
    catch (error) {
        console.error('Error fetching all vehicles:', error);
        return [];
    }
};
export const getVehicleById = async (id) => {
    try {
        return await prisma.vehicle.findUnique({
            where: { id },
            include: { serviceRecords: { orderBy: { date: 'desc' } }, tasks: true, assignedDriver: true }
        });
    }
    catch (error) {
        console.error(`Error fetching vehicle with ID ${id}:`, error);
        return null;
    }
};
export const updateVehicle = async (id, data) => {
    try {
        return await prisma.vehicle.update({
            where: { id },
            data,
            include: { serviceRecords: { orderBy: { date: 'desc' } }, tasks: true, assignedDriver: true }
        });
    }
    catch (error) {
        console.error(`Error updating vehicle with ID ${id}:`, error);
        if (error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2025') {
            return null;
        }
        if (error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2002' &&
            data.vin) {
            throw new Error(`Another vehicle with VIN ${data.vin} already exists.`);
        }
        return null;
    }
};
export const deleteVehicle = async (id) => {
    try {
        return await prisma.$transaction(async (tx) => {
            const vehicleToDelete = await tx.vehicle.findUnique({ where: { id }, include: { assignedDriver: true } });
            if (!vehicleToDelete)
                return null;
            // Delete related ServiceRecords
            await tx.serviceRecord.deleteMany({ where: { vehicleId: id } });
            // Delete related Tasks or disassociate them (current schema sets vehicleId to null on vehicle delete if task is not deleted)
            // If we want to delete tasks associated with a vehicle, it should be:
            // await tx.task.deleteMany({ where: { vehicleId: id } });
            // Current schema has onDelete: SetNull for Task.vehicle, so no explicit action needed on tasks to delete vehicle
            // unless we want to cascade delete tasks.
            // If a driver is assigned to this vehicle, their assignedVehicleId should be nullified
            // The relation "DriverVehicle" with onDelete: SetNull on Employee side handles this.
            // No explicit update needed here due to schema definition.
            await tx.vehicle.delete({ where: { id } });
            return vehicleToDelete;
        });
    }
    catch (error) {
        console.error(`Error deleting vehicle with ID ${id}:`, error);
        if (error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2025') {
            return null;
        }
        return null;
    }
};
//# sourceMappingURL=vehicle.model.js.map