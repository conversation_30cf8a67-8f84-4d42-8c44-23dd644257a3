'use client';

import Link from 'next/link';
import {useEffect, useState} from 'react';

// Set page metadata
if (typeof document !== 'undefined') {
	document.title = 'Dashboard - WorkHub';
}
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
	CardFooter,
} from '@/components/ui/card';
import {PageHeader} from '@/components/ui/PageHeader';
import {useApiMethods} from '@/hooks/useAuthenticatedApi';
import type {Vehicle, Delegation, Task, Employee} from '@/lib/types';
import {ActionButton} from '@/components/ui/action-button';
import {DataLoader, SkeletonLoader} from '@/components/ui/loading';
import {
	LayoutDashboard,
	Car,
	Wrench,
	CalendarClock,
	FileText,
	BarChart2,
	DollarSign,
	Lightbulb,
	PlusCircle,
	Eye,
	ListChecks,
	Printer,
	CarFront,
	Settings2,
	History,
	Briefcase,
	ClipboardList,
	UsersRound,
	Route,
} from 'lucide-react';

interface StatCardProps {
	title: string;
	value: string | number;
	icon: React.ElementType;
	description?: string;
	link?: string;
	linkText?: string;
	isFuture?: boolean;
}

function StatCard({
	title,
	value,
	icon: Icon,
	description,
	link,
	linkText,
	isFuture,
}: StatCardProps) {
	return (
		<Card className='shadow-md bg-card'>
			<CardHeader className='p-5 flex flex-row items-center justify-between space-y-0 pb-2'>
				<CardTitle className='text-sm font-medium text-primary'>
					{title}
				</CardTitle>
				<Icon className='h-4 w-4 text-muted-foreground' />
			</CardHeader>
			<CardContent className='p-5 pt-2'>
				<div className='text-2xl font-bold text-foreground'>{value}</div>
				{description && (
					<p className='text-xs text-muted-foreground'>{description}</p>
				)}
				{isFuture && (
					<p className='text-xs text-amber-600 dark:text-amber-500'>
						(Future Feature)
					</p>
				)}
				{link && linkText && (
					<ActionButton
						actionType='tertiary'
						size='sm'
						asChild
						className='mt-2' // Added some margin for spacing
					>
						<Link href={link}>{linkText}</Link>
					</ActionButton>
				)}
			</CardContent>
		</Card>
	);
}

interface FeatureCardProps {
	title: string;
	description: string;
	icon: React.ElementType;
	link?: string;
	actionText?: string;
	isFuture?: boolean;
}

function FeatureCard({
	title,
	description,
	icon: Icon,
	link,
	actionText = 'Learn More',
	isFuture = false,
}: FeatureCardProps) {
	return (
		<Card className='shadow-md flex flex-col bg-card'>
			<CardHeader className='p-5 pb-4'>
				<div className='flex items-center gap-3 mb-2'>
					<Icon className='h-6 w-6 text-accent' />
					<CardTitle className='text-lg font-semibold text-primary'>
						{title}
					</CardTitle>
				</div>
				<CardDescription className='text-sm leading-relaxed'>
					{description}
				</CardDescription>
			</CardHeader>
			<CardContent className='p-5 pt-0 flex-grow'>
				{isFuture && (
					<p className='text-xs text-amber-600 dark:text-amber-500 mb-2'>
						(Coming Soon)
					</p>
				)}
			</CardContent>
			<CardFooter className='p-5 pt-0'>
				{link && (
					<ActionButton actionType='tertiary' asChild className='w-full'>
						<Link href={link}>{actionText}</Link>
					</ActionButton>
				)}
				{!link && isFuture && (
					<ActionButton actionType='tertiary' className='w-full' disabled>
						{actionText}
					</ActionButton>
				)}
			</CardFooter>
		</Card>
	);
}

export default function DashboardPage() {
	const {
		getVehicles,
		getDelegations,
		getTasks,
		getEmployees,
		isAuthenticated,
		isLoading: authLoading,
	} = useApiMethods();

	const [dashboardData, setDashboardData] = useState<{
		vehicleCount: number;
		totalServices: number;
		delegationCount: number;
		taskCount: number;
		employeeCount: number;
	} | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchDashboardData = async () => {
			// Only fetch data if user is authenticated and not loading
			if (!isAuthenticated || authLoading) {
				return;
			}

			setLoading(true);
			setError(null);

			try {
				let vehicleCount = 0;
				let totalServices = 0;
				let delegationCount = 0;
				let taskCount = 0;
				let employeeCount = 0;

				try {
					const vehiclesData = await getVehicles();
					if (Array.isArray(vehiclesData)) {
						vehicleCount = vehiclesData.length;
						totalServices = vehiclesData.reduce(
							(acc, v) => acc + (v.serviceHistory?.length || 0),
							0
						);
					}
				} catch (error) {
					console.error('Failed to fetch vehicle data for dashboard:', error);
				}

				try {
					const delegationsData = await getDelegations();
					if (Array.isArray(delegationsData)) {
						delegationCount = delegationsData.length;
					}
				} catch (error) {
					console.error(
						'Failed to fetch delegation data for dashboard:',
						error
					);
				}

				try {
					const tasksData = await getTasks();
					if (Array.isArray(tasksData)) {
						taskCount = tasksData.filter(
							(t: Task) => t.status !== 'Completed' && t.status !== 'Cancelled'
						).length;
					}
				} catch (error) {
					console.error('Failed to fetch task data for dashboard:', error);
				}

				try {
					const employeesData = await getEmployees();
					if (Array.isArray(employeesData)) {
						employeeCount = employeesData.length;
					}
				} catch (error) {
					console.error('Failed to fetch employee data for dashboard:', error);
				}

				setDashboardData({
					vehicleCount,
					totalServices,
					delegationCount,
					taskCount,
					employeeCount,
				});
			} catch (error) {
				console.error('Error fetching dashboard data:', error);
				setError('Failed to load dashboard data. Please try again.');
			} finally {
				setLoading(false);
			}
		};

		fetchDashboardData();
	}, [isAuthenticated, authLoading]); // Removed API methods from dependencies to prevent infinite loop

	return (
		<div className='space-y-8'>
			<PageHeader
				title='Dashboard'
				description='Welcome to your WorkHub dashboard. Manage everything from here.'
				icon={LayoutDashboard}
			/>
			<section aria-labelledby='quick-actions-title'>
				<h2
					id='quick-actions-title'
					className='text-2xl font-semibold text-foreground mb-4'>
					Quick Actions
				</h2>
				<div className='grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4'>
					<ActionButton
						actionType='secondary'
						size='lg'
						className='w-full py-3 text-base md:text-lg'
						icon={<Eye className='h-4 w-4' />}
						asChild>
						<Link href='/vehicles'>Assets</Link>
					</ActionButton>
					<ActionButton
						actionType='primary'
						size='lg'
						className='w-full py-3 text-base md:text-lg'
						icon={<PlusCircle className='h-4 w-4' />}
						asChild>
						<Link href='/add-vehicle'>Add Asset</Link>
					</ActionButton>
					<ActionButton
						actionType='tertiary'
						size='lg'
						className='w-full py-3 text-base md:text-lg'
						icon={<History className='h-4 w-4' />}
						asChild>
						<Link href='/service-history'>Maintenance</Link>
					</ActionButton>
					<ActionButton
						actionType='tertiary'
						size='lg'
						className='w-full py-3 text-base md:text-lg'
						icon={<Briefcase className='h-4 w-4' />}
						asChild>
						<Link href='/delegations'>Projects</Link>
					</ActionButton>
					<ActionButton
						actionType='tertiary'
						size='lg'
						className='w-full py-3 text-base md:text-lg'
						icon={<ClipboardList className='h-4 w-4' />}
						asChild>
						<Link href='/tasks'>Tasks</Link>
					</ActionButton>
					<ActionButton
						actionType='tertiary'
						size='lg'
						className='w-full py-3 text-base md:text-lg'
						icon={<UsersRound className='h-4 w-4' />}
						asChild>
						<Link href='/employees'>Team</Link>
					</ActionButton>
				</div>
			</section>
			<section aria-labelledby='overview-title'>
				<h2
					id='overview-title'
					className='text-2xl font-semibold text-foreground mb-4'>
					At a Glance
				</h2>
				<DataLoader
					isLoading={loading || authLoading}
					error={error}
					data={dashboardData}
					onRetry={() => window.location.reload()}
					loadingComponent={<SkeletonLoader variant='stats' count={5} />}>
					{(data) => (
						<div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5'>
							<StatCard
								title='Total Assets'
								value={data.vehicleCount}
								icon={Car}
								description='Assets tracked.'
								link='/vehicles'
								linkText='Manage Assets'
							/>
							<StatCard
								title='Maintenance Logged'
								value={data.totalServices}
								icon={Wrench}
								description='Maintenance records.'
								link='/service-history'
								linkText='View History'
							/>
							<StatCard
								title='Active Delegations'
								value={data.delegationCount}
								icon={Briefcase}
								description='Delegations managed.'
								link='/delegations'
								linkText='View Delegations'
							/>
							<StatCard
								title='Pending Tasks'
								value={data.taskCount}
								icon={ClipboardList}
								description='Tasks needing attention.'
								link='/tasks'
								linkText='View Tasks'
							/>
							<StatCard
								title='Team Members'
								value={data.employeeCount}
								icon={UsersRound}
								description='Team members in system.'
								link='/employees'
								linkText='Manage Team'
							/>
						</div>
					)}
				</DataLoader>
			</section>
			<section aria-labelledby='features-title'>
				<h2
					id='features-title'
					className='text-2xl font-semibold text-foreground mb-6'>
					Features & Tools
				</h2>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
					<FeatureCard
						title='Asset Management'
						description='Easily add, view, and manage details for all your assets. Keep everything organized in one central hub.'
						icon={CarFront}
						link='/vehicles'
						actionText='Manage Assets'
					/>
					<FeatureCard
						title='Maintenance History'
						description='Access a comprehensive log of all maintenance for all assets. Filter by asset or service type, and print reports.'
						icon={History}
						link='/service-history'
						actionText='View Maintenance History'
					/>
					<FeatureCard
						title='Delegation & Trip Management'
						description='Track and manage events, trips, delegates, and related flight details efficiently.'
						icon={Briefcase}
						link='/delegations'
						actionText='Manage Delegations'
					/>
					<FeatureCard
						title='Task & Dispatch Management'
						description='Create, assign, and track tasks. Optimize workload and ensure timely completion.'
						icon={Route}
						link='/tasks'
						actionText='Manage Tasks'
					/>
					<FeatureCard
						title='Team Management'
						description='Manage team member profiles, roles, contact information, and assignments.'
						icon={UsersRound}
						link='/employees'
						actionText='Manage Team'
					/>
					<FeatureCard
						title='AI Maintenance Advisor'
						description="Receive intelligent maintenance suggestions based on your asset's details and service history."
						icon={Lightbulb}
						actionText='Get Suggestions'
						link='/vehicles'
					/>
					<FeatureCard
						title='Printable PDF Reports'
						description='Generate professional PDF reports of asset details, maintenance histories, delegations and task lists.'
						icon={Printer}
						actionText='Access Reporting Features'
						isFuture={false}
					/>
					<FeatureCard
						title='Expense Tracking & Analysis'
						description='Monitor all asset-related expenses, categorize spending, and visualize cost trends over time.'
						icon={DollarSign}
						isFuture={true}
						actionText='Track Expenses'
					/>
					<FeatureCard
						title='Automated Reminders'
						description='Set up and receive automated reminders for upcoming maintenance, inspections, and other important dates.'
						icon={CalendarClock}
						isFuture={true}
						actionText='Setup Reminders'
					/>
				</div>
			</section>
		</div>
	);
}
