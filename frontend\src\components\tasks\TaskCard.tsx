'use client';

import Link from 'next/link';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>le,
	CardDescription,
} from '@/components/ui/card';
import {ActionButton} from '@/components/ui/action-button';
import type {Task, Employee} from '@/lib/types';
import {
	ArrowR<PERSON>,
	ClipboardList,
	MapPin,
	Clock,
	AlertTriangle,
	User,
	CalendarDays,
	Users,
} from 'lucide-react';
import {Badge} from '@/components/ui/badge';
import {Separator} from '@/components/ui/separator';
import {cn} from '@/lib/utils';
import {format, parseISO} from 'date-fns';
import {getEmployeeById} from '@/lib/store';
import {useEffect, useState} from 'react';

interface TaskCardProps {
	task: Task;
}

const getStatusColor = (status: Task['status']) => {
	switch (status) {
		case 'Pending':
			return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
		case 'Assigned':
			return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';
		case 'In Progress':
			return 'bg-indigo-500/20 text-indigo-700 border-indigo-500/30 dark:text-indigo-400 dark:bg-indigo-500/10 dark:border-indigo-500/20';
		case 'Completed':
			return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
		case 'Cancelled':
			return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
		default:
			return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
	}
};

const getPriorityColor = (priority: Task['priority']) => {
	switch (priority) {
		case 'Low':
			return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
		case 'Medium':
			return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
		case 'High':
			return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
		default:
			return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
	}
};

const formatDate = (dateString: string | undefined) => {
	if (!dateString) return 'N/A';
	try {
		return format(parseISO(dateString), 'MMM d, yyyy HH:mm');
	} catch (e) {
		return 'Invalid Date';
	}
};

export default function TaskCard({task}: TaskCardProps) {
	const [assignedEmployee, setAssignedEmployee] = useState<
		Employee | null | undefined
	>(undefined);

	useEffect(() => {
		if (task.assignedEmployeeId) {
			setAssignedEmployee(getEmployeeById(task.assignedEmployeeId));
		} else {
			setAssignedEmployee(null);
		}
	}, [task.assignedEmployeeId]);

	const assigneeName = assignedEmployee?.fullName;
	const AssigneeIcon = assignedEmployee?.role === 'driver' ? User : Users;

	return (
		<Card className='overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60'>
			<CardHeader className='p-5'>
				<div className='flex justify-between items-start gap-2'>
					<CardTitle
						className='text-lg font-semibold text-primary line-clamp-2'
						title={task.description}>
						{task.description}
					</CardTitle>
					<div className='flex flex-col items-end gap-1 shrink-0'>
						<Badge
							className={cn(
								'text-xs py-1 px-2 font-semibold',
								getStatusColor(task.status)
							)}>
							{task.status}
						</Badge>
						<Badge
							className={cn(
								'text-xs py-1 px-2 font-semibold',
								getPriorityColor(task.priority)
							)}>
							{task.priority} Priority
						</Badge>
					</div>
				</div>
				<CardDescription className='text-sm text-muted-foreground flex items-center pt-1'>
					<MapPin className='h-4 w-4 mr-1.5 flex-shrink-0 text-accent' />
					{task.location}
				</CardDescription>
			</CardHeader>
			<CardContent className='p-5 flex-grow flex flex-col'>
				<Separator className='my-3 bg-border/50' />
				<div className='space-y-2.5 text-sm text-foreground flex-grow'>
					<div className='flex items-center'>
						<CalendarDays className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
						<div>
							<span className='text-muted-foreground'>Start: </span>
							<strong className='font-semibold'>
								{formatDate(task.dateTime)}
							</strong>
						</div>
					</div>
					{task.deadline && (
						<div className='flex items-center'>
							<Clock className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
							<div>
								<span className='text-muted-foreground'>Deadline: </span>
								<strong className='font-semibold'>
									{formatDate(task.deadline)}
								</strong>
							</div>
						</div>
					)}
					<div className='flex items-center'>
						<Clock className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
						<div>
							<span className='text-muted-foreground'>Duration: </span>
							<strong className='font-semibold'>
								{task.estimatedDuration} mins
							</strong>
						</div>
					</div>
					{assigneeName && (
						<div className='flex items-center'>
							<AssigneeIcon className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
							<div>
								<span className='text-muted-foreground'>Assigned to: </span>
								<strong className='font-semibold'>
									{assigneeName} (
									{assignedEmployee?.role
										? assignedEmployee.role.charAt(0).toUpperCase() +
										  assignedEmployee.role.slice(1).replace('_', ' ')
										: 'Employee'}
									)
								</strong>
							</div>
						</div>
					)}
					{!assigneeName &&
						task.status !== 'Completed' &&
						task.status !== 'Cancelled' && (
							<div className='flex items-center'>
								<User className='mr-2.5 h-4 w-4 text-destructive flex-shrink-0' />
								<strong className='font-semibold text-destructive'>
									Unassigned
								</strong>
							</div>
						)}
				</div>
				{task.notes && (
					<p
						className='mt-3 text-xs text-muted-foreground line-clamp-2 pt-2 border-t border-dashed border-border/50'
						title={task.notes}>
						{task.notes}
					</p>
				)}
			</CardContent>
			<CardFooter className='p-4 border-t border-border/60 bg-muted/20'>
				<ActionButton
					actionType='tertiary'
					className='w-full'
					icon={<ArrowRight className='h-4 w-4' />}
					asChild>
					<Link href={`/tasks/${task.id}`}>View Details</Link>
				</ActionButton>
			</CardFooter>
		</Card>
	);
}
