// @ts-nocheck
'use client';

import {useState} from 'react';
import {Button} from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {Textarea} from '@/components/ui/textarea';
import {Alert, AlertDescription, AlertTitle} from '@/components/ui/alert';
import {Lightbulb, AlertTriangle, Loader2, Info} from 'lucide-react';
import type {Vehicle, ServiceRecord} from '@/lib/types';
import {getMaintenanceSuggestions} from '@/app/actions';
import type {
	SuggestMaintenanceScheduleInput,
	SuggestMaintenanceScheduleOutput,
} from '@/ai/flows/suggest-maintenance-schedule';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';

interface MaintenanceSuggestorProps {
	vehicle: Vehicle;
}

function formatServiceHistoryForAI(serviceHistory: ServiceRecord[]): string {
	if (!serviceHistory || serviceHistory.length === 0) {
		return 'No service history available.';
	}
	return serviceHistory
		.map(
			(record) =>
				`Date: ${record.date}, Mileage: ${
					record.odometer
				} miles, Service: ${record.servicePerformed.join(', ')}${
					record.notes ? `, Notes: ${record.notes}` : ''
				}${record.cost ? `, Cost: $${record.cost}` : ''}`
		)
		.join('; ');
}

export default function MaintenanceSuggestor({
	vehicle,
}: MaintenanceSuggestorProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [suggestion, setSuggestion] =
		useState<SuggestMaintenanceScheduleOutput | null>(null);
	const [error, setError] = useState<string | null>(null);
	const [currentOdometer, setCurrentOdometer] = useState<number>(
		vehicle.serviceHistory.length > 0
			? Math.max(...vehicle.serviceHistory.map((s) => s.odometer))
			: vehicle.initialOdometer || 0
	);

	const handleGetSuggestion = async () => {
		setIsLoading(true);
		setSuggestion(null);
		setError(null);

		if (currentOdometer < vehicle.initialOdometer) {
			setError('Current odometer cannot be less than initial odometer.');
			setIsLoading(false);
			return;
		}
		if (
			vehicle.serviceHistory.some((record) => record.odometer > currentOdometer)
		) {
			setError(
				'Current odometer cannot be less than a previously logged service odometer.'
			);
			setIsLoading(false);
			return;
		}

		const formattedHistory = formatServiceHistoryForAI(vehicle.serviceHistory);
		const input: SuggestMaintenanceScheduleInput = {
			vehicleMake: vehicle.make,
			vehicleModel: vehicle.model,
			vehicleYear: vehicle.year,
			currentOdometer: currentOdometer,
			serviceHistory: formattedHistory,
		};

		const result = await getMaintenanceSuggestions(input);

		if (result.success && result.data) {
			setSuggestion(result.data);
		} else {
			setError(result.error || 'Failed to get suggestions.');
		}
		setIsLoading(false);
	};

	return (
		<Card className='shadow-lg bg-card'>
			<CardHeader>
				<CardTitle className='text-xl font-semibold text-primary flex items-center'>
					<Lightbulb className='mr-2 h-5 w-5 text-accent' />
					AI Maintenance Advisor
				</CardTitle>
				<CardDescription>
					Get AI-powered maintenance schedule suggestions based on your
					vehicle's details and service history.
				</CardDescription>
			</CardHeader>
			<CardContent className='space-y-4'>
				<div>
					<Label
						htmlFor='currentOdometerAi'
						className='mb-1 block text-sm font-medium'>
						Current Odometer for Suggestion
					</Label>
					<Input
						id='currentOdometerAi'
						type='number'
						value={currentOdometer}
						onChange={(e) =>
							setCurrentOdometer(parseInt(e.target.value, 10) || 0)
						}
						placeholder='Enter current mileage'
						className='mb-4'
					/>
				</div>
				<Button
					onClick={handleGetSuggestion}
					disabled={isLoading}
					className='w-full bg-accent text-accent-foreground hover:bg-accent/90'>
					{isLoading ? (
						<>
							<Loader2 className='mr-2 h-4 w-4 animate-spin' />
							Generating...
						</>
					) : (
						'Get Maintenance Suggestions'
					)}
				</Button>

				{error && (
					<Alert variant='destructive' className='mt-4'>
						<AlertTriangle className='h-4 w-4' />
						<AlertTitle>Error</AlertTitle>
						<AlertDescription>{error}</AlertDescription>
					</Alert>
				)}

				{suggestion && (
					<div className='mt-6 space-y-4 p-4 border border-border rounded-lg bg-background'>
						<div>
							<h4 className='font-semibold text-lg text-primary mb-2'>
								Suggested Maintenance Schedule:
							</h4>
							<Textarea
								readOnly
								value={suggestion.suggestedMaintenanceSchedule}
								className='min-h-[150px] bg-muted/50'
								aria-label='Suggested Maintenance Schedule'
							/>
						</div>
						<div>
							<h4 className='font-semibold text-lg text-primary mb-2'>
								Reasoning:
							</h4>
							<Textarea
								readOnly
								value={suggestion.reasoning}
								className='min-h-[100px] bg-muted/50'
								aria-label='Reasoning for suggestions'
							/>
						</div>
					</div>
				)}
				{!suggestion && !isLoading && !error && (
					<Alert className='mt-4 border-primary/30'>
						<Info className='h-4 w-4 text-primary' />
						<AlertTitle className='text-primary'>Ready for Advice?</AlertTitle>
						<AlertDescription>
							Enter your vehicle's current odometer reading above and click the
							button to receive personalized maintenance suggestions from our AI
							Advisor.
						</AlertDescription>
					</Alert>
				)}
			</CardContent>
		</Card>
	);
}
