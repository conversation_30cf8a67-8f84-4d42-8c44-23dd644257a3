import request from 'supertest';
import express from 'express';
// import { prisma } from '../services/database.service'; // Removed
// import directServiceRecordRoutes from '../routes/directServiceRecord.routes'; // To be dynamically imported
// import serviceRecordRoutes from '../routes/serviceRecord.routes'; // To be dynamically imported
// import * as serviceRecordController from '../controllers/serviceRecord.controller'; // To be mocked

// --- Mock Controller Functions START ---
const mockCtrlGetAllEnrichedServiceRecords = jest.fn();
const mockCtrlGetServiceRecordById = jest.fn();
const mockCtrlCreateServiceRecord = jest.fn();
const mockCtrlUpdateServiceRecord = jest.fn();
const mockCtrlDeleteServiceRecord = jest.fn();
const mockCtrlGetServiceRecordsByVehicleId = jest.fn();
const mockCtrlCreateServiceRecordForVehicle = jest.fn();

(jest as any).unstable_mockModule(
	'src/controllers/serviceRecord.controller.ts',
	() => ({
		__esModule: true, // Needed if controller uses ES Modules and is imported with import * as
		getAllEnrichedServiceRecords: mockCtrlGetAllEnrichedServiceRecords,
		getServiceRecordById: mockCtrlGetServiceRecordById,
		createServiceRecord: mockCtrlCreateServiceRecord,
		updateServiceRecord: mockCtrlUpdateServiceRecord,
		deleteServiceRecord: mockCtrlDeleteServiceRecord,
		getServiceRecordsByVehicleId: mockCtrlGetServiceRecordsByVehicleId,
		createServiceRecordForVehicle: mockCtrlCreateServiceRecordForVehicle,
	})
);
// --- Mock Controller Functions END ---

// --- Mock Service Functions START ---
// Prisma Client is part of database.service.ts, so we mock that service.
// The mock for database.service.ts will provide the mocked prisma instance.
const mockPrismaServiceRecordFindMany = jest.fn();
const mockPrismaServiceRecordFindUnique = jest.fn();
const mockPrismaServiceRecordCreate = jest.fn();
const mockPrismaServiceRecordUpdate = jest.fn();
const mockPrismaServiceRecordDelete = jest.fn();
const mockPrismaVehicleFindMany = jest.fn();
const mockPrismaEmployeeFindMany = jest.fn();
const mockTestDatabaseConnections = jest.fn();
const mockGetDatabaseConfig = jest.fn();

(jest as any).unstable_mockModule('src/services/database.service.ts', () => ({
	__esModule: true,
	prisma: {
		serviceRecord: {
			findMany: mockPrismaServiceRecordFindMany,
			findUnique: mockPrismaServiceRecordFindUnique,
			create: mockPrismaServiceRecordCreate,
			update: mockPrismaServiceRecordUpdate,
			delete: mockPrismaServiceRecordDelete,
		},
		vehicle: {
			findMany: mockPrismaVehicleFindMany,
		},
		employee: {
			findMany: mockPrismaEmployeeFindMany,
		},
	},
	supabase: null, // Or mock if used
	testDatabaseConnections: mockTestDatabaseConnections,
	getDatabaseConfig: mockGetDatabaseConfig,
}));

const mockLoggerInfo = jest.fn();
const mockLoggerWarn = jest.fn();
const mockLoggerError = jest.fn();
const mockLoggerDebug = jest.fn();

(jest as any).unstable_mockModule('src/utils/logger.ts', () => ({
	__esModule: true,
	default: {
		info: mockLoggerInfo,
		warn: mockLoggerWarn,
		error: mockLoggerError,
		debug: mockLoggerDebug,
	},
	logger: {
		// Also provide named export if it's used like logger.info()
		info: mockLoggerInfo,
		warn: mockLoggerWarn,
		error: mockLoggerError,
		debug: mockLoggerDebug,
	},
}));

const mockEmitServiceRecordChange = jest.fn();

(jest as any).unstable_mockModule('src/services/socket.service.ts', () => ({
	__esModule: true,
	emitServiceRecordChange: mockEmitServiceRecordChange,
	SOCKET_EVENTS: {
		SERVICE_RECORD_CREATED: 'service_record_created',
		SERVICE_RECORD_UPDATED: 'service_record_updated',
		SERVICE_RECORD_DELETED: 'service_record_deleted',
		VEHICLE_UPDATED: 'vehicle_updated',
	},
}));
// --- Mock Service Functions END ---

// Old jest.mock for database.service removed
// Old jest.mock for logger removed
// Old jest.mock for socket.service removed

// Dynamically imported routes and controller
let directServiceRecordRoutes: express.Router;
let serviceRecordRoutes: express.Router;
// let serviceRecordController: typeof import('../controllers/serviceRecord.controller');
// let prismaMock: any; // This will be the mocked prisma instance from database.service mock

describe('Service Record Routes', () => {
	let app: express.Application;

	beforeAll(async () => {
		// Ensure mocks are loaded first by importing the modules we mocked.
		// The actual controller is not needed here because its methods are mocked directly.
		// const controllerModule = await import('src/controllers/serviceRecord.controller.ts');
		// serviceRecordController = controllerModule;
		await import('src/services/database.service.ts');
		// const dbModule = await import('src/services/database.service.ts');
		// prismaMock = dbModule.prisma;
		await import('src/utils/logger.ts');
		await import('src/services/socket.service.ts');

		// Dynamically import routes AFTER mocks are set up
		const directRoutesModule = await import(
			'../routes/directServiceRecord.routes'
		);
		directServiceRecordRoutes = directRoutesModule.default;
		const vehicleRoutesModule = await import('../routes/serviceRecord.routes');
		serviceRecordRoutes = vehicleRoutesModule.default;
	});

	beforeEach(() => {
		app = express();
		app.use(express.json());
		app.use('/api/servicerecords', directServiceRecordRoutes);
		app.use('/api/vehicles/:vehicleId/servicerecords', serviceRecordRoutes);

		// Reset all mocks before each test
		jest.clearAllMocks();

		// More specific resets for controller mocks that might have mockImplementations
		mockCtrlGetAllEnrichedServiceRecords.mockReset();
		mockCtrlGetServiceRecordById.mockReset();
		mockCtrlCreateServiceRecord.mockReset();
		mockCtrlUpdateServiceRecord.mockReset();
		mockCtrlDeleteServiceRecord.mockReset();
		mockCtrlGetServiceRecordsByVehicleId.mockReset();
		mockCtrlCreateServiceRecordForVehicle.mockReset();
	});

	describe('GET /api/servicerecords/enriched', () => {
		it('should return enriched service records', async () => {
			const mockServiceRecords = [
				{
					id: '1',
					vehicleId: 1,
					employeeId: 1,
					date: new Date(),
					odometer: 10000,
					servicePerformed: ['Oil Change'],
					notes: 'Regular maintenance',
					cost: 50,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			];

			const mockVehicles = [
				{
					id: 1,
					make: 'Toyota',
					model: 'Camry',
					year: 2020,
					licensePlate: 'ABC123',
				},
			];

			const mockEmployees = [
				{
					id: 1,
					name: 'John Doe',
					fullName: 'John A. Doe',
					role: 'mechanic',
				},
			];

			const expectedEnrichedResponse = [
				{
					...mockServiceRecords[0],
					vehicleMake: mockVehicles[0].make,
					vehicleModel: mockVehicles[0].model,
					vehicleYear: mockVehicles[0].year,
					vehiclePlateNumber: mockVehicles[0].licensePlate,
					employeeName: mockEmployees[0].name,
					employeeFullName: mockEmployees[0].fullName,
					employeeRole: mockEmployees[0].role,
				},
			];

			mockCtrlGetAllEnrichedServiceRecords.mockImplementation((req, res) => {
				res.status(200).json(expectedEnrichedResponse);
			});

			const response = await request(app).get('/api/servicerecords/enriched');

			expect(response.status).toBe(200);
			expect(response.body).toEqual(expectedEnrichedResponse);
			expect(mockCtrlGetAllEnrichedServiceRecords).toHaveBeenCalledTimes(1);
		});

		it('should handle empty service records gracefully', async () => {
			mockCtrlGetAllEnrichedServiceRecords.mockImplementation((req, res) => {
				res.status(200).json([]);
			});
			const response = await request(app).get('/api/servicerecords/enriched');
			expect(response.status).toBe(200);
			expect(response.body).toEqual([]);
			expect(mockCtrlGetAllEnrichedServiceRecords).toHaveBeenCalledTimes(1);
		});

		it('should handle controller errors gracefully by returning 500', async () => {
			mockCtrlGetAllEnrichedServiceRecords.mockImplementation((req, res) => {
				// Simulate an error by the controller, which should be caught by error handling middleware (if any) or result in 500
				res.status(500).json({message: 'Controller error'});
			});
			const response = await request(app).get('/api/servicerecords/enriched');
			expect(response.status).toBe(500);
			expect(response.body).toEqual({message: 'Controller error'});
			expect(mockCtrlGetAllEnrichedServiceRecords).toHaveBeenCalledTimes(1);
		});
	});

	describe('GET /api/servicerecords/:id', () => {
		it('should return a service record by ID', async () => {
			const mockServiceRecord = {
				id: '1',
				vehicleId: 1,
				employeeId: 1,
				date: new Date(),
				odometer: 10000,
				servicePerformed: ['Oil Change'],
				notes: 'Regular maintenance',
				cost: 50,
				createdAt: new Date(),
				updatedAt: new Date(),
			};
			mockCtrlGetServiceRecordById.mockImplementation((req, res) => {
				if (req.params.id === '1') {
					res.status(200).json(mockServiceRecord);
				} else {
					res.status(404).json({message: 'Service record not found'});
				}
			});

			const response = await request(app).get('/api/servicerecords/1');

			expect(response.status).toBe(200);
			expect(response.body).toEqual(mockServiceRecord);
			expect(mockCtrlGetServiceRecordById).toHaveBeenCalledTimes(1);
		});

		it('should return 404 if service record not found', async () => {
			mockCtrlGetServiceRecordById.mockImplementation((req, res) => {
				res.status(404).json({message: 'Service record not found'});
			});

			const response = await request(app).get('/api/servicerecords/999');

			expect(response.status).toBe(404);
			expect(response.body).toHaveProperty(
				'message',
				'Service record not found'
			);
			expect(mockCtrlGetServiceRecordById).toHaveBeenCalledTimes(1);
		});

		it('should handle controller errors gracefully by returning 500', async () => {
			mockCtrlGetServiceRecordById.mockImplementation((req, res) => {
				res.status(500).json({message: 'Controller error'});
			});

			const response = await request(app).get('/api/servicerecords/123'); // ID doesn't matter here

			expect(response.status).toBe(500);
			expect(response.body).toEqual({message: 'Controller error'});
			expect(mockCtrlGetServiceRecordById).toHaveBeenCalledTimes(1);
		});
	});
});
