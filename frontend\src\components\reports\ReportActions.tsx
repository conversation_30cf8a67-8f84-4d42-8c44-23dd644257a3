'use client';

import React, {useState} from 'react';
import {ActionButton} from '@/components/ui/action-button';
import {
	Printer,
	Download,
	FileText,
	FileSpreadsheet,
	Loader2,
} from 'lucide-react';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	generateCsvFromData,
	extractTableDataForCsv,
} from '@/lib/utils/fileUtils';
import {useToast} from '@/hooks/use-toast';
import {cn} from '@/lib/utils';

export interface ReportActionsProps {
	/** ID or selector of the report content element for UI purposes */
	reportContentId: string;
	/** Type of report to download (e.g., 'vehicle', 'service', 'delegation') */
	reportType: string;
	/** ID of the entity to generate the report for */
	entityId?: string;
	/** ID or selector of the table element (for CSV export from table) */
	tableId?: string;
	/** Base name for downloaded files (without extension) */
	fileName: string;
	/** Whether CSV export is available */
	enableCsv?: boolean;
	/** Raw data for CSV export (alternative to tableId if CSV is enabled) */
	csvData?: {
		headers: string[];
		data: any[][];
	};
	/** Additional CSS class names */
	className?: string;
}

/**
 * Standardized report actions component for print and download functionality
 */
export function ReportActions({
	reportContentId,
	reportType,
	entityId,
	tableId,
	fileName,
	enableCsv = false,
	csvData,
	className,
}: ReportActionsProps) {
	const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
	const [isGeneratingCsv, setIsGeneratingCsv] = useState(false);
	const {toast} = useToast();

	const handlePrint = () => {
		if (typeof window !== 'undefined') {
			window.print();
		}
	};

	const handleDownloadPdf = async () => {
		setIsGeneratingPdf(true);
		try {
			// Use the backend API to generate and download the PDF
			const apiUrl = `/api/reports/${reportType}${
				entityId ? `/${entityId}` : ''
			}`;

			// Create a link to download the file directly from the API
			const link = document.createElement('a');
			link.href = apiUrl;
			link.download = `${fileName}.pdf`;
			link.target = '_blank'; // Open in a new tab/window
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			toast({
				title: 'PDF Downloaded',
				description: 'Your report is being downloaded as a PDF.',
				variant: 'default',
			});
		} catch (error: any) {
			console.error('Error generating PDF:', error);
			toast({
				title: 'Download Failed',
				description: `PDF download failed: ${
					error.message || 'Please try again.'
				}`,
				variant: 'destructive',
			});
		} finally {
			setIsGeneratingPdf(false);
		}
	};

	const handleDownloadCsv = async () => {
		if (!enableCsv) return;
		setIsGeneratingCsv(true);
		try {
			if (csvData && csvData.data && csvData.headers) {
				generateCsvFromData(csvData.data, csvData.headers, `${fileName}.csv`);
			} else if (tableId) {
				const extractedData = extractTableDataForCsv(tableId);
				generateCsvFromData(
					extractedData.data,
					extractedData.headers,
					`${fileName}.csv`
				);
			} else {
				throw new Error(
					'CSV export requires either `csvData` or a `tableId` to be provided.'
				);
			}
			toast({
				title: 'CSV Downloaded',
				description: 'Your report has been downloaded as a CSV file.',
				variant: 'default',
			});
		} catch (error: any) {
			console.error('Error generating CSV:', error);
			toast({
				title: 'Download Failed',
				description: `CSV generation failed: ${
					error.message || 'Please try again.'
				}`,
				variant: 'destructive',
			});
		} finally {
			setIsGeneratingCsv(false);
		}
	};

	const isGenerating = isGeneratingPdf || isGeneratingCsv;

	return (
		<div className={cn('flex items-center gap-2 no-print', className)}>
			<ActionButton
				actionType='secondary'
				size='icon'
				onClick={handlePrint}
				aria-label='Print report'
				title='Print Report'>
				<Printer className='h-4 w-4' />
			</ActionButton>

			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<ActionButton
						actionType='secondary'
						size='icon'
						aria-label='Download report'
						disabled={isGenerating}
						title='Download Report'>
						{isGenerating ? (
							<Loader2 className='h-4 w-4 animate-spin' />
						) : (
							<Download className='h-4 w-4' />
						)}
					</ActionButton>
				</DropdownMenuTrigger>
				<DropdownMenuContent align='end'>
					<DropdownMenuItem
						onClick={handleDownloadPdf}
						disabled={isGeneratingPdf}>
						{isGeneratingPdf ? (
							<Loader2 className='mr-2 h-4 w-4 animate-spin' />
						) : (
							<FileText className='mr-2 h-4 w-4' />
						)}
						<span>Download PDF</span>
					</DropdownMenuItem>
					{enableCsv && (
						<DropdownMenuItem
							onClick={handleDownloadCsv}
							disabled={isGeneratingCsv}>
							{isGeneratingCsv ? (
								<Loader2 className='mr-2 h-4 w-4 animate-spin' />
							) : (
								<FileSpreadsheet className='mr-2 h-4 w-4' />
							)}
							<span>Download CSV</span>
						</DropdownMenuItem>
					)}
				</DropdownMenuContent>
			</DropdownMenu>
		</div>
	);
}
