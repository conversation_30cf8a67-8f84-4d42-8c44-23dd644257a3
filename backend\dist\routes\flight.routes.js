/**
 * Flight Routes
 *
 * This file defines the routes for flight-related API endpoints.
 */
import { Router } from 'express';
import * as flightController from '../controllers/flight.controller.js';
import { authenticateSupabaseUser } from '../middleware/supabaseAuth.js';
const router = Router();
// 🚨 EMERGENCY SECURITY: All flight routes require authentication
router.use(authenticateSupabaseUser);
/**
 * @openapi
 * /flights/search:
 *   get:
 *     tags: [Flights]
 *     summary: Search for flights by callsign
 *     parameters:
 *       - in: query
 *         name: callsign
 *         required: true
 *         schema:
 *           type: string
 *         description: Flight callsign to search for (e.g., "SWR123")
 *     responses:
 *       200:
 *         description: A list of flights matching the callsign
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Server error
 */
router.get('/search', flightController.searchFlights);
/**
 * @openapi
 * /flights/airport:
 *   get:
 *     tags: [Flights]
 *     summary: Get flights by airport (arrivals or departures)
 *     parameters:
 *       - in: query
 *         name: airport
 *         required: true
 *         schema:
 *           type: string
 *         description: ICAO code of the airport (e.g., "EDDF" for Frankfurt)
 *       - in: query
 *         name: begin
 *         required: true
 *         schema:
 *           type: integer
 *         description: Start time in seconds since epoch (Unix timestamp)
 *       - in: query
 *         name: end
 *         required: true
 *         schema:
 *           type: integer
 *         description: End time in seconds since epoch (Unix timestamp)
 *       - in: query
 *         name: type
 *         required: false
 *         schema:
 *           type: string
 *           enum: [arrival, departure]
 *         description: Type of flights to retrieve (default is arrival)
 *     responses:
 *       200:
 *         description: A list of flights for the specified airport
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Server error
 */
router.get('/airport', flightController.getFlightsByAirport);
/**
 * @openapi
 * /flights/interval:
 *   get:
 *     tags: [Flights]
 *     summary: Get flights by time interval
 *     parameters:
 *       - in: query
 *         name: begin
 *         required: true
 *         schema:
 *           type: integer
 *         description: Start time in seconds since epoch (Unix timestamp)
 *       - in: query
 *         name: end
 *         required: true
 *         schema:
 *           type: integer
 *         description: End time in seconds since epoch (Unix timestamp)
 *     responses:
 *       200:
 *         description: A list of flights in the specified time interval
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Server error
 */
router.get('/interval', flightController.getFlightsByTimeInterval);
export default router;
//# sourceMappingURL=flight.routes.js.map