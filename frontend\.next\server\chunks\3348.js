"use strict";exports.id=3348,exports.ids=[3348],exports.modules={15036:(e,a,s)=>{s.d(a,{A:()=>t});let t=(0,s(82614).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},15079:(e,a,s)=>{s.d(a,{bq:()=>x,eb:()=>j,gC:()=>h,l6:()=>c,yv:()=>m});var t=s(60687),l=s(43210),r=s(22670),i=s(61662),n=s(89743),d=s(58450),o=s(4780);let c=r.bL;r.YJ;let m=r.WT,x=l.forwardRef(({className:e,children:a,...s},l)=>(0,t.jsxs)(r.l9,{ref:l,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[a,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=r.l9.displayName;let u=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)(r.PP,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})}));u.displayName=r.PP.displayName;let p=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)(r.wn,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}));p.displayName=r.wn.displayName;let h=l.forwardRef(({className:e,children:a,position:s="popper",...l},i)=>(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{ref:i,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...l,children:[(0,t.jsx)(u,{}),(0,t.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,t.jsx)(p,{})]})}));h.displayName=r.UC.displayName,l.forwardRef(({className:e,...a},s)=>(0,t.jsx)(r.JU,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=r.JU.displayName;let j=l.forwardRef(({className:e,children:a,...s},l)=>(0,t.jsxs)(r.q7,{ref:l,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(r.p4,{children:a})]}));j.displayName=r.q7.displayName,l.forwardRef(({className:e,...a},s)=>(0,t.jsx)(r.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=r.wv.displayName},34729:(e,a,s)=>{s.d(a,{T:()=>i});var t=s(60687),l=s(43210),r=s(4780);let i=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...a}));i.displayName="Textarea"},48041:(e,a,s)=>{s.d(a,{z:()=>l});var t=s(60687);function l({title:e,description:a,icon:s,children:l}){return(0,t.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[s&&(0,t.jsx)(s,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),a&&(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:a})]}),l&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:l})]})}s(43210)},55817:(e,a,s)=>{s.d(a,{A:()=>t});let t=(0,s(82614).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},60477:(e,a,s)=>{s.d(a,{l8:()=>r,pj:()=>d,xb:()=>i});var t=s(45880),l=s(65594);let r=t.k5(["Pending","Assigned","In Progress","Completed","Cancelled"]),i=t.k5(["Low","Medium","High"]),n=t.Ik({id:t.Yj().uuid().optional(),title:t.Yj().min(1,"Subtask title cannot be empty"),completed:t.zM().default(!1)}),d=t.Ik({id:t.Yj().uuid().optional(),description:t.Yj().min(1,"Task description is required"),location:t.Yj().min(1,"Location is required"),dateTime:t.Yj().min(1,"Start date & time is required").refine(e=>(0,l.isValidDateString)(e),{message:"Please enter a valid date and time in YYYY-MM-DD HH:MM format"}),estimatedDuration:t.au.number().int().min(1,"Estimated duration must be at least 1 minute"),requiredSkills:t.YO(t.Yj()).optional().default([]),priority:i.default("Medium"),deadline:t.Yj().refine(e=>""===e||(0,l.isValidDateString)(e),{message:"Please enter a valid deadline date and time in YYYY-MM-DD HH:MM format"}).optional().transform(e=>""===e?void 0:e),status:r.default("Pending"),assignedEmployeeIds:t.YO(t.ai().int().positive("Employee ID must be a positive integer.")).optional().default([]),subTasks:t.YO(n).optional().default([]),notes:t.Yj().optional().or(t.eu("")),vehicleId:t.ai().int().positive("Vehicle ID must be a positive integer.").nullable().optional(),statusChangeReason:t.Yj().optional()}).superRefine((e,a)=>{if(e.dateTime&&e.deadline){let s=new Date(e.dateTime);new Date(e.deadline)<s&&a.addIssue({code:t.eq.custom,message:"Deadline cannot be earlier than the start date & time",path:["deadline"]})}})},71273:(e,a,s)=>{s.d(a,{A:()=>t});let t=(0,s(82614).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},71669:(e,a,s)=>{s.d(a,{C5:()=>f,MJ:()=>j,eI:()=>p,lR:()=>h,lV:()=>o,zB:()=>m});var t=s(60687),l=s(43210),r=s(8730),i=s(27605),n=s(4780),d=s(80013);let o=i.Op,c=l.createContext({}),m=({...e})=>(0,t.jsx)(c.Provider,{value:{name:e.name},children:(0,t.jsx)(i.xI,{...e})}),x=()=>{let e=l.useContext(c),a=l.useContext(u),{getFieldState:s,formState:t}=(0,i.xW)(),r=s(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=a;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...r}},u=l.createContext({}),p=l.forwardRef(({className:e,...a},s)=>{let r=l.useId();return(0,t.jsx)(u.Provider,{value:{id:r},children:(0,t.jsx)("div",{ref:s,className:(0,n.cn)("space-y-2",e),...a})})});p.displayName="FormItem";let h=l.forwardRef(({className:e,...a},s)=>{let{error:l,formItemId:r}=x();return(0,t.jsx)(d.J,{ref:s,className:(0,n.cn)(l&&"text-destructive",e),htmlFor:r,...a})});h.displayName="FormLabel";let j=l.forwardRef(({...e},a)=>{let{error:s,formItemId:l,formDescriptionId:i,formMessageId:n}=x();return(0,t.jsx)(r.DX,{ref:a,id:l,"aria-describedby":s?`${i} ${n}`:`${i}`,"aria-invalid":!!s,...e})});j.displayName="FormControl",l.forwardRef(({className:e,...a},s)=>{let{formDescriptionId:l}=x();return(0,t.jsx)("p",{ref:s,id:l,className:(0,n.cn)("text-sm text-muted-foreground",e),...a})}).displayName="FormDescription";let f=l.forwardRef(({className:e,children:a,...s},l)=>{let{error:r,formMessageId:i}=x(),d=r?String(r?.message??""):a;return d?(0,t.jsx)("p",{ref:l,id:i,className:(0,n.cn)("text-sm font-medium text-destructive",e),...s,children:d}):null});f.displayName="FormMessage"},91188:(e,a,s)=>{s.d(a,{A:()=>I});var t=s(60687),l=s(27605),r=s(63442),i=s(60477),n=s(29523),d=s(89667),o=s(34729),c=s(44493),m=s(71669),x=s(15079),u=s(80489),p=s(15036),h=s(14975),j=s(55817),f=s(71273),g=s(16189),v=s(29867);s(28840);var y=s(43210),b=s(65594),N=s(76869);let w="__UNASSIGNED__",C="__NO_VEHICLE__";function I({onSubmit:e,initialData:a,isEditing:s=!1}){let I=(0,g.useRouter)(),{toast:k}=(0,v.dj)(),[A,M]=(0,y.useState)([]),[S,R]=(0,y.useState)([]),D=A.filter(e=>"Active"===e.status),T=(0,l.mN)({resolver:(0,r.u)(i.pj),defaultValues:{description:a?.description||"",location:a?.location||"",dateTime:a?(0,b.formatDateForInput)(a.dateTime,"datetime-local"):(0,N.GP)(new Date,"yyyy-MM-dd'T'HH:mm"),estimatedDuration:a?.estimatedDuration||60,requiredSkills:a?.requiredSkills||[],priority:a?.priority||"Medium",deadline:a?.deadline?(0,b.formatDateForInput)(a.deadline,"datetime-local"):"",status:a?.status||"Pending",assignedEmployeeIds:a?.assignedTo?.map(e=>Number(e)).filter(e=>!isNaN(e))||[],notes:a?.notes||"",vehicleId:a?.vehicleId?Number(a.vehicleId):null}});return(0,t.jsx)(m.lV,{...T,children:(0,t.jsx)("form",{onSubmit:T.handleSubmit(a=>{e({...a,dateTime:(0,b.formatDateForApi)(a.dateTime),deadline:a.deadline?(0,b.formatDateForApi)(a.deadline):void 0,vehicleId:a.vehicleId?Number(a.vehicleId):null,assignedEmployeeIds:Array.isArray(a.assignedEmployeeIds)?a.assignedEmployeeIds.map(e=>Number(e)):[]})}),children:(0,t.jsxs)(c.Zp,{className:"shadow-lg",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"text-2xl text-primary",children:s?"Edit Task":"Add New Task"}),(0,t.jsx)(c.BT,{children:"Enter the details for the task."})]}),(0,t.jsxs)(c.Wu,{className:"space-y-6",children:[(0,t.jsxs)("section",{className:"space-y-4 p-4 border rounded-lg bg-card",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,t.jsx)(u.A,{className:"mr-2 h-5 w-5 text-accent"}),"Task Details"]}),(0,t.jsx)(m.zB,{control:T.control,name:"description",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Description"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(o.T,{placeholder:"e.g., Pick up package from Warehouse A",...e})}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsx)(m.zB,{control:T.control,name:"location",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Location"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(d.p,{placeholder:"e.g., 123 Main St, City Center",...e})}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(m.zB,{control:T.control,name:"dateTime",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Start Date & Time"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(d.p,{type:"datetime-local",...e})}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsx)(m.zB,{control:T.control,name:"estimatedDuration",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Estimated Duration (minutes)"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(d.p,{type:"number",placeholder:"e.g., 60",...e})}),(0,t.jsx)(m.C5,{})]})})]})]}),(0,t.jsxs)("section",{className:"space-y-4 p-4 border rounded-lg bg-card",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,t.jsx)(p.A,{className:"mr-2 h-5 w-5 text-accent"}),"Scheduling & Assignment"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(m.zB,{control:T.control,name:"priority",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Priority"}),(0,t.jsxs)(x.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,t.jsx)(m.MJ,{children:(0,t.jsx)(x.bq,{children:(0,t.jsx)(x.yv,{placeholder:"Select priority"})})}),(0,t.jsx)(x.gC,{children:i.xb.options.map(e=>(0,t.jsx)(x.eb,{value:e,children:e},e))})]}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsx)(m.zB,{control:T.control,name:"deadline",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Deadline (Optional)"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(d.p,{type:"datetime-local",...e,value:e.value||""})}),(0,t.jsx)(m.C5,{})]})})]}),(0,t.jsx)(m.zB,{control:T.control,name:"status",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Status"}),(0,t.jsxs)(x.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,t.jsx)(m.MJ,{children:(0,t.jsx)(x.bq,{children:(0,t.jsx)(x.yv,{placeholder:"Select status"})})}),(0,t.jsx)(x.gC,{children:i.l8.options.map(e=>(0,t.jsx)(x.eb,{value:e,children:e},e))})]}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsx)(m.zB,{control:T.control,name:"assignedEmployeeIds",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Assign to Employees (Optional)"}),(0,t.jsxs)(x.l6,{onValueChange:a=>{a===w?e.onChange([]):a?e.onChange([Number(a)]):e.onChange([])},value:e.value&&e.value.length>0?String(e.value[0]):w,children:[(0,t.jsx)(m.MJ,{children:(0,t.jsx)(x.bq,{children:(0,t.jsx)(x.yv,{placeholder:"Select an employee (optional)"})})}),(0,t.jsxs)(x.gC,{children:[(0,t.jsx)(x.eb,{value:w,children:"Unassigned"}),D.map(e=>{let a="driver"===e.role&&e.vehicleId?S.find(a=>a.id===Number(e.vehicleId)):null;return(0,t.jsxs)(x.eb,{value:String(e.id),children:[e.fullName," (",e.role.charAt(0).toUpperCase()+e.role.slice(1).replace("_"," "),", ",e.status,"driver"===e.role&&e.availability&&(0,t.jsxs)("span",{children:[", ",e.availability.replace("_"," ")]}),"driver"===e.role&&e.currentLocation&&(0,t.jsxs)("span",{children:[", @ ",e.currentLocation]}),"driver"===e.role&&(0,t.jsxs)("span",{children:[", Vehicle:"," ",a?`${a.make} ${a.model}`:"None"]}),")"]},e.id)})]})]}),(0,t.jsx)(m.C5,{})]})})]}),(0,t.jsxs)("section",{className:"space-y-4 p-4 border rounded-lg bg-card",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,t.jsx)(h.A,{className:"mr-2 h-5 w-5 text-accent"}),"Additional Information"]}),(0,t.jsx)(m.zB,{control:T.control,name:"requiredSkills",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Required Skills (Optional, comma-separated)"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(d.p,{placeholder:"e.g., Forklift License, Customer Service",value:Array.isArray(e.value)?e.value.join(", "):"",onChange:a=>e.onChange(a.target.value.split(",").map(e=>e.trim()).filter(e=>e))})}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsx)(m.zB,{control:T.control,name:"vehicleId",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Associated Vehicle (Optional)"}),(0,t.jsxs)(x.l6,{onValueChange:a=>{e.onChange(a===C?null:Number(a))},value:null===e.value?C:e.value?String(e.value):void 0,children:[(0,t.jsx)(m.MJ,{children:(0,t.jsx)(x.bq,{children:(0,t.jsx)(x.yv,{placeholder:"Select a vehicle"})})}),(0,t.jsxs)(x.gC,{children:[(0,t.jsx)(x.eb,{value:C,children:"No Specific Vehicle"}),S.map(e=>(0,t.jsxs)(x.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,") -"," ",e.licensePlate||"N/A"]},e.id))]})]}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsx)(m.zB,{control:T.control,name:"notes",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Notes (Optional)"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(o.T,{placeholder:"e.g., Gate code is 1234, contact person: Jane Smith",...e})}),(0,t.jsx)(m.C5,{})]})})]})]}),(0,t.jsxs)(c.wL,{className:"flex justify-between gap-2 border-t pt-6",children:[(0,t.jsxs)(n.$,{type:"button",variant:"outline",onClick:()=>I.back(),children:[(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),(0,t.jsxs)(n.$,{type:"submit",className:"bg-accent text-accent-foreground hover:bg-accent/90",children:[(0,t.jsx)(f.A,{className:"mr-2 h-4 w-4"}),s?"Save Changes":"Create Task"]})]})]})})})}}};