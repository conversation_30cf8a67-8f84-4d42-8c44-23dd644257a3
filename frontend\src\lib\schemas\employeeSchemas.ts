import * as z from 'zod';
import {DriverAvailabilitySchema} from './driverSchemas'; // Import for reuse

export const EmployeeStatusSchema = z.enum([
	'Active',
	'On Leave',
	'Terminated',
	'Inactive', // Added Inactive based on seed data
]);

export const EmployeeRoleSchema = z.enum([
	'driver',
	'mechanic',
	'administrator',
	'office_staff',
	'manager',
	'service_advisor',
	'technician', // Added Technician based on seed data
	'other',
]);

export const EmployeeFormSchema = z.object({
	name: z.string().min(1, 'Name is required'),
	fullName: z.string().min(1, 'Full name is required').optional(), // Made optional, can default to 'name'
	employeeId: z.string().min(1, 'Employee ID (unique business ID) is required'),
	position: z.string().min(1, 'Position/Title is required'),
	department: z.string().min(1, 'Department is required'),
	contactInfo: z
		.string()
		.min(1, 'Primary contact (email or phone) is required')
		.refine((val) => {
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			const phoneRegex = /^[\d\s()+-]{7,20}$/; // Basic phone regex
			return emailRegex.test(val) || phoneRegex.test(val);
		}, 'Must be a valid email or phone number.'),
	contactEmail: z
		.string()
		.email('Invalid email address')
		.optional()
		.nullable()
		.or(z.literal('')),
	contactPhone: z.string().optional().nullable().or(z.literal('')),
	contactMobile: z.string().optional().nullable().or(z.literal('')),
	hireDate: z.string().refine((val) => val && !isNaN(Date.parse(val)), {
		message: 'Invalid hire date',
	}),
	status: EmployeeStatusSchema.default('Active'),
	role: EmployeeRoleSchema.default('other'),
	availability: DriverAvailabilitySchema.optional().nullable(),
	currentLocation: z.string().optional().or(z.literal('')),
	workingHours: z.string().optional().or(z.literal('')),
	assignedVehicleId: z.number().int().positive().nullable().optional(), // Corrected to number
	skills: z.array(z.string()).optional().default([]),
	shiftSchedule: z.string().optional().or(z.literal('')),
	generalAssignments: z.array(z.string()).optional().default([]),
	notes: z.string().optional().or(z.literal('')),
	profileImageUrl: z
		.string()
		.url('Invalid URL for profile image')
		.optional()
		.or(z.literal('')),
	statusChangeReason: z.string().optional().nullable(), // For update controller
});

export type EmployeeFormData = z.infer<typeof EmployeeFormSchema>;
