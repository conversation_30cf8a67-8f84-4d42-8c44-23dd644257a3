## Testing & Monitoring Plan: PDF Report Generation System

This document outlines the testing and monitoring plan for the new PDF report generation system, encompassing both the backend service and frontend integration.

**1. Backend Testing**

*   **Unit Tests:**
    *   **Focus:** Individual modules, functions, or classes in isolation. Ensuring each small piece of code behaves as expected.
    *   **Examples:**
        *   **`DataFetchingService`:** Test functions that transform raw data from database queries into the specific structures needed for report templates. Mock database calls.
        *   **`ValidationService` / Schemas:** Test input validation rules for `reportType` and various `reportParams` (e.g., date formats, required fields, data types).
        *   **Utility Functions:** Test any helper functions, e.g., for filename generation, date formatting specific to reports.
        *   **`TemplateRenderingService`:** Test the logic that selects a template based on `reportType` (mock the actual template engine).
    *   **Mocking:**
        *   External dependencies will be heavily mocked.
        *   Database interactions (e.g., using `jest.mock('./database')`).
        *   The Puppeteer/Playwright module itself (e.g., `jest.mock('puppeteer')`) to verify that it's invoked with correct parameters (HTML content, PDF options), not to test actual PDF output.
        *   External API calls (e.g., to storage services).
    *   **Tools:** Jest, Mocha, Chai, Sinon (for spies and stubs).

*   **Integration Tests:**
    *   **Focus:** Interactions and data flow between different components or modules within the backend service.
    *   **Examples:**
        *   **Controller to Service Flow:** Test the path from an incoming HTTP request mock at the `ReportController` level, through the `ReportService`, to the `DataFetchingService`. Mock the database layer itself but verify that the correct data is passed between services.
        *   **Template Rendering:** Provide sample data to the `TemplateRenderingService` and verify that it correctly invokes the templating engine (e.g., EJS, Handlebars) and produces the expected HTML string structure. This tests the integration of data with the template logic.
        *   **Basic PDF Generation:**
            *   Provide a very simple, fixed HTML string to the `PdfGenerationService`.
            *   Allow the test to *actually launch a headless browser instance* in the test environment (this requires the test environment to have browser dependencies).
            *   Verify that a non-empty, structurally valid PDF file/buffer is generated. Assertions might include checking for PDF magic numbers (e.g., `%PDF-`), or using a simple PDF parsing library to check page count (e.g., should be 1 page for simple HTML).
            *   This test is *not* for visual perfection but for ensuring the core PDF generation mechanism is working.
        *   **Queue Integration (if using async processing):** Test that a message is correctly published to the queue by the API endpoint and that a worker service can correctly consume it (mock the queue client for unit tests, use a test queue instance for integration tests).
    *   **Environment:** May require a test database instance (or an in-memory alternative if feasible) and access to a headless browser.

*   **API Contract Tests:**
    *   **Focus:** Ensuring the API strictly adheres to its defined schema for requests and responses. This guarantees that the backend meets the expectations of its consumers (primarily the frontend).
    *   **Methods:**
        *   **Provider-Side Schema Validation:** Use libraries like Joi, Zod, or OpenAPI validation tools (e.g., `express-openapi-validator`) integrated into the backend's test suite. Validate incoming requests against the API schema and ensure outgoing responses also conform.
        *   **Consumer-Driven Contracts (CDC) - (More Advanced):**
            *   Tools like **Pact**.
            *   The frontend (consumer) defines its expectations (interactions) for the API, which are captured in a "pact file."
            *   The backend (provider) then verifies these pacts in its tests, ensuring it fulfills the agreed-upon contract.
            *   This is powerful for maintaining sync between independently developed frontend and backend services.
    *   **Benefits:** Catches breaking changes in the API early.

**2. Frontend Testing**

*   **Unit Tests:**
    *   **Focus:** Individual React components, custom hooks, or utility functions in isolation.
    *   **Examples:**
        *   **`DownloadReportButton` Component:**
            *   Test initial rendering (button text, disabled state).
            *   Test state changes when the button is clicked (e.g., `isLoading` becomes true, button text changes to "Generating...").
            *   Test that the `generateReportApi` service function is called with the correct `reportType` and `reportParams` when the button is clicked (mock the API service).
            *   Test error state rendering when the API call mock rejects.
            *   Test that download logic (e.g., creating an anchor tag, `saveAs` from `file-saver`) is invoked correctly when the API call mock resolves successfully (mock these browser/library functions).
        *   **Report Parameter Collection Logic:** Test utility functions that gather filter values or other parameters from the UI state to be sent to the API.
    *   **Mocking:** Mock API service calls (`jest.mock('../services/apiService')`), browser APIs (like `window.URL.createObjectURL`), and any external libraries not directly under test.
    *   **Tools:** Jest with React Testing Library (RTL) or Enzyme.

*   **Integration Tests (Frontend):**
    *   **Focus:** Interaction between multiple frontend components, simulating parts of the user flow within the frontend.
    *   **Examples:**
        *   Testing a full report page that includes filter components and the `DownloadReportButton`.
        *   Simulate user interaction: change filter values, then click the "Download PDF" button.
        *   Verify that the `DownloadReportButton` component receives the correct, updated parameters based on the filter changes and that the API call (mocked) is made with these parameters.
    *   **Tools:** Jest with React Testing Library (RTL). RTL excels at tests that interact with components as a user would.

**3. End-to-End (E2E) Tests**

*   **Focus:** Simulating complete user scenarios from the frontend UI through the backend services and back to the browser. Validates the entire system flow.
*   **Tools:** Cypress, Playwright (for E2E), Selenium. Playwright is a strong candidate as it can also be used for headless browser tasks on the backend.
*   **Scenarios:**
    1.  **Successful PDF Download (Blob Stream):**
        *   User navigates to a specific report page.
        *   User applies a set of predefined filters.
        *   User clicks the "Download PDF" button.
        *   Verify that an API call is made to `/api/reports/generate/{reportType}` with the correct parameters (can be spied on by the E2E test tool).
        *   Verify that the browser initiates a PDF download.
        *   (Optional, advanced) If the E2E tool can access the downloaded file: verify it's a non-zero size file and has a `.pdf` extension. Some tools might allow basic PDF validation.
    2.  **Successful PDF Download (URL from JSON):** Similar to above, but verify the API response is JSON with a `pdfUrl` and the download is initiated from that URL.
    3.  **API Error Handling:**
        *   Simulate a backend error (e.g., using request interception in the E2E tool to force a 500 error from the PDF generation API).
        *   User clicks "Download PDF."
        *   Verify that a user-friendly error message is displayed on the frontend.
    4.  **Invalid Input:**
        *   User attempts to generate a report with invalid parameters (e.g., an impossible date range if the UI allows it before backend validation).
        *   Verify that appropriate validation messages are shown (either from frontend validation or from a 4xx backend response).
*   **Considerations:**
    *   E2E tests are the most comprehensive but are also slower to run and can be more brittle (sensitive to UI changes).
    *   Run them as part of a dedicated E2E test suite, perhaps less frequently than unit/integration tests (e.g., on pre-production builds or nightly).
    *   Requires a fully deployed environment (or a close approximation).

**4. Visual Regression Testing for PDFs (Advanced)**

*   **Focus:** Ensuring the visual appearance of generated PDFs remains consistent and accurate across code changes, style updates, or headless browser version changes. This is crucial for maintaining high fidelity.
*   **Tools/Techniques:**
    1.  **Baseline Generation:**
        *   Create a suite of test cases with diverse report types and parameters.
        *   Generate "golden" or baseline PDFs for each test case and store them securely (e.g., in Git LFS or dedicated artifact storage).
    2.  **Comparison:**
        *   In subsequent test runs (e.g., in CI), generate new PDFs using the same test cases.
        *   Compare these newly generated PDFs against the corresponding baseline PDFs.
        *   **Pixel-by-Pixel Comparison (often via Image Conversion):**
            *   Convert PDF pages to images (e.g., PNG) using tools like `pdf-image` (Node.js wrapper for `pdftoppm`) or Puppeteer's screenshot capability if rendering a page of the PDF.
            *   Use image comparison libraries like `jest-image-snapshot`, `pixelmatch`, or `Resemble.js` to detect visual differences.
        *   **Structural/Textual Comparison:**
            *   Tools like `diff-pdf` can compare the textual content and basic structure of PDFs.
            *   Custom scripts using libraries like `pdf-parse` (Node.js) can extract text, metadata, and page counts for comparison. This is less about visual fidelity but good for content integrity.
    *   **Challenges:**
        *   **Sensitivity:** Pixel-based comparisons can be extremely sensitive to minor rendering variations (anti-aliasing, font hinting) that might not be visually significant to a human. Thresholds for differences need to be configurable.
        *   **Baseline Management:** Updating baseline files needs a clear process.
        *   **Environment Consistency:** Ensure the environment (OS, fonts, browser version) for generating test PDFs is identical to the one used for baselines to minimize false positives.
    *   **Benefit:** Provides strong confidence in the visual integrity of the reports.

**5. Monitoring Strategy (Production)**

*   **Key Metrics (Backend Service):**
    *   **API Endpoint Metrics (for `POST /api/reports/generate/{reportType}`):**
        *   **Request Rate:** Number of requests per minute/second.
        *   **Error Rate:** Percentage of 4xx and 5xx responses. Track specific error codes.
        *   **Latency:** Average, p90, p95, p99 response times. Segment by `reportType` if possible.
    *   **PDF Generation Performance:**
        *   Average time taken from receiving a request to successfully generating and returning/storing a PDF.
        *   Histogram of generation times to identify outliers.
    *   **Resource Usage (per container instance):**
        *   CPU Utilization (average and peak).
        *   Memory Utilization (average and peak – critical for headless browsers).
        *   Disk space (if temporary files are used).
    *   **Headless Browser Health (if trackable):**
        *   Number of browser launch successes/failures.
        *   Browser instance crash rate (might require custom tracking within the application).
        *   Pool metrics (if using `puppeteer-cluster`): number of active/idle instances, queue length for tasks.
    *   **Queue Metrics (if using asynchronous processing):**
        *   **Queue Length:** Number of messages waiting to be processed.
        *   **Message Age:** How long messages are staying in the queue (oldest message age).
        *   **Processing Rate:** Number of messages processed per minute by workers.
        *   **Dead-Letter Queue (DLQ) Size:** Number of messages that failed processing multiple times. This is a critical indicator of persistent issues.

*   **Logging:**
    *   **Format:** Use structured logging (e.g., JSON) for easier parsing and analysis by log management systems.
    *   **Backend:**
        *   Log incoming request details (reportType, key parameters – be mindful of PII).
        *   User identifiers (for traceability, if applicable and allowed).
        *   Key stages: data fetching start/end, template rendering start/end, PDF generation start/end, storage start/end.
        *   Errors encountered at each stage, including full stack traces.
        *   Successful PDF generation events: report type, generated filename/ID, generation time.
        *   Puppeteer/Playwright console messages or crash information, if possible to capture.
    *   **Frontend:**
        *   Log API call initiation to the report generation service.
        *   Log success (including whether it was a blob or URL) and any errors returned by the API.
        *   Log errors encountered during client-side download handling.

*   **Alerting (thresholds to be defined based on baseline performance):**
    *   **Critical Alerts (requiring immediate attention):**
        *   Significant spike in 5xx error rate on the API endpoint.
        *   PDF generation latency exceeding a high threshold (e.g., p95 > 60 seconds for synchronous, or processing time for async).
        *   CPU or Memory utilization consistently above 85-90% on service instances.
        *   Rapidly growing dead-letter queue size.
        *   Failures in headless browser initialization or persistent crashes.
        *   Queue length for async processing growing uncontrollably.
    *   **Warning Alerts (requiring investigation):**
        *   Moderate increase in 4xx/5xx error rates.
        *   Moderate increase in latency.
        *   Elevated resource usage.
        *   Messages appearing in the DLQ.

*   **Tools:**
    *   **Monitoring & Alerting:** AWS CloudWatch, Google Cloud Monitoring, Azure Monitor, Prometheus & Grafana, Datadog, New Relic.
    *   **Log Management:** ELK Stack (Elasticsearch, Logstash, Kibana), Splunk, Grafana Loki, platform-specific logging services (CloudWatch Logs, Google Cloud Logging).

This testing and monitoring plan aims to provide a comprehensive approach to ensure the quality, reliability, and performance of the PDF report generation system. It balances different testing types and emphasizes proactive monitoring in production.
