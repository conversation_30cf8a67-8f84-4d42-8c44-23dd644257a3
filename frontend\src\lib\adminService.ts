import {fetchData} from './apiService';
import {withRetry} from './utils/apiUtils';
import {withResponseAdapter} from './utils/responseAdapter';

/**
 * Status of a system component (database, Supabase, etc.)
 */
export interface HealthComponentStatus {
	/** Current status: 'UP', 'DOWN', or other status values */
	status: string;
	/** Type of the component (e.g., 'PostgreSQL via Prisma') */
	type?: string;
	/** URL or connection string (redacted for security) */
	url?: string;
	/** Error details if status is not 'UP' */
	error?: string | null;
}

/**
 * Response from the health status endpoint
 */
export interface HealthResponse {
	/** Overall system status: 'UP', 'DOWN', or 'ERROR' */
	status: string;
	/** Human-readable status message */
	message: string;
	/** Status of individual system components */
	components: {
		/** Database component status */
		database: HealthComponentStatus;
		/** Supabase component status (if configured) */
		supabase?: HealthComponentStatus;
	};
	/** System configuration information */
	config: {
		/** Whether Supabase is enabled */
		useSupabase: boolean;
		/** Whether Supabase is properly configured */
		supabaseConfigured: boolean;
		/** Database connection mode */
		connectionMode?: string;
	};
	/** Timestamp of when the health check was performed */
	timestamp: string;
	/** System version */
	version?: string;
	/** System uptime in seconds */
	uptime?: number;
}

/**
 * Database performance metrics
 */
export interface PerformanceMetrics {
	/** Cache hit rates for database operations */
	cacheHitRate: {
		/** Percentage of index lookups served from cache */
		indexHitRate: number;
		/** Percentage of table lookups served from cache */
		tableHitRate: number;
	};
	/** Number of active database connections */
	connectionCount: number;
	/** Number of currently executing queries */
	activeQueries: number;
	/** Average query execution time in milliseconds */
	avgQueryTime: number;
	/** Timestamp when metrics were collected */
	timestamp: string;
}

/**
 * Log levels for system logs
 */
export type LogLevel = 'ERROR' | 'WARNING' | 'INFO';

/**
 * Entry in the system error log
 */
export interface ErrorLogEntry {
	/** Unique identifier for the log entry */
	id: string;
	/** Timestamp when the log was created */
	timestamp: string;
	/** Log level: ERROR, WARNING, or INFO */
	level: LogLevel;
	/** Log message */
	message: string;
	/** Additional structured details about the log entry */
	details?: Record<string, any>;
	/** Source of the log (component, service, etc.) */
	source?: string;
}

/**
 * Pagination metadata for paginated responses
 */
export interface PaginationMetadata {
	/** Current page number (1-based) */
	page: number;
	/** Number of items per page */
	limit: number;
	/** Total number of items across all pages */
	total: number;
	/** Total number of pages */
	totalPages: number;
}

/**
 * Paginated response containing data and pagination metadata
 */
export interface PaginatedResponse<T> {
	/** Array of data items */
	data: T[];
	/** Pagination metadata */
	pagination: PaginationMetadata;
}

/**
 * Parameters for fetching error logs
 */
export interface ErrorLogParams {
	/** Page number (1-based) */
	page?: number;
	/** Number of items per page */
	limit?: number;
	/** Filter by log level */
	level?: LogLevel;
}

/**
 * Get system health status
 * @returns Promise resolving to health status information
 */
export const getHealthStatus = async (): Promise<HealthResponse> => {
	return withResponseAdapter<HealthResponse, []>(() =>
		withRetry(() => fetchData(`/admin/health`))
	)();
};

/**
 * Get database performance metrics
 * @returns Promise resolving to performance metrics
 */
export const getPerformanceMetrics = async (): Promise<PerformanceMetrics> => {
	return withResponseAdapter<PerformanceMetrics, []>(() =>
		withRetry(() => fetchData(`/admin/performance`))
	)();
};

/**
 * Get system error logs with pagination and filtering
 * @param params Pagination and filtering parameters
 * @returns Promise resolving to paginated error logs
 */
export const getRecentErrors = async (
	params: ErrorLogParams = {}
): Promise<PaginatedResponse<ErrorLogEntry>> => {
	const {page = 1, limit = 10, level} = params;

	let url = `/admin/errors?page=${page}&limit=${limit}`;
	if (level) {
		url += `&level=${level}`;
	}

	// For paginated responses, we need to handle the pagination metadata separately
	const response = await withRetry(() => fetchData(url));

	// If the response is already in the expected format, return it as is
	if (
		response &&
		typeof response === 'object' &&
		'data' in response &&
		'pagination' in response
	) {
		return response as PaginatedResponse<ErrorLogEntry>;
	}

	// If the response is wrapped in a status/data structure, extract the data and pagination
	if (
		response &&
		typeof response === 'object' &&
		'status' in response &&
		response.status === 'success'
	) {
		return {
			data: response.data,
			pagination: response.pagination,
		} as PaginatedResponse<ErrorLogEntry>;
	}

	// Fallback: return the response as is
	return response as PaginatedResponse<ErrorLogEntry>;
};

/**
 * Mock functions for development (will be replaced with actual API calls)
 */

/**
 * Get mock health status data
 * @returns Mock health status response
 */
export const getMockHealthStatus = (): HealthResponse => {
	return {
		status: 'UP',
		message: 'Backend service is healthy',
		components: {
			database: {
				status: 'UP',
				type: 'PostgreSQL via Prisma',
				url: 'db.example.com',
				error: null,
			},
			supabase: {
				status: 'UP',
				url: 'project-id.supabase.co',
				error: null,
			},
		},
		config: {
			useSupabase: true,
			supabaseConfigured: true,
			connectionMode: 'transaction',
		},
		timestamp: new Date().toISOString(),
		version: '1.0.0',
		uptime: 3600,
	};
};

/**
 * Get mock performance metrics data
 * @returns Mock performance metrics
 */
export const getMockPerformanceMetrics = (): PerformanceMetrics => {
	return {
		cacheHitRate: {
			indexHitRate: 98.5,
			tableHitRate: 95.2,
		},
		connectionCount: 12,
		activeQueries: 3,
		avgQueryTime: 1.2,
		timestamp: new Date().toISOString(),
	};
};

/**
 * Get mock error logs with pagination
 * @param params Pagination and filtering parameters
 * @returns Mock paginated error logs
 */
export const getMockRecentErrors = (
	params: ErrorLogParams = {}
): PaginatedResponse<ErrorLogEntry> => {
	const {page = 1, limit = 10, level} = params;
	const errors: ErrorLogEntry[] = [];

	// Generate 50 mock errors total
	const totalErrors = 50;
	const startIndex = (page - 1) * limit;
	const endIndex = Math.min(startIndex + limit, totalErrors);

	// Generate the requested page of errors
	for (let i = startIndex; i < endIndex; i++) {
		const errorLevel: LogLevel =
			i % 3 === 0 ? 'ERROR' : i % 2 === 0 ? 'WARNING' : 'INFO';

		// Skip if filtering by level and this error doesn't match
		if (level && errorLevel !== level) {
			continue;
		}

		errors.push({
			id: `err-${i}`,
			timestamp: new Date(Date.now() - i * 3600000).toISOString(),
			level: errorLevel,
			message: `Sample ${errorLevel.toLowerCase()} message ${i + 1}`,
			details: {
				source: 'Mock data',
				code: i * 100,
				component:
					i % 4 === 0
						? 'database'
						: i % 4 === 1
						? 'api'
						: i % 4 === 2
						? 'supabase'
						: 'auth',
			},
			source:
				i % 4 === 0
					? 'database'
					: i % 4 === 1
					? 'api'
					: i % 4 === 2
					? 'supabase'
					: 'auth',
		});
	}

	// Calculate total filtered errors
	let filteredTotal = totalErrors;
	if (level) {
		// If filtering by level, calculate how many errors match the filter
		filteredTotal = Math.floor(totalErrors / 3); // Approximately 1/3 of errors are of each level
	}

	return {
		data: errors,
		pagination: {
			page,
			limit,
			total: filteredTotal,
			totalPages: Math.ceil(filteredTotal / limit),
		},
	};
};
