(()=>{var e={};e.id=9527,e.ids=[9527],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21705:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(r,o);let d={children:["",{children:["add-vehicle",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,45805)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\add-vehicle\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\add-vehicle\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/add-vehicle/page",pathname:"/add-vehicle",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25291:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(60687),a=t(95950),i=t(28840),l=t(16189),n=t(24920);function o(){let e=(0,l.useRouter)(),r=async r=>{try{await (0,i.addVehicle)(r),e.push("/vehicles")}catch(e){console.error("Error adding vehicle:",e)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,s.jsx)(n.A,{className:"h-8 w-8 text-primary"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-primary",children:"Add New Vehicle"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Enter the details of your new vehicle."})]})]}),(0,s.jsx)(a.A,{onSubmit:r})]})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},45805:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\add-vehicle\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\add-vehicle\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55817:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(82614).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67311:(e,r,t)=>{Promise.resolve().then(t.bind(t,45805))},74075:e=>{"use strict";e.exports=require("zlib")},75559:(e,r,t)=>{Promise.resolve().then(t.bind(t,25291))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95950:(e,r,t)=>{"use strict";t.d(r,{A:()=>j});var s=t(60687),a=t(43210),i=t(16189),l=t(27605),n=t(63442),o=t(45880);let d=o.Ik({make:o.Yj().min(1,"Make is required"),model:o.Yj().min(1,"Model is required"),year:o.au.number().min(1900,"Year must be 1900 or later").max(new Date().getFullYear()+1,`Year cannot be more than ${new Date().getFullYear()+1}`),vin:o.Yj().min(1,"VIN is required").regex(/^[A-HJ-NPR-Z0-9]{17}$/,"VIN must be a valid 17-character format (only capital letters A-H, J-N, P-R, Z and numbers 0-9)"),licensePlate:o.Yj().min(1,"License plate is required"),ownerName:o.Yj().min(1,"Owner name is required"),ownerContact:o.Yj().min(1,"Owner contact is required"),color:o.Yj().optional(),initialOdometer:o.au.number().min(0,"Odometer reading cannot be negative").optional(),imageUrl:o.Yj().url("Invalid image URL").optional().or(o.eu(""))});var c=t(29523),m=t(89667),p=t(80013),x=t(44493),h=t(29867),u=t(55817);let j=({onSubmit:e,initialData:r={},isEditing:t=!1,submitButtonText:o=t?"Save Changes":"Create Vehicle",isLoading:j=!1})=>{let g=(0,i.useRouter)(),{toast:v}=(0,h.dj)(),{register:y,handleSubmit:f,formState:{errors:w,isSubmitting:N},reset:b,setValue:k}=(0,l.mN)({resolver:(0,n.u)(d),defaultValues:{make:r?.make||"",model:r?.model||"",year:r?.year||new Date().getFullYear(),vin:r?.vin||"",licensePlate:r?.licensePlate||"",ownerName:r?.ownerName||"",ownerContact:r?.ownerContact||"",color:r?.color||"",initialOdometer:r?.initialOdometer??0,imageUrl:r?.imageUrl||""}});(0,a.useEffect)(()=>{r&&(k("make",r.make||""),k("model",r.model||""),k("year",r.year||new Date().getFullYear()),k("vin",r.vin||""),k("licensePlate",r.licensePlate||""),k("ownerName",r.ownerName||""),k("ownerContact",r.ownerContact||""),k("color",r.color||""),k("initialOdometer",r.initialOdometer??0),k("imageUrl",r.imageUrl||""))},[r,k]);let C=async r=>{await e(r),v({title:t?"Vehicle Updated":"Vehicle Added",description:`${r.make} ${r.model} has been successfully ${t?"updated":"added"}.`,variant:"default"})};return(0,s.jsxs)(x.Zp,{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)(x.aR,{children:[(0,s.jsx)(x.ZB,{children:t?"Edit Vehicle":"Add New Vehicle"}),(0,s.jsx)(x.BT,{children:t?"Update the details of the vehicle.":"Enter the details for the new vehicle."})]}),(0,s.jsxs)("form",{onSubmit:f(C),children:[(0,s.jsxs)(x.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"make",children:"Make"}),(0,s.jsx)(m.p,{id:"make",...y("make"),placeholder:"e.g., Toyota"}),w.make&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:w.make.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"model",children:"Model"}),(0,s.jsx)(m.p,{id:"model",...y("model"),placeholder:"e.g., Camry"}),w.model&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:w.model.message})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"year",children:"Year"}),(0,s.jsx)(m.p,{id:"year",type:"number",...y("year"),placeholder:"e.g., 2023"}),w.year&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:w.year.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"vin",children:"VIN"}),(0,s.jsx)(m.p,{id:"vin",...y("vin"),placeholder:"Vehicle Identification Number"}),w.vin&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:w.vin.message}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"VIN must be exactly 17 characters, using capital letters A-H, J-N, P-R, Z and numbers 0-9."})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"licensePlate",children:"License Plate"}),(0,s.jsx)(m.p,{id:"licensePlate",...y("licensePlate"),placeholder:"e.g., ABC-123"}),w.licensePlate&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:w.licensePlate.message})]}),(0,s.jsx)("hr",{className:"my-6"}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"ownerName",children:"Owner Name"}),(0,s.jsx)(m.p,{id:"ownerName",...y("ownerName"),placeholder:"e.g., John Doe"}),w.ownerName&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:w.ownerName.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"ownerContact",children:"Owner Contact (Email/Phone)"}),(0,s.jsx)(m.p,{id:"ownerContact",...y("ownerContact"),placeholder:"e.g., <EMAIL> or 555-1234"}),w.ownerContact&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:w.ownerContact.message})]}),(0,s.jsx)("hr",{className:"my-6"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"color",children:"Color (Optional)"}),(0,s.jsx)(m.p,{id:"color",...y("color"),placeholder:"e.g., Blue"}),w.color&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:w.color.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"initialOdometer",children:"Initial Odometer (Optional)"}),(0,s.jsx)(m.p,{id:"initialOdometer",type:"number",...y("initialOdometer"),placeholder:"e.g., 100"}),w.initialOdometer&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:w.initialOdometer.message})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"imageUrl",children:"Image URL (Optional)"}),(0,s.jsx)(m.p,{id:"imageUrl",...y("imageUrl"),placeholder:"https://example.com/image.png"}),w.imageUrl&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:w.imageUrl.message})]})]}),(0,s.jsxs)(x.wL,{className:"flex justify-end space-x-3 pt-6",children:[(0,s.jsxs)(c.$,{type:"button",variant:"outline",onClick:()=>g.back(),disabled:N||j,children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),(0,s.jsx)(c.$,{type:"submit",disabled:N||j,children:N||j?"Processing...":o})]})]})]})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3744,1658,5880,3442,8141,3983],()=>t(21705));module.exports=s})();