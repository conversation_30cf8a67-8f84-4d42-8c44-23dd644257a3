(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1272],{17759:(e,t,r)=>{"use strict";r.d(t,{C5:()=>f,MJ:()=>g,eI:()=>x,lR:()=>p,lV:()=>c,zB:()=>m});var s=r(95155),a=r(12115),i=r(99708),l=r(62177),n=r(59434),o=r(85057);let c=l.Op,d=a.createContext({}),m=e=>{let{...t}=e;return(0,s.jsx)(d.Provider,{value:{name:t.name},children:(0,s.jsx)(l.xI,{...t})})},u=()=>{let e=a.useContext(d),t=a.useContext(h),{getFieldState:r,formState:s}=(0,l.xW)(),i=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...i}},h=a.createContext({}),x=a.forwardRef((e,t)=>{let{className:r,...i}=e,l=a.useId();return(0,s.jsx)(h.Provider,{value:{id:l},children:(0,s.jsx)("div",{ref:t,className:(0,n.cn)("space-y-2",r),...i})})});x.displayName="FormItem";let p=a.forwardRef((e,t)=>{let{className:r,...a}=e,{error:i,formItemId:l}=u();return(0,s.jsx)(o.J,{ref:t,className:(0,n.cn)(i&&"text-destructive",r),htmlFor:l,...a})});p.displayName="FormLabel";let g=a.forwardRef((e,t)=>{let{...r}=e,{error:a,formItemId:l,formDescriptionId:n,formMessageId:o}=u();return(0,s.jsx)(i.DX,{ref:t,id:l,"aria-describedby":a?"".concat(n," ").concat(o):"".concat(n),"aria-invalid":!!a,...r})});g.displayName="FormControl",a.forwardRef((e,t)=>{let{className:r,...a}=e,{formDescriptionId:i}=u();return(0,s.jsx)("p",{ref:t,id:i,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})}).displayName="FormDescription";let f=a.forwardRef((e,t)=>{var r;let{className:a,children:i,...l}=e,{error:o,formMessageId:c}=u(),d=o?String(null!=(r=null==o?void 0:o.message)?r:""):i;return d?(0,s.jsx)("p",{ref:t,id:c,className:(0,n.cn)("text-sm font-medium text-destructive",a),...l,children:d}):null});f.displayName="FormMessage"},24865:(e,t,r)=>{"use strict";r.d(t,{M:()=>c});var s=r(95155);r(12115);var a=r(6874),i=r.n(a),l=r(15300),n=r(61840),o=r(6560);function c(e){let{href:t,getReportUrl:r,isList:a=!1,className:c}=e;if(!t&&!r)return console.error("ViewReportButton requires either href or getReportUrl prop"),null;let d=a?"View List Report":"View Report";return t?(0,s.jsx)(o.r,{actionType:"secondary",asChild:!0,icon:(0,s.jsx)(l.A,{className:"h-4 w-4"}),className:c,children:(0,s.jsxs)(i(),{href:t,target:"_blank",rel:"noopener noreferrer",children:[d,(0,s.jsx)(n.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"}),(0,s.jsx)("span",{className:"sr-only",children:"(opens in new tab)"})]})}):(0,s.jsxs)(o.r,{actionType:"secondary",onClick:()=>{if(r){let e=r();window.open(e,"_blank","noopener,noreferrer")}},icon:(0,s.jsx)(l.A,{className:"h-4 w-4"}),className:c,children:[d,(0,s.jsx)(n.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"})]})}},28041:(e,t,r)=>{Promise.resolve().then(r.bind(r,95334))},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(95155),a=r(12115),i=r(59434);let l=a.forwardRef((e,t)=>{let{className:r,type:a,...l}=e;return(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...l})});l.displayName="Input"},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(95155),a=r(12115),i=r(40968),l=r(74466),n=r(59434);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.b,{ref:t,className:(0,n.cn)(o(),r),...a})});c.displayName=i.b.displayName},87481:(e,t,r)=>{"use strict";r.d(t,{dj:()=>u});var s=r(12115);let a=0,i=new Map,l=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?l(r):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],c={toasts:[]};function d(e){c=n(c,e),o.forEach(e=>{e(c)})}function m(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function u(){let[e,t]=s.useState(c);return s.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},88539:(e,t,r)=>{"use strict";r.d(t,{T:()=>l});var s=r(95155),a=r(12115),i=r(59434);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...a})});l.displayName="Textarea"},95334:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ee});var s=r(95155),a=r(12115),i=r(35695),l=r(6874),n=r.n(l),o=r(2730),c=r(95647),d=r(6560),m=r(28328),u=r(31949),h=r(12543),x=r(18763),p=r(77223),g=r(87481),f=r(77023),v=r(66766),j=r(66695),b=r(98328),y=r(53054),N=r(82733),S=r(69321),w=r(3235);function A(e){let{vehicle:t}=e,r=t.serviceHistory.length>0,a=null!==t.initialOdometer,i=r?Math.max(...t.serviceHistory.map(e=>e.odometer),t.initialOdometer||0):t.initialOdometer||0;return(0,s.jsxs)(j.Zp,{className:"shadow-lg bg-card",children:[(0,s.jsx)(j.aR,{className:"flex flex-row items-start justify-between gap-4 border-b p-5",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)(j.ZB,{className:"text-2xl font-bold text-primary mb-1",children:[t.make," ",t.model]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Vehicle Overview"})]})}),(0,s.jsxs)(j.Wu,{className:"p-5 grid md:grid-cols-2 gap-x-8 gap-y-4",children:[(0,s.jsx)("div",{className:"relative aspect-[16/10] w-full md:col-span-2 lg:col-span-1 rounded-lg overflow-hidden mb-4 lg:mb-0 shadow-sm",children:(0,s.jsx)(v.default,{src:t.imageUrl||"https://picsum.photos/seed/".concat(t.id,"/600/375"),alt:"".concat(t.make," ").concat(t.model),layout:"fill",objectFit:"cover",className:"bg-muted","data-ai-hint":"car side",priority:!0})}),(0,s.jsxs)("div",{className:"space-y-3 text-foreground lg:col-span-1",children:[(0,s.jsx)(R,{icon:b.A,label:"Year",value:t.year.toString()}),t.licensePlate&&(0,s.jsx)(R,{icon:y.A,label:"License Plate",value:t.licensePlate}),t.color&&(0,s.jsx)(R,{icon:N.A,label:"Color",value:t.color}),(0,s.jsx)(R,{icon:S.A,label:"Initial Odometer",value:a?"".concat(t.initialOdometer.toLocaleString()," miles"):"Not recorded"}),(0,s.jsx)(R,{icon:S.A,label:"Latest Odometer",value:r?"".concat(i.toLocaleString()," miles"):a?"".concat(t.initialOdometer.toLocaleString()," miles"):"No odometer data"}),(0,s.jsx)(R,{icon:w.A,label:"Services Logged",value:t.serviceHistory.length.toString()})]})]})]})}function R(e){let{icon:t,label:r,value:a}=e;return(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(t,{className:"h-5 w-5 text-accent mr-3 shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:r}),(0,s.jsx)("p",{className:"text-md font-semibold",children:a})]})]})}var T=r(62177),O=r(90221),C=r(55594),k=r(62523),I=r(88539),F=r(17759),M=r(76981),E=r(10518),D=r(59434);let P=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(M.bL,{ref:t,className:(0,D.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",r),...a,children:(0,s.jsx)(M.C1,{className:(0,D.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(E.A,{className:"h-4 w-4"})})})});P.displayName=M.bL.displayName;var L=r(34301);let V=["Oil Change","Tire Rotation","Brake Service","Battery Replacement","Air Filter Replacement","Cabin Air Filter Replacement","Wiper Blade Replacement","Fluid Check/Top-up","Spark Plug Replacement","Coolant Flush","Transmission Service","Wheel Alignment","State Inspection","Other"],_=C.Ik({date:C.Yj().refine(e=>!isNaN(Date.parse(e)),{message:"Invalid date format"}),odometer:C.au.number().min(0,"Odometer cannot be negative"),servicePerformed:C.YO(C.Yj()).min(1,{message:"At least one service must be selected"}),notes:C.Yj().optional(),cost:C.au.number().min(0,"Cost cannot be negative").optional()});function B(e){let{vehicleId:t,onServiceRecordAdded:i,currentOdometerReading:l=0}=e,{toast:n}=(0,g.dj)(),o=(0,T.mN)({resolver:(0,O.u)(_),defaultValues:{date:new Date().toISOString().split("T")[0],odometer:l>0?l:void 0,servicePerformed:[],notes:"",cost:void 0}}),{addServiceRecord:c}=r(2730),[m,u]=(0,a.useState)(!1),h=async e=>{u(!0);try{let r={...e,cost:e.cost?Number(e.cost):void 0,odometer:Number(e.odometer)},s=await c(t,r);s?(i(s),n({title:"Service Record Added",description:"The new service record has been successfully logged."}),o.reset({date:new Date().toISOString().split("T")[0],odometer:e.odometer,servicePerformed:[],notes:"",cost:void 0})):n({title:"Error",description:"Failed to add service record. Vehicle not found.",variant:"destructive"})}catch(e){console.error("Error adding service record:",e),n({title:"Error",description:"An error occurred while adding the service record. Please try again.",variant:"destructive"})}finally{u(!1)}};return(0,s.jsxs)(j.Zp,{className:"shadow-md bg-card",children:[(0,s.jsx)(j.aR,{className:"p-5",children:(0,s.jsx)(j.ZB,{className:"text-xl font-semibold text-primary",children:"Log New Service"})}),(0,s.jsx)(F.lV,{...o,children:(0,s.jsxs)("form",{onSubmit:o.handleSubmit(h),children:[(0,s.jsxs)(j.Wu,{className:"space-y-4 p-5",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(F.zB,{control:o.control,name:"date",render:e=>{let{field:t}=e;return(0,s.jsxs)(F.eI,{children:[(0,s.jsx)(F.lR,{children:"Date of Service"}),(0,s.jsx)(F.MJ,{children:(0,s.jsx)(k.p,{type:"date",...t})}),(0,s.jsx)(F.C5,{})]})}}),(0,s.jsx)(F.zB,{control:o.control,name:"odometer",render:e=>{let{field:t}=e;return(0,s.jsxs)(F.eI,{children:[(0,s.jsx)(F.lR,{children:"Odometer (miles)"}),(0,s.jsx)(F.MJ,{children:(0,s.jsx)(k.p,{type:"number",placeholder:"e.g., 25000",...t})}),(0,s.jsx)(F.C5,{})]})}})]}),(0,s.jsx)(F.zB,{control:o.control,name:"servicePerformed",render:()=>(0,s.jsxs)(F.eI,{children:[(0,s.jsx)(F.lR,{children:"Services Performed"}),(0,s.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-x-4 gap-y-2",children:V.map(e=>(0,s.jsx)(F.zB,{control:o.control,name:"servicePerformed",render:t=>{var r;let{field:a}=t;return(0,s.jsxs)(F.eI,{className:"flex flex-row items-center space-x-2 space-y-0",children:[(0,s.jsx)(F.MJ,{children:(0,s.jsx)(P,{checked:null==(r=a.value)?void 0:r.includes(e),onCheckedChange:t=>{let r=a.value||[];t?a.onChange([...r,e]):a.onChange(r.filter(t=>t!==e))}})}),(0,s.jsx)(F.lR,{className:"font-normal text-sm",children:e})]})}},e))}),(0,s.jsx)(F.C5,{})]})}),(0,s.jsx)(F.zB,{control:o.control,name:"notes",render:e=>{let{field:t}=e;return(0,s.jsxs)(F.eI,{children:[(0,s.jsx)(F.lR,{children:"Notes (Optional)"}),(0,s.jsx)(F.MJ,{children:(0,s.jsx)(I.T,{placeholder:"e.g., Used synthetic oil, checked tire pressure.",...t})}),(0,s.jsx)(F.C5,{})]})}}),(0,s.jsx)(F.zB,{control:o.control,name:"cost",render:e=>{let{field:t}=e;return(0,s.jsxs)(F.eI,{children:[(0,s.jsx)(F.lR,{children:"Cost (Optional)"}),(0,s.jsx)(F.MJ,{children:(0,s.jsx)(k.p,{type:"number",step:"0.01",placeholder:"e.g., 75.50",...t})}),(0,s.jsx)(F.C5,{})]})}})]}),(0,s.jsx)(j.wL,{className:"p-5",children:(0,s.jsx)(d.r,{type:"submit",actionType:"primary",isLoading:m,loadingText:"Saving...",icon:(0,s.jsx)(L.A,{className:"h-4 w-4"}),className:"w-full",children:"Add Service Record"})})]})})]})}var z=r(58824),H=r(5611),J=r(30285),U=r(55365),W=r(17569),Y=r(50172),Z=r(50594),X=r(34477);let G=(0,X.createServerReference)("40dd32b54045de468888c5a0cbd463ab3a748bed0f",X.callServer,void 0,X.findSourceMapURL,"getMaintenanceSuggestions");var q=r(85057);function $(e){let{vehicle:t}=e,[r,i]=(0,a.useState)(!1),[l,n]=(0,a.useState)(null),[o,c]=(0,a.useState)(null),[d,m]=(0,a.useState)(t.serviceHistory.length>0?Math.max(...t.serviceHistory.map(e=>e.odometer)):t.initialOdometer||0),h=async()=>{var e;if(i(!0),n(null),c(null),d<t.initialOdometer){c("Current odometer cannot be less than initial odometer."),i(!1);return}if(t.serviceHistory.some(e=>e.odometer>d)){c("Current odometer cannot be less than a previously logged service odometer."),i(!1);return}let r=(e=t.serviceHistory)&&0!==e.length?e.map(e=>"Date: ".concat(e.date,", Mileage: ").concat(e.odometer," miles, Service: ").concat(e.servicePerformed.join(", ")).concat(e.notes?", Notes: ".concat(e.notes):"").concat(e.cost?", Cost: $".concat(e.cost):"")).join("; "):"No service history available.",s={vehicleMake:t.make,vehicleModel:t.model,vehicleYear:t.year,currentOdometer:d,serviceHistory:r},a=await G(s);a.success&&a.data?n(a.data):c(a.error||"Failed to get suggestions."),i(!1)};return(0,s.jsxs)(j.Zp,{className:"shadow-lg bg-card",children:[(0,s.jsxs)(j.aR,{children:[(0,s.jsxs)(j.ZB,{className:"text-xl font-semibold text-primary flex items-center",children:[(0,s.jsx)(W.A,{className:"mr-2 h-5 w-5 text-accent"}),"AI Maintenance Advisor"]}),(0,s.jsx)(j.BT,{children:"Get AI-powered maintenance schedule suggestions based on your vehicle's details and service history."})]}),(0,s.jsxs)(j.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(q.J,{htmlFor:"currentOdometerAi",className:"mb-1 block text-sm font-medium",children:"Current Odometer for Suggestion"}),(0,s.jsx)(k.p,{id:"currentOdometerAi",type:"number",value:d,onChange:e=>m(parseInt(e.target.value,10)||0),placeholder:"Enter current mileage",className:"mb-4"})]}),(0,s.jsx)(J.$,{onClick:h,disabled:r,className:"w-full bg-accent text-accent-foreground hover:bg-accent/90",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(Y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Generating..."]}):"Get Maintenance Suggestions"}),o&&(0,s.jsxs)(U.Fc,{variant:"destructive",className:"mt-4",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),(0,s.jsx)(U.XL,{children:"Error"}),(0,s.jsx)(U.TN,{children:o})]}),l&&(0,s.jsxs)("div",{className:"mt-6 space-y-4 p-4 border border-border rounded-lg bg-background",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-lg text-primary mb-2",children:"Suggested Maintenance Schedule:"}),(0,s.jsx)(I.T,{readOnly:!0,value:l.suggestedMaintenanceSchedule,className:"min-h-[150px] bg-muted/50","aria-label":"Suggested Maintenance Schedule"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-lg text-primary mb-2",children:"Reasoning:"}),(0,s.jsx)(I.T,{readOnly:!0,value:l.reasoning,className:"min-h-[100px] bg-muted/50","aria-label":"Reasoning for suggestions"})]})]}),!l&&!r&&!o&&(0,s.jsxs)(U.Fc,{className:"mt-4 border-primary/30",children:[(0,s.jsx)(Z.A,{className:"h-4 w-4 text-primary"}),(0,s.jsx)(U.XL,{className:"text-primary",children:"Ready for Advice?"}),(0,s.jsx)(U.TN,{children:"Enter your vehicle's current odometer reading above and click the button to receive personalized maintenance suggestions from our AI Advisor."})]})]})]})}var K=r(15080),Q=r(24865);let ee=()=>{let e=(0,i.useRouter)(),t=(0,i.useParams)(),{toast:r}=(0,g.dj)(),l=t.id?Number(t.id):null,[v,j]=(0,a.useState)(null),[b,y]=(0,a.useState)([]),[N,S]=(0,a.useState)(!0),[w,R]=(0,a.useState)(!0),[T,O]=(0,a.useState)(null),[C,k]=(0,a.useState)(null),[I,F]=(0,a.useState)(!1),M=(0,a.useCallback)(async()=>{if(!l){O("No vehicle ID provided."),S(!1);return}S(!0);try{let e=await (0,o.getVehicleById)(l);e?j(e):O("Vehicle not found.")}catch(e){console.error("Failed to fetch vehicle details:",e),O(e.message||"Failed to load vehicle data.")}finally{S(!1)}},[l]),E=(0,a.useCallback)(async()=>{if(l){R(!0),k(null);try{let e=await (0,H._X)(l);y(e)}catch(e){console.error("Failed to fetch service records:",e),k(e.message||"Failed to load service records.")}finally{R(!1)}}},[l]),D=(0,a.useCallback)(()=>{E()},[E]);(0,a.useEffect)(()=>{M(),E()},[M,E]);let P=async()=>{if(l&&window.confirm("Are you sure you want to delete this vehicle permanently?")){F(!0);try{await (0,o.deleteVehicle)(l),r({title:"Vehicle Deleted",description:"".concat(null==v?void 0:v.make," ").concat(null==v?void 0:v.model," has been deleted."),variant:"default"}),e.push("/vehicles")}catch(e){console.error("Failed to delete vehicle:",e),O(e.message||"Could not delete vehicle."),r({title:"Error Deleting Vehicle",description:e.message||"Failed to delete vehicle. Please try again.",variant:"destructive"})}finally{F(!1)}}},L=e=>{j(e),E(),r({title:"Service Record Added",description:"The new service record has been successfully logged."})};return(0,s.jsx)(K.A,{children:(0,s.jsx)("div",{className:"container mx-auto py-8 space-y-8",children:(0,s.jsx)(f.gO,{isLoading:N,error:T,data:v,onRetry:M,loadingComponent:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.z,{title:"Loading Vehicle...",icon:m.A}),(0,s.jsx)(f.jt,{variant:"card",count:1}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6 items-start",children:[(0,s.jsx)(f.jt,{variant:"card",count:1,className:"lg:col-span-2"}),(0,s.jsx)(f.jt,{variant:"card",count:1})]})]}),emptyComponent:(0,s.jsxs)("div",{className:"text-center py-10",children:[(0,s.jsx)(c.z,{title:"Vehicle Not Found",icon:u.A}),(0,s.jsx)("p",{className:"mb-4",children:"The requested vehicle could not be found."}),(0,s.jsx)(d.r,{actionType:"primary",onClick:()=>e.push("/vehicles"),icon:(0,s.jsx)(h.A,{className:"h-4 w-4"}),children:"Back to Vehicles"})]}),children:e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.z,{title:"".concat(e.make," ").concat(e.model),description:"VIN: ".concat(e.vin," | Plate: ").concat(e.licensePlate),icon:m.A,children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(d.r,{actionType:"secondary",asChild:!0,icon:(0,s.jsx)(x.A,{className:"h-4 w-4"}),children:(0,s.jsx)(n(),{href:"/vehicles/".concat(e.id,"/edit"),children:"Edit"})}),(0,s.jsx)(Q.M,{href:"/vehicles/".concat(e.id,"/report")}),(0,s.jsx)(d.r,{actionType:"danger",onClick:P,isLoading:I,icon:(0,s.jsx)(p.A,{className:"h-4 w-4"}),loadingText:"Deleting...",children:"Delete"})]})}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6 items-start",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,s.jsx)(A,{vehicle:e}),(0,s.jsx)(z.R,{records:b,isLoading:w,error:C,onRetry:D,showVehicleInfo:!1,vehicleSpecific:!0})]}),(0,s.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,s.jsx)(B,{vehicleId:String(e.id),onServiceRecordAdded:L,currentOdometerReading:Math.max(...e.serviceHistory.map(e=>e.odometer),e.initialOdometer||0)}),(0,s.jsx)($,{vehicle:e})]})]})]})})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5769,8360,832,7529,4066,6766,1997,8162,2730,536,8610,8441,1684,7358],()=>t(28041)),_N_E=e.O()}]);