'use client';

import { Skeleton } from '@/components/ui/skeleton';

export default function DelegationCardSkeleton() {
  return (
    <div className='overflow-hidden shadow-lg flex flex-col h-full bg-card border-border/60 rounded-lg'>
      <Skeleton className='aspect-[16/10] w-full bg-muted/50' />
      <div className='p-5 flex-grow flex flex-col'>
        <Skeleton className='h-7 w-3/4 mb-1 bg-muted/50' />
        <Skeleton className='h-4 w-1/2 mb-3 bg-muted/50' />
        <Skeleton className='h-px w-full my-3 bg-border/50' />
        <div className='space-y-2.5 flex-grow'>
          {[...Array(3)].map((_, i) => (
            <div key={i} className='flex items-center'>
              <Skeleton className='mr-2.5 h-5 w-5 rounded-full bg-muted/50' />
              <Skeleton className='h-5 w-2/3 bg-muted/50' />
            </div>
          ))}
        </div>
      </div>
      <div className='p-4 border-t border-border/60 bg-muted/20'>
        <Skeleton className='h-10 w-full bg-muted/50' />
      </div>
    </div>
  );
}
