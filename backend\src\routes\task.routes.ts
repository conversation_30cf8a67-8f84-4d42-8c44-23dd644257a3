import {Router} from 'express';
import * as taskController from '../controllers/task.controller.js';
import {validate} from '../middleware/validation.js';
import {
	authenticateSupabaseUser,
	requireRole,
} from '../middleware/supabaseAuth.js';
import {
	taskCreateSchema,
	taskUpdateSchema,
	taskIdSchema,
} from '../schemas/task.schema.js';
import {sanitizeTaskData} from '../middleware/taskDataSanitizer.js';
import {sanitizeTaskResponse} from '../middleware/taskResponseSanitizer.js';

const router = Router();

// 🚨 EMERGENCY SECURITY: All task routes require authentication
router.use(authenticateSupabaseUser);

// Apply task data sanitization middleware to all task routes
router.use(sanitizeTaskData);
router.use(sanitizeTaskResponse);

/**
 * @openapi
 * /tasks:
 *   post:
 *     tags: [Tasks]
 *     summary: Create a new task
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TaskCreateInput'
 *     responses:
 *       201:
 *         description: Task created successfully.
 *       400:
 *         description: Invalid input.
 *   get:
 *     tags: [Tasks]
 *     summary: Retrieve a list of all tasks
 *     responses:
 *       200:
 *         description: A list of tasks.
 */
router.post('/', validate(taskCreateSchema), taskController.createTask);
router.get('/', taskController.getAllTasks);

/**
 * @openapi
 * /tasks/{id}:
 *   get:
 *     tags: [Tasks]
 *     summary: Retrieve a specific task by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Details of the task.
 *       404:
 *         description: Task not found.
 *   put:
 *     tags: [Tasks]
 *     summary: Update a specific task by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TaskUpdateInput'
 *     responses:
 *       200:
 *         description: Task updated successfully.
 *       400:
 *         description: Invalid input.
 *       404:
 *         description: Task not found.
 *   delete:
 *     tags: [Tasks]
 *     summary: Delete a specific task by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Task deleted successfully.
 *       404:
 *         description: Task not found.
 */
router.get(
	'/:id',
	validate(taskIdSchema, 'params'),
	taskController.getTaskById
);
router.put(
	'/:id',
	validate(taskIdSchema, 'params'),
	validate(taskUpdateSchema),
	taskController.updateTask
);
router.delete(
	'/:id',
	requireRole(['MANAGER', 'ADMIN', 'SUPER_ADMIN']),
	validate(taskIdSchema, 'params'),
	taskController.deleteTask
);

export default router;
