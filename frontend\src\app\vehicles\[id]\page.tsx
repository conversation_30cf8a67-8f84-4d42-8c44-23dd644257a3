'use client';

import React, {useEffect, useState, useCallback} from 'react';
import {useRouter, useParams} from 'next/navigation';
import Link from 'next/link';
import {getVehicleById, deleteVehicle} from '@/lib/store';
import type {Vehicle} from '@/lib/types';
import {PageHeader} from '@/components/ui/PageHeader';
import {ActionButton} from '@/components/ui/action-button';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
	CardFooter,
} from '@/components/ui/card';
import {Alert, AlertDescription, AlertTitle} from '@/components/ui/alert';
import {Separator} from '@/components/ui/separator';
import {
	Car,
	Edit,
	Trash2,
	CalendarDays,
	Gauge,
	Palette,
	ShieldCheck,
	UserCircle,
	Phone,
	Mail,
	AlertTriangle,
	Users,
	Briefcase,
	ClipboardList,
	Settings,
	ArrowLeft,
} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';
import {<PERSON>kel<PERSON><PERSON>oa<PERSON>, DataLoader} from '@/components/ui/loading';
import {Button} from '@/components/ui/button';
import VehicleInfoCard from './components/VehicleInfoCard';
import ServiceLogForm from './components/ServiceLogForm';
import {EnhancedServiceHistoryContainer} from '@/components/service-history/EnhancedServiceHistoryContainer';
import {getVehicleServiceRecords} from '@/lib/services/serviceRecordService';
import MaintenanceSuggestor from './components/MaintenanceSuggestor'; // Import the new component
import ErrorBoundary from '@/components/ErrorBoundary';
import {ViewReportButton} from '@/components/reports/ViewReportButton';

const VehicleDetailPage = () => {
	const router = useRouter();
	const params = useParams();
	const {toast} = useToast();

	const vehicleId = params.id ? Number(params.id) : null;

	const [vehicle, setVehicle] = useState<Vehicle | null>(null);
	const [serviceRecords, setServiceRecords] = useState<EnrichedServiceRecord[]>(
		[]
	);
	const [isLoading, setIsLoading] = useState(true);
	const [isLoadingRecords, setIsLoadingRecords] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [recordsError, setRecordsError] = useState<string | null>(null);
	const [isDeleting, setIsDeleting] = useState(false);

	const fetchVehicleDetails = useCallback(async () => {
		if (!vehicleId) {
			setError('No vehicle ID provided.');
			setIsLoading(false);
			return;
		}
		setIsLoading(true);
		try {
			const data = await getVehicleById(vehicleId);
			if (data) {
				setVehicle(data);
			} else {
				setError('Vehicle not found.');
			}
		} catch (err: any) {
			console.error('Failed to fetch vehicle details:', err);
			setError(err.message || 'Failed to load vehicle data.');
		} finally {
			setIsLoading(false);
		}
	}, [vehicleId]);

	// Fetch service records for the vehicle
	const fetchServiceRecords = useCallback(async () => {
		if (!vehicleId) return;

		setIsLoadingRecords(true);
		setRecordsError(null);

		try {
			const records = await getVehicleServiceRecords(vehicleId);
			setServiceRecords(records);
		} catch (err: any) {
			console.error('Failed to fetch service records:', err);
			setRecordsError(err.message || 'Failed to load service records.');
		} finally {
			setIsLoadingRecords(false);
		}
	}, [vehicleId]);

	// Handle retry for service records
	const handleRetryRecords = useCallback(() => {
		fetchServiceRecords();
	}, [fetchServiceRecords]);

	useEffect(() => {
		fetchVehicleDetails();
		fetchServiceRecords();
	}, [fetchVehicleDetails, fetchServiceRecords]);

	const handleDelete = async () => {
		if (!vehicleId) return;
		if (
			window.confirm(
				'Are you sure you want to delete this vehicle permanently?'
			)
		) {
			setIsDeleting(true);
			try {
				await deleteVehicle(vehicleId);
				toast({
					title: 'Vehicle Deleted',
					description: `${vehicle?.make} ${vehicle?.model} has been deleted.`,
					variant: 'default',
				});
				router.push('/vehicles');
			} catch (err: any) {
				console.error('Failed to delete vehicle:', err);
				setError(err.message || 'Could not delete vehicle.');
				toast({
					title: 'Error Deleting Vehicle',
					description:
						err.message || 'Failed to delete vehicle. Please try again.',
					variant: 'destructive',
				});
			} finally {
				setIsDeleting(false);
			}
		}
	};

	const handleServiceRecordAdded = (updatedVehicle: Vehicle) => {
		setVehicle(updatedVehicle); // Update the vehicle state with the new service record
		fetchServiceRecords(); // Refresh the service records
		toast({
			title: 'Service Record Added',
			description: 'The new service record has been successfully logged.',
		});
	};

	return (
		<ErrorBoundary>
			<div className='container mx-auto py-8 space-y-8'>
				<DataLoader
					isLoading={isLoading}
					error={error}
					data={vehicle}
					onRetry={fetchVehicleDetails}
					loadingComponent={
						<div className='space-y-6'>
							<PageHeader title='Loading Vehicle...' icon={Car} />
							<SkeletonLoader variant='card' count={1} />
							<div className='grid lg:grid-cols-3 gap-6 items-start'>
								<SkeletonLoader
									variant='card'
									count={1}
									className='lg:col-span-2'
								/>
								<SkeletonLoader variant='card' count={1} />
							</div>
						</div>
					}
					emptyComponent={
						<div className='text-center py-10'>
							<PageHeader title='Vehicle Not Found' icon={AlertTriangle} />
							<p className='mb-4'>The requested vehicle could not be found.</p>
							<ActionButton
								actionType='primary'
								onClick={() => router.push('/vehicles')}
								icon={<ArrowLeft className='h-4 w-4' />}>
								Back to Vehicles
							</ActionButton>
						</div>
					}>
					{(loadedVehicle) => (
						<>
							<PageHeader
								title={`${loadedVehicle.make} ${loadedVehicle.model}`}
								description={`VIN: ${loadedVehicle.vin} | Plate: ${loadedVehicle.licensePlate}`}
								icon={Car}>
								<div className='flex space-x-2'>
									<ActionButton
										actionType='secondary'
										asChild
										icon={<Edit className='h-4 w-4' />}>
										<Link href={`/vehicles/${loadedVehicle.id}/edit`}>
											Edit
										</Link>
									</ActionButton>
									<ViewReportButton
										href={`/vehicles/${loadedVehicle.id}/report`}
									/>
									<ActionButton
										actionType='danger'
										onClick={handleDelete}
										isLoading={isDeleting}
										icon={<Trash2 className='h-4 w-4' />}
										loadingText='Deleting...'>
										Delete
									</ActionButton>
								</div>
							</PageHeader>

							<div className='grid lg:grid-cols-3 gap-6 items-start'>
								<div className='lg:col-span-2 space-y-6'>
									<VehicleInfoCard vehicle={loadedVehicle} />
									<EnhancedServiceHistoryContainer
										records={serviceRecords}
										isLoading={isLoadingRecords}
										error={recordsError}
										onRetry={handleRetryRecords}
										showVehicleInfo={false}
										vehicleSpecific={true}
									/>
								</div>
								<div className='lg:col-span-1 space-y-6'>
									<ServiceLogForm
										vehicleId={String(loadedVehicle.id)}
										onServiceRecordAdded={handleServiceRecordAdded}
										currentOdometerReading={Math.max(
											...loadedVehicle.serviceHistory.map((s) => s.odometer),
											loadedVehicle.initialOdometer || 0
										)}
									/>
									<MaintenanceSuggestor vehicle={loadedVehicle} />
								</div>
							</div>
						</>
					)}
				</DataLoader>
			</div>
		</ErrorBoundary>
	);
};

export default VehicleDetailPage;
