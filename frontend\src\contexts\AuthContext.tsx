import React, {createContext, useContext, ReactNode, useEffect} from 'react';
import {useAuth, UseAuthReturn} from '../hooks/useAuth';
import {setAuthToken as setApiServiceToken} from '../lib/services/apiService';
import {setAuthToken as setLegacyApiServiceToken} from '../lib/apiService';

interface AuthContextType extends UseAuthReturn {}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
	children: ReactNode;
}

/**
 * EMERGENCY SECURITY PROVIDER - Authentication Context
 *
 * This provider wraps the application and provides authentication state
 * and methods to all child components.
 *
 * CRITICAL: This provider is part of the emergency security implementation
 */
export function AuthProvider({children}: AuthProviderProps) {
	const auth = useAuth();

	// 🚨 EMERGENCY SECURITY: Sync auth token with API services
	useEffect(() => {
		const token = auth.session?.access_token || null;

		// Update both API services with the current token
		setApiServiceToken(token);
		setLegacyApiServiceToken(token);

		if (process.env.NODE_ENV !== 'production') {
			console.log(
				'🔐 AuthProvider: Token synced to API services',
				token ? 'Token set' : 'Token cleared'
			);
		}
	}, [auth.session?.access_token]);

	return <AuthContext.Provider value={auth}>{children}</AuthContext.Provider>;
}

/**
 * Hook to use the authentication context
 *
 * @throws {Error} If used outside of AuthProvider
 */
export function useAuthContext(): AuthContextType {
	const context = useContext(AuthContext);

	if (context === undefined) {
		throw new Error('useAuthContext must be used within an AuthProvider');
	}

	return context;
}

// Export the context for advanced use cases
export {AuthContext};
