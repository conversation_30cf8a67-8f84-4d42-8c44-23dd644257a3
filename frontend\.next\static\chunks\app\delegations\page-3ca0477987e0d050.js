(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7530],{11695:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>T});var a=t(95155),s=t(12115),l=t(6874),n=t.n(l),d=t(35695),i=t(6560),o=t(57082),c=t(34301),u=t(75074),h=t(95647),m=t(62523),x=t(2730);function g(e){let{searchTerm:r="",children:t}=e;(0,d.useRouter)();let[l,n]=(0,s.useState)([]),[i,o]=(0,s.useState)([]),[c,u]=(0,s.useState)(!0),[h,m]=(0,s.useState)(null),[g,p]=(0,s.useState)(0),f=(0,s.useCallback)(async()=>{u(!0),m(null);try{let e=await (0,x.getDelegations)();e.sort((e,r)=>new Date(r.durationFrom).getTime()-new Date(e.durationFrom).getTime()),n(e)}catch(e){console.error("Error fetching delegations:",e),m("Failed to load delegations. Please try again later.")}finally{u(!1)}},[]);return(0,s.useEffect)(()=>{f()},[f,g]),(0,s.useEffect)(()=>{if(!r)return void o(l);let e=r.toLowerCase();o(l.filter(r=>r.eventName.toLowerCase().includes(e)||r.location.toLowerCase().includes(e)||r.status.toLowerCase().includes(e)||r.delegates.some(r=>r.name.toLowerCase().includes(e))))},[r,l]),(0,a.jsx)(a.Fragment,{children:t({delegations:i,loading:c,error:h,fetchDelegations:()=>(p(e=>e+1),f())})})}var p=t(77023),f=t(66766),y=t(66695),b=t(83662),v=t(98328),k=t(50286),j=t(58260),w=t(50594),N=t(19968),A=t(26126),M=t(22346),C=t(59434),P=t(73168),D=t(99673);let R=e=>{switch(e){case"Planned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";case"Confirmed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Completed":return"bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20";case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}};function L(e){let{delegation:r}=e,t=e=>(0,P.GP)(new Date(e),"MMM d, yyyy");return(0,a.jsxs)(y.Zp,{className:"overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60",children:[(0,a.jsxs)(y.aR,{className:"p-0 relative",children:[(0,a.jsx)("div",{className:"aspect-[16/10] w-full relative",children:(0,a.jsx)(f.default,{src:r.imageUrl||"https://picsum.photos/seed/".concat(r.id,"/400/250"),alt:r.eventName,layout:"fill",objectFit:"cover",className:"bg-muted","data-ai-hint":"conference meeting",priority:!0})}),(0,a.jsx)(A.E,{className:(0,C.cn)("absolute top-2 right-2 text-xs py-1 px-2 font-semibold",R(r.status)),children:(0,D.fZ)(r.status)})]}),(0,a.jsxs)(y.Wu,{className:"p-5 flex-grow flex flex-col",children:[(0,a.jsx)(y.ZB,{className:"text-xl font-semibold mb-1 text-primary",children:r.eventName}),(0,a.jsxs)(y.BT,{className:"text-sm text-muted-foreground mb-3 flex items-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-1.5 flex-shrink-0 text-accent"}),r.location]}),(0,a.jsx)(M.w,{className:"my-3 bg-border/50"}),(0,a.jsxs)("div",{className:"space-y-2.5 text-sm text-foreground flex-grow",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Duration: "}),(0,a.jsxs)("strong",{className:"font-semibold",children:[t(r.durationFrom)," -"," ",t(r.durationTo)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(k.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Delegates: "}),(0,a.jsx)("strong",{className:"font-semibold",children:r.delegates.length})]})]}),(r.flightArrivalDetails||r.flightDepartureDetails)&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,a.jsx)("span",{className:"font-semibold text-muted-foreground",children:"Flight details logged"})]})]}),r.notes&&(0,a.jsxs)("p",{className:"mt-3 text-xs text-muted-foreground line-clamp-2 pt-2 border-t border-dashed border-border/50",title:r.notes,children:[(0,a.jsx)(w.A,{size:12,className:"inline mr-1 text-accent"}),r.notes]})]}),(0,a.jsx)(y.wL,{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,a.jsx)(i.r,{actionType:"tertiary",className:"w-full",icon:(0,a.jsx)(N.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(n(),{href:"/delegations/".concat(r.id),children:"View Details"})})})]})}var S=t(15080),z=t(24865);function T(){(0,d.useRouter)();let[e,r]=(0,s.useState)("");return(0,a.jsx)(S.A,{children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)(h.z,{title:"Manage Delegations",description:"Track and manage all your events, trips, and delegate information.",icon:o.A,children:(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(i.r,{actionType:"primary",icon:(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),asChild:!0,children:(0,a.jsx)(n(),{href:"/delegations/add",children:"Add New Delegation"})}),(0,a.jsx)(z.M,{getReportUrl:()=>{let r=new URLSearchParams({searchTerm:e}).toString();return"/delegations/report/list?".concat(r)},isList:!0})]})}),(0,a.jsx)("div",{className:"mb-6 p-4 bg-card rounded-lg shadow-md",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground"}),(0,a.jsx)(m.p,{type:"text",placeholder:"Search delegations (Event, Location, Delegate, Status...)",value:e,onChange:e=>r(e.target.value),className:"pl-10 w-full"})]})}),(0,a.jsx)(g,{searchTerm:e,children:r=>{let{delegations:t,loading:s,error:l,fetchDelegations:d}=r;return(0,a.jsx)(p.gO,{isLoading:s,error:l,data:t,onRetry:d,loadingComponent:(0,a.jsx)(p.jt,{variant:"card",count:3}),emptyComponent:(0,a.jsxs)("div",{className:"text-center py-12 bg-card rounded-lg shadow-md",children:[(0,a.jsx)(o.A,{className:"mx-auto h-16 w-16 text-muted-foreground mb-6"}),(0,a.jsx)("h3",{className:"text-2xl font-semibold text-foreground mb-2",children:e?"No Delegations Match Your Search":"No Delegations Yet!"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2 mb-6 max-w-md mx-auto",children:e?"Try adjusting your search terms or add a new delegation.":"It looks like you haven't added any delegations yet. Get started by adding one."}),!e&&(0,a.jsx)(i.r,{actionType:"primary",size:"lg",icon:(0,a.jsx)(c.A,{className:"h-4 w-4"}),asChild:!0,children:(0,a.jsx)(n(),{href:"/delegations/add",children:"Add Your First Delegation"})})]}),children:e=>(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,a.jsx)(L,{delegation:e},e.id))})})}})]})})}},15300:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},19968:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},22346:(e,r,t)=>{"use strict";t.d(r,{w:()=>d});var a=t(95155),s=t(12115),l=t(87489),n=t(59434);let d=s.forwardRef((e,r)=>{let{className:t,orientation:s="horizontal",decorative:d=!0,...i}=e;return(0,a.jsx)(l.b,{ref:r,decorative:d,orientation:s,className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",t),...i})});d.displayName=l.b.displayName},24865:(e,r,t)=>{"use strict";t.d(r,{M:()=>o});var a=t(95155);t(12115);var s=t(6874),l=t.n(s),n=t(15300),d=t(61840),i=t(6560);function o(e){let{href:r,getReportUrl:t,isList:s=!1,className:o}=e;if(!r&&!t)return console.error("ViewReportButton requires either href or getReportUrl prop"),null;let c=s?"View List Report":"View Report";return r?(0,a.jsx)(i.r,{actionType:"secondary",asChild:!0,icon:(0,a.jsx)(n.A,{className:"h-4 w-4"}),className:o,children:(0,a.jsxs)(l(),{href:r,target:"_blank",rel:"noopener noreferrer",children:[c,(0,a.jsx)(d.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"}),(0,a.jsx)("span",{className:"sr-only",children:"(opens in new tab)"})]})}):(0,a.jsxs)(i.r,{actionType:"secondary",onClick:()=>{if(t){let e=t();window.open(e,"_blank","noopener,noreferrer")}},icon:(0,a.jsx)(n.A,{className:"h-4 w-4"}),className:o,children:[c,(0,a.jsx)(d.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"})]})}},26126:(e,r,t)=>{"use strict";t.d(r,{E:()=>d});var a=t(95155);t(12115);var s=t(74466),l=t(59434);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:r,variant:t,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),r),...s})}},34301:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},35695:(e,r,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},50286:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},50594:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},57082:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58260:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},61840:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var a=t(95155),s=t(12115),l=t(59434);let n=s.forwardRef((e,r)=>{let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...n})});n.displayName="Input"},67554:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},74156:(e,r,t)=>{Promise.resolve().then(t.bind(t,11695))},75074:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},83662:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},87489:(e,r,t)=>{"use strict";t.d(r,{b:()=>o});var a=t(12115),s=t(63655),l=t(95155),n="horizontal",d=["horizontal","vertical"],i=a.forwardRef((e,r)=>{var t;let{decorative:a,orientation:i=n,...o}=e,c=(t=i,d.includes(t))?i:n;return(0,l.jsx)(s.sG.div,{"data-orientation":c,...a?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:r})});i.displayName="Separator";var o=i},98328:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},99673:(e,r,t)=>{"use strict";function a(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}t.d(r,{fZ:()=>a})}},e=>{var r=r=>e(e.s=r);e.O(0,[5769,8360,832,7529,6766,8162,2730,536,8441,1684,7358],()=>r(74156)),_N_E=e.O()}]);