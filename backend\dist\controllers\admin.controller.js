/**
 * Admin Controller
 *
 * This module provides controller functions for admin routes, including:
 * - Health status
 * - Performance metrics
 * - Error logs
 */
import * as adminService from '../services/admin.service.js';
import logger from '../utils/logger.js';
import HttpError from '../utils/HttpError.js';
/**
 * Get system health status
 * @param req Express request
 * @param res Express response
 */
export const getHealthStatus = async (req, res) => {
    try {
        logger.info('Health status check requested');
        const healthStatus = await adminService.getHealthStatus();
        // Return 200 if healthy, 503 if unhealthy
        const statusCode = healthStatus.status === 'UP' ? 200 : 503;
        // Return the health status wrapped in a standard response format
        res.status(statusCode).json({
            status: 'success',
            data: healthStatus,
        });
    }
    catch (error) {
        logger.error('Error in health status endpoint:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to retrieve health status',
            error: error.message,
        });
    }
};
/**
 * Get comprehensive system diagnostics
 * @param req Express request
 * @param res Express response
 */
export const getDiagnostics = async (req, res) => {
    try {
        logger.info('System diagnostics requested');
        // Get health status and performance metrics
        const [healthStatus, metrics] = await Promise.all([
            adminService.getHealthStatus(),
            adminService.getPerformanceMetrics(),
        ]);
        const diagnostics = {
            timestamp: new Date().toISOString(),
            health: healthStatus,
            performance: metrics,
            system: {
                nodeVersion: process.version,
                platform: process.platform,
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
            },
        };
        res.status(200).json({
            status: 'success',
            data: diagnostics,
        });
    }
    catch (error) {
        logger.error('Error in diagnostics endpoint:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to retrieve system diagnostics',
            error: error.message,
        });
    }
};
/**
 * Get database performance metrics
 * @param req Express request
 * @param res Express response
 */
export const getPerformanceMetrics = async (_req, res) => {
    try {
        logger.info('Performance metrics requested');
        const metrics = await adminService.getPerformanceMetrics();
        res.status(200).json({
            status: 'success',
            data: metrics,
        });
    }
    catch (error) {
        logger.error('Error in performance metrics endpoint:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to retrieve performance metrics',
            error: error.message,
        });
    }
};
/**
 * Get error logs with pagination and filtering
 * @param req Express request with query parameters
 * @param res Express response
 */
export const getErrorLogs = async (req, res) => {
    try {
        // Get validated query parameters from req.validatedData
        const { page, limit, level } = req.validatedData;
        logger.info(`Error logs requested - page: ${page}, limit: ${limit}, level: ${level || 'all'}`);
        const logs = await adminService.getErrorLogs(page, limit, level);
        res.status(200).json({
            status: 'success',
            data: logs.data,
            pagination: logs.pagination,
        });
    }
    catch (error) {
        logger.error('Error in error logs endpoint:', error);
        if (error instanceof HttpError) {
            res.status(error.status).json({
                status: 'error',
                message: error.message,
            });
        }
        else {
            res.status(500).json({
                status: 'error',
                message: 'Failed to retrieve error logs',
                error: error.message,
            });
        }
    }
};
//# sourceMappingURL=admin.controller.js.map