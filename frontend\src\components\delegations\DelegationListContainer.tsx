
'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { getDelegations } from '@/lib/store';
import type { Delegation } from '@/lib/types';
// DelegationCard and Skeleton are rendered by the parent, so they are not needed here.
// import DelegationCard from './DelegationCard'; 
// import DelegationCardSkeleton from './DelegationCardSkeleton';
import { ActionButton } from '@/components/ui/action-button'; // Changed
import { AlertTriangle } from 'lucide-react';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

interface DelegationListContainerProps {
  searchTerm?: string;
  children: (data: {
    delegations: Delegation[];
    loading: boolean; // Keep this for initial load indication
    error: string | null;
    fetchDelegations: () => Promise<void>; // Expose fetch for retry
    // isRefreshing and socketTriggered might be handled by a higher-level hook or context if needed for UI feedback
  }) => React.ReactNode;
}

export default function DelegationListContainer({ 
  searchTerm = '',
  children 
}: DelegationListContainerProps) {
  const router = useRouter();
  const [allDelegations, setAllDelegations] = useState<Delegation[]>([]);
  const [filteredDelegations, setFilteredDelegations] = useState<Delegation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Fetch delegations from API
  const fetchDelegations = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const delegationsData = await getDelegations();
      delegationsData.sort(
        (a, b) =>
          new Date(b.durationFrom).getTime() - new Date(a.durationFrom).getTime()
      );
      setAllDelegations(delegationsData);
      // Initial filtering is handled by the useEffect below
    } catch (err) {
      console.error('Error fetching delegations:', err);
      setError('Failed to load delegations. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initial fetch and retry mechanism
  useEffect(() => {
    fetchDelegations();
  }, [fetchDelegations, retryCount]); // retryCount dependency for manual retries

  // Filter delegations based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredDelegations(allDelegations);
      return;
    }
    const lowercaseSearchTerm = searchTerm.toLowerCase();
    const filtered = allDelegations.filter((delegation) => {
      return (
        delegation.eventName.toLowerCase().includes(lowercaseSearchTerm) ||
        delegation.location.toLowerCase().includes(lowercaseSearchTerm) ||
        delegation.status.toLowerCase().includes(lowercaseSearchTerm) ||
        delegation.delegates.some((delegate) =>
          delegate.name.toLowerCase().includes(lowercaseSearchTerm)
        )
      );
    });
    setFilteredDelegations(filtered);
  }, [searchTerm, allDelegations]);

  return (
    <>
      {children({
        delegations: filteredDelegations, // Pass the filtered list
        loading: isLoading,
        error: error,
        fetchDelegations: () => { // Provide a way to manually trigger fetch (e.g., for retry)
          setRetryCount(prev => prev + 1); 
          return fetchDelegations();
        }
        // Not passing isRefreshing or socketTriggered as this component focuses on data fetching and filtering
        // Real-time updates would ideally be managed by a global state or context if needed across many components
      })}
    </>
  );
}
