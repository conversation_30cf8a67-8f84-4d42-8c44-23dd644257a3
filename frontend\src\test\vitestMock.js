/**
 * This file provides Jest equivalents for Vitest functions
 * to make tests compatible with both testing frameworks.
 */

// Create a more robust mocked function
const mockedFn = (fn) => {
	// If fn is already a Jest mock, return it as is.
	if (
		typeof fn === 'function' &&
		typeof fn.mockImplementation === 'function' &&
		typeof fn.mock !== 'undefined'
	) {
		return fn;
	}

	// If fn is a regular function, create a Jest mock that wraps it.
	// jest.fn(implementation) creates a mock that calls the implementation.
	const mock = jest.fn(fn);
	return mock;
};

// Map Vitest functions to Jest equivalents
module.exports = {
	describe: global.describe,
	it: global.it,
	test: global.test,
	expect: global.expect,
	beforeEach: global.beforeEach,
	afterEach: global.afterEach,
	beforeAll: global.beforeAll,
	afterAll: global.afterAll,
	vi: {
		fn: jest.fn,
		mock: jest.mock,
		spyOn: jest.spyOn,
		clearAllMocks: jest.clearAllMocks,
		resetAllMocks: jest.resetAllMocks,
		restoreAllMocks: jest.restoreAllMocks,
		mocked: mockedFn,
		useFakeTimers: () => jest.useFakeTimers(),
		useRealTimers: () => jest.useRealTimers(),
		advanceTimersByTime: (ms) => jest.advanceTimersByTime(ms),
		runAllTimers: () => jest.runAllTimers(),
	},
};
