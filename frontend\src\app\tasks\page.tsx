'use client';

import React, {useEffect, useState, useMemo, useCallback} from 'react';
import Link from 'next/link';
import {useRouter} from 'next/navigation';
import {PlusCircle, Search, ClipboardCheck, Filter} from 'lucide-react';
import type {Task, Employee} from '@/lib/types';
import {getTasks, getEmployees, getEmployeeById} from '@/lib/store';
import {PageHeader} from '@/components/ui/PageHeader';
import {Input} from '@/components/ui/input';
import TaskCard from '@/components/tasks/TaskCard';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {TaskStatusSchema, TaskPrioritySchema} from '@/lib/schemas/taskSchemas';
import {Card, CardContent} from '@/components/ui/card';
import {Label} from '@/components/ui/label';
import {ActionButton} from '@/components/ui/action-button';
import {SkeletonLoader, DataLoader} from '@/components/ui/loading';
import ErrorBoundary from '@/components/ErrorBoundary';
import {ViewReportButton} from '@/components/reports/ViewReportButton';

function TaskCardSkeleton() {
	return (
		<div className='overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60 rounded-lg'>
			<div className='p-5 flex-grow flex flex-col'>
				<div className='flex justify-between items-start'>
					<SkeletonLoader
						variant='default'
						count={1}
						className='h-7 w-3/5 mb-1 bg-muted/50'
					/>
					<SkeletonLoader
						variant='default'
						count={1}
						className='h-5 w-1/4 mb-1 bg-muted/50 rounded-full'
					/>
				</div>
				<SkeletonLoader
					variant='default'
					count={1}
					className='h-4 w-1/2 mb-3 bg-muted/50'
				/>
				<SkeletonLoader
					variant='default'
					count={1}
					className='h-px w-full my-3 bg-border/50'
				/>
				<div className='space-y-2.5 flex-grow'>
					{[...Array(3)].map((_, i) => (
						<div key={i} className='flex items-center'>
							<SkeletonLoader
								variant='default'
								count={1}
								className='mr-2.5 h-5 w-5 rounded-full bg-muted/50'
							/>
							<SkeletonLoader
								variant='default'
								count={1}
								className='h-5 w-2/3 bg-muted/50'
							/>
						</div>
					))}
				</div>
			</div>
			<div className='p-4 border-t border-border/60 bg-muted/20'>
				<SkeletonLoader
					variant='default'
					count={1}
					className='h-10 w-full bg-muted/50'
				/>
			</div>
		</div>
	);
}

const TasksPageContent = () => {
	const router = useRouter();
	const [allTasks, setAllTasks] = useState<Task[]>([]);
	const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [searchTerm, setSearchTerm] = useState('');
	const [statusFilter, setStatusFilter] = useState<string>('all');
	const [priorityFilter, setPriorityFilter] = useState<string>('all');
	const [employeeFilter, setEmployeeFilter] = useState<string>('all');
	const [employeesList, setEmployeesList] = useState<
		{id: string; name: string; role: string}[]
	>([]);

	const fetchInitialData = useCallback(async () => {
		setIsLoading(true);
		setError(null);
		try {
			const [tasksData, employeesData] = await Promise.all([
				getTasks(),
				getEmployees(),
			]);

			if (Array.isArray(tasksData)) {
				tasksData.sort(
					(a, b) =>
						new Date(b.dateTime).getTime() - new Date(a.dateTime).getTime()
				);
				setAllTasks(tasksData);
			} else {
				console.error('getTasks did not return an array:', tasksData);
				setAllTasks([]);
			}

			if (Array.isArray(employeesData)) {
				const formattedEmployees = employeesData.map((e) => ({
					id: String(e.id),
					name: e.fullName || e.name,
					role: e.role,
				}));
				setEmployeesList(formattedEmployees);
			} else {
				console.error('getEmployees did not return an array:', employeesData);
				setEmployeesList([]);
			}
		} catch (err) {
			console.error('Error fetching data:', err);
			setError(err instanceof Error ? err.message : 'Failed to load data');
			setAllTasks([]);
			setEmployeesList([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchInitialData();
	}, [fetchInitialData]);

	useEffect(() => {
		let tempTasks = [...allTasks];
		const lowercasedSearch = searchTerm.toLowerCase();

		if (statusFilter !== 'all') {
			tempTasks = tempTasks.filter((task) => task.status === statusFilter);
		}
		if (priorityFilter !== 'all') {
			tempTasks = tempTasks.filter((task) => task.priority === priorityFilter);
		}
		if (employeeFilter !== 'all') {
			tempTasks = tempTasks.filter(
				(task) =>
					(task.assignedEmployeeId &&
						task.assignedEmployeeId === employeeFilter) ||
					(employeeFilter === 'unassigned' && !task.assignedEmployeeId)
			);
		}

		if (lowercasedSearch) {
			tempTasks = tempTasks.filter((task) => {
				const employee = task.assignedEmployeeId
					? employeesList.find((e) => e.id === String(task.assignedEmployeeId))
					: null;
				return (
					task.description.toLowerCase().includes(lowercasedSearch) ||
					task.location.toLowerCase().includes(lowercasedSearch) ||
					(task.notes && task.notes.toLowerCase().includes(lowercasedSearch)) ||
					(employee && employee.name.toLowerCase().includes(lowercasedSearch))
				);
			});
		}
		setFilteredTasks(tempTasks);
	}, [
		searchTerm,
		allTasks,
		statusFilter,
		priorityFilter,
		employeeFilter,
		employeesList,
	]);

	const activeFilters =
		searchTerm ||
		statusFilter !== 'all' ||
		priorityFilter !== 'all' ||
		employeeFilter !== 'all';

	const getTasksReportUrl = () => {
		const queryParams = new URLSearchParams({
			searchTerm,
			status: statusFilter,
			priority: priorityFilter,
			employee: employeeFilter,
		}).toString();

		return `/tasks/report?${queryParams}`;
	};

	return (
		<div className='space-y-8'>
			<PageHeader
				title='Manage Tasks'
				description='Oversee all tasks, assignments, and progress.'
				icon={ClipboardCheck}>
				<div className='flex gap-2 items-center'>
					<ActionButton
						actionType='primary'
						icon={<PlusCircle className='h-4 w-4' />}
						asChild>
						<Link href='/tasks/add'>Add New Task</Link>
					</ActionButton>
					<ViewReportButton getReportUrl={getTasksReportUrl} isList={true} />
				</div>
			</PageHeader>
			<Card className='mb-6 p-4 shadow'>
				<CardContent className='pt-4'>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end'>
						<div className='relative lg:col-span-1'>
							<Label
								htmlFor='search-tasks'
								className='block text-sm font-medium text-muted-foreground mb-1'>
								Search Tasks
							</Label>
							<Search className='absolute left-3 top-[calc(50%_-_0.5rem_+_12px)] h-5 w-5 text-muted-foreground' />
							<Input
								id='search-tasks'
								type='text'
								placeholder='Description, location, assignee...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='pl-10 w-full'
							/>
						</div>
						<div>
							<Label
								htmlFor='status-filter'
								className='block text-sm font-medium text-muted-foreground mb-1'>
								Filter by Status
							</Label>
							<Select value={statusFilter} onValueChange={setStatusFilter}>
								<SelectTrigger id='status-filter'>
									<SelectValue placeholder='All Statuses' />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value='all'>All Statuses</SelectItem>
									{TaskStatusSchema.options.map((s) => (
										<SelectItem key={s} value={s}>
											{s}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
						<div>
							<Label
								htmlFor='priority-filter'
								className='block text-sm font-medium text-muted-foreground mb-1'>
								Filter by Priority
							</Label>
							<Select value={priorityFilter} onValueChange={setPriorityFilter}>
								<SelectTrigger id='priority-filter'>
									<SelectValue placeholder='All Priorities' />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value='all'>All Priorities</SelectItem>
									{TaskPrioritySchema.options.map((p) => (
										<SelectItem key={p} value={p}>
											{p}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
						<div>
							<Label
								htmlFor='employee-filter'
								className='block text-sm font-medium text-muted-foreground mb-1'>
								Filter by Employee
							</Label>
							<Select value={employeeFilter} onValueChange={setEmployeeFilter}>
								<SelectTrigger id='employee-filter'>
									<SelectValue placeholder='All Employees' />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value='all'>All Employees</SelectItem>
									{employeesList.map((e) => (
										<SelectItem key={e.id} value={e.id}>
											{e.name} (
											{e.role.charAt(0).toUpperCase() +
												e.role.slice(1).replace('_', ' ')}
											)
										</SelectItem>
									))}
									<SelectItem value='unassigned'>Unassigned</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>
				</CardContent>
			</Card>

			<DataLoader
				isLoading={isLoading}
				error={error}
				data={filteredTasks}
				onRetry={fetchInitialData}
				loadingComponent={
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{[...Array(3)].map((_, i) => (
							<TaskCardSkeleton key={i} />
						))}
					</div>
				}
				emptyComponent={
					<div className='text-center py-12 bg-card rounded-lg shadow-md'>
						<ClipboardCheck className='mx-auto h-16 w-16 text-muted-foreground mb-6' />
						<h3 className='text-2xl font-semibold text-foreground mb-2'>
							{activeFilters
								? 'No Tasks Match Your Filters'
								: 'No Tasks Created Yet'}
						</h3>
						<p className='text-muted-foreground mt-2 mb-6 max-w-md mx-auto'>
							{activeFilters
								? 'Try adjusting your search or filter criteria.'
								: "It looks like you haven't created any tasks yet. Get started by adding one."}
						</p>
						{!activeFilters && (
							<ActionButton
								actionType='primary'
								size='lg'
								icon={<PlusCircle className='h-4 w-4' />}
								asChild>
								<Link href='/tasks/add'>Create Your First Task</Link>
							</ActionButton>
						)}
					</div>
				}>
				{(tasksData) => (
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-8'>
						{tasksData.map((task) => (
							<TaskCard key={task.id} task={task} />
						))}
					</div>
				)}
			</DataLoader>
		</div>
	);
};

export default function TasksPage() {
	return (
		<ErrorBoundary>
			<TasksPageContent />
		</ErrorBoundary>
	);
}
