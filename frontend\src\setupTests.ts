// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';
import {TextDecoder, TextEncoder} from 'util';
import React from 'react';

// Mock global fetch
global.fetch = jest.fn();

// Mock global TextEncoder/TextDecoder
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder as any;

// Mock Next.js components and hooks
jest.mock('next/navigation', () => ({
	useRouter: () => ({
		push: jest.fn(),
		replace: jest.fn(),
		prefetch: jest.fn(),
		back: jest.fn(),
		forward: jest.fn(),
		refresh: jest.fn(),
		pathname: '/test-path',
		query: {},
	}),
	usePathname: () => '/test-path',
	useSearchParams: () => new URLSearchParams(),
}));

// Mock next/link
jest.mock('next/link', () => {
	const mockLink = function (props) {
		return require('react').createElement('a', props, props.children);
	};
	mockLink.displayName = 'Link';
	return mockLink;
});

// Mock IntersectionObserver
class MockIntersectionObserver {
	observe = jest.fn();
	disconnect = jest.fn();
	unobserve = jest.fn();
}

Object.defineProperty(window, 'IntersectionObserver', {
	writable: true,
	configurable: true,
	value: MockIntersectionObserver,
});

// Mock ResizeObserver
class MockResizeObserver {
	observe = jest.fn();
	disconnect = jest.fn();
	unobserve = jest.fn();
}

Object.defineProperty(window, 'ResizeObserver', {
	writable: true,
	configurable: true,
	value: MockResizeObserver,
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
	writable: true,
	value: jest.fn().mockImplementation((query) => ({
		matches: false,
		media: query,
		onchange: null,
		addListener: jest.fn(), // Deprecated
		removeListener: jest.fn(), // Deprecated
		addEventListener: jest.fn(),
		removeEventListener: jest.fn(),
		dispatchEvent: jest.fn(),
	})),
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
	constructor(callback: any) {}
	disconnect() {
		return null;
	}
	observe() {
		return null;
	}
	takeRecords() {
		return [];
	}
	unobserve() {
		return null;
	}
} as any;

// Suppress console errors during tests
const originalConsoleError = console.error;
console.error = (...args) => {
	// Filter out React-specific warnings that pollute test output
	if (
		typeof args[0] === 'string' &&
		(args[0].includes('Warning: ReactDOM.render') ||
			args[0].includes('Warning: React.createElement'))
	) {
		return;
	}
	originalConsoleError(...args);
};
