(()=>{var e={};e.id=5692,e.ids=[5692],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12174:(e,t,r)=>{Promise.resolve().then(r.bind(r,66612))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66612:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(60687);r(43210);var n=r(27965);function i(){return(0,s.jsx)(n.OJ,{children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"\uD83D\uDEA8 Emergency Security Test"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Testing Supabase Authentication Implementation"})]}),(0,s.jsx)(n.OV,{children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"✅ Authentication Successful"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"You are now authenticated and can access protected content."}),(0,s.jsx)(n.Fv,{variant:"card"})]}),(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-green-800 font-medium mb-2",children:"\uD83C\uDF89 Emergency Security Implementation Status"}),(0,s.jsxs)("ul",{className:"text-green-700 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"✅ Supabase client configured"}),(0,s.jsx)("li",{children:"✅ Authentication hook implemented"}),(0,s.jsx)("li",{children:"✅ Login form functional"}),(0,s.jsx)("li",{children:"✅ Protected routes working"}),(0,s.jsx)("li",{children:"✅ User profile display active"}),(0,s.jsx)("li",{children:"✅ Session management operational"})]})]})]})})]})})})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73338:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\auth-test\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\auth-test\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94022:(e,t,r)=>{Promise.resolve().then(r.bind(r,73338))},94735:e=>{"use strict";e.exports=require("events")},97289:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>d});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let d={children:["",{children:["auth-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,73338)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\auth-test\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\auth-test\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth-test/page",pathname:"/auth-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3744,1658,8141],()=>r(97289));module.exports=s})();