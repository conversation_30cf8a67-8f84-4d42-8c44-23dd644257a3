import { Router } from 'express';
import * as vehicleController from '../controllers/vehicle.controller.js';
import { validate } from '../middleware/validation.js';
import { authenticateSupabaseUser, requireRole, } from '../middleware/supabaseAuth.js';
import { vehicleCreateSchema, vehicleUpdateSchema, vehicleIdSchema, } from '../schemas/vehicle.schema.js';
const router = Router();
/**
 * @openapi
 * /vehicles:
 *   get:
 *     tags:
 *       - Vehicles
 *     summary: Retrieve a list of all vehicles
 *     responses:
 *       200:
 *         description: A list of vehicles.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Vehicle' # Assuming you'll define Vehicle schema
 *   post:
 *     tags:
 *       - Vehicles
 *     summary: Create a new vehicle
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/VehicleCreateInput' # Assuming VehicleCreateInput schema
 *     responses:
 *       201:
 *         description: Vehicle created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Vehicle'
 *       400:
 *         description: Invalid input
 *       409:
 *         description: Conflict - Vehicle with VIN already exists
 */
// 🚨 EMERGENCY SECURITY: All vehicle routes require authentication
router.get('/', authenticateSupabaseUser, vehicleController.getAllVehicles);
router.post('/', authenticateSupabaseUser, requireRole(['MANAGER', 'ADMIN', 'SUPER_ADMIN']), validate(vehicleCreateSchema), vehicleController.createVehicle);
/**
 * @openapi
 * /vehicles/{id}:
 *   get:
 *     tags:
 *       - Vehicles
 *     summary: Retrieve a specific vehicle by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The vehicle ID
 *     responses:
 *       200:
 *         description: Details of the vehicle.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Vehicle'
 *       404:
 *         description: Vehicle not found
 *   put:
 *     tags:
 *       - Vehicles
 *     summary: Update a specific vehicle by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The vehicle ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/VehicleUpdateInput' # Assuming VehicleUpdateInput schema
 *     responses:
 *       200:
 *         description: Vehicle updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Vehicle'
 *       400:
 *         description: Invalid input
 *       404:
 *         description: Vehicle not found
 *       409:
 *         description: Conflict - Vehicle with VIN already exists (if updating VIN)
 *   delete:
 *     tags:
 *       - Vehicles
 *     summary: Delete a specific vehicle by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The vehicle ID
 *     responses:
 *       200:
 *         description: Vehicle deleted successfully.
 *       204:
 *         description: Vehicle deleted successfully (No Content).
 *       404:
 *         description: Vehicle not found
 */
router.get('/:id', authenticateSupabaseUser, validate(vehicleIdSchema, 'params'), vehicleController.getVehicleById);
router.put('/:id', authenticateSupabaseUser, requireRole(['MANAGER', 'ADMIN', 'SUPER_ADMIN']), validate(vehicleIdSchema, 'params'), validate(vehicleUpdateSchema), vehicleController.updateVehicle);
router.delete('/:id', authenticateSupabaseUser, requireRole(['ADMIN', 'SUPER_ADMIN']), validate(vehicleIdSchema, 'params'), vehicleController.deleteVehicle);
export default router;
//# sourceMappingURL=vehicle.routes.js.map