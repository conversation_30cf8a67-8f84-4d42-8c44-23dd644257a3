(()=>{var e={};e.id=548,e.ids=[548],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43943:(e,r,t)=>{Promise.resolve().then(t.bind(t,52578))},45425:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),i=t(37716),o=t(28840),a=t(16189),n=t(48041);let d=(0,t(82614).A)("UserRoundPlus",[["path",{d:"M2 21a8 8 0 0 1 13.292-6",key:"bjp14o"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M19 16v6",key:"tddt3s"}],["path",{d:"M22 19h-6",key:"vcuq98"}]]);var l=t(29867),p=t(43210);function c(){let e=(0,a.useRouter)(),{toast:r}=(0,l.dj)(),[t,c]=(0,p.useState)(!1),[u,m]=(0,p.useState)(null),y=async t=>{c(!0),m(null);try{await (0,o.addEmployee)(t),r({title:"Employee Added",description:`The employee "${t.fullName||t.name}" has been successfully created.`,variant:"default"}),e.push("/employees")}catch(e){if(console.error("Error adding employee:",e),e.validationErrors&&Array.isArray(e.validationErrors)){let t=e.validationErrors.map(e=>`${e.path}: ${e.message}`).join("\n");console.log("Validation errors details:",t),m(`Validation failed: ${t}`),r({title:"Validation Error",description:"Please check the form for errors",variant:"destructive"})}else{let t=e.message||"Failed to add employee. Please try again.";m(t),r({title:"Error Adding Employee",description:t,variant:"destructive"})}}finally{c(!1)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(n.z,{title:"Add New Employee",description:"Enter the details for the new employee.",icon:d}),u&&(0,s.jsx)("div",{className:"bg-destructive/20 p-3 rounded-md text-destructive text-sm",children:u}),(0,s.jsx)(i.A,{onSubmit:y,isEditing:!1,isLoading:t})]})}},52578:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\employees\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\add\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71421:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>l});var s=t(65239),i=t(48088),o=t(88170),a=t.n(o),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let l={children:["",{children:["employees",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52578)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\add\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\add\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/employees/add/page",pathname:"/employees/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83263:(e,r,t)=>{Promise.resolve().then(t.bind(t,45425))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3744,1658,5880,2729,3442,8141,3983,3860],()=>t(71421));module.exports=s})();