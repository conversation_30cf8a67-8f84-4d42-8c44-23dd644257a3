(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={1132:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7112:(e,r,t)=>{Promise.resolve().then(t.bind(t,12454))},9587:(e,r,t)=>{Promise.resolve().then(t.bind(t,22538))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12454:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),a=t(58369),n=t(99196),o=t(44493),i=t(91821);function c(){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,s.jsx)(a.A,{className:"h-8 w-8 text-primary"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-primary",children:"Admin Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"System administration and diagnostics"})]})]}),(0,s.jsxs)(i.Fc,{children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)(i.XL,{children:"Information"}),(0,s.jsx)(i.TN,{children:"This admin dashboard provides system diagnostics and monitoring tools. No authentication is required for demonstration purposes."})]}),(0,s.jsxs)(o.Zp,{className:"shadow-md",children:[(0,s.jsxs)(o.aR,{className:"p-5",children:[(0,s.jsx)(o.ZB,{className:"text-xl font-semibold text-primary",children:"System Status"}),(0,s.jsx)(o.BT,{children:"Overview of system components and services"})]}),(0,s.jsx)(o.Wu,{className:"p-5",children:(0,s.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,s.jsxs)(o.Zp,{className:"shadow-sm",children:[(0,s.jsxs)(o.aR,{className:"pb-2 p-4",children:[(0,s.jsx)(o.ZB,{className:"text-base font-semibold",children:"Backend API"}),(0,s.jsx)(o.BT,{className:"text-xs",children:"Node.js API Server"})]}),(0,s.jsxs)(o.Wu,{className:"p-4 pt-0",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-green-500",children:"Online"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Version: 1.0.0"})]})]}),(0,s.jsxs)(o.Zp,{className:"shadow-sm",children:[(0,s.jsxs)(o.aR,{className:"pb-2 p-4",children:[(0,s.jsx)(o.ZB,{className:"text-base font-semibold",children:"Frontend"}),(0,s.jsx)(o.BT,{className:"text-xs",children:"Next.js Application"})]}),(0,s.jsxs)(o.Wu,{className:"p-4 pt-0",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-green-500",children:"Online"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Version: 1.0.0"})]})]}),(0,s.jsxs)(o.Zp,{className:"shadow-sm",children:[(0,s.jsxs)(o.aR,{className:"pb-2 p-4",children:[(0,s.jsx)(o.ZB,{className:"text-base font-semibold",children:"Database"}),(0,s.jsx)(o.BT,{className:"text-xs",children:"Supabase PostgreSQL"})]}),(0,s.jsxs)(o.Wu,{className:"p-4 pt-0",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-green-500",children:"Connected"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Phase 1 Security Active"})]})]})]})})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22538:(e,r,t)=>{"use strict";t.d(r,{Breadcrumb:()=>a,BreadcrumbItem:()=>o,BreadcrumbLink:()=>i,BreadcrumbList:()=>n,BreadcrumbPage:()=>c,BreadcrumbSeparator:()=>d});var s=t(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","Breadcrumb"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbList() from the server but BreadcrumbList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbList"),o=(0,s.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbItem() from the server but BreadcrumbItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbItem"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbLink() from the server but BreadcrumbLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbLink"),c=(0,s.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbPage() from the server but BreadcrumbPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbPage"),d=(0,s.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbSeparator() from the server but BreadcrumbSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbSeparator");(0,s.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbEllipsis() from the server but BreadcrumbEllipsis is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbEllipsis")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43560:(e,r,t)=>{Promise.resolve().then(t.bind(t,1132))},46539:(e,r,t)=>{Promise.resolve().then(t.bind(t,70640))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69795:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(82614).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70640:(e,r,t)=>{"use strict";t.d(r,{Breadcrumb:()=>c,BreadcrumbItem:()=>l,BreadcrumbLink:()=>m,BreadcrumbList:()=>d,BreadcrumbPage:()=>p,BreadcrumbSeparator:()=>u});var s=t(60687),a=t(43210),n=t(74158),o=(t(69795),t(8730)),i=t(4780);let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("nav",{ref:t,"aria-label":"breadcrumb",className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),...r}));c.displayName="Breadcrumb";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("ol",{ref:t,className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),...r}));d.displayName="BreadcrumbList";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("li",{ref:t,className:(0,i.cn)("inline-flex items-center gap-1.5",e),...r}));l.displayName="BreadcrumbItem";let m=a.forwardRef(({asChild:e,className:r,...t},a)=>{let n=e?o.DX:"a";return(0,s.jsx)(n,{ref:a,className:(0,i.cn)("transition-colors hover:text-foreground",r),...t})});m.displayName="BreadcrumbLink";let p=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("span",{ref:t,role:"link","aria-current":"page","aria-disabled":"true",className:(0,i.cn)("font-normal text-foreground",e),...r}));p.displayName="BreadcrumbPage";let u=({children:e,className:r,...t})=>(0,s.jsx)("span",{role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",r),...t,children:e??(0,s.jsx)(n.A,{className:"h-4 w-4"})});u.displayName="BreadcrumbSeparator"},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91273:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>l,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),n=t(88170),o=t.n(n),i=t(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(r,c);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99111:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>n});var s=t(37413),a=t(22538);let n={title:"Admin Dashboard - WorkHub",description:"Administrative dashboard for WorkHub system"};function o({children:e}){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(a.Breadcrumb,{className:"mb-4",children:(0,s.jsxs)(a.BreadcrumbList,{children:[(0,s.jsx)(a.BreadcrumbItem,{children:(0,s.jsx)(a.BreadcrumbLink,{href:"/",children:"Home"})}),(0,s.jsx)(a.BreadcrumbSeparator,{}),(0,s.jsx)(a.BreadcrumbItem,{children:(0,s.jsx)(a.BreadcrumbPage,{children:"Admin"})})]})})}),(0,s.jsx)("div",{className:"flex-1",children:e})]})}},99196:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(82614).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3744,1658,8141],()=>t(91273));module.exports=s})();