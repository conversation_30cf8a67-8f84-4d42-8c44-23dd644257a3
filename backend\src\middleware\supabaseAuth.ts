import {Request, Response, NextFunction} from 'express';
import {User} from '@supabase/supabase-js';
import {supabaseAdmin} from '../lib/supabase.js';

// Extend Express Request interface to include user
declare global {
	namespace Express {
		interface Request {
			user?: User;
			userId?: string;
		}
	}
}

/**
 * EMERGENCY SECURITY MIDDLEWARE - Supabase Authentication
 *
 * This middleware verifies JWT tokens issued by Supabase Auth
 * and attaches the authenticated user to the request object.
 *
 * CRITICAL: This middleware MUST be applied to all protected API routes
 */
export const authenticateSupabaseUser = async (
	req: Request,
	res: Response,
	next: NextFunction
): Promise<void> => {
	try {
		const authHeader = req.headers.authorization;
		const token = authHeader && authHeader.split(' ')[1];

		if (!token) {
			res.status(401).json({
				error: 'Access token required',
				code: 'NO_TOKEN',
				message:
					'Please provide a valid authorization token in the format: Bearer <token>',
			});
			return;
		}

		// Verify JWT token with Supabase
		const {
			data: {user},
			error,
		} = await supabaseAdmin.auth.getUser(token);

		if (error || !user) {
			console.error(
				'Authentication failed:',
				error?.message || 'No user found'
			);
			res.status(401).json({
				error: 'Invalid or expired token',
				code: 'INVALID_TOKEN',
				message: 'The provided token is invalid, expired, or malformed',
			});
			return;
		}

		// Check if user email is confirmed (optional security check)
		if (!user.email_confirmed_at) {
			res.status(401).json({
				error: 'Email not verified',
				code: 'EMAIL_NOT_VERIFIED',
				message: 'Please verify your email address before accessing the system',
			});
			return;
		}

		// Attach user to request for use in route handlers
		req.user = user;
		req.userId = user.id;

		// Log successful authentication (for audit purposes)
		console.log(`✅ User authenticated: ${user.email} (${user.id})`);

		next();
	} catch (error: any) {
		console.error('Authentication service error:', error);
		res.status(500).json({
			error: 'Authentication service unavailable',
			code: 'AUTH_SERVICE_ERROR',
			message: 'Unable to verify authentication at this time',
		});
	}
};

/**
 * Enhanced middleware for role-based access control
 * Uses custom claims injected by the Auth Hook from user_profiles table
 *
 * Phase 4 Update: Removed fallback to raw_user_meta_data - now relies solely on JWT custom claims
 */
export const requireRole = (allowedRoles: string[]) => {
	return async (
		req: Request,
		res: Response,
		next: NextFunction
	): Promise<void> => {
		try {
			if (!req.user) {
				res.status(401).json({
					error: 'Authentication required',
					code: 'NO_USER',
					message: 'User must be authenticated to access this resource',
				});
				return;
			}

			// Get role from JWT custom claims (injected by Auth Hook)
			const customClaims = req.user.app_metadata?.custom_claims;
			const userRole = customClaims?.user_role;
			const isActive = customClaims?.is_active;

			// Ensure we have role information from JWT custom claims
			if (!userRole) {
				res.status(401).json({
					error: 'Role information missing',
					code: 'NO_ROLE_CLAIMS',
					message:
						'User role information not found in JWT token. Please sign in again.',
				});
				return;
			}

			// Check if user is active
			if (isActive === false) {
				res.status(403).json({
					error: 'Account inactive',
					code: 'ACCOUNT_INACTIVE',
					message:
						'Your account has been deactivated. Please contact an administrator.',
				});
				return;
			}

			if (!allowedRoles.includes(userRole)) {
				res.status(403).json({
					error: 'Insufficient permissions',
					code: 'INSUFFICIENT_PERMISSIONS',
					message: `Access denied. Required roles: ${allowedRoles.join(
						', '
					)}. Your role: ${userRole}`,
				});
				return;
			}

			// Log successful authorization
			console.log(
				`✅ User authorized: ${req.user.email} (Role: ${userRole} from JWT_CLAIMS)`
			);

			next();
		} catch (error: any) {
			console.error('Role authorization error:', error);
			res.status(500).json({
				error: 'Authorization service unavailable',
				code: 'AUTHZ_SERVICE_ERROR',
			});
		}
	};
};
