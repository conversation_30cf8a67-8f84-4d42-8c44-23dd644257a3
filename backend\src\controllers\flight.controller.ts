/**
 * Flight Controller
 *
 * This controller handles requests related to flight information
 * using the OpenSky Network API.
 */

import {Request, Response} from 'express';
import * as openSkyService from '../services/opensky.service.js';
import {logger} from '../utils/logger.js';

/**
 * Search for flights by callsign
 *
 * @route GET /api/flights/search
 * @param req Request object with callsign query parameter
 * @param res Response object
 */
// Define a type for the response object
interface FlightSearchResponse {
	flights: any[];
	message?: string;
	details?: {
		possibleReasons?: string[];
		apiInfo?: string;
		searchParams?: {
			callsign: string;
			date: string;
		};
	};
	error?: string;
	timestamp?: string;
}

export const searchFlights = async (
	req: Request,
	res: Response
): Promise<void> => {
	const startTime = Date.now();
	const clientIP = req.ip || 'unknown';
	const userAgent = req.headers['user-agent'] || 'unknown';

	try {
		logger.info(`Flight search request received`, {
			ip: clientIP,
			userAgent,
			query: req.query,
		});

		const {callsign, date} = req.query;

		if (!callsign || typeof callsign !== 'string') {
			logger.warn(`Invalid request: Missing callsign parameter`, {
				ip: clientIP,
			});
			res.status(400).json({message: 'Callsign query parameter is required'});
			return;
		}

		if (
			!date ||
			typeof date !== 'string' ||
			!/^\d{4}-\d{2}-\d{2}$/.test(date)
		) {
			logger.warn(`Invalid request: Missing or invalid date parameter`, {
				ip: clientIP,
				callsign,
				date,
			});
			res.status(400).json({
				message: 'Date query parameter is required in YYYY-MM-DD format',
			});
			return;
		}

		if (callsign.trim().length < 2) {
			logger.warn(`Invalid request: Callsign too short`, {
				callsign,
				ip: clientIP,
			});
			res.status(400).json({message: 'Callsign must be at least 2 characters'});
			return;
		}

		logger.info(
			`Searching for flights with callsign: ${callsign} on date: ${date}`
		);
		const flights = await openSkyService.findFlightsByCallsignForDate(
			callsign as string,
			date as string
		);

		const responseTime = Date.now() - startTime;
		logger.info(`Flight search completed in ${responseTime}ms`, {
			callsign,
			resultsCount: flights.length,
			responseTime,
		});

		// If no flights found, provide a more detailed response
		if (flights.length === 0) {
			logger.info(
				`No flights found for callsign: ${callsign} on date: ${date}`
			);
			res.status(200).json({
				flights: [],
				message: `No flights found matching "${callsign}" on ${date}`,
				details: {
					possibleReasons: [
						"The flight doesn't exist with this callsign on this date",
						"The OpenSky Network API doesn't have data for this flight",
						"The callsign format might be incorrect (try airline code + flight number, e.g., 'BA123')",
					],
					apiInfo:
						'OpenSky Network API provides historical flight data, not future schedules',
					searchParams: {callsign, date},
				},
			});
			return;
		}

		res.status(200).json(flights);
	} catch (error: any) {
		const responseTime = Date.now() - startTime;
		logger.error(`Error searching flights: ${error.message}`, {
			ip: clientIP,
			userAgent,
			query: req.query,
			responseTime,
			stack: error.stack,
		});

		// Determine appropriate status code based on error type
		let statusCode = 500;
		let errorMessage = 'Error searching flights';

		// Check for specific error types
		if (error.message.includes('future date')) {
			statusCode = 400; // Bad Request for future dates
			errorMessage = 'Future date not supported';
		} else if (error.message.includes('rate limit')) {
			statusCode = 429; // Too Many Requests
			errorMessage = 'API rate limit exceeded';
		}

		// Extract callsign and date from query params for error response
		const queryCallsign = (req.query.callsign as string) || 'unknown';
		const queryDate = (req.query.date as string) || 'unknown';

		res.status(statusCode).json({
			message: errorMessage,
			error: error.message,
			timestamp: new Date().toISOString(),
			details: {
				apiInfo:
					'OpenSky Network API provides historical flight data, not future schedules',
				searchParams: {
					callsign: queryCallsign,
					date: queryDate,
				},
			},
		});
	}
};

/**
 * Get flights by airport (arrivals or departures)
 *
 * @route GET /api/flights/airport
 * @param req Request object with airport, begin, end, and type query parameters
 * @param res Response object
 */
export const getFlightsByAirport = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const {airport, begin, end, type} = req.query;

		if (!airport || typeof airport !== 'string') {
			res.status(400).json({message: 'Airport ICAO code is required'});
			return;
		}

		if (!begin || !end) {
			res.status(400).json({message: 'Begin and end timestamps are required'});
			return;
		}

		const beginTimestamp = parseInt(begin as string, 10);
		const endTimestamp = parseInt(end as string, 10);

		if (isNaN(beginTimestamp) || isNaN(endTimestamp)) {
			res
				.status(400)
				.json({message: 'Begin and end must be valid Unix timestamps'});
			return;
		}

		// Check if the time interval is within limits (max 7 days for airport endpoints)
		const maxInterval = 7 * 24 * 60 * 60; // 7 days in seconds
		if (endTimestamp - beginTimestamp > maxInterval) {
			res.status(400).json({
				message: `Time interval too large. Maximum interval is ${maxInterval} seconds (7 days)`,
			});
			return;
		}

		// Default to arrivals if type is not specified
		const isArrival = type !== 'departure';

		const flights = await openSkyService.getFlightsByAirport(
			airport,
			beginTimestamp,
			endTimestamp,
			isArrival
		);

		res.status(200).json(flights);
	} catch (error: any) {
		logger.error(`Error fetching flights by airport: ${error.message}`);
		res
			.status(500)
			.json({message: 'Error fetching flights', error: error.message});
	}
};

/**
 * Get flights by time interval
 *
 * @route GET /api/flights/interval
 * @param req Request object with begin and end query parameters
 * @param res Response object
 */
export const getFlightsByTimeInterval = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const {begin, end} = req.query;

		if (!begin || !end) {
			res.status(400).json({message: 'Begin and end timestamps are required'});
			return;
		}

		const beginTimestamp = parseInt(begin as string, 10);
		const endTimestamp = parseInt(end as string, 10);

		if (isNaN(beginTimestamp) || isNaN(endTimestamp)) {
			res
				.status(400)
				.json({message: 'Begin and end must be valid Unix timestamps'});
			return;
		}

		// Check if the time interval is within limits (max 2 hours for all flights endpoint)
		const maxInterval = 2 * 60 * 60; // 2 hours in seconds
		if (endTimestamp - beginTimestamp > maxInterval) {
			res.status(400).json({
				message: `Time interval too large. Maximum interval is ${maxInterval} seconds (2 hours)`,
			});
			return;
		}

		const flights = await openSkyService.getFlightsByTimeInterval(
			beginTimestamp,
			endTimestamp
		);
		res.status(200).json(flights);
	} catch (error: any) {
		logger.error(`Error fetching flights by time interval: ${error.message}`);
		res
			.status(500)
			.json({message: 'Error fetching flights', error: error.message});
	}
};
