"use strict";exports.id=8016,exports.ids=[8016],exports.modules={15079:(e,t,a)=>{a.d(t,{bq:()=>x,eb:()=>f,gC:()=>p,l6:()=>c,yv:()=>m});var s=a(60687),r=a(43210),l=a(22670),i=a(61662),n=a(89743),d=a(58450),o=a(4780);let c=l.bL;l.YJ;let m=l.WT,x=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(l.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,s.jsx)(l.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=l.l9.displayName;let h=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.PP,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=l.PP.displayName;let u=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.wn,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=l.wn.displayName;let p=r.forwardRef(({className:e,children:t,position:a="popper",...r},i)=>(0,s.jsx)(l.ZL,{children:(0,s.jsxs)(l.UC,{ref:i,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,s.jsx)(h,{}),(0,s.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(u,{})]})}));p.displayName=l.UC.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.JU,{ref:a,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=l.JU.displayName;let f=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(l.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)(l.p4,{children:t})]}));f.displayName=l.q7.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.wv,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=l.wv.displayName},15795:(e,t,a)=>{function s(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}a.d(t,{fZ:()=>s})},34729:(e,t,a)=>{a.d(t,{T:()=>i});var s=a(60687),r=a(43210),l=a(4780);let i=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));i.displayName="Textarea"},48041:(e,t,a)=>{a.d(t,{z:()=>r});var s=a(60687);function r({title:e,description:t,icon:a,children:r}){return(0,s.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[a&&(0,s.jsx)(a,{className:"h-8 w-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),t&&(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:t})]}),r&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:r})]})}a(43210)},67632:(e,t,a)=>{a.d(t,{A:()=>er});var s=a(60687),r=a(27605),l=a(63442),i=a(29088),n=a(68752),d=a(89667),o=a(34729),c=a(44493),m=a(71669),x=a(15079),h=a(99196),u=a(48206),p=a(57207),f=a(35265),j=a(93242),g=a(41936),y=a(48409),N=a(55817),v=a(71273),b=a(16189),w=a(58261),D=a(76869),T=a(43210),A=a(29867),C=a(26622),R=a(11516),I=a(52856),F=a(26134),M=a(78726),z=a(4780);let k=F.bL;F.l9;let S=F.ZL;F.bm;let P=T.forwardRef(({className:e,...t},a)=>(0,s.jsx)(F.hJ,{ref:a,className:(0,z.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));P.displayName=F.hJ.displayName;let J=T.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(S,{children:[(0,s.jsx)(P,{}),(0,s.jsxs)(F.UC,{ref:r,className:(0,z.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,s.jsxs)(F.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(M.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));J.displayName=F.UC.displayName;let B=({className:e,...t})=>(0,s.jsx)("div",{className:(0,z.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});B.displayName="DialogHeader";let O=({className:e,...t})=>(0,s.jsx)("div",{className:(0,z.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});O.displayName="DialogFooter";let L=T.forwardRef(({className:e,...t},a)=>(0,s.jsx)(F.hE,{ref:a,className:(0,z.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));L.displayName=F.hE.displayName;let $=T.forwardRef(({className:e,...t},a)=>(0,s.jsx)(F.VY,{ref:a,className:(0,z.cn)("text-sm text-muted-foreground",e),...t}));$.displayName=F.VY.displayName;var U=a(68123);let _=T.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(U.bL,{ref:r,className:(0,z.cn)("relative overflow-hidden",e),...a,children:[(0,s.jsx)(U.LM,{className:"h-full w-full rounded-[inherit]",children:t}),(0,s.jsx)(V,{}),(0,s.jsx)(U.OK,{})]}));_.displayName=U.bL.displayName;let V=T.forwardRef(({className:e,orientation:t="vertical",...a},r)=>(0,s.jsx)(U.VM,{ref:r,orientation:t,className:(0,z.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...a,children:(0,s.jsx)(U.lr,{className:"relative flex-1 rounded-full bg-border"})}));V.displayName=U.VM.displayName;var E=a(49254);let G=async(e,t)=>{if(!t)throw console.error("Search date is required for historical flight search."),Error("Search date is required");if(new Date(`${t}T00:00:00.000Z`)>new Date)throw console.warn(`Search for future date rejected: ${t}`),Error(`OpenSky API does not provide data for future dates. The date ${t} is in the future.`);let a=await (0,E.Fd)(`/flights/search?callsign=${encodeURIComponent(e)}&date=${t}`);if(Array.isArray(a))return a;if(a.flights)return a.flights;if(a.message){let e=Error(a.message);throw e.details=a.details,e}return[]};var H=a(40599);let Z=H.bL,q=H.l9,Y=T.forwardRef(({className:e,align:t="center",sideOffset:a=4,...r},l)=>(0,s.jsx)(H.ZL,{children:(0,s.jsx)(H.UC,{ref:l,align:t,sideOffset:a,className:(0,z.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",e),...r})}));Y.displayName=H.UC.displayName;var W=a(43967),K=a(74158),Q=a(6800),X=a(29523);function ee({className:e,classNames:t,showOutsideDays:a=!0,...r}){return(0,s.jsx)(Q.hv,{showOutsideDays:a,className:(0,z.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,z.cn)((0,X.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,z.cn)((0,X.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:({className:e,...t})=>(0,s.jsx)(W.A,{className:(0,z.cn)("h-4 w-4",e),...t}),IconRight:({className:e,...t})=>(0,s.jsx)(K.A,{className:(0,z.cn)("h-4 w-4",e),...t})},...r})}function et({isOpen:e,onClose:t,onSelectFlight:a,type:r}){let[l,i]=(0,T.useState)(""),[o,c]=(0,T.useState)(new Date),[m,x]=(0,T.useState)(!1),[h,u]=(0,T.useState)([]),[p,f]=(0,T.useState)(null),{toast:N}=(0,A.dj)(),v=(0,T.useCallback)(function(e,t){let a=null,s=function(...t){let s=this;a&&clearTimeout(a),a=setTimeout(()=>{a=null,e.apply(s,t)},500)};return s.cancel=function(){a&&(clearTimeout(a),a=null)},s}(async(e,t)=>{if(e.length<2)return void u([]);if(!t){f("Please select a date to search."),N({title:"Date Required",description:"Please select a date before searching for flights.",variant:"destructive"}),u([]);return}x(!0),f(null);let a=(0,D.GP)(t,"yyyy-MM-dd");try{let t=await G(e,a);u(t),0===t.length&&N({title:"No Flights Found",description:`No flights found matching "${e}" on ${a}.`,variant:"default"})}catch(e){f(e.message||"Failed to search flights"),N({title:"Error Searching Flights",description:`Failed to search flights: ${e.message}. Please try again.`,variant:"destructive"})}finally{x(!1)}},500),[N]),b=e=>{a(e),t()},w=e=>e?new Date(1e3*e).toLocaleString():"Unknown";return(0,s.jsx)(k,{open:e,onOpenChange:e=>!e&&t(),children:(0,s.jsxs)(J,{className:"sm:max-w-[600px] max-h-[80vh] flex flex-col",children:[(0,s.jsxs)(B,{children:[(0,s.jsxs)(L,{className:"flex items-center",children:["arrival"===r?(0,s.jsx)(j.A,{className:"mr-2 h-5 w-5 text-accent"}):(0,s.jsx)(y.A,{className:"mr-2 h-5 w-5 text-accent"}),"Search ","arrival"===r?"Arrival":"Departure"," Flights"]}),(0,s.jsx)($,{children:"Enter a flight callsign (e.g., BA123) and select a date to search for flights."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(g.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(d.p,{placeholder:"Callsign (e.g., BA123)",className:"pl-10",value:l,onChange:e=>i(e.target.value)})]}),(0,s.jsx)("div",{children:(0,s.jsxs)(Z,{children:[(0,s.jsx)(q,{asChild:!0,children:(0,s.jsx)(n.r,{actionType:"tertiary",className:(0,z.cn)("w-full justify-start text-left font-normal",!o&&"text-muted-foreground"),icon:(0,s.jsx)(C.A,{className:"h-4 w-4"}),children:o?(0,D.GP)(o,"PPP"):(0,s.jsx)("span",{children:"Pick a date"})})}),(0,s.jsx)(Y,{className:"w-auto p-0",children:(0,s.jsx)(ee,{mode:"single",selected:o,onSelect:c,initialFocus:!0})})]})})]}),m?(0,s.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,s.jsx)(R.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,s.jsx)("span",{className:"ml-2 text-muted-foreground",children:"Searching flights..."})]}):p?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsxs)("div",{className:"bg-destructive/15 text-destructive p-4 rounded-md mb-4 text-sm",children:[(0,s.jsx)("h4",{className:"font-bold mb-2",children:"Error Details:"}),(0,s.jsx)("p",{className:"mb-2",children:p}),p&&p.details&&(0,s.jsxs)("div",{className:"mt-3 border-t border-destructive/20 pt-3",children:[p.details.possibleReasons&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("h5",{className:"font-semibold text-xs mb-1",children:"Possible Reasons:"}),(0,s.jsx)("ul",{className:"list-disc list-inside text-xs",children:p.details.possibleReasons.map((e,t)=>(0,s.jsx)("li",{className:"mb-1",children:e},t))})]}),p.details.apiInfo&&(0,s.jsxs)("div",{className:"mt-2 text-xs",children:[(0,s.jsx)("h5",{className:"font-semibold mb-1",children:"API Information:"}),(0,s.jsx)("p",{children:p.details.apiInfo})]})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground mt-3",children:["API URL:"," ","http://localhost:3001/api"]})]}),(0,s.jsx)("div",{className:"flex justify-center gap-2",children:p.toString().includes("future date")?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.r,{actionType:"tertiary",onClick:()=>{c(new Date),setTimeout(()=>v(l,new Date),100)},children:"Try with Today's Date"}),(0,s.jsx)(n.r,{actionType:"tertiary",onClick:()=>{let e=new Date;e.setDate(e.getDate()-1),c(e),setTimeout(()=>v(l,e),100)},children:"Try with Yesterday"})]}):(0,s.jsx)(n.r,{actionType:"tertiary",onClick:()=>v(l,o),children:"Try Again"})})]}):0===h.length?(0,s.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:l.length>0?(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-amber-500 font-medium mb-2",children:['No flights found matching "',l,'" on'," ",o?(0,D.GP)(o,"PPP"):"selected date"]}),(0,s.jsxs)("div",{className:"bg-muted p-4 rounded-md mb-4 text-sm",children:[(0,s.jsx)("h4",{className:"font-semibold mb-2",children:"Suggestions:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside text-muted-foreground text-sm",children:[(0,s.jsx)("li",{className:"mb-1",children:'Check if the callsign format is correct (e.g., "RYR441J" for Ryanair flight 441J)'}),(0,s.jsx)("li",{className:"mb-1",children:"Try searching for a different date - OpenSky may not have data for all dates"}),(0,s.jsx)("li",{className:"mb-1",children:"OpenSky Network API primarily provides historical data, not future schedules"}),(0,s.jsx)("li",{className:"mb-1",children:"Some flights may not be tracked by OpenSky Network"})]})]}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground p-2 bg-muted rounded-md inline-block",children:[(0,s.jsxs)("p",{children:["API URL:"," ","http://localhost:3001/api","/flights/search"]}),(0,s.jsxs)("p",{children:["Search Term: ",l]}),(0,s.jsxs)("p",{children:["Date:"," ",o?(0,D.GP)(o,"yyyy-MM-dd"):"Not selected"]})]})]}):"Enter a flight callsign to search."}):(0,s.jsx)(_,{className:"flex-1 max-h-[400px] pr-4",children:(0,s.jsx)("div",{className:"space-y-2",children:h.map(e=>(0,s.jsxs)("div",{className:"p-3 border rounded-md hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors",onClick:()=>b(e),children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold",children:e.callsign}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.departureAirport||"Unknown"," →"," ",e.arrivalAirport||"Unknown"]})]}),(0,s.jsxs)("div",{className:"text-right text-sm",children:[(0,s.jsxs)("p",{className:"flex items-center",children:[(0,s.jsx)(I.A,{className:"inline-block h-3 w-3 mr-1 text-muted-foreground"}),e.icao24]}),void 0!==e.onGround&&(0,s.jsx)("p",{className:e.onGround?"text-amber-500":"text-green-500",children:e.onGround?"On Ground":"In Air"})]})]}),(e.departureTime||e.arrivalTime)&&(0,s.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:[e.departureTime&&(0,s.jsxs)("p",{children:["Departure: ",w(e.departureTime)]}),e.arrivalTime&&(0,s.jsxs)("p",{children:["Arrival: ",w(e.arrivalTime)]})]})]},`${e.icao24}-${e.callsign}`))})}),(0,s.jsx)(O,{className:"mt-4",children:(0,s.jsx)(n.r,{actionType:"tertiary",onClick:t,children:"Cancel"})})]})})}ee.displayName="Calendar";var ea=a(15795);let es=(e,t)=>{if(!e)return"";try{let a=(0,w.H)(e);if("date"===t)return(0,D.GP)(a,"yyyy-MM-dd");return(0,D.GP)(a,"yyyy-MM-dd'T'HH:mm")}catch(t){return console.warn("Invalid date string for input formatting:",e),""}};function er({onSubmit:e,initialData:t,isEditing:C=!1,isSubmitting:R=!1}){let I=(0,b.useRouter)(),{toast:F}=(0,A.dj)(),[M,z]=(0,T.useState)(!1),[k,S]=(0,T.useState)(!1),P=(0,r.mN)({resolver:(0,l.u)(i.eL),defaultValues:{eventName:t?.eventName||"",location:t?.location||"",durationFrom:t?es(t.durationFrom,"date"):"",durationTo:t?es(t.durationTo,"date"):"",invitationFrom:t?.invitationFrom||"",invitationTo:t?.invitationTo||"",delegates:t?.delegates?.length?t.delegates:[{name:"",title:"",notes:""}],flightArrivalDetails:t?.flightArrivalDetails?{...t.flightArrivalDetails,dateTime:es(t.flightArrivalDetails.dateTime,"datetime-local")}:null,flightDepartureDetails:t?.flightDepartureDetails?{...t.flightDepartureDetails,dateTime:es(t.flightDepartureDetails.dateTime,"datetime-local")}:null,status:t?.status||"Planned",notes:t?.notes||"",imageUrl:t?.imageUrl||""}}),{fields:J,append:B,remove:O}=(0,r.jz)({control:P.control,name:"delegates"}),L=e=>{if(!e||(!e.flightNumber||""===e.flightNumber.trim())&&(!e.dateTime||""===e.dateTime.trim())&&(!e.airport||""===e.airport.trim()))return null;if(!(e.flightNumber&&""!==e.flightNumber.trim()&&e.dateTime&&""!==e.dateTime.trim()&&e.airport&&""!==e.airport.trim()))return console.warn("Incomplete flight details detected:",e),null;try{let{formatDateForApi:t,isValidDateString:s}=a(65594),r={...e};if(r.dateTime&&(r.dateTime=t(r.dateTime),!r.dateTime))return console.error("Invalid date format after processing:",e.dateTime),null;return r}catch(e){return console.error("Error processing flight details:",e),null}},$=(e,t)=>{try{let a="arrival"===t?e.arrivalTime:e.departureTime,s="";if(a){let e=new Date(1e3*a);s=(0,D.GP)(e,"yyyy-MM-dd'T'HH:mm")}else{let e=new Date;if("departure"===t&&P.getValues("durationFrom"))try{let t=(0,w.H)(P.getValues("durationFrom"));e.setDate(t.getDate()-1)}catch(e){console.warn("Could not parse durationFrom date, using current date")}s=(0,D.GP)(e,"yyyy-MM-dd'T'HH:mm")}"arrival"===t?(P.setValue("flightArrivalDetails.flightNumber",e.callsign),P.setValue("flightArrivalDetails.dateTime",s),P.setValue("flightArrivalDetails.airport",e.arrivalAirport||"")):(P.setValue("flightDepartureDetails.flightNumber",e.callsign),P.setValue("flightDepartureDetails.dateTime",s),P.setValue("flightDepartureDetails.airport",e.departureAirport||"")),F({title:"Flight Details Updated",description:`${"arrival"===t?"Arrival":"Departure"} flight details have been populated.`,variant:"default"})}catch(e){console.error("Error setting flight details:",e),F({title:"Error",description:"Failed to set flight details. Please try again or enter manually.",variant:"destructive"})}};return(0,s.jsx)(m.lV,{...P,children:(0,s.jsx)("form",{onSubmit:P.handleSubmit(t=>{e({...t,flightArrivalDetails:L(t.flightArrivalDetails),flightDepartureDetails:L(t.flightDepartureDetails)})}),children:(0,s.jsxs)(c.Zp,{className:"shadow-lg",children:[(0,s.jsxs)(c.aR,{children:[(0,s.jsx)(c.ZB,{className:"text-2xl text-primary",children:C?"Edit Delegation":"Add New Delegation"}),(0,s.jsx)(c.BT,{children:"Fill in the details for the delegation event or trip."})]}),(0,s.jsxs)(c.Wu,{className:"space-y-6",children:[(0,s.jsxs)("section",{className:"space-y-4 p-6 border rounded-lg bg-card",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,s.jsx)(h.A,{className:"mr-2 h-5 w-5 text-accent"}),"Basic Information"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(m.zB,{control:P.control,name:"eventName",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Event Name"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., Annual Summit 2024",...e})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"location",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Location"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., New York, USA",...e})}),(0,s.jsx)(m.C5,{})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(m.zB,{control:P.control,name:"durationFrom",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Duration From"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{type:"date",...e})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"durationTo",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Duration To"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{type:"date",...e})}),(0,s.jsx)(m.C5,{})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(m.zB,{control:P.control,name:"invitationFrom",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Invitation From (Optional)"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., Global Corp",...e})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"invitationTo",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Invitation To (Optional)"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., Our CEO",...e})}),(0,s.jsx)(m.C5,{})]})})]}),(0,s.jsx)(m.zB,{control:P.control,name:"status",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Status"}),(0,s.jsxs)(x.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,s.jsx)(m.MJ,{children:(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{placeholder:"Select status"})})}),(0,s.jsx)(x.gC,{children:i.Qw.options.map(e=>(0,s.jsx)(x.eb,{value:e,children:(0,ea.fZ)(e)},e))})]}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"imageUrl",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Image URL (Optional)"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"https://example.com/image.jpg",...e})}),(0,s.jsx)(m.C5,{})]})})]}),(0,s.jsxs)("section",{className:"space-y-4 p-6 border rounded-lg bg-card",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,s.jsx)(u.A,{className:"mr-2 h-5 w-5 text-accent"}),"Delegates"]}),J.map((e,t)=>(0,s.jsxs)("div",{className:"p-3 border rounded-md space-y-3 bg-background relative",children:[(0,s.jsx)(m.zB,{control:P.control,name:`delegates.${t}.name`,render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Delegate Name"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"Full Name",...e})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:`delegates.${t}.title`,render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Title/Role"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., CEO, Head of Department",...e})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:`delegates.${t}.notes`,render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Notes (Optional)"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(o.T,{placeholder:"Any specific notes for this delegate",...e})}),(0,s.jsx)(m.C5,{})]})}),J.length>1&&(0,s.jsx)(n.r,{type:"button",actionType:"danger",size:"sm",onClick:()=>O(t),className:"absolute top-2 right-2",icon:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]},e.id)),(0,s.jsx)(n.r,{type:"button",actionType:"secondary",onClick:()=>B({name:"",title:"",notes:""}),icon:(0,s.jsx)(f.A,{className:"h-4 w-4"}),children:"Add Delegate"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)("section",{className:"space-y-4 p-6 border rounded-lg bg-card",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,s.jsx)(j.A,{className:"mr-2 h-5 w-5 text-accent"}),"Arrival Flight (Optional)"]}),(0,s.jsx)(n.r,{type:"button",actionType:"tertiary",size:"sm",onClick:()=>z(!0),icon:(0,s.jsx)(g.A,{className:"h-4 w-4"}),children:"Search Flights"})]}),(0,s.jsx)(m.zB,{control:P.control,name:"flightArrivalDetails.flightNumber",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Flight Number"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., BA245",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"flightArrivalDetails.dateTime",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Date & Time"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{type:"datetime-local",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"flightArrivalDetails.airport",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Airport"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., JFK",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"flightArrivalDetails.terminal",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Terminal (Optional)"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., T4",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"flightArrivalDetails.notes",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Notes (Optional)"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(o.T,{placeholder:"Arrival notes",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})})]}),(0,s.jsxs)("section",{className:"space-y-4 p-6 border rounded-lg bg-card",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,s.jsx)(y.A,{className:"mr-2 h-5 w-5 text-accent"}),"Departure Flight (Optional)"]}),(0,s.jsx)(n.r,{type:"button",actionType:"tertiary",size:"sm",onClick:()=>S(!0),icon:(0,s.jsx)(g.A,{className:"h-4 w-4"}),children:"Search Flights"})]}),(0,s.jsx)(m.zB,{control:P.control,name:"flightDepartureDetails.flightNumber",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Flight Number"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., AF123",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"flightDepartureDetails.dateTime",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Date & Time"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{type:"datetime-local",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"flightDepartureDetails.airport",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Airport"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., LHR",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"flightDepartureDetails.terminal",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Terminal (Optional)"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., T5",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:P.control,name:"flightDepartureDetails.notes",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Notes (Optional)"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(o.T,{placeholder:"Departure notes",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})})]})]}),(0,s.jsx)(et,{isOpen:M,onClose:()=>z(!1),onSelectFlight:e=>$(e,"arrival"),type:"arrival"}),(0,s.jsx)(et,{isOpen:k,onClose:()=>S(!1),onSelectFlight:e=>$(e,"departure"),type:"departure"}),(0,s.jsxs)("section",{className:"space-y-4 p-6 border rounded-lg bg-card",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,s.jsx)(h.A,{className:"mr-2 h-5 w-5 text-accent"}),"General Notes (Optional)"]}),(0,s.jsx)(m.zB,{control:P.control,name:"notes",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Additional Notes for the Delegation"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(o.T,{placeholder:"Any other relevant information...",...e,value:e.value||""})}),(0,s.jsx)(m.C5,{})]})})]})]}),(0,s.jsxs)(c.wL,{className:"flex justify-between gap-2 border-t pt-6",children:[(0,s.jsx)(n.r,{type:"button",actionType:"tertiary",onClick:()=>I.back(),icon:(0,s.jsx)(N.A,{className:"h-4 w-4"}),disabled:R,children:"Cancel"}),(0,s.jsx)(n.r,{type:"submit",actionType:"primary",isLoading:R,loadingText:C?"Saving...":"Creating...",icon:(0,s.jsx)(v.A,{className:"h-4 w-4"}),children:C?"Save Changes":"Create Delegation"})]})]})})})}},68752:(e,t,a)=>{a.d(t,{r:()=>o});var s=a(60687),r=a(43210),l=a.n(r),i=a(29523),n=a(11516),d=a(4780);let o=l().forwardRef(({actionType:e="primary",icon:t,isLoading:a=!1,loadingText:r,className:l,children:o,disabled:c,asChild:m=!1,...x},h)=>{let{variant:u,className:p}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[e];return(0,s.jsx)(i.$,{ref:h,variant:u,className:(0,d.cn)(p,l),disabled:a||c,asChild:m,...x,children:a?(0,s.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4 animate-spin"}),r||o]}):(0,s.jsxs)("span",{className:"inline-flex items-center",children:[" ",t&&(0,s.jsx)("span",{className:"mr-2",children:t}),o]})})});o.displayName="ActionButton"},71669:(e,t,a)=>{a.d(t,{C5:()=>j,MJ:()=>f,eI:()=>u,lR:()=>p,lV:()=>o,zB:()=>m});var s=a(60687),r=a(43210),l=a(8730),i=a(27605),n=a(4780),d=a(80013);let o=i.Op,c=r.createContext({}),m=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(i.xI,{...e})}),x=()=>{let e=r.useContext(c),t=r.useContext(h),{getFieldState:a,formState:s}=(0,i.xW)(),l=a(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...l}},h=r.createContext({}),u=r.forwardRef(({className:e,...t},a)=>{let l=r.useId();return(0,s.jsx)(h.Provider,{value:{id:l},children:(0,s.jsx)("div",{ref:a,className:(0,n.cn)("space-y-2",e),...t})})});u.displayName="FormItem";let p=r.forwardRef(({className:e,...t},a)=>{let{error:r,formItemId:l}=x();return(0,s.jsx)(d.J,{ref:a,className:(0,n.cn)(r&&"text-destructive",e),htmlFor:l,...t})});p.displayName="FormLabel";let f=r.forwardRef(({...e},t)=>{let{error:a,formItemId:r,formDescriptionId:i,formMessageId:n}=x();return(0,s.jsx)(l.DX,{ref:t,id:r,"aria-describedby":a?`${i} ${n}`:`${i}`,"aria-invalid":!!a,...e})});f.displayName="FormControl",r.forwardRef(({className:e,...t},a)=>{let{formDescriptionId:r}=x();return(0,s.jsx)("p",{ref:a,id:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t})}).displayName="FormDescription";let j=r.forwardRef(({className:e,children:t,...a},r)=>{let{error:l,formMessageId:i}=x(),d=l?String(l?.message??""):t;return d?(0,s.jsx)("p",{ref:r,id:i,className:(0,n.cn)("text-sm font-medium text-destructive",e),...a,children:d}):null});j.displayName="FormMessage"}};