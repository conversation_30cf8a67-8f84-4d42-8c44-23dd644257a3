'use client';

import Link from 'next/link';
import Image from 'next/image';
import {
	<PERSON>,
	<PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON>eader,
	CardTitle,
	CardDescription,
} from '@/components/ui/card';
import {ActionButton} from '@/components/ui/action-button';
import type {Employee, Vehicle} from '@/lib/types';
import {
	ArrowRight,
	UserCircle2,
	Briefcase,
	Building,
	Mail,
	Phone,
	ShieldCheck,
	MapPin,
	Car,
	Wrench,
} from 'lucide-react';
import {Badge} from '@/components/ui/badge';
import {Separator} from '@/components/ui/separator';
import {cn} from '@/lib/utils';
import {format, parseISO} from 'date-fns';
import {useEffect, useState} from 'react';

interface EmployeeCardProps {
	employee: Employee;
}

const getStatusColor = (status: Employee['status']) => {
	switch (status) {
		case 'Active':
			return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
		case 'On Leave':
			return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
		case 'Terminated':
			return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
		default:
			return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
	}
};

const getAvailabilityColor = (availability: Employee['availability']) => {
	if (!availability) return '';
	switch (availability) {
		case 'On_Shift':
			return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
		case 'Off_Shift':
			return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
		case 'On_Break':
			return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
		case 'Busy':
			return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
		default:
			return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
	}
};

export default function EmployeeCard({employee}: EmployeeCardProps) {
	const hasEnrichedVehicleData = 'assignedVehicleDetails' in employee;

	return (
		<Card className='overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60'>
			<CardHeader className='p-5'>
				<div className='flex justify-between items-start'>
					<div className='flex items-center gap-3'>
						<div className='relative w-12 h-12 rounded-full overflow-hidden bg-muted flex items-center justify-center ring-2 ring-primary/30'>
							{employee.profileImageUrl ? (
								<Image
									src={employee.profileImageUrl}
									alt={employee.fullName || employee.name}
									layout='fill'
									objectFit='cover'
									data-ai-hint='employee photo'
								/>
							) : (
								<UserCircle2 className='h-8 w-8 text-muted-foreground' />
							)}
						</div>
						<div>
							<CardTitle className='text-xl font-semibold text-primary'>
								{employee.fullName || employee.name}
							</CardTitle>
							<CardDescription className='text-sm text-muted-foreground'>
								{employee.position} (
								{employee.role
									? employee.role.charAt(0).toUpperCase() +
									  employee.role.slice(1).replace('_', ' ')
									: 'N/A'}
								)
							</CardDescription>
						</div>
					</div>
					<div className='flex flex-col items-end gap-1'>
						<Badge
							className={cn(
								'text-xs py-1 px-2 font-semibold',
								getStatusColor(employee.status)
							)}>
							{employee.status}
						</Badge>
						{employee.role === 'driver' && employee.availability && (
							<Badge
								className={cn(
									'text-xs py-1 px-2 font-semibold',
									getAvailabilityColor(employee.availability)
								)}>
								{employee.availability?.replace('_', ' ')}
							</Badge>
						)}
					</div>
				</div>
			</CardHeader>
			<CardContent className='p-5 flex-grow flex flex-col'>
				<Separator className='my-3 bg-border/50' />
				<div className='space-y-2.5 text-sm text-foreground flex-grow'>
					<div className='flex items-center'>
						<Building className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
						<div>
							<span className='text-muted-foreground'>Department: </span>
							<strong className='font-semibold'>{employee.department}</strong>
						</div>
					</div>
					<div className='flex items-center'>
						<Mail className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
						<div>
							<span className='text-muted-foreground'>Email: </span>
							<strong className='font-semibold'>{employee.contactEmail}</strong>
						</div>
					</div>
					{employee.contactMobile && (
						<div className='flex items-center'>
							<Phone className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
							<div>
								<span className='text-muted-foreground'>Mobile: </span>
								<strong className='font-semibold'>
									{employee.contactMobile}
								</strong>
							</div>
						</div>
					)}
					<div className='flex items-center'>
						<ShieldCheck className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
						<div>
							<span className='text-muted-foreground'>Hire Date: </span>
							<strong className='font-semibold'>
								{format(parseISO(employee.hireDate), 'MMM d, yyyy')}
							</strong>
						</div>
					</div>

					{employee.role === 'driver' && (
						<>
							{employee.currentLocation && (
								<div className='flex items-center'>
									<MapPin className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
									<div>
										<span className='text-muted-foreground'>Location: </span>
										<strong className='font-semibold'>
											{employee.currentLocation}
										</strong>
									</div>
								</div>
							)}
							{employee.assignedVehicleId && (
								<div className='flex items-center'>
									<Car className='mr-2.5 h-4 w-4 text-accent flex-shrink-0' />
									<div>
										<span className='text-muted-foreground'>Vehicle: </span>
										<strong className='font-semibold'>
											{hasEnrichedVehicleData &&
											employee.assignedVehicleDetails ? (
												<>
													{employee.assignedVehicleDetails.make}{' '}
													{employee.assignedVehicleDetails.model} (
													{employee.assignedVehicleDetails.year})
													{employee.assignedVehicleDetails.licensePlate && (
														<span className='ml-1 text-xs text-muted-foreground'>
															[{employee.assignedVehicleDetails.licensePlate}]
														</span>
													)}
													{employee.assignedVehicleDetails.color && (
														<span className='ml-1 text-xs text-muted-foreground'>
															• {employee.assignedVehicleDetails.color}
														</span>
													)}
												</>
											) : (
												<>Vehicle ID: {employee.assignedVehicleId}</>
											)}
										</strong>
									</div>
								</div>
							)}
						</>
					)}
					{employee.skills && employee.skills.length > 0 && (
						<div className='flex items-start pt-1'>
							<Wrench className='mr-2.5 h-4 w-4 text-accent flex-shrink-0 mt-0.5' />
							<div>
								<span className='text-muted-foreground'>Skills: </span>
								<p className='text-xs font-semibold leading-tight'>
									{employee.skills.join(', ')}
								</p>
							</div>
						</div>
					)}
				</div>
			</CardContent>
			<CardFooter className='p-4 border-t border-border/60 bg-muted/20'>
				<ActionButton
					actionType='tertiary'
					className='w-full'
					icon={<ArrowRight className='h-4 w-4' />}
					asChild>
					<Link href={`/employees/${employee.id}`}>View Details</Link>
				</ActionButton>
			</CardFooter>
		</Card>
	);
}
