'use client';

import React, {useState} from 'react';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {Progress} from '@/components/ui/progress';
import {RefreshCw} from 'lucide-react';
import {ActionButton} from '@/components/ui/action-button';
import {
	PerformanceMetrics,
	getPerformanceMetrics,
	getMockPerformanceMetrics,
} from '@/lib/adminService';
import {Skeleton} from '@/components/ui/skeleton';
import {useApi} from '@/hooks/useApi';
import {LoadingError} from '@/components/ui/loading-states';
import {ErrorBoundary} from '@/components/ui/error-boundary';

/**
 * Component that displays database health metrics
 */
export function HealthMetrics() {
	const {
		data: metrics,
		isLoading,
		error,
		refetch,
	} = useApi<PerformanceMetrics>(() => getPerformanceMetrics(), {
		maxRetries: 3,
		initialDelay: 500,
		deps: [Math.floor(Date.now() / 60000)], // Refresh every 60 seconds
	});

	const [refreshing, setRefreshing] = useState(false);

	const handleRefresh = async () => {
		setRefreshing(true);
		await refetch();
		setRefreshing(false);
	};

	const getProgressColor = (value: number) => {
		if (value >= 90) return 'bg-green-500';
		if (value >= 70) return 'bg-yellow-500';
		return 'bg-red-500';
	};

	return (
		<ErrorBoundary>
			<Card className='shadow-md'>
				<CardHeader className='pb-2 p-5'>
					<CardTitle className='text-xl font-semibold text-primary'>
						Database Health
					</CardTitle>
					<CardDescription>
						Performance metrics and health indicators
					</CardDescription>
				</CardHeader>
				<CardContent className='p-5'>
					{isLoading || refreshing ? (
						<div className='space-y-3'>
							<Skeleton className='h-6 w-full' />
							<Skeleton className='h-6 w-full' />
							<Skeleton className='h-6 w-full' />
							<Skeleton className='h-6 w-full' />
						</div>
					) : error ? (
						<LoadingError message={error} onRetry={refetch} />
					) : metrics ? (
						<div className='space-y-4'>
							<div>
								<div className='flex justify-between mb-1'>
									<span className='text-sm font-medium'>
										Index Cache Hit Rate
									</span>
									<span className='text-sm font-medium'>
										{metrics.cacheHitRate.indexHitRate.toFixed(1)}%
									</span>
								</div>
								<Progress
									value={metrics.cacheHitRate.indexHitRate}
									className={getProgressColor(
										metrics.cacheHitRate.indexHitRate
									)}
								/>
								<p className='text-xs text-muted-foreground mt-1'>
									Percentage of index lookups served from cache
								</p>
							</div>

							<div>
								<div className='flex justify-between mb-1'>
									<span className='text-sm font-medium'>
										Table Cache Hit Rate
									</span>
									<span className='text-sm font-medium'>
										{metrics.cacheHitRate.tableHitRate.toFixed(1)}%
									</span>
								</div>
								<Progress
									value={metrics.cacheHitRate.tableHitRate}
									className={getProgressColor(
										metrics.cacheHitRate.tableHitRate
									)}
								/>
								<p className='text-xs text-muted-foreground mt-1'>
									Percentage of table lookups served from cache
								</p>
							</div>

							<div className='grid grid-cols-2 gap-4 pt-2'>
								<div className='border rounded-md p-3'>
									<div className='text-2xl font-bold'>
										{metrics.connectionCount}
									</div>
									<div className='text-xs text-muted-foreground'>
										Active Connections
									</div>
								</div>

								<div className='border rounded-md p-3'>
									<div className='text-2xl font-bold'>
										{metrics.activeQueries}
									</div>
									<div className='text-xs text-muted-foreground'>
										Running Queries
									</div>
								</div>

								<div className='border rounded-md p-3 col-span-2'>
									<div className='text-2xl font-bold'>
										{metrics.avgQueryTime.toFixed(2)}ms
									</div>
									<div className='text-xs text-muted-foreground'>
										Average Query Time
									</div>
								</div>
							</div>

							{metrics.timestamp && (
								<div className='text-xs text-muted-foreground'>
									Last updated: {new Date(metrics.timestamp).toLocaleString()}
								</div>
							)}
						</div>
					) : null}
				</CardContent>
				<CardFooter className='p-5'>
					<ActionButton
						actionType='tertiary'
						size='sm'
						className='w-full'
						onClick={handleRefresh}
						isLoading={refreshing || isLoading}
						loadingText='Refreshing...'
						icon={<RefreshCw className='h-4 w-4' />}>
						Refresh Metrics
					</ActionButton>
				</CardFooter>
			</Card>
		</ErrorBoundary>
	);
}
