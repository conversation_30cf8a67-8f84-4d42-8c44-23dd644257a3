"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5267],{9449:(e,n,r)=>{r.d(n,{H_:()=>np,UC:()=>ni,YJ:()=>ns,q7:()=>nc,VF:()=>nv,JU:()=>nd,ZL:()=>nl,z6:()=>nf,hN:()=>nm,bL:()=>na,wv:()=>ng,Pb:()=>nh,G5:()=>nx,ZP:()=>nw,l9:()=>nu});var t=r(12115),o=r(85185),a=r(6101),u=r(46081),l=r(5845),i=r(63655),s=r(37328),d=r(94315),c=r(19178),p=r(92293),f=r(25519),m=r(61285),v=r(35152),g=r(34378),h=r(28905),w=r(39033),x=r(95155),y="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},C="RovingFocusGroup",[M,R,j]=(0,s.N)(C),[D,N]=(0,u.A)(C,[j]),[_,I]=D(C),k=t.forwardRef((e,n)=>(0,x.jsx)(M.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(M.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(T,{...e,ref:n})})}));k.displayName=C;var T=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,orientation:u,loop:s=!1,dir:c,currentTabStopId:p,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:m,onEntryFocus:v,preventScrollOnEntryFocus:g=!1,...h}=e,M=t.useRef(null),j=(0,a.s)(n,M),D=(0,d.jH)(c),[N,I]=(0,l.i)({prop:p,defaultProp:null!=f?f:null,onChange:m,caller:C}),[k,T]=t.useState(!1),E=(0,w.c)(v),P=R(r),O=t.useRef(!1),[S,F]=t.useState(0);return t.useEffect(()=>{let e=M.current;if(e)return e.addEventListener(y,E),()=>e.removeEventListener(y,E)},[E]),(0,x.jsx)(_,{scope:r,orientation:u,dir:D,loop:s,currentTabStopId:N,onItemFocus:t.useCallback(e=>I(e),[I]),onItemShiftTab:t.useCallback(()=>T(!0),[]),onFocusableItemAdd:t.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>F(e=>e-1),[]),children:(0,x.jsx)(i.sG.div,{tabIndex:k||0===S?-1:0,"data-orientation":u,...h,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let n=!O.current;if(e.target===e.currentTarget&&n&&!k){let n=new CustomEvent(y,b);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){let e=P().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===N),...e].filter(Boolean).map(e=>e.ref.current),g)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>T(!1))})})}),E="RovingFocusGroupItem",P=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:u=!1,tabStopId:l,children:s,...d}=e,c=(0,m.B)(),p=l||c,f=I(E,r),v=f.currentTabStopId===p,g=R(r),{onFocusableItemAdd:h,onFocusableItemRemove:w,currentTabStopId:y}=f;return t.useEffect(()=>{if(a)return h(),()=>w()},[a,h,w]),(0,x.jsx)(M.ItemSlot,{scope:r,id:p,focusable:a,active:u,children:(0,x.jsx)(i.sG.span,{tabIndex:v?0:-1,"data-orientation":f.orientation,...d,ref:n,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?f.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>f.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let n=function(e,n,r){var t;let o=(t=e.key,"rtl"!==r?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===n&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===n&&["ArrowUp","ArrowDown"].includes(o)))return O[o]}(e,f.orientation,f.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)r.reverse();else if("prev"===n||"next"===n){"prev"===n&&r.reverse();let t=r.indexOf(e.currentTarget);r=f.loop?function(e,n){return e.map((r,t)=>e[(n+t)%e.length])}(r,t+1):r.slice(t+1)}setTimeout(()=>A(r))}}),children:"function"==typeof s?s({isCurrentTabStop:v,hasTabStop:null!=y}):s})})});P.displayName=E;var O={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let t of e)if(t===r||(t.focus({preventScroll:n}),document.activeElement!==r))return}var S=r(99708),F=r(38168),L=r(93795),K=["Enter"," "],U=["ArrowUp","PageDown","End"],G=["ArrowDown","PageUp","Home",...U],B={ltr:[...K,"ArrowRight"],rtl:[...K,"ArrowLeft"]},V={ltr:["ArrowLeft"],rtl:["ArrowRight"]},W="Menu",[H,X,q]=(0,s.N)(W),[z,Z]=(0,u.A)(W,[q,v.Bk,N]),Y=(0,v.Bk)(),J=N(),[Q,$]=z(W),[ee,en]=z(W),er=e=>{let{__scopeMenu:n,open:r=!1,children:o,dir:a,onOpenChange:u,modal:l=!0}=e,i=Y(n),[s,c]=t.useState(null),p=t.useRef(!1),f=(0,w.c)(u),m=(0,d.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,x.jsx)(v.bL,{...i,children:(0,x.jsx)(Q,{scope:n,open:r,onOpenChange:f,content:s,onContentChange:c,children:(0,x.jsx)(ee,{scope:n,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:m,modal:l,children:o})})})};er.displayName=W;var et=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=Y(r);return(0,x.jsx)(v.Mz,{...o,...t,ref:n})});et.displayName="MenuAnchor";var eo="MenuPortal",[ea,eu]=z(eo,{forceMount:void 0}),el=e=>{let{__scopeMenu:n,forceMount:r,children:t,container:o}=e,a=$(eo,n);return(0,x.jsx)(ea,{scope:n,forceMount:r,children:(0,x.jsx)(h.C,{present:r||a.open,children:(0,x.jsx)(g.Z,{asChild:!0,container:o,children:t})})})};el.displayName=eo;var ei="MenuContent",[es,ed]=z(ei),ec=t.forwardRef((e,n)=>{let r=eu(ei,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=$(ei,e.__scopeMenu),u=en(ei,e.__scopeMenu);return(0,x.jsx)(H.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(h.C,{present:t||a.open,children:(0,x.jsx)(H.Slot,{scope:e.__scopeMenu,children:u.modal?(0,x.jsx)(ep,{...o,ref:n}):(0,x.jsx)(ef,{...o,ref:n})})})})}),ep=t.forwardRef((e,n)=>{let r=$(ei,e.__scopeMenu),u=t.useRef(null),l=(0,a.s)(n,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,F.Eq)(e)},[]),(0,x.jsx)(ev,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),ef=t.forwardRef((e,n)=>{let r=$(ei,e.__scopeMenu);return(0,x.jsx)(ev,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),em=(0,S.TL)("MenuContent.ScrollLock"),ev=t.forwardRef((e,n)=>{let{__scopeMenu:r,loop:u=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:w,onInteractOutside:y,onDismiss:b,disableOutsideScroll:C,...M}=e,R=$(ei,r),j=en(ei,r),D=Y(r),N=J(r),_=X(r),[I,T]=t.useState(null),E=t.useRef(null),P=(0,a.s)(n,E,R.onContentChange),O=t.useRef(0),A=t.useRef(""),S=t.useRef(0),F=t.useRef(null),K=t.useRef("right"),B=t.useRef(0),V=C?L.A:t.Fragment,W=e=>{var n,r;let t=A.current+e,o=_().filter(e=>!e.disabled),a=document.activeElement,u=null==(n=o.find(e=>e.ref.current===a))?void 0:n.textValue,l=function(e,n,r){var t;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,a=r?e.indexOf(r):-1,u=(t=Math.max(a,0),e.map((n,r)=>e[(t+r)%e.length]));1===o.length&&(u=u.filter(e=>e!==r));let l=u.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(o.map(e=>e.textValue),t,u),i=null==(r=o.find(e=>e.textValue===l))?void 0:r.ref.current;!function e(n){A.current=n,window.clearTimeout(O.current),""!==n&&(O.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(O.current),[]),(0,p.Oh)();let H=t.useCallback(e=>{var n,r;return K.current===(null==(n=F.current)?void 0:n.side)&&function(e,n){return!!n&&function(e,n){let{x:r,y:t}=e,o=!1;for(let e=0,a=n.length-1;e<n.length;a=e++){let u=n[e],l=n[a],i=u.x,s=u.y,d=l.x,c=l.y;s>t!=c>t&&r<(d-i)*(t-s)/(c-s)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},n)}(e,null==(r=F.current)?void 0:r.area)},[]);return(0,x.jsx)(es,{scope:r,searchRef:A,onItemEnter:t.useCallback(e=>{H(e)&&e.preventDefault()},[H]),onItemLeave:t.useCallback(e=>{var n;H(e)||(null==(n=E.current)||n.focus(),T(null))},[H]),onTriggerLeave:t.useCallback(e=>{H(e)&&e.preventDefault()},[H]),pointerGraceTimerRef:S,onPointerGraceIntentChange:t.useCallback(e=>{F.current=e},[]),children:(0,x.jsx)(V,{...C?{as:em,allowPinchZoom:!0}:void 0,children:(0,x.jsx)(f.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(i,e=>{var n;e.preventDefault(),null==(n=E.current)||n.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,x.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:w,onInteractOutside:y,onDismiss:b,children:(0,x.jsx)(k,{asChild:!0,...N,dir:j.dir,orientation:"vertical",loop:u,currentTabStopId:I,onCurrentTabStopIdChange:T,onEntryFocus:(0,o.m)(m,e=>{j.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,x.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":eV(R.open),"data-radix-menu-content":"",dir:j.dir,...D,...M,ref:P,style:{outline:"none",...M.style},onKeyDown:(0,o.m)(M.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&W(e.key));let o=E.current;if(e.target!==o||!G.includes(e.key))return;e.preventDefault();let a=_().filter(e=>!e.disabled).map(e=>e.ref.current);U.includes(e.key)&&a.reverse(),function(e){let n=document.activeElement;for(let r of e)if(r===n||(r.focus(),document.activeElement!==n))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(O.current),A.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eX(e=>{let n=e.target,r=B.current!==e.clientX;e.currentTarget.contains(n)&&r&&(K.current=e.clientX>B.current?"right":"left",B.current=e.clientX)}))})})})})})})});ec.displayName=ei;var eg=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,x.jsx)(i.sG.div,{role:"group",...t,ref:n})});eg.displayName="MenuGroup";var eh=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,x.jsx)(i.sG.div,{...t,ref:n})});eh.displayName="MenuLabel";var ew="MenuItem",ex="menu.itemSelect",ey=t.forwardRef((e,n)=>{let{disabled:r=!1,onSelect:u,...l}=e,s=t.useRef(null),d=en(ew,e.__scopeMenu),c=ed(ew,e.__scopeMenu),p=(0,a.s)(n,s),f=t.useRef(!1);return(0,x.jsx)(eb,{...l,ref:p,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!r&&e){let n=new CustomEvent(ex,{bubbles:!0,cancelable:!0});e.addEventListener(ex,e=>null==u?void 0:u(e),{once:!0}),(0,i.hO)(e,n),n.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:n=>{var r;null==(r=e.onPointerDown)||r.call(e,n),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var n;f.current||null==(n=e.currentTarget)||n.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=""!==c.searchRef.current;r||n&&" "===e.key||K.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ey.displayName=ew;var eb=t.forwardRef((e,n)=>{let{__scopeMenu:r,disabled:u=!1,textValue:l,...s}=e,d=ed(ew,r),c=J(r),p=t.useRef(null),f=(0,a.s)(n,p),[m,v]=t.useState(!1),[g,h]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var n;h((null!=(n=e.textContent)?n:"").trim())}},[s.children]),(0,x.jsx)(H.ItemSlot,{scope:r,disabled:u,textValue:null!=l?l:g,children:(0,x.jsx)(P,{asChild:!0,...c,focusable:!u,children:(0,x.jsx)(i.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eX(e=>{u?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eX(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),eC=t.forwardRef((e,n)=>{let{checked:r=!1,onCheckedChange:t,...a}=e;return(0,x.jsx)(ek,{scope:e.__scopeMenu,checked:r,children:(0,x.jsx)(ey,{role:"menuitemcheckbox","aria-checked":eW(r)?"mixed":r,...a,ref:n,"data-state":eH(r),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!eW(r)||!r),{checkForDefaultPrevented:!1})})})});eC.displayName="MenuCheckboxItem";var eM="MenuRadioGroup",[eR,ej]=z(eM,{value:void 0,onValueChange:()=>{}}),eD=t.forwardRef((e,n)=>{let{value:r,onValueChange:t,...o}=e,a=(0,w.c)(t);return(0,x.jsx)(eR,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,x.jsx)(eg,{...o,ref:n})})});eD.displayName=eM;var eN="MenuRadioItem",e_=t.forwardRef((e,n)=>{let{value:r,...t}=e,a=ej(eN,e.__scopeMenu),u=r===a.value;return(0,x.jsx)(ek,{scope:e.__scopeMenu,checked:u,children:(0,x.jsx)(ey,{role:"menuitemradio","aria-checked":u,...t,ref:n,"data-state":eH(u),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});e_.displayName=eN;var eI="MenuItemIndicator",[ek,eT]=z(eI,{checked:!1}),eE=t.forwardRef((e,n)=>{let{__scopeMenu:r,forceMount:t,...o}=e,a=eT(eI,r);return(0,x.jsx)(h.C,{present:t||eW(a.checked)||!0===a.checked,children:(0,x.jsx)(i.sG.span,{...o,ref:n,"data-state":eH(a.checked)})})});eE.displayName=eI;var eP=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,x.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});eP.displayName="MenuSeparator";var eO=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=Y(r);return(0,x.jsx)(v.i3,{...o,...t,ref:n})});eO.displayName="MenuArrow";var eA="MenuSub",[eS,eF]=z(eA),eL=e=>{let{__scopeMenu:n,children:r,open:o=!1,onOpenChange:a}=e,u=$(eA,n),l=Y(n),[i,s]=t.useState(null),[d,c]=t.useState(null),p=(0,w.c)(a);return t.useEffect(()=>(!1===u.open&&p(!1),()=>p(!1)),[u.open,p]),(0,x.jsx)(v.bL,{...l,children:(0,x.jsx)(Q,{scope:n,open:o,onOpenChange:p,content:d,onContentChange:c,children:(0,x.jsx)(eS,{scope:n,contentId:(0,m.B)(),triggerId:(0,m.B)(),trigger:i,onTriggerChange:s,children:r})})})};eL.displayName=eA;var eK="MenuSubTrigger",eU=t.forwardRef((e,n)=>{let r=$(eK,e.__scopeMenu),u=en(eK,e.__scopeMenu),l=eF(eK,e.__scopeMenu),i=ed(eK,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,x.jsx)(et,{asChild:!0,...p,children:(0,x.jsx)(eb,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":eV(r.open),...e,ref:(0,a.t)(n,l.onTriggerChange),onClick:n=>{var t;null==(t=e.onClick)||t.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eX(n=>{i.onItemEnter(n),!n.defaultPrevented&&(e.disabled||r.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eX(e=>{var n,t;f();let o=null==(n=r.content)?void 0:n.getBoundingClientRect();if(o){let n=null==(t=r.content)?void 0:t.dataset.side,a="right"===n,u=o[a?"left":"right"],l=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:u,y:o.bottom}],side:n}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,n=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==n.key)&&B[u.dir].includes(n.key)){var o;r.onOpenChange(!0),null==(o=r.content)||o.focus(),n.preventDefault()}})})})});eU.displayName=eK;var eG="MenuSubContent",eB=t.forwardRef((e,n)=>{let r=eu(ei,e.__scopeMenu),{forceMount:u=r.forceMount,...l}=e,i=$(ei,e.__scopeMenu),s=en(ei,e.__scopeMenu),d=eF(eG,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(n,c);return(0,x.jsx)(H.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(h.C,{present:u||i.open,children:(0,x.jsx)(H.Slot,{scope:e.__scopeMenu,children:(0,x.jsx)(ev,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:p,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;s.isUsingKeyboardRef.current&&(null==(n=c.current)||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),r=V[s.dir].includes(e.key);if(n&&r){var t;i.onOpenChange(!1),null==(t=d.trigger)||t.focus(),e.preventDefault()}})})})})})});function eV(e){return e?"open":"closed"}function eW(e){return"indeterminate"===e}function eH(e){return eW(e)?"indeterminate":e?"checked":"unchecked"}function eX(e){return n=>"mouse"===n.pointerType?e(n):void 0}eB.displayName=eG;var eq="DropdownMenu",[ez,eZ]=(0,u.A)(eq,[Z]),eY=Z(),[eJ,eQ]=ez(eq),e$=e=>{let{__scopeDropdownMenu:n,children:r,dir:o,open:a,defaultOpen:u,onOpenChange:i,modal:s=!0}=e,d=eY(n),c=t.useRef(null),[p,f]=(0,l.i)({prop:a,defaultProp:null!=u&&u,onChange:i,caller:eq});return(0,x.jsx)(eJ,{scope:n,triggerId:(0,m.B)(),triggerRef:c,contentId:(0,m.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,x.jsx)(er,{...d,open:p,onOpenChange:f,dir:o,modal:s,children:r})})};e$.displayName=eq;var e0="DropdownMenuTrigger",e1=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,disabled:t=!1,...u}=e,l=eQ(e0,r),s=eY(r);return(0,x.jsx)(et,{asChild:!0,...s,children:(0,x.jsx)(i.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.t)(n,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e1.displayName=e0;var e5=e=>{let{__scopeDropdownMenu:n,...r}=e,t=eY(n);return(0,x.jsx)(el,{...t,...r})};e5.displayName="DropdownMenuPortal";var e3="DropdownMenuContent",e9=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...a}=e,u=eQ(e3,r),l=eY(r),i=t.useRef(!1);return(0,x.jsx)(ec,{id:u.contentId,"aria-labelledby":u.triggerId,...l,...a,ref:n,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;i.current||null==(n=u.triggerRef.current)||n.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let n=e.detail.originalEvent,r=0===n.button&&!0===n.ctrlKey,t=2===n.button||r;(!u.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e9.displayName=e3;var e2=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(eg,{...o,...t,ref:n})});e2.displayName="DropdownMenuGroup";var e8=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(eh,{...o,...t,ref:n})});e8.displayName="DropdownMenuLabel";var e6=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(ey,{...o,...t,ref:n})});e6.displayName="DropdownMenuItem";var e7=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(eC,{...o,...t,ref:n})});e7.displayName="DropdownMenuCheckboxItem";var e4=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(eD,{...o,...t,ref:n})});e4.displayName="DropdownMenuRadioGroup";var ne=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(e_,{...o,...t,ref:n})});ne.displayName="DropdownMenuRadioItem";var nn=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(eE,{...o,...t,ref:n})});nn.displayName="DropdownMenuItemIndicator";var nr=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(eP,{...o,...t,ref:n})});nr.displayName="DropdownMenuSeparator",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(eO,{...o,...t,ref:n})}).displayName="DropdownMenuArrow";var nt=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(eU,{...o,...t,ref:n})});nt.displayName="DropdownMenuSubTrigger";var no=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eY(r);return(0,x.jsx)(eB,{...o,...t,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});no.displayName="DropdownMenuSubContent";var na=e$,nu=e1,nl=e5,ni=e9,ns=e2,nd=e8,nc=e6,np=e7,nf=e4,nm=ne,nv=nn,ng=nr,nh=e=>{let{__scopeDropdownMenu:n,children:r,open:t,onOpenChange:o,defaultOpen:a}=e,u=eY(n),[i,s]=(0,l.i)({prop:t,defaultProp:null!=a&&a,onChange:o,caller:"DropdownMenuSub"});return(0,x.jsx)(eL,{...u,open:i,onOpenChange:s,children:r})},nw=nt,nx=no},28905:(e,n,r)=>{r.d(n,{C:()=>u});var t=r(12115),o=r(6101),a=r(52712),u=e=>{let{present:n,children:r}=e,u=function(e){var n,r;let[o,u]=t.useState(),i=t.useRef(null),s=t.useRef(e),d=t.useRef("none"),[c,p]=(n=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,n)=>{let t=r[e][n];return null!=t?t:e},n));return t.useEffect(()=>{let e=l(i.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let n=i.current,r=s.current;if(r!==e){let t=d.current,o=l(n);e?p("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?p("UNMOUNT"):r&&t!==o?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,a.N)(()=>{if(o){var e;let n,r=null!=(e=o.ownerDocument.defaultView)?e:window,t=e=>{let t=l(i.current).includes(e.animationName);if(e.target===o&&t&&(p("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=l(i.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{r.clearTimeout(n),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:t.useCallback(e=>{i.current=e?getComputedStyle(e):null,u(e)},[])}}(n),i="function"==typeof r?r({present:u.isPresent}):t.Children.only(r),s=(0,o.s)(u.ref,function(e){var n,r;let t=null==(n=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:n.get,o=t&&"isReactWarning"in t&&t.isReactWarning;return o?e.ref:(o=(t=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||u.isPresent?t.cloneElement(i,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}u.displayName="Presence"},70154:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(40157).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},73158:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(40157).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}}]);