'use client';

import React, {useEffect, useState, useCallback} from 'react';
import {Employee} from '@/lib/types';
import {useApiMethods} from '@/hooks/useAuthenticatedApi';
import {useSocketRefresh} from '@/hooks/useSocketRefresh';
import {SOCKET_EVENTS} from '@/hooks/useSocket';

interface EmployeeListContainerProps {
	children: (data: {
		employees: Employee[];
		loading: boolean;
		error: string | null;
		handleDelete: (id: number) => Promise<void>;
		fetchEmployees: () => Promise<void>;
		isRefreshing: boolean;
		isConnected: boolean;
		socketTriggered: boolean;
	}) => React.ReactNode;
}

const EmployeeListContainer: React.FC<EmployeeListContainerProps> = ({
	children,
}) => {
	const [employees, setEmployees] = useState<Employee[]>([]);
	const [loading, setLoading] = useState<boolean>(true);
	const [error, setError] = useState<string | null>(null);

	// Use authenticated API methods
	const {
		getEmployees,
		deleteEmployee,
		isAuthenticated,
		isLoading: authLoading,
	} = useApiMethods();

	const fetchEmployees = useCallback(async () => {
		// Don't fetch if not authenticated or still loading auth
		if (!isAuthenticated || authLoading) {
			setLoading(false);
			return;
		}

		setLoading(true);

		try {
			console.info('Fetching employees with authentication');
			const data = await getEmployees();

			// Transform the data to match the Employee interface
			const transformedEmployees = Array.isArray(data)
				? data.map((e: any) => ({
						...e,
						id: Number(e.id),
						// Map assignedVehicleId from backend to vehicleId for frontend
						vehicleId: e.assignedVehicleId ? String(e.assignedVehicleId) : null,
						assignedTasks: Array.isArray(e.assignedTasks)
							? e.assignedTasks
							: [],
						statusHistory: Array.isArray(e.statusHistory)
							? e.statusHistory
							: [],
						createdAt: e.createdAt || new Date(0).toISOString(),
						updatedAt: e.updatedAt || new Date(0).toISOString(),
				  }))
				: [];

			setEmployees(transformedEmployees);
			setError(null);
		} catch (err) {
			console.error('Error fetching employees:', err);
			setError('Failed to fetch employees. Please try again.');
		} finally {
			setLoading(false);
		}
	}, [getEmployees, isAuthenticated, authLoading]);

	// Set up socket refresh for employees
	const {
		isRefreshing,
		refresh: refreshData,
		isConnected,
		socketTriggered,
	} = useSocketRefresh(
		fetchEmployees,
		[
			SOCKET_EVENTS.EMPLOYEE_CREATED,
			SOCKET_EVENTS.EMPLOYEE_UPDATED,
			SOCKET_EVENTS.EMPLOYEE_DELETED,
			SOCKET_EVENTS.REFRESH_EMPLOYEES,
		],
		{
			interval: 30000,
			enabled: true,
			immediate: true,
			enableSocket: true,
			enablePolling: true,
			onError: (error) => {
				console.error('Socket refresh error:', error);
			},
		}
	);

	// Initial data fetch on mount
	useEffect(() => {
		if (!socketTriggered) {
			fetchEmployees();
		}
	}, [fetchEmployees, socketTriggered]);

	const handleDelete = async (id: number) => {
		try {
			await deleteEmployee(id);
			setEmployees((prevEmployees) =>
				prevEmployees.filter((employee) => employee.id !== id)
			);
		} catch (err: any) {
			console.error('Error deleting employee:', err);
			setError(
				'Failed to delete employee: ' + (err.message || 'Unknown error')
			);
			throw err;
		}
	};

	return (
		<>
			{children({
				employees,
				loading,
				error,
				handleDelete,
				fetchEmployees: refreshData,
				isRefreshing,
				isConnected,
				socketTriggered,
			})}
		</>
	);
};

export default EmployeeListContainer;
