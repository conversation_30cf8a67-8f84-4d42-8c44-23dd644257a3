'use client';

import React, {useEffect, useState, useCallback} from 'react';
import {Employee} from '@/lib/types';
import {
	getEmployees,
	getEnrichedEmployees,
	deleteEmployee as deleteEmployeeFromStore,
} from '@/lib/store';
import {useSocketRefresh} from '@/hooks/useSocketRefresh';
import {SOCKET_EVENTS} from '@/hooks/useSocket';

interface EmployeeListContainerProps {
	children: (data: {
		employees: Employee[];
		loading: boolean;
		error: string | null;
		handleDelete: (id: number) => Promise<void>;
		fetchEmployees: () => Promise<void>;
		isRefreshing: boolean;
		isConnected: boolean;
		socketTriggered: boolean;
	}) => React.ReactNode;
}

const EmployeeListContainer: React.FC<EmployeeListContainerProps> = ({
	children,
}) => {
	const [employees, setEmployees] = useState<Employee[]>([]);
	const [loading, setLoading] = useState<boolean>(true);
	const [error, setError] = useState<string | null>(null);

	const fetchEmployees = useCallback(async () => {
		if (loading) {
			setLoading(true);
		}

		try {
			// Try to get enriched employees first
			try {
				console.info('Fetching enriched employees');
				const enrichedData = await getEnrichedEmployees();
				setEmployees(enrichedData);
				setError(null);
				return;
			} catch (enrichedError) {
				console.warn(
					'Failed to fetch enriched employees, falling back to regular endpoint:',
					enrichedError
				);
				// Fall back to regular employees endpoint
				const data = await getEmployees();
				setEmployees(data);
				setError(null);
			}
		} catch (err) {
			console.error('Error fetching employees:', err);
			setError('Failed to fetch employees. Please try again.');
		} finally {
			setLoading(false);
		}
	}, [loading]);

	// Set up socket refresh for employees
	const {
		isRefreshing,
		refresh: refreshData,
		isConnected,
		socketTriggered,
	} = useSocketRefresh(
		fetchEmployees,
		[
			SOCKET_EVENTS.EMPLOYEE_CREATED,
			SOCKET_EVENTS.EMPLOYEE_UPDATED,
			SOCKET_EVENTS.EMPLOYEE_DELETED,
			SOCKET_EVENTS.REFRESH_EMPLOYEES,
		],
		{
			interval: 30000,
			enabled: true,
			immediate: true,
			enableSocket: true,
			enablePolling: true,
			onError: (error) => {
				console.error('Socket refresh error:', error);
			},
		}
	);

	// Initial data fetch on mount
	useEffect(() => {
		if (!socketTriggered) {
			fetchEmployees();
		}
	}, [fetchEmployees, socketTriggered]);

	const handleDelete = async (id: number) => {
		try {
			await deleteEmployeeFromStore(id);
			setEmployees((prevEmployees) =>
				prevEmployees.filter((employee) => employee.id !== id)
			);
		} catch (err: any) {
			console.error('Error deleting employee:', err);
			setError(
				'Failed to delete employee: ' + (err.message || 'Unknown error')
			);
			throw err;
		}
	};

	return (
		<>
			{children({
				employees,
				loading,
				error,
				handleDelete,
				fetchEmployees: refreshData,
				isRefreshing,
				isConnected,
				socketTriggered,
			})}
		</>
	);
};

export default EmployeeListContainer;
