"use server";

import { suggestMaintenanceSchedule as suggestMaintenanceScheduleFlow, type SuggestMaintenanceScheduleInput, type SuggestMaintenanceScheduleOutput } from "@/ai/flows/suggest-maintenance-schedule";

export async function getMaintenanceSuggestions(input: SuggestMaintenanceScheduleInput): Promise<{ success: boolean; data?: SuggestMaintenanceScheduleOutput; error?: string }> {
  try {
    // Basic validation (more thorough validation can be done with <PERSON><PERSON> if needed here too)
    if (!input.vehicleMake || !input.vehicleModel || !input.vehicleYear || input.currentOdometer == null) {
      return { success: false, error: "Missing required vehicle information." };
    }
    if (input.currentOdometer < 0) {
        return { success: false, error: "Current odometer cannot be negative." };
    }
    if (input.vehicleYear < 1886 || input.vehicleYear > new Date().getFullYear() + 1) { // Benz Patent-Motorwagen to next year
        return { success: false, error: "Invalid vehicle year."}
    }


    const result = await suggestMaintenanceScheduleFlow(input);
    return { success: true, data: result };
  } catch (error)
 {
    console.error("Error getting maintenance suggestions:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred while fetching suggestions.";
    // Check for specific Genkit/AI errors if possible, e.g., quota issues, model errors
    if (errorMessage.includes(" candidats")) { // Crude check for "No candidates" or similar errors from AI
        return { success: false, error: "The AI model could not generate a suggestion with the provided data. Please ensure the service history is detailed enough."}
    }
    return { success: false, error: errorMessage };
  }
}
