// Import types
import type {
	ServiceRecord,
	EnrichedServiceRecord,
	Vehicle,
	ServiceRecordQueryParams,
} from './types';
import {formatDateForApi} from './utils/dateUtils';

// Global token storage for API requests
let globalAuthToken: string | null = null;

/**
 * Set the global authentication token for API requests
 * This should be called whenever the user's session changes
 */
export function setAuthToken(token: string | null) {
	globalAuthToken = token;
	if (process.env.NODE_ENV !== 'production') {
		console.log(
			'🔐 Legacy API Service: Auth token updated',
			token ? 'Token set' : 'Token cleared'
		);
	}
}

/**
 * Get the current authentication token
 * This function can be used to dynamically retrieve the token at request time
 */
export function getCurrentAuthToken(): string | null {
	return globalAuthToken;
}

// Determine the API base URL based on environment
// In Docker, services can communicate using their service names
// For local development, use localhost
const isDockerEnvironment =
	typeof window !== 'undefined' &&
	window.location.hostname !== 'localhost' &&
	window.location.hostname !== '127.0.0.1';

// Try environment variable first, then Docker service name, then localhost fallback
export const API_BASE_URL =
	process.env.NEXT_PUBLIC_API_BASE_URL ||
	(isDockerEnvironment
		? 'http://backend:3001/api'
		: 'http://localhost:3001/api');

// Log the API base URL for debugging
if (typeof window !== 'undefined') {
	console.log(`[API Service] Using API base URL: ${API_BASE_URL}`);
	console.log(
		`[API Service] Environment: ${isDockerEnvironment ? 'Docker' : 'Local'}`
	);
}

// Define common types for API responses if possible, or use any for now
// Example: export interface Vehicle { id: number; make: string; ... }

// Helper function for fetch requests with generic type support
export async function fetchData<T = any>(
	relativePath: string,
	options?: RequestInit
): Promise<T> {
	const fullUrl = `${API_BASE_URL}${relativePath}`;

	// Prepare headers with authentication
	const headers: Record<string, string> = {
		'Content-Type': 'application/json',
		...options?.headers,
	};

	// Add Authorization header if token is available
	if (globalAuthToken) {
		headers['Authorization'] = `Bearer ${globalAuthToken}`;
	}

	const requestOptions: RequestInit = {
		...options,
		headers,
	};

	try {
		// Log outgoing request for debugging
		console.debug(`API Request: ${requestOptions?.method || 'GET'} ${fullUrl}`);
		if (requestOptions?.body) {
			try {
				console.debug(
					'Request payload:',
					JSON.parse(requestOptions.body as string)
				);
			} catch (e) {
				console.debug('Request payload (non-JSON):', requestOptions.body);
			}
		}

		const response = await fetch(fullUrl, requestOptions);
		if (!response.ok) {
			// Attempt to parse error response body
			let errorBody;
			try {
				errorBody = await response.json();
			} catch (e) {
				// If response is not JSON, use status text or a generic message
				errorBody = {
					message: response.statusText || `HTTP error ${response.status}`,
				};
			}

			// Log the full error body for debugging
			console.error(`API Error ${response.status} at ${fullUrl}:`, errorBody);

			// Enhanced handling for validation errors
			if (
				errorBody?.status === 'error' &&
				errorBody?.message === 'Validation failed'
			) {
				console.error('Validation errors:', errorBody.errors);

				// Create a more detailed error message that includes validation details
				let validationMessage = 'Validation failed';
				if (errorBody.errors && Array.isArray(errorBody.errors)) {
					const errorDetails = errorBody.errors
						.map((err: any) => `${err.path}: ${err.message}`)
						.join('; ');

					if (errorDetails) {
						validationMessage += `: ${errorDetails}`;
					}
				}

				// Create a custom error with validation details
				const validationError = new Error(
					`API request to ${fullUrl} failed with status ${response.status}: ${validationMessage}`
				);

				// Attach the errors array and received data to the error object
				(validationError as any).validationErrors = errorBody.errors;
				(validationError as any).receivedData = errorBody.receivedData;
				(validationError as any).status = response.status;

				throw validationError;
			}

			// Check for validation errors array (fallback for older API endpoints)
			if (errorBody?.errors && Array.isArray(errorBody.errors)) {
				console.error('Validation errors:', errorBody.errors);
				// Attach the errors array to the error object
				const error = new Error(
					`API request to ${fullUrl} failed with status ${response.status}: ${
						errorBody.message || 'Validation failed'
					}`
				);
				(error as any).validationErrors = errorBody.errors;
				throw error;
			}

			// Check for enhanced error details
			if (errorBody?.details) {
				const error = new Error(
					errorBody?.error?.message ||
						errorBody?.message ||
						'Unknown server error'
				);
				// Attach the details to the error object
				(error as any).details = errorBody.details;
				throw error;
			}

			// Prefer backend's specific error structure if available (e.g., error.message or just message)
			const detailedMessage =
				errorBody?.error?.message ||
				errorBody?.message ||
				'Unknown server error';
			console.error(`API Error details: ${detailedMessage}`);
			throw new Error(
				`API request to ${fullUrl} failed with status ${response.status}: ${detailedMessage}`
			);
		}

		if (response.status === 204) {
			// No Content
			return null as T;
		}

		// Parse and return the response
		const data = await response.json();
		console.debug(`API Response from ${fullUrl}:`, data);
		return data as T;
	} catch (error: any) {
		// This block handles network errors (like "Failed to fetch") or errors re-thrown from the `if (!response.ok)` block
		if (error.message.startsWith('API request to')) {
			// If it's an error we threw above
			console.error('API Error Propagated:', error.message); // Already logged with details
			throw error; // Re-throw the enriched error
		}
		// This is for true network errors caught by fetch itself (e.g. server down, DNS issue, CORS if it reaches here)
		const networkErrorMessage = `Network error when trying to fetch ${fullUrl}. The server might be down, unreachable, or there could be a CORS issue. Original error: ${
			error.message || 'Unknown fetch error'
		}`;
		console.error(
			'Fetch setup/network error details:',
			networkErrorMessage,
			'Options:',
			options,
			'Original error object:',
			error
		);
		throw new Error(networkErrorMessage);
	}
}

// Vehicle API functions
export const getVehicles = async () => {
	return fetchData(`/vehicles`);
};

export const getVehicleById = async (id: number) => {
	return fetchData(`/vehicles/${id}`);
};

export const createVehicle = async (vehicleData: any) => {
	// Replace 'any' with a proper VehicleCreateInput type
	return fetchData(`/vehicles`, {
		method: 'POST',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(vehicleData),
	});
};

export const updateVehicle = async (id: number, vehicleData: any) => {
	// Replace 'any' with VehicleUpdateInput
	return fetchData(`/vehicles/${id}`, {
		method: 'PUT',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(vehicleData),
	});
};

export const deleteVehicle = async (id: number) => {
	return fetchData(`/vehicles/${id}`, {
		method: 'DELETE',
	});
};

// Service Record API functions

/**
 * Creates a new service record for a vehicle
 * @param vehicleId - The ID of the vehicle
 * @param serviceRecordData - The service record data to create
 * @returns The created service record
 */
export const createServiceRecord = async (
	vehicleId: number,
	serviceRecordData: Omit<ServiceRecord, 'id'>
): Promise<ServiceRecord> => {
	return fetchData(`/vehicles/${vehicleId}/servicerecords`, {
		method: 'POST',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(serviceRecordData),
	});
};

/**
 * Gets all service records for a specific vehicle
 * @param vehicleId - The ID of the vehicle
 * @returns An array of service records
 */
export const getServiceRecords = async (
	vehicleId: number
): Promise<ServiceRecord[]> => {
	return fetchData(`/vehicles/${vehicleId}/servicerecords`);
};

/**
 * Gets all service records across all vehicles
 * @param params - Optional query parameters for filtering and pagination
 * @returns An array of service records
 */
export const getAllServiceRecords = async (
	params: ServiceRecordQueryParams = {}
): Promise<ServiceRecord[]> => {
	try {
		// Build query string from params
		const queryParams = new URLSearchParams();

		// Add parameters to query string if they exist
		if (params.vehicleId) queryParams.append('vehicleId', params.vehicleId);
		if (params.startDate) queryParams.append('startDate', params.startDate);
		if (params.endDate) queryParams.append('endDate', params.endDate);
		if (params.limit) queryParams.append('limit', params.limit.toString());
		if (params.offset) queryParams.append('offset', params.offset.toString());
		if (params.sortBy) queryParams.append('sortBy', params.sortBy);
		if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

		// Construct the URL with query parameters if any
		const url = `/servicerecords${
			queryParams.toString() ? `?${queryParams.toString()}` : ''
		}`;

		console.info('Fetching service records', {url, params});

		// Implement retry logic for network errors
		let retryCount = 0;
		const maxRetries = 3;
		const retryDelay = 1000; // 1 second
		const timeout = 10000; // 10 seconds timeout

		while (retryCount <= maxRetries) {
			// Create a new AbortController for each attempt
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), timeout);

			try {
				const response = await fetch(`${API_BASE_URL}${url}`, {
					signal: controller.signal,
				});

				// Clear the timeout since the request completed
				clearTimeout(timeoutId);

				// For 400 errors, we don't retry as they indicate client errors
				if (!response.ok && response.status === 400) {
					const errorData = await response.json();
					console.warn('400 Bad Request error (not retrying)', {
						url,
						status: response.status,
						errorData,
					});
					throw new Error(errorData.message || 'Bad Request');
				}

				// For server errors, we retry
				if (!response.ok && response.status >= 500) {
					if (retryCount < maxRetries) {
						console.warn(`Server error (${response.status}), retrying...`, {
							url,
							attempt: retryCount + 1,
							status: response.status,
						});

						// Wait before retrying
						await new Promise((resolve) =>
							setTimeout(resolve, retryDelay * (retryCount + 1))
						);
						retryCount++;
						continue;
					}
				}

				// If response is not OK and we haven't continued, throw an error
				if (!response.ok) {
					const errorData = await response.json().catch(() => ({}));
					throw new Error(errorData.message || `HTTP error ${response.status}`);
				}

				// Success case
				const data = await response.json();

				// Validate that the response is an array
				if (!Array.isArray(data)) {
					console.error('Expected array but received:', data);
					throw new Error(
						'Invalid response format: expected an array of service records'
					);
				}

				console.debug('Service records fetched successfully', {
					count: data.length,
				});

				return data;
			} catch (error) {
				// Clear the timeout to prevent memory leaks
				clearTimeout(timeoutId);

				// Handle abort errors (timeouts)
				if (error instanceof Error && error.name === 'AbortError') {
					console.warn(`Request timed out after ${timeout}ms, retrying...`, {
						url,
						attempt: retryCount + 1,
					});

					if (retryCount < maxRetries) {
						retryCount++;
						continue;
					} else {
						throw new Error(`Request timed out after ${maxRetries} attempts`);
					}
				}

				if (error instanceof Error && error.message.includes('Bad Request')) {
					// Don't retry 400 errors
					throw error;
				}

				if (retryCount < maxRetries) {
					console.warn(`Fetch attempt ${retryCount + 1} failed, retrying...`, {
						url,
						error: error instanceof Error ? error.message : 'Unknown error',
					});

					// Wait before retrying
					await new Promise((resolve) =>
						setTimeout(resolve, retryDelay * (retryCount + 1))
					);
					retryCount++;
				} else {
					console.error(`All ${maxRetries} retry attempts failed`, {
						url,
						error: error instanceof Error ? error.message : 'Unknown error',
					});
					throw error;
				}
			}
		}

		// This should never be reached due to the while loop and throw statements
		throw new Error('Failed to fetch service records after retries');
	} catch (error) {
		console.error('Service records fetch exception', {
			error: error instanceof Error ? error.message : 'Unknown error',
			params,
		});

		// Return empty array instead of throwing to prevent UI from getting stuck
		return [];
	}
};

/**
 * Gets a specific service record by ID for a specific vehicle
 * @param vehicleId - The ID of the vehicle
 * @param serviceRecordId - The ID of the service record
 * @returns The service record
 */
export const getServiceRecordById = async (
	vehicleId: number,
	serviceRecordId: string
): Promise<ServiceRecord> => {
	return fetchData(`/vehicles/${vehicleId}/servicerecords/${serviceRecordId}`);
};

/**
 * Gets a specific service record by ID directly (without vehicle context)
 * @param serviceRecordId - The ID of the service record
 * @returns The service record
 */
export const getServiceRecordByIdDirect = async (
	serviceRecordId: string
): Promise<ServiceRecord> => {
	return fetchData(`/servicerecords/${serviceRecordId}`);
};

/**
 * Updates a service record for a specific vehicle
 * @param vehicleId - The ID of the vehicle
 * @param serviceRecordId - The ID of the service record
 * @param serviceRecordData - The updated service record data
 * @returns The updated service record
 */
export const updateServiceRecord = async (
	vehicleId: number,
	serviceRecordId: string,
	serviceRecordData: Partial<Omit<ServiceRecord, 'id'>>
): Promise<ServiceRecord> => {
	return fetchData(`/vehicles/${vehicleId}/servicerecords/${serviceRecordId}`, {
		method: 'PUT',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(serviceRecordData),
	});
};

/**
 * Updates a service record directly (without vehicle context)
 * @param serviceRecordId - The ID of the service record
 * @param serviceRecordData - The updated service record data
 * @returns The updated service record
 */
export const updateServiceRecordDirect = async (
	serviceRecordId: string,
	serviceRecordData: Partial<Omit<ServiceRecord, 'id'>>
): Promise<ServiceRecord> => {
	return fetchData(`/servicerecords/${serviceRecordId}`, {
		method: 'PUT',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(serviceRecordData),
	});
};

/**
 * Deletes a service record for a specific vehicle
 * @param vehicleId - The ID of the vehicle
 * @param serviceRecordId - The ID of the service record
 * @returns The deleted service record
 */
export const deleteServiceRecord = async (
	vehicleId: number,
	serviceRecordId: string
): Promise<void> => {
	return fetchData(`/vehicles/${vehicleId}/servicerecords/${serviceRecordId}`, {
		method: 'DELETE',
	});
};

/**
 * Deletes a service record directly (without vehicle context)
 * @param serviceRecordId - The ID of the service record
 * @returns The deleted service record
 */
export const deleteServiceRecordDirect = async (
	serviceRecordId: string
): Promise<void> => {
	return fetchData(`/servicerecords/${serviceRecordId}`, {
		method: 'DELETE',
	});
};

// Employee API functions
export const getEmployees = async () => {
	return fetchData(`/employees`);
};

export const getEmployeeById = async (id: number) => {
	return fetchData(`/employees/${id}`);
};

export const createEmployee = async (employeeData: any) => {
	// Format the data to ensure proper backend validation
	const formattedData = {...employeeData};

	// Fix hireDate format: Use the existing utility function
	if (formattedData.hireDate) {
		formattedData.hireDate = formatDateForApi(formattedData.hireDate);
	}

	// Fix contactEmail: Convert empty strings to null
	if (formattedData.contactEmail === '') {
		formattedData.contactEmail = null;
	}

	// Fix other optional email fields that might be empty strings
	if (formattedData.contactPhone === '') {
		formattedData.contactPhone = null;
	}
	if (formattedData.contactMobile === '') {
		formattedData.contactMobile = null;
	}

	return fetchData(`/employees`, {
		method: 'POST',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(formattedData),
	});
};

export const updateEmployee = async (id: number, employeeData: any) => {
	// Format the data to ensure proper backend validation
	const formattedData = {...employeeData};

	// Fix hireDate format: Use the existing utility function
	if (formattedData.hireDate) {
		formattedData.hireDate = formatDateForApi(formattedData.hireDate);
	}

	// Fix contactEmail: Convert empty strings to null
	if (formattedData.contactEmail === '') {
		formattedData.contactEmail = null;
	}

	// Fix other optional email fields that might be empty strings
	if (formattedData.contactPhone === '') {
		formattedData.contactPhone = null;
	}
	if (formattedData.contactMobile === '') {
		formattedData.contactMobile = null;
	}

	return fetchData(`/employees/${id}`, {
		method: 'PUT',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(formattedData),
	});
};

export const deleteEmployee = async (id: number) => {
	return fetchData(`/employees/${id}`, {
		method: 'DELETE',
	});
};

// Task API functions
export const getTasks = async () => {
	return fetchData(`/tasks`);
};

export const getTaskById = async (id: string) => {
	return fetchData(`/tasks/${id}`);
};

export const createTask = async (taskData: any) => {
	return fetchData(`/tasks`, {
		method: 'POST',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(taskData),
	});
};

export const updateTask = async (id: string, taskData: any) => {
	return fetchData(`/tasks/${id}`, {
		method: 'PUT',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(taskData),
	});
};

export const deleteTask = async (id: string) => {
	return fetchData(`/tasks/${id}`, {
		method: 'DELETE',
	});
};

// Delegation API functions
export const getDelegations = async () => {
	return fetchData(`/delegations`);
};

export const getDelegationById = async (id: string) => {
	return fetchData(`/delegations/${id}`);
};

export const createDelegation = async (delegationData: any) => {
	return fetchData(`/delegations`, {
		method: 'POST',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(delegationData),
	});
};

export const updateDelegation = async (id: string, delegationData: any) => {
	return fetchData(`/delegations/${id}`, {
		method: 'PUT',
		headers: {'Content-Type': 'application/json'},
		body: JSON.stringify(delegationData),
	});
};

export const deleteDelegation = async (id: string) => {
	return fetchData(`/delegations/${id}`, {
		method: 'DELETE',
	});
};

/**
 * Gets all service records and enriches them with vehicle information
 * @returns An array of enriched service records
 */
export const getAllEnrichedServiceRecords = async (): Promise<
	EnrichedServiceRecord[]
> => {
	try {
		console.info('Starting to fetch enriched service records');

		// Fetch all vehicles and service records in parallel with individual error handling
		let vehicles: Vehicle[] = [];
		let serviceRecords: ServiceRecord[] = [];

		try {
			// Use Promise.allSettled to continue even if one promise fails
			const results = await Promise.allSettled([
				getVehicles(),
				getAllServiceRecords(),
			]);

			// Handle the results of each promise
			if (results[0].status === 'fulfilled') {
				vehicles = Array.isArray(results[0].value) ? results[0].value : [];
				console.debug(`Successfully fetched ${vehicles.length} vehicles`);
			} else {
				console.error('Failed to fetch vehicles:', results[0].reason);
				// Continue with empty vehicles array
			}

			if (results[1].status === 'fulfilled') {
				serviceRecords = Array.isArray(results[1].value)
					? results[1].value
					: [];
				console.debug(
					`Successfully fetched ${serviceRecords.length} service records`
				);
			} else {
				console.error('Failed to fetch service records:', results[1].reason);
				// Continue with empty service records array
			}
		} catch (error) {
			console.error('Error in parallel fetch operations:', error);
			// Continue with whatever data we have
		}

		// If both fetches failed, throw an error
		if (vehicles.length === 0 && serviceRecords.length === 0) {
			throw new Error('Failed to fetch both vehicles and service records');
		}

		// Create a map of vehicles by ID for quick lookup
		const vehicleMap = new Map<number, Vehicle>();
		vehicles.forEach((vehicle: Vehicle) => {
			if (vehicle && typeof vehicle.id === 'number') {
				vehicleMap.set(vehicle.id, vehicle);
			}
		});

		// Enrich service records with vehicle information
		const enrichedRecords: EnrichedServiceRecord[] = serviceRecords.map(
			(record: ServiceRecord) => {
				// Ensure record is valid
				if (!record || typeof record.vehicleId === 'undefined') {
					console.warn('Invalid service record found:', record);
					return {
						id: record?.id || 'unknown',
						vehicleId: 'unknown',
						date: record?.date || new Date().toISOString(),
						odometer: record?.odometer || 0,
						servicePerformed: record?.servicePerformed || ['Unknown'],
						vehicleMake: 'Unknown',
						vehicleModel: 'Unknown',
						vehicleYear: 0,
						vehiclePlateNumber: 'Unknown',
					};
				}

				const vehicleId = Number(record.vehicleId);
				const vehicle = vehicleMap.get(vehicleId);

				if (!vehicle) {
					console.warn(
						`Vehicle with ID ${record.vehicleId} not found for service record ${record.id}`
					);
					return {
						...record,
						vehicleId: String(record.vehicleId),
						vehicleMake: 'Unknown',
						vehicleModel: 'Unknown',
						vehicleYear: 0,
						vehiclePlateNumber: 'Unknown',
					};
				}

				return {
					...record,
					vehicleId: String(vehicle.id),
					vehicleMake: vehicle.make || 'Unknown',
					vehicleModel: vehicle.model || 'Unknown',
					vehicleYear: vehicle.year || 0,
					vehiclePlateNumber: vehicle.licensePlate || 'Unknown',
				};
			}
		);

		// Sort by date (newest first) and then by odometer (highest first)
		const sortedRecords = enrichedRecords.sort((a, b) => {
			try {
				const dateA = new Date(a.date).getTime();
				const dateB = new Date(b.date).getTime();

				// Check for invalid dates
				if (isNaN(dateA) || isNaN(dateB)) {
					return 0;
				}

				const dateComparison = dateB - dateA;
				if (dateComparison !== 0) return dateComparison;

				// Ensure odometer values are numbers
				const odometerA = typeof a.odometer === 'number' ? a.odometer : 0;
				const odometerB = typeof b.odometer === 'number' ? b.odometer : 0;

				return odometerB - odometerA;
			} catch (error) {
				console.error('Error sorting records:', error);
				return 0;
			}
		});

		console.info(`Returning ${sortedRecords.length} enriched service records`);
		return sortedRecords;
	} catch (error) {
		console.error('Error fetching enriched service records:', error);
		// Return empty array instead of throwing to prevent UI from getting stuck
		return [];
	}
};

// It would be good to define interfaces/types for Vehicle, Employee, Task,
// VehicleCreateInput, VehicleUpdateInput, TaskCreateInput, etc., in a shared types directory
// or import them if they are published from the backend.
