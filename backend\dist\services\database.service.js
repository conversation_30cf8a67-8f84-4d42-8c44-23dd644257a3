import dotenv from 'dotenv';
import { PrismaClient } from '../generated/prisma/index.js';
import { createClient } from '@supabase/supabase-js';
import logger from '../utils/logger.js';
// Load environment variables
dotenv.config();
// Get database configuration from environment variables
const getDatabaseConfig = () => {
    // Check if USE_SUPABASE is set to true
    const useSupabase = process.env.USE_SUPABASE === 'true';
    // Get database URL
    const databaseUrl = process.env.DATABASE_URL || '';
    // Get Supabase credentials if using Supabase
    const supabaseUrl = useSupabase ? process.env.SUPABASE_URL : undefined;
    const supabaseKey = useSupabase ? process.env.SUPABASE_KEY : undefined;
    // Log configuration details (hiding sensitive info)
    logger.info('Database Configuration:', {
        useSupabase,
        databaseUrl: databaseUrl
            ? databaseUrl.replace(/\/\/.*?@/, '//****@')
            : 'not set',
        supabaseUrl: supabaseUrl ? supabaseUrl : 'not set',
        supabaseKeyProvided: !!supabaseKey,
        connectionMode: databaseUrl.includes(':6543/') ? 'transaction' : 'session',
    });
    return {
        useSupabase,
        databaseUrl,
        supabaseUrl,
        supabaseKey,
    };
};
// Initialize Prisma client
const prisma = new PrismaClient({
    log: [
        {
            emit: 'stdout',
            level: 'query',
        },
        {
            emit: 'stdout',
            level: 'info',
        },
        {
            emit: 'stdout',
            level: 'warn',
        },
        {
            emit: 'stdout',
            level: 'error',
        },
    ],
});
// Initialize Supabase client if configured
let supabase = null;
const initializeSupabase = () => {
    const config = getDatabaseConfig();
    if (config.useSupabase) {
        if (!config.supabaseUrl) {
            logger.error('Failed to initialize Supabase: SUPABASE_URL is not set');
            return null;
        }
        if (!config.supabaseKey) {
            logger.error('Failed to initialize Supabase: SUPABASE_KEY is not set');
            return null;
        }
        try {
            logger.info(`Initializing Supabase client with URL: ${config.supabaseUrl}`);
            const options = {
                auth: {
                    autoRefreshToken: true,
                    persistSession: true,
                },
                global: {
                    headers: {
                        'X-Client-Info': 'car-service-tracking-system',
                    },
                },
            };
            supabase = createClient(config.supabaseUrl, config.supabaseKey, options);
            logger.info('Supabase client initialized successfully');
            return supabase;
        }
        catch (error) {
            logger.error('Failed to initialize Supabase client:', error);
            logger.error('Error details:', JSON.stringify(error, null, 2));
            logger.error('Stack trace:', error.stack);
            return null;
        }
    }
    else {
        logger.info('Supabase initialization skipped - USE_SUPABASE is not set to true');
    }
    return null;
};
// Test database connections
const testDatabaseConnections = async () => {
    const config = getDatabaseConfig();
    const results = {
        prisma: false,
        supabase: false,
        timestamp: new Date().toISOString(),
        details: {
            prisma: {
                error: null,
                connectionString: config.databaseUrl
                    ? config.databaseUrl.replace(/\/\/.*?@/, '//****@')
                    : 'not set',
            },
            supabase: {
                error: null,
                url: config.supabaseUrl || 'not set',
                keyProvided: !!config.supabaseKey,
                clientInitialized: !!supabase,
                data: null,
            },
        },
    };
    // Test Prisma connection
    try {
        logger.info('Testing Prisma connection...');
        const startTime = Date.now();
        await prisma.$connect();
        await prisma.$queryRaw `SELECT 1`;
        const endTime = Date.now();
        logger.info(`✅ Successfully connected to the PostgreSQL database via Prisma (${endTime - startTime}ms)`);
        results.prisma = true;
    }
    catch (error) {
        logger.error('❌ Failed to connect to the PostgreSQL database via Prisma:', error);
        logger.error('Prisma error details:', JSON.stringify(error, null, 2));
        logger.error('Stack trace:', error.stack);
        results.details.prisma.error = {
            message: error.message || 'Unknown error',
            code: error.code || 'UNKNOWN',
            meta: error.meta || {},
        };
    }
    // Test Supabase connection if configured
    if (config.useSupabase) {
        if (supabase) {
            try {
                logger.info('Testing Supabase connection...');
                const startTime = Date.now();
                // First, test a simple health check
                const { data: healthData, error: healthError } = await supabase.rpc('pg_is_in_recovery');
                if (healthError) {
                    logger.error('❌ Supabase health check failed:', healthError.message);
                    results.details.supabase.error = {
                        message: healthError.message || 'Health check failed',
                        code: healthError.code || 'HEALTH_CHECK_FAILED',
                        details: healthError.details || '',
                    };
                }
                else {
                    logger.info('✓ Supabase health check passed');
                    // Now try to query a table
                    const { data, error } = await supabase
                        .from('_prisma_migrations')
                        .select('*')
                        .limit(1);
                    const endTime = Date.now();
                    if (error) {
                        logger.error('❌ Failed to query Supabase table:', error.message);
                        logger.error('Supabase error details:', JSON.stringify(error, null, 2));
                        results.details.supabase.error = {
                            message: error.message || 'Query failed',
                            code: error.code || 'QUERY_FAILED',
                            details: error.details || '',
                            hint: error.hint || '',
                        };
                    }
                    else {
                        logger.info(`✅ Successfully connected to Supabase and queried data (${endTime - startTime}ms)`);
                        logger.debug('Supabase data sample:', JSON.stringify(data));
                        results.supabase = true;
                        results.details.supabase.data = data || [];
                    }
                }
            }
            catch (error) {
                logger.error('❌ Error testing Supabase connection:', error);
                logger.error('Error details:', JSON.stringify(error, null, 2));
                logger.error('Stack trace:', error.stack);
                results.details.supabase.error = {
                    message: error.message || 'Unknown error',
                    name: error.name || 'Error',
                    stack: error.stack || '',
                };
            }
        }
        else {
            logger.warn('⚠️ Supabase is enabled but client initialization failed');
            results.details.supabase.error = {
                message: 'Supabase client initialization failed',
                clientInitialized: false,
            };
        }
    }
    else {
        logger.info('ℹ️ Supabase connection test skipped - not configured');
        results.details.supabase.error = {
            message: 'Supabase not configured (USE_SUPABASE is not set to true)',
        };
    }
    // Log the final results
    logger.info('Database connection test results:', JSON.stringify(results, null, 2));
    return results;
};
// Initialize Supabase if configured
initializeSupabase();
export { prisma, supabase, testDatabaseConnections, getDatabaseConfig, initializeSupabase, };
export default prisma;
//# sourceMappingURL=database.service.js.map