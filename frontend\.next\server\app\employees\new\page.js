(()=>{var e={};e.id=6841,e.ids=[6841],e.modules={2288:(e,r,t)=>{Promise.resolve().then(t.bind(t,7707))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7707:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\new\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12016:(e,r,t)=>{Promise.resolve().then(t.bind(t,47229))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},47229:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),n=t(43210),o=t(16189),i=t(37716),a=t(28840),d=t(48041),p=t(28399),l=t(29867);let c=()=>{let e=(0,o.useRouter)(),{toast:r}=(0,l.dj)(),[t,c]=(0,n.useState)(!1),[u,m]=(0,n.useState)(null),x=async t=>{c(!0),m(null);try{let s=await (0,a.addEmployee)(t);r({title:"Employee Added",description:`${s.name||s.fullName} has been successfully added.`,variant:"default"}),e.push("/employees")}catch(t){console.error("Failed to add employee:",t);let e=t.response?.data?.error||t.message||"An unexpected error occurred.";m(e),r({title:"Error Adding Employee",description:e,variant:"destructive"})}finally{c(!1)}};return(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,s.jsx)(d.z,{title:"Add New Employee",description:"Enter the details for the new employee.",icon:p.A}),u&&(0,s.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4",role:"alert",children:[(0,s.jsx)("strong",{className:"font-bold",children:"Error: "}),(0,s.jsx)("span",{className:"block sm:inline",children:u})]}),(0,s.jsx)(i.A,{onSubmit:x,isEditing:!1,isLoading:t})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94345:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>p});var s=t(65239),n=t(48088),o=t(88170),i=t.n(o),a=t(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let p={children:["",{children:["employees",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7707)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\new\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\new\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/employees/new/page",pathname:"/employees/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3744,1658,5880,2729,3442,8141,3983,3860],()=>t(94345));module.exports=s})();