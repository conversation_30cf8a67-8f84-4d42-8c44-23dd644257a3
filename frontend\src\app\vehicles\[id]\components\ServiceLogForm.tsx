// @ts-nocheck
'use client';

import {useState} from 'react';
import {useForm, type SubmitHandler} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import * as z from 'zod';
import {ActionButton} from '@/components/ui/action-button';
import {Input} from '@/components/ui/input';
import {Textarea} from '@/components/ui/textarea';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardFooter,
} from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import {Checkbox} from '@/components/ui/checkbox';
import type {ServiceRecord, Vehicle} from '@/lib/types';
import type {ServiceRecord} from '@/lib/types';
import {useToast} from '@/hooks/use-toast';
import {PlusCircle, Loader2} from 'lucide-react';

const commonServices = [
	'Oil Change',
	'Tire Rotation',
	'Brake Service',
	'Battery Replacement',
	'Air Filter Replacement',
	'Cabin Air Filter Replacement',
	'Wiper Blade Replacement',
	'Fluid Check/Top-up',
	'Spark Plug Replacement',
	'Coolant Flush',
	'Transmission Service',
	'Wheel Alignment',
	'State Inspection',
	'Other',
];

const serviceRecordSchema = z.object({
	date: z
		.string()
		.refine((val) => !isNaN(Date.parse(val)), {message: 'Invalid date format'}),
	odometer: z.coerce.number().min(0, 'Odometer cannot be negative'),
	servicePerformed: z
		.array(z.string())
		.min(1, {message: 'At least one service must be selected'}),
	notes: z.string().optional(),
	cost: z.coerce.number().min(0, 'Cost cannot be negative').optional(),
});

export type ServiceRecordFormData = z.infer<typeof serviceRecordSchema>;

interface ServiceLogFormProps {
	vehicleId: number;
	onServiceRecordAdded: (updatedVehicle: Vehicle) => void;
	currentOdometerReading?: number;
}

export default function ServiceLogForm({
	vehicleId,
	onServiceRecordAdded,
	currentOdometerReading = 0,
}: ServiceLogFormProps) {
	const {toast} = useToast();
	const form = useForm<ServiceRecordFormData>({
		resolver: zodResolver(serviceRecordSchema),
		defaultValues: {
			date: new Date().toISOString().split('T')[0],
			odometer: currentOdometerReading > 0 ? currentOdometerReading : undefined,
			servicePerformed: [],
			notes: '',
			cost: undefined,
		},
	});

	const {addServiceRecord: storeAddServiceRecord} = require('@/lib/store');
	const [isSubmitting, setIsSubmitting] = useState(false);

	const handleFormSubmit: SubmitHandler<ServiceRecordFormData> = async (
		data
	) => {
		setIsSubmitting(true);

		try {
			const newRecordData: Omit<ServiceRecord, 'id'> = {
				...data,
				cost: data.cost ? Number(data.cost) : undefined,
				odometer: Number(data.odometer),
			};

			const updatedVehicle = await storeAddServiceRecord(
				vehicleId,
				newRecordData
			);

			if (updatedVehicle) {
				onServiceRecordAdded(updatedVehicle);
				toast({
					title: 'Service Record Added',
					description: 'The new service record has been successfully logged.',
				});
				form.reset({
					date: new Date().toISOString().split('T')[0],
					odometer: data.odometer,
					servicePerformed: [],
					notes: '',
					cost: undefined,
				});
			} else {
				toast({
					title: 'Error',
					description: 'Failed to add service record. Vehicle not found.',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Error adding service record:', error);
			toast({
				title: 'Error',
				description:
					'An error occurred while adding the service record. Please try again.',
				variant: 'destructive',
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Card className='shadow-md bg-card'>
			<CardHeader className='p-5'>
				<CardTitle className='text-xl font-semibold text-primary'>
					Log New Service
				</CardTitle>
			</CardHeader>
			<Form {...form}>
				<form onSubmit={form.handleSubmit(handleFormSubmit)}>
					<CardContent className='space-y-4 p-5'>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<FormField
								control={form.control}
								name='date'
								render={({field}) => (
									<FormItem>
										<FormLabel>Date of Service</FormLabel>
										<FormControl>
											<Input type='date' {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name='odometer'
								render={({field}) => (
									<FormItem>
										<FormLabel>Odometer (miles)</FormLabel>
										<FormControl>
											<Input
												type='number'
												placeholder='e.g., 25000'
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<FormField
							control={form.control}
							name='servicePerformed'
							render={() => (
								<FormItem>
									<FormLabel>Services Performed</FormLabel>
									<div className='grid grid-cols-2 sm:grid-cols-3 gap-x-4 gap-y-2'>
										{commonServices.map((service) => (
											<FormField
												key={service}
												control={form.control}
												name='servicePerformed'
												render={({field}) => {
													return (
														<FormItem className='flex flex-row items-center space-x-2 space-y-0'>
															<FormControl>
																<Checkbox
																	checked={field.value?.includes(service)}
																	onCheckedChange={(checked) => {
																		const currentValue = field.value || [];
																		if (checked) {
																			field.onChange([
																				...currentValue,
																				service,
																			]);
																		} else {
																			field.onChange(
																				currentValue.filter(
																					(value) => value !== service
																				)
																			);
																		}
																	}}
																/>
															</FormControl>
															<FormLabel className='font-normal text-sm'>
																{service}
															</FormLabel>
														</FormItem>
													);
												}}
											/>
										))}
									</div>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name='notes'
							render={({field}) => (
								<FormItem>
									<FormLabel>Notes (Optional)</FormLabel>
									<FormControl>
										<Textarea
											placeholder='e.g., Used synthetic oil, checked tire pressure.'
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name='cost'
							render={({field}) => (
								<FormItem>
									<FormLabel>Cost (Optional)</FormLabel>
									<FormControl>
										<Input
											type='number'
											step='0.01'
											placeholder='e.g., 75.50'
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</CardContent>
					<CardFooter className='p-5'>
						<ActionButton
							type='submit'
							actionType='primary'
							isLoading={isSubmitting}
							loadingText='Saving...'
							icon={<PlusCircle className='h-4 w-4' />}
							className='w-full'>
							Add Service Record
						</ActionButton>
					</CardFooter>
				</form>
			</Form>
		</Card>
	);
}
