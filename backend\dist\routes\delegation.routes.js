import { Router } from 'express';
import * as delegationController from '../controllers/delegation.controller.js';
import { validate } from '../middleware/validation.js';
import { authenticateSupabaseUser, requireRole, } from '../middleware/supabaseAuth.js';
import { delegationCreateSchema, delegationUpdateSchema, delegationIdSchema, } from '../schemas/delegation.schema.js';
const router = Router();
/**
 * @openapi
 * /delegations:
 *   post:
 *     tags: [Delegations]
 *     summary: Create a new delegation
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/DelegationCreateInput'
 *     responses:
 *       201:
 *         description: Delegation created successfully.
 *       400:
 *         description: Invalid input.
 *   get:
 *     tags: [Delegations]
 *     summary: Retrieve a list of all delegations
 *     responses:
 *       200:
 *         description: A list of delegations.
 */
// 🚨 EMERGENCY SECURITY: All delegation routes require authentication
router.post('/', authenticateSupabaseUser, validate(delegationCreateSchema), delegationController.createDelegation);
router.get('/', authenticate<PERSON><PERSON><PERSON>se<PERSON><PERSON>, delegationController.getAllDelegations);
/**
 * @openapi
 * /delegations/{id}:
 *   get:
 *     tags: [Delegations]
 *     summary: Retrieve a specific delegation by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Details of the delegation.
 *       404:
 *         description: Delegation not found.
 *   put:
 *     tags: [Delegations]
 *     summary: Update a specific delegation by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/DelegationUpdateInput'
 *     responses:
 *       200:
 *         description: Delegation updated successfully.
 *       400:
 *         description: Invalid input.
 *       404:
 *         description: Delegation not found.
 *   delete:
 *     tags: [Delegations]
 *     summary: Delete a specific delegation by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Delegation deleted successfully.
 *       404:
 *         description: Delegation not found.
 */
router.get('/:id', authenticateSupabaseUser, validate(delegationIdSchema, 'params'), delegationController.getDelegationById);
router.put('/:id', authenticateSupabaseUser, validate(delegationIdSchema, 'params'), validate(delegationUpdateSchema), delegationController.updateDelegation);
router.delete('/:id', authenticateSupabaseUser, requireRole(['MANAGER', 'ADMIN', 'SUPER_ADMIN']), validate(delegationIdSchema, 'params'), delegationController.deleteDelegation);
export default router;
//# sourceMappingURL=delegation.routes.js.map