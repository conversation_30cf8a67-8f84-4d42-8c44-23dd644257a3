"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8610],{5611:(e,r,t)=>{t.d(r,{Lv:()=>s,_X:()=>n});var a=t(16051);async function s(e){try{let r=await a.FH.get("/servicerecords/enriched",e);return(0,a.CZ)(r,"/servicerecords/enriched")||[]}catch(e){throw console.error("Error fetching enriched service records:",e),e}}async function o(e,r){try{let t="/vehicles/".concat(e,"/servicerecords"),s=await a.FH.get(t,r);return(0,a.CZ)(s,t)||[]}catch(r){throw console.error("Error fetching service records for vehicle ".concat(e,":"),r),r}}async function n(e,r){try{try{return(await s(r)).filter(r=>Number(r.vehicleId)===e)}catch(n){console.warn("Falling back to manual enrichment for vehicle service records",n);let a=await o(e,r),s=await Promise.resolve().then(t.bind(t,2730)).then(r=>r.getVehicleById(e));if(!s)throw Error("Vehicle with ID ".concat(e," not found"));return a.map(e=>({...e,vehicleId:String(e.vehicleId),vehicleMake:s.make,vehicleModel:s.model,vehicleYear:s.year,vehiclePlateNumber:s.licensePlate||void 0}))}}catch(r){throw console.error("Error fetching enriched service records for vehicle ".concat(e,":"),r),r}}},16051:(e,r,t)=>{t.d(r,{FH:()=>m,CZ:()=>h,O5:()=>n});var a=function(e){return e.NETWORK_ERROR="network_error",e.TIMEOUT="timeout",e.SERVER_ERROR="server_error",e.RATE_LIMIT="rate_limit",e.CLIENT_ERROR="client_error",e.VALIDATION_ERROR="validation_error",e.AUTHENTICATION_ERROR="authentication_error",e.AUTHORIZATION_ERROR="authorization_error",e.NOT_FOUND="not_found",e.PARSING_ERROR="parsing_error",e.UNKNOWN="unknown",e}({});class s extends Error{static create(e,r,t){return new s(e,{status:r,validationErrors:null==t?void 0:t.validationErrors,receivedData:null==t?void 0:t.receivedData,details:null==t?void 0:t.details,errorType:null==t?void 0:t.errorType})}determineErrorType(){return this.status>=500?"server_error":429===this.status?"rate_limit":401===this.status?"authentication_error":403===this.status?"authorization_error":400===this.status&&this.isValidationError()?"validation_error":this.status>=400&&this.status<500?"client_error":"unknown"}isRetryable(){return"server_error"===this.errorType||"network_error"===this.errorType||"timeout"===this.errorType||"rate_limit"===this.errorType}isValidationError(){return 400===this.status&&Array.isArray(this.validationErrors)&&this.validationErrors.length>0}getFormattedMessage(){if(this.isValidationError()){let e=this.validationErrors.map(e=>"".concat(e.path,": ").concat(e.message)).join("; ");return"Validation failed: ".concat(e)}switch(this.errorType){case"network_error":return"Network error: Unable to connect to the server. Please check your internet connection.";case"timeout":return"Request timed out. The server is taking too long to respond.";case"rate_limit":return"Too many requests. Please try again later.";case"authentication_error":return"Authentication required. Please log in and try again.";case"authorization_error":return"You do not have permission to perform this action.";case"not_found":return"Resource not found: ".concat(this.endpoint||"The requested resource"," could not be found.");case"parsing_error":return"Could not parse the server response. Please try again or contact support.";case"server_error":return"Server error (".concat(this.status,"): ").concat(this.message,". Please try again later.");default:return this.message}}getTechnicalDetails(){let e=["Status: ".concat(this.status),"Type: ".concat(this.errorType),"Message: ".concat(this.message)];return this.details&&e.push("Details: ".concat(JSON.stringify(this.details))),this.validationErrors&&e.push("Validation Errors: ".concat(JSON.stringify(this.validationErrors))),e.join("\n")}constructor(e,r){super(e),this.name="ApiError",this.status=r.status,this.endpoint=r.endpoint,this.validationErrors=r.validationErrors,this.receivedData=r.receivedData,this.details=r.details,this.errorType=r.errorType||this.determineErrorType(),this.retryable=this.isRetryable(),Object.setPrototypeOf(this,s.prototype)}}let o=null;function n(e){o=e}"localhost"!==window.location.hostname&&window.location.hostname;let i=e=>new Promise(r=>setTimeout(r,e)),l=(e,r)=>Math.min(r*Math.pow(2,e),3e4);async function c(e){let r,t;if(204===e.status)return null;if(e.ok)try{return await e.json()}catch(r){throw console.error("Failed to parse successful API response:",{status:e.status,statusText:e.statusText,url:e.url,error:r instanceof Error?r.message:"Unknown error"}),new s("Failed to parse successful response: ".concat(r instanceof Error?r.message:"Unknown error"),{status:e.status,endpoint:e.url,errorType:a.PARSING_ERROR})}let o=function(e){if(e>=500)return a.SERVER_ERROR;if(429===e)return a.RATE_LIMIT;if(401===e)return a.AUTHENTICATION_ERROR;if(403===e)return a.AUTHORIZATION_ERROR;if(404===e)return a.NOT_FOUND;else if(e>=400&&e<500)return a.CLIENT_ERROR;else return a.UNKNOWN}(e.status),n="HTTP error ".concat(e.status);try{if(n=(null==(r=await e.json())?void 0:r.message)||n,t=null==r?void 0:r.details,400===e.status&&(null==r?void 0:r.status)==="error"&&(null==r?void 0:r.message)==="Validation failed"){o=a.VALIDATION_ERROR;let t=r.errors;throw console.error("API validation errors:",{endpoint:e.url,status:e.status,errors:t,receivedData:r.receivedData}),new s(r.message,{status:e.status,endpoint:e.url,errorType:o,validationErrors:t,receivedData:r.receivedData})}}catch(r){try{if(r instanceof s)throw r;{let r=await e.text();r&&(n=r)}}catch(r){if(r instanceof s)throw r;n=e.statusText||n}}throw console.error("API error (".concat(e.status,"):"),{endpoint:e.url,status:e.status,message:n,details:t,errorType:o}),new s(n,{status:e.status,endpoint:e.url,details:t,errorType:o})}async function d(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=function(){let e={"Content-Type":"application/json"};return o&&(e.Authorization="Bearer ".concat(o)),{headers:e,retries:3,retryDelay:1e3,timeout:1e4}}(),n={...t,...r,headers:{...t.headers,...r.headers}},{retries:d=3,retryDelay:u=1e3,timeout:h=1e4,skipRetryLogging:m=!1}=n,f=null;for(let r=0;r<=d;r++)try{let r=new AbortController,t=setTimeout(()=>{r.abort()},h),a={...n,signal:r.signal},s=e.startsWith("http")?e:"".concat("http://localhost:3001/api").concat(e),o=await fetch(s,a);return clearTimeout(t),await c(o)}catch(t){if(f=t,function(e){if(e instanceof s){if(e.status>=400&&e.status<500)return 429===e.status?a.RATE_LIMIT:a.CLIENT_ERROR;if(e.status>=500)return a.SERVER_ERROR}return e instanceof TypeError&&e.message.includes("network")?a.NETWORK_ERROR:e instanceof DOMException&&"AbortError"===e.name?a.TIMEOUT:a.UNKNOWN}(t)===a.CLIENT_ERROR||r===d)throw t;let e=l(r,u);m||console.warn("API request failed (attempt ".concat(r+1,"/").concat(d+1,"), retrying in ").concat(e/1e3,"s:"),t),await i(e)}throw f||Error("Failed to fetch after retries")}async function u(e,r,t){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s={...a,method:e};return"GET"!==e&&t&&(s.body=JSON.stringify(t)),d(r,s)}function h(e,r){if(e&&"object"==typeof e){if("data"in e&&"status"in e)return e.data;Array.isArray(e)||"status"in e||"message"in e||!("errors"in e)}return e}let m={get:(e,r)=>u("GET",e,void 0,r),post:(e,r,t)=>u("POST",e,r,t),put:(e,r,t)=>u("PUT",e,r,t),patch:(e,r,t)=>u("PATCH",e,r,t),delete:(e,r)=>u("DELETE",e,void 0,r)}},26126:(e,r,t)=>{t.d(r,{E:()=>i});var a=t(95155);t(12115);var s=t(74466),o=t(59434);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:r,variant:t,...s}=e;return(0,a.jsx)("div",{className:(0,o.cn)(n({variant:t}),r),...s})}},29797:(e,r,t)=>{t.d(r,{$o:()=>x});var a=t(95155),s=t(12115),o=t(59434),n=t(965),i=t(73158),l=t(3561),c=t(30285);let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex justify-center",t),...s})});d.displayName="Pagination";let u=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("ul",{ref:r,className:(0,o.cn)("flex flex-row items-center gap-1",t),...s})});u.displayName="PaginationContent";let h=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("li",{ref:r,className:(0,o.cn)("",t),...s})});h.displayName="PaginationItem";let m=s.forwardRef((e,r)=>{let{className:t,isActive:s,...n}=e;return(0,a.jsx)(c.$,{ref:r,"aria-current":s?"page":void 0,variant:s?"outline":"ghost",size:"icon",className:(0,o.cn)("h-9 w-9",t),...n})});m.displayName="PaginationLink";let f=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsxs)(c.$,{ref:r,variant:"ghost",size:"icon",className:(0,o.cn)("h-9 w-9 gap-1",t),...s,children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Previous page"})]})});f.displayName="PaginationPrevious";let v=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsxs)(c.$,{ref:r,variant:"ghost",size:"icon",className:(0,o.cn)("h-9 w-9 gap-1",t),...s,children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Next page"})]})});v.displayName="PaginationNext";let p=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsxs)("span",{ref:r,"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",t),...s,children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"More pages"})]})});function x(e){let{currentPage:r,totalPages:t,onPageChange:s,className:o}=e,n=(()=>{let e=[];e.push(1);let a=Math.max(2,r-1),s=Math.min(t-1,r+1);a>2&&e.push("ellipsis1");for(let r=a;r<=s;r++)e.push(r);return s<t-1&&e.push("ellipsis2"),t>1&&e.push(t),e})();return t<=1?null:(0,a.jsx)(d,{className:o,children:(0,a.jsxs)(u,{children:[(0,a.jsx)(h,{children:(0,a.jsx)(f,{onClick:()=>s(r-1),disabled:1===r,"aria-disabled":1===r?"true":void 0,"aria-label":"Go to previous page"})}),n.map((e,t)=>"ellipsis1"===e||"ellipsis2"===e?(0,a.jsx)(h,{children:(0,a.jsx)(p,{})},"ellipsis-".concat(t)):(0,a.jsx)(h,{children:(0,a.jsx)(m,{onClick:()=>s(e),isActive:r===e,"aria-label":"Go to page ".concat(e),children:e})},"page-".concat(e))),(0,a.jsx)(h,{children:(0,a.jsx)(v,{onClick:()=>s(r+1),disabled:r===t,"aria-disabled":r===t?"true":void 0,"aria-label":"Go to next page"})})]})})}p.displayName="PaginationEllipsis"},58824:(e,r,t)=>{t.d(r,{R:()=>y});var a=t(95155),s=t(12115),o=t(66695),n=t(55365),i=t(30285),l=t(31949),c=t(50594),d=t(85127),u=t(77381),h=t(79556),m=t(59434);function f(e){let{records:r,showVehicleInfo:t=!0,sortField:s,sortDirection:o,onSort:n,className:i}=e,l=e=>{n&&n(e)},c=e=>s!==e?null:"asc"===o?(0,a.jsx)(u.A,{className:"inline-block h-4 w-4 ml-1","aria-hidden":"true"}):(0,a.jsx)(h.A,{className:"inline-block h-4 w-4 ml-1","aria-hidden":"true"});return(0,a.jsxs)(d.XI,{id:"service-history-table",className:i,children:[(0,a.jsx)(d.A0,{children:(0,a.jsxs)(d.Hj,{children:[(0,a.jsxs)(d.nd,{onClick:()=>l("date"),className:(0,m.cn)(n?"cursor-pointer":""),"aria-sort":"date"===s?o:void 0,role:"columnheader",tabIndex:n?0:void 0,onKeyDown:e=>{n&&("Enter"===e.key||" "===e.key)&&(e.preventDefault(),l("date"))},"aria-label":"Sort by date",children:["Date ",c("date")]}),t&&(0,a.jsxs)(d.nd,{onClick:()=>l("vehicleMake"),className:(0,m.cn)(n?"cursor-pointer":""),"aria-sort":"vehicleMake"===s?o:void 0,role:"columnheader",tabIndex:n?0:void 0,onKeyDown:e=>{n&&("Enter"===e.key||" "===e.key)&&(e.preventDefault(),l("vehicleMake"))},"aria-label":"Sort by vehicle",children:["Vehicle ",c("vehicleMake")]}),(0,a.jsxs)(d.nd,{onClick:()=>l("servicePerformed"),className:(0,m.cn)(n?"cursor-pointer":""),"aria-sort":"servicePerformed"===s?o:void 0,role:"columnheader",tabIndex:n?0:void 0,onKeyDown:e=>{n&&("Enter"===e.key||" "===e.key)&&(e.preventDefault(),l("servicePerformed"))},"aria-label":"Sort by services performed",children:["Service(s) ",c("servicePerformed")]}),(0,a.jsxs)(d.nd,{onClick:()=>l("odometer"),className:(0,m.cn)(n?"cursor-pointer":""),"aria-sort":"odometer"===s?o:void 0,role:"columnheader",tabIndex:n?0:void 0,onKeyDown:e=>{n&&("Enter"===e.key||" "===e.key)&&(e.preventDefault(),l("odometer"))},"aria-label":"Sort by odometer reading",children:["Odometer ",c("odometer")]}),(0,a.jsxs)(d.nd,{onClick:()=>l("cost"),className:(0,m.cn)("text-right",n?"cursor-pointer":""),"aria-sort":"cost"===s?o:void 0,role:"columnheader",tabIndex:n?0:void 0,onKeyDown:e=>{n&&("Enter"===e.key||" "===e.key)&&(e.preventDefault(),l("cost"))},"aria-label":"Sort by cost",children:["Cost ",c("cost")]}),(0,a.jsx)(d.nd,{className:"print-notes-col",children:"Notes"})]})}),(0,a.jsx)(d.BF,{children:r.map(e=>(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nA,{children:new Date(e.date).toLocaleDateString()}),t&&(0,a.jsxs)(d.nA,{children:[e.vehicleMake," ",e.vehicleModel," (",e.vehicleYear,")",e.vehiclePlateNumber&&(0,a.jsx)("span",{className:"block text-xs text-muted-foreground",children:e.vehiclePlateNumber})]}),(0,a.jsx)(d.nA,{className:"max-w-xs truncate print-service-col",title:e.servicePerformed.join(", "),children:e.servicePerformed.join(", ")}),(0,a.jsx)(d.nA,{children:e.odometer.toLocaleString()}),(0,a.jsx)(d.nA,{className:"text-right",children:e.cost?"$".concat(Number(e.cost).toFixed(2)):"-"}),(0,a.jsx)(d.nA,{className:"max-w-xs truncate print-notes-col",title:e.notes,children:e.notes||"-"})]},e.id))})]})}var v=t(26126);function p(e){let{value:r,label:t,className:s,textColor:n="text-muted-foreground",colSpan:i}=e;return(0,a.jsx)(o.Zp,{className:(0,m.cn)("overflow-hidden",s,i),children:(0,a.jsxs)(o.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-semibold text-card-foreground",children:r}),(0,a.jsx)("p",{className:(0,m.cn)("text-sm",n),children:t})]})})}function x(e){let{records:r,vehicleSpecific:t=!1,className:s}=e,n=r.length,i=r.reduce((e,r)=>e+(Number(r.cost)||0),0),l=r.map(e=>new Date(e.date).getTime()),c=l.length?new Date(Math.min(...l)):null,d=l.length?new Date(Math.max(...l)):null,u=Object.entries(r.reduce((e,r)=>(r.servicePerformed.forEach(r=>{e[r]=(e[r]||0)+1}),e),{})).sort((e,r)=>r[1]-e[1]).slice(0,3),h=t&&r.length>0?Math.max(...r.map(e=>e.odometer))-Math.min(...r.map(e=>e.odometer)):0,f=!t&&r.length>0?new Set(r.map(e=>e.vehicleId)).size:0;return(0,a.jsxs)("div",{className:(0,m.cn)("mt-6 grid grid-cols-2 sm:grid-cols-3 gap-4 summary-grid",s),children:[(0,a.jsx)(p,{value:n,label:"Total Services",className:"border-primary/10"}),(0,a.jsx)(p,{value:"$".concat(i.toFixed(2)),label:"Total Cost",className:"border-primary/10"}),!t&&r.length>0&&(0,a.jsx)(p,{value:f,label:"Vehicles Serviced",className:"border-primary/10"}),t&&r.length>0&&(0,a.jsx)(p,{value:h.toLocaleString(),label:"Odometer Range Covered",className:"border-primary/10"}),r.length>0&&(0,a.jsx)(p,{value:"".concat(null==c?void 0:c.toLocaleDateString()," - ").concat(null==d?void 0:d.toLocaleDateString()),label:"Date Range",className:"border-primary/10",colSpan:"col-span-2 sm:col-span-3"}),u.length>0&&(0,a.jsx)(o.Zp,{className:"overflow-hidden col-span-2 sm:col-span-3 border-primary/10",children:(0,a.jsxs)(o.Wu,{className:"p-4",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold mb-2 text-card-foreground",children:"Top Services"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:u.map(e=>{let[r,t]=e;return(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs px-2 py-1","aria-label":"".concat(r,": ").concat(t," services"),children:[r," (",t,")"]},r)})})]})})]})}var g=t(29797),N=t(77023);function y(e){let{records:r,isLoading:t,error:d,onRetry:u,showVehicleInfo:h=!0,vehicleSpecific:m=!1,className:v}=e,[p,y]=(0,s.useState)("date"),[b,j]=(0,s.useState)("desc"),[w,R]=(0,s.useState)(1),[T]=(0,s.useState)(10),E=(0,s.useMemo)(()=>[...r].sort((e,r)=>{let t,a;switch(p){case"date":t=new Date(e.date).getTime(),a=new Date(r.date).getTime();break;case"cost":t=Number(e.cost)||0,a=Number(r.cost)||0;break;case"odometer":t=e.odometer,a=r.odometer;break;case"servicePerformed":t=e.servicePerformed.join(", ").toLowerCase(),a=r.servicePerformed.join(", ").toLowerCase();break;case"vehicleMake":t="".concat(e.vehicleMake," ").concat(e.vehicleModel).toLowerCase(),a="".concat(r.vehicleMake," ").concat(r.vehicleModel).toLowerCase();break;default:t=e[p],a=r[p]}return t<a?"asc"===b?-1:1:t>a?"asc"===b?1:-1:0}),[r,p,b]),k=(0,s.useCallback)(e=>{p===e?j("asc"===b?"desc":"asc"):(y(e),j("asc"))},[p,b]),_=w*T,A=_-T,O=(0,s.useMemo)(()=>E.slice(A,_),[E,A,_]),I=(0,s.useMemo)(()=>Math.ceil(E.length/T),[E.length,T]),P=(0,s.useCallback)(e=>{R(e)},[]);return((0,s.useEffect)(()=>{R(1)},[r]),t)?(0,a.jsx)("div",{className:"space-y-4","data-testid":"loading-skeleton",children:(0,a.jsx)(N.jt,{variant:"table",count:5})}):d?(0,a.jsxs)(n.Fc,{variant:"destructive",className:"mb-6",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)(n.XL,{children:"Error"}),(0,a.jsx)(n.TN,{children:d}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:u,className:"mt-2","aria-label":"Try loading service records again",children:"Try Again"})]}):0===r.length?(0,a.jsxs)(n.Fc,{variant:"default",className:"mb-6",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)(n.XL,{children:"No Service Records"}),(0,a.jsx)(n.TN,{children:m?"No service records available for this vehicle.":"No service records match your current filters. Try adjusting your search or filter criteria."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(x,{records:E,vehicleSpecific:m}),(0,a.jsx)(o.Zp,{className:"shadow-md card-print",children:(0,a.jsx)(o.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsx)(f,{records:O,showVehicleInfo:h,sortField:p,sortDirection:b,onSort:k})})})}),E.length>T&&(0,a.jsx)("div",{className:"flex justify-center mt-4 no-print",children:(0,a.jsx)(g.$o,{currentPage:w,totalPages:I,onPageChange:P})})]})}},85127:(e,r,t)=>{t.d(r,{A0:()=>i,BF:()=>l,Hj:()=>c,XI:()=>n,nA:()=>u,nd:()=>d});var a=t(95155),s=t(12115),o=t(59434);let n=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:r,className:(0,o.cn)("w-full caption-bottom text-sm",t),...s})})});n.displayName="Table";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("thead",{ref:r,className:(0,o.cn)("[&_tr]:border-b",t),...s})});i.displayName="TableHeader";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("tbody",{ref:r,className:(0,o.cn)("[&_tr:last-child]:border-0",t),...s})});l.displayName="TableBody",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("tfoot",{ref:r,className:(0,o.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...s})}).displayName="TableFooter";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("tr",{ref:r,className:(0,o.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...s})});c.displayName="TableRow";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("th",{ref:r,className:(0,o.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...s})});d.displayName="TableHead";let u=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("td",{ref:r,className:(0,o.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...s})});u.displayName="TableCell",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("caption",{ref:r,className:(0,o.cn)("mt-4 text-sm text-muted-foreground",t),...s})}).displayName="TableCaption"}}]);