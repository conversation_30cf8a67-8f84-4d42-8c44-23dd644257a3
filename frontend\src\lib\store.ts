'use client';

import type {
	Vehicle,
	ServiceRecord,
	Delegation,
	Delegate,
	StatusHistoryEntry,
	DelegationStatus,
	FlightDetails,
	Task,
	Employee,
	TaskStatus,
	DriverAvailability,
	EmployeeStatus,
	EmployeeRole,
} from './types';
import type {DelegationFormData} from './schemas/delegationSchemas';
import {DelegationStatusSchema} from './schemas/delegationSchemas'; // Import Zod schema
import type {TaskFormData} from './schemas/taskSchemas';
// import type { DriverFormData } from './schemas/driverSchemas'; // Driver specific schema is removed
import type {EmployeeFormData} from './schemas/employeeSchemas';

// Import API service functions
import * as apiService from './apiService';

// const VEHICLES_KEY = 'carLifeTrackerVehicles'; // No longer used for API-driven data
const DELEGATIONS_KEY = 'carLifeTrackerDelegations';
const TASKS_KEY = 'carLifeTrackerTasks';
// const EMPLOYEES_KEY = 'carLifeTrackerEmployees'; // No longer used for API-driven data

// Helper to check and update delegation status
const checkAndUpdateDelegationStatus = (
	delegation: Delegation
): {updatedDelegation: Delegation; modified: boolean} => {
	const now = new Date();
	const durationToDate = new Date(delegation.durationTo);
	let modified = false;

	if (
		durationToDate < now &&
		delegation.status !== DelegationStatusSchema.Enum.No_details &&
		delegation.status !== DelegationStatusSchema.Enum.Completed &&
		delegation.status !== DelegationStatusSchema.Enum.Cancelled
	) {
		const statusToSet = DelegationStatusSchema.Enum.No_details;
		const newStatusEntry: StatusHistoryEntry = {
			id: crypto.randomUUID(),
			status: statusToSet,
			changedAt: now.toISOString(),
			reason: 'Delegation duration has passed.',
		};
		const updatedDelegation = {
			...delegation,
			status: statusToSet,
			statusHistory: [...delegation.statusHistory, newStatusEntry],
		};
		modified = true;
		return {updatedDelegation, modified};
	}
	return {updatedDelegation: delegation, modified};
};

// Vehicle Store Functions - Refactored for API
export const getVehicles = async (): Promise<Vehicle[]> => {
	try {
		const vehiclesFromApi = await apiService.getVehicles();
		return vehiclesFromApi.map((v: any) => ({
			...v,
			id: Number(v.id),
			serviceHistory: Array.isArray(v.serviceHistory) ? v.serviceHistory : [],
		}));
	} catch (error) {
		console.error('Store: Error fetching vehicles from API', error);
		throw error;
	}
};

// saveVehicles is no longer needed as data is saved via API calls

export const getVehicleById = async (
	id: number
): Promise<Vehicle | undefined> => {
	try {
		const vehicleFromApi = await apiService.getVehicleById(id);
		if (vehicleFromApi) {
			return {
				...vehicleFromApi,
				id: Number(vehicleFromApi.id),
				serviceHistory: Array.isArray(vehicleFromApi.serviceHistory)
					? vehicleFromApi.serviceHistory
					: [],
			};
		}
		return undefined;
	} catch (error) {
		console.error(
			`Store: Error fetching vehicle with ID ${id} from API`,
			error
		);
		throw error;
	}
};

export const addVehicle = async (
	vehicleData: Omit<
		Vehicle,
		'id' | 'serviceHistory' | 'createdAt' | 'updatedAt'
	>
): Promise<Vehicle> => {
	const dataToCreate = {
		make: vehicleData.make,
		model: vehicleData.model,
		year: vehicleData.year,
		vin: vehicleData.vin,
		licensePlate: vehicleData.licensePlate,
		ownerName: vehicleData.ownerName,
		ownerContact: vehicleData.ownerContact,
		color: vehicleData.color,
		initialOdometer: vehicleData.initialOdometer,
		imageUrl: vehicleData.imageUrl,
	};

	try {
		const newVehicleFromApi = await apiService.createVehicle(dataToCreate);
		return {
			...newVehicleFromApi,
			id: Number(newVehicleFromApi.id),
			serviceHistory: Array.isArray(newVehicleFromApi.serviceHistory)
				? newVehicleFromApi.serviceHistory
				: [],
		};
	} catch (error) {
		console.error('Store: Error adding vehicle via API', error);
		throw error;
	}
};

export const updateVehicle = async (
	id: number,
	updatedVehicleData: Partial<
		Omit<Vehicle, 'id' | 'createdAt' | 'updatedAt' | 'serviceHistory'>
	>
): Promise<Vehicle | undefined> => {
	try {
		const vehicleFromApi = await apiService.updateVehicle(
			id,
			updatedVehicleData
		);
		if (vehicleFromApi) {
			return {
				...vehicleFromApi,
				id: Number(vehicleFromApi.id),
				serviceHistory: Array.isArray(vehicleFromApi.serviceHistory)
					? vehicleFromApi.serviceHistory
					: [],
			};
		}
		return undefined;
	} catch (error) {
		console.error(`Store: Error updating vehicle with ID ${id} via API`, error);
		throw error;
	}
};

export const deleteVehicle = async (id: number): Promise<void> => {
	try {
		await apiService.deleteVehicle(id);
	} catch (error) {
		console.error(`Store: Error deleting vehicle with ID ${id} via API`, error);
		throw error;
	}
};

export const addServiceRecord = async (
	vehicleId: string,
	serviceRecordData: Omit<ServiceRecord, 'id'>
): Promise<Vehicle | undefined> => {
	try {
		const vehicleIdNum = Number(vehicleId);
		if (isNaN(vehicleIdNum)) {
			console.error('Invalid vehicle ID format for adding service record');
			return undefined;
		}

		// Create the service record via API
		await apiService.createServiceRecord(vehicleIdNum, serviceRecordData);

		// Fetch the updated vehicle to get the latest data including the new service record
		const updatedVehicle = await getVehicleById(vehicleIdNum);
		return updatedVehicle;
	} catch (error) {
		console.error(
			`Store: Error adding service record to vehicle ${vehicleId}`,
			error
		);
		return undefined;
	}
};

export const deleteServiceRecord = async (
	vehicleId: string,
	serviceRecordId: string
): Promise<Vehicle | undefined> => {
	try {
		const vehicleIdNum = Number(vehicleId);
		if (isNaN(vehicleIdNum)) {
			console.error('Invalid vehicle ID format for deleting service record');
			return undefined;
		}

		// Delete the service record via API
		await apiService.deleteServiceRecord(vehicleIdNum, serviceRecordId);

		// Fetch the updated vehicle to get the latest data without the deleted service record
		const updatedVehicle = await getVehicleById(vehicleIdNum);
		return updatedVehicle;
	} catch (error) {
		console.error(
			`Store: Error deleting service record ${serviceRecordId} from vehicle ${vehicleId}`,
			error
		);
		return undefined;
	}
};

/**
 * Get all service records across all vehicles
 * @returns An array of service records
 */
export const getAllServiceRecords = async (): Promise<ServiceRecord[]> => {
	try {
		return await apiService.getAllServiceRecords();
	} catch (error) {
		console.error('Store: Error fetching all service records', error);
		throw error;
	}
};

/**
 * Get all service records for a specific vehicle
 * @param vehicleId - The ID of the vehicle
 * @returns An array of service records
 */
export const getServiceRecordsForVehicle = async (
	vehicleId: number
): Promise<ServiceRecord[]> => {
	try {
		return await apiService.getServiceRecords(vehicleId);
	} catch (error) {
		console.error(
			`Store: Error fetching service records for vehicle ${vehicleId}`,
			error
		);
		throw error;
	}
};

/**
 * Get all service records enriched with vehicle information
 * @returns An array of enriched service records
 */
export const getAllEnrichedServiceRecords = async (): Promise<
	EnrichedServiceRecord[]
> => {
	try {
		return await apiService.getAllEnrichedServiceRecords();
	} catch (error) {
		console.error('Store: Error fetching enriched service records', error);
		throw error;
	}
};

// Delegation Store Functions
const processFlightDetailsForStorage = (
	details:
		| DelegationFormData['flightArrivalDetails']
		| DelegationFormData['flightDepartureDetails']
): FlightDetails | null => {
	// Import the date utility functions
	const {formatDateForApi, isValidDateString} = require('./utils/dateUtils');

	if (!details || typeof details !== 'object') return null;

	// Log the incoming details for debugging
	console.debug('Processing flight details for storage:', details);

	const hasFlightNumber =
		details.flightNumber && details.flightNumber.trim() !== '';
	const hasDateTime = details.dateTime && details.dateTime.trim() !== '';
	const hasAirport = details.airport && details.airport.trim() !== '';

	// If all fields are empty, return null (no flight details)
	if (!hasFlightNumber && !hasDateTime && !hasAirport) {
		return null;
	}

	// If any required field is missing but others are present, log warning and return null
	if (!hasFlightNumber || !hasDateTime || !hasAirport) {
		console.warn(
			'Partial flight details provided, but all key fields (flightNumber, dateTime, airport) are required if any are set.',
			{details}
		);
		return null;
	}

	// Format the date using the utility function
	let formattedDateTime;
	try {
		formattedDateTime = formatDateForApi(details.dateTime as string);

		// Validate the formatted date
		if (!formattedDateTime || !isValidDateString(formattedDateTime)) {
			console.warn(
				'Invalid flight dateTime after formatting:',
				details.dateTime,
				formattedDateTime
			);
			return null;
		}
	} catch (error) {
		console.error('Error formatting flight dateTime:', error, details.dateTime);
		return null;
	}

	// Return properly formatted flight details
	return {
		flightNumber: details.flightNumber as string,
		dateTime: formattedDateTime,
		airport: details.airport as string,
		terminal: details.terminal || null,
		notes: details.notes || null,
	};
};

// Get delegations from localStorage (legacy method)
export const getDelegationsFromLocalStorage = (): Delegation[] => {
	if (typeof window === 'undefined') return [];
	try {
		const data = localStorage.getItem(DELEGATIONS_KEY);
		const delegations: Delegation[] = data ? JSON.parse(data) : [];

		let anyModified = false;
		const processedDelegations = delegations.map((del) => {
			const {updatedDelegation, modified} = checkAndUpdateDelegationStatus(del);
			if (modified) {
				anyModified = true;
			}
			return updatedDelegation;
		});

		if (anyModified) {
			saveDelegationsToLocalStorage(processedDelegations);
		}
		return processedDelegations;
	} catch (error) {
		console.error('Error reading delegations from localStorage', error);
		return [];
	}
};

// Save delegations to localStorage (legacy method)
export const saveDelegationsToLocalStorage = (
	delegations: Delegation[]
): void => {
	if (typeof window === 'undefined') return;
	try {
		localStorage.setItem(DELEGATIONS_KEY, JSON.stringify(delegations));
	} catch (error) {
		console.error('Error saving delegations to localStorage', error);
	}
};

// Get delegations from API (new method)
export const getDelegations = async (): Promise<Delegation[]> => {
	try {
		const delegationsFromApi = await apiService.getDelegations();
		const processedDelegations = delegationsFromApi.map((d: any) => ({
			...d,
			id: d.id,
			delegates: Array.isArray(d.delegates) ? d.delegates : [],
			statusHistory: Array.isArray(d.statusHistory) ? d.statusHistory : [],
			durationFrom: d.durationFrom,
			durationTo: d.durationTo,
			flightArrivalDetails: d.flightArrivalDetails || null,
			flightDepartureDetails: d.flightDepartureDetails || null,
		}));

		// Process status updates based on current date
		let anyModified = false;
		const finalDelegations = processedDelegations.map((del) => {
			const {updatedDelegation, modified} = checkAndUpdateDelegationStatus(del);
			if (modified) {
				anyModified = true;
				// Update the delegation in the backend if status changed
				console.log(
					'Updating delegation status automatically for ID:',
					updatedDelegation.id,
					'New status:',
					updatedDelegation.status
				);
				apiService
					.updateDelegation(updatedDelegation.id, {
						status: updatedDelegation.status, // This should be 'No_details' (underscore)
						// statusHistory is managed by the backend on status change if reason is provided.
						// For automatic updates like this, the backend might add its own history entry or not.
						// If we want to ensure the history entry from checkAndUpdateDelegationStatus is saved,
						// we'd need to make sure the backend API can accept statusHistory updates or has logic for it.
						// For now, just sending the status. The backend model handles history if statusChangeReason is present.
					})
					.catch((error) => {
						console.error(
							`Store: Error auto-updating delegation status for ID ${updatedDelegation.id}:`,
							error
						);
					});
			}
			return updatedDelegation;
		});

		return finalDelegations;
	} catch (error) {
		console.error('Store: Error fetching delegations from API', error);
		// Fallback to localStorage if API fails
		console.warn('Falling back to localStorage for delegations');
		return getDelegationsFromLocalStorage();
	}
};

// Save delegations (new method - updates via API)
export const saveDelegations = async (
	delegations: Delegation[]
): Promise<void> => {
	// This function is kept for backward compatibility
	// In the new API-based approach, individual delegations are saved via
	// addDelegation, updateDelegation, or deleteDelegation functions
	console.warn(
		'saveDelegations is deprecated. Use individual API calls instead.'
	);

	// For backward compatibility, still save to localStorage
	saveDelegationsToLocalStorage(delegations);
};

// Get delegation by ID from localStorage (legacy method)
export const getDelegationByIdFromLocalStorage = (
	id: string
): Delegation | undefined => {
	const delegations = getDelegationsFromLocalStorage();
	return delegations.find((delegation) => delegation.id === id);
};

// Get delegation by ID from API (new method)
export const getDelegationById = async (
	id: string
): Promise<Delegation | undefined> => {
	try {
		const delegationFromApi = await apiService.getDelegationById(id);
		if (delegationFromApi) {
			// Process the fetched delegation
			const processedDelegation = {
				...delegationFromApi,
				delegates: Array.isArray(delegationFromApi.delegates)
					? delegationFromApi.delegates
					: [],
				statusHistory: Array.isArray(delegationFromApi.statusHistory)
					? delegationFromApi.statusHistory
					: [],
				flightArrivalDetails: delegationFromApi.flightArrivalDetails || null,
				flightDepartureDetails:
					delegationFromApi.flightDepartureDetails || null,
			};
			// Check and update status if needed (this also calls API if modified)
			const {updatedDelegation} =
				checkAndUpdateDelegationStatus(processedDelegation);
			return updatedDelegation;
		}
		return undefined;
	} catch (error) {
		console.error(
			`Store: Error fetching delegation with ID ${id} from API`,
			error
		);
		// Fallback to localStorage if API fails
		console.warn(`Falling back to localStorage for delegation ID ${id}`);
		const fromStorage = getDelegationByIdFromLocalStorage(id);
		if (fromStorage) {
			const {updatedDelegation} = checkAndUpdateDelegationStatus(fromStorage);
			return updatedDelegation;
		}
		return undefined;
	}
};

// Add delegation to localStorage (legacy method)
export const addDelegationToLocalStorage = (
	delegationData: DelegationFormData
): Delegation => {
	const delegations = getDelegationsFromLocalStorage();
	const initialStatus =
		delegationData.status || DelegationStatusSchema.Enum.Planned;

	const newDelegation: Delegation = {
		id: crypto.randomUUID(),
		eventName: delegationData.eventName,
		location: delegationData.location,
		durationFrom: new Date(delegationData.durationFrom).toISOString(),
		durationTo: new Date(delegationData.durationTo).toISOString(),
		invitationFrom: delegationData.invitationFrom,
		invitationTo: delegationData.invitationTo,
		delegates: delegationData.delegates.map((d) => ({
			...d,
			id: d.id || crypto.randomUUID(),
		})),
		flightArrivalDetails: processFlightDetailsForStorage(
			delegationData.flightArrivalDetails
		),
		flightDepartureDetails: processFlightDetailsForStorage(
			delegationData.flightDepartureDetails
		),
		status: initialStatus,
		statusHistory: [
			{
				id: crypto.randomUUID(),
				status: initialStatus,
				changedAt: new Date().toISOString(),
				reason: 'Delegation created',
			},
		],
		notes: delegationData.notes,
		imageUrl:
			delegationData.imageUrl ||
			`https://picsum.photos/seed/${encodeURIComponent(
				delegationData.eventName
			)}/400/250`,
	};
	const updatedDelegations = [...delegations, newDelegation];
	saveDelegationsToLocalStorage(updatedDelegations);
	return newDelegation;
};

// Add delegation via API (new method)
export const addDelegation = async (
	delegationData: DelegationFormData
): Promise<Delegation> => {
	try {
		const initialStatus =
			delegationData.status || DelegationStatusSchema.Enum.Planned;

		// Import formatDateForApi for proper date conversion
		const {formatDateForApi} = await import('@/lib/utils/dateUtils');

		// Prepare data for API
		const dataToCreate = {
			eventName: delegationData.eventName,
			location: delegationData.location,
			// Use formatDateForApi to properly convert date inputs to ISO datetime
			durationFrom: formatDateForApi(delegationData.durationFrom),
			durationTo: formatDateForApi(delegationData.durationTo),
			invitationFrom: delegationData.invitationFrom,
			invitationTo: delegationData.invitationTo,
			delegates: delegationData.delegates.map((d) => {
				// Remove id field for creation as backend schema omits it
				const {id, ...delegateWithoutId} = d;
				return delegateWithoutId;
			}),
			flightArrivalDetails: processFlightDetailsForStorage(
				delegationData.flightArrivalDetails
			),
			flightDepartureDetails: processFlightDetailsForStorage(
				delegationData.flightDepartureDetails
			),
			status: initialStatus, // Ensure this uses the Zod enum value
			notes: delegationData.notes,
			imageUrl:
				delegationData.imageUrl ||
				`https://picsum.photos/seed/${encodeURIComponent(
					delegationData.eventName
				)}/400/250`,
		};

		// Validate that the dates were converted properly
		if (!dataToCreate.durationFrom || !dataToCreate.durationTo) {
			throw new Error(
				'Invalid date format provided. Please ensure dates are valid.'
			);
		}

		// Create delegation via API
		console.log('=== DELEGATION PAYLOAD DEBUG ===');
		console.log(
			'durationFrom:',
			dataToCreate.durationFrom,
			'Type:',
			typeof dataToCreate.durationFrom
		);
		console.log(
			'durationTo:',
			dataToCreate.durationTo,
			'Type:',
			typeof dataToCreate.durationTo
		);
		console.log('Flight arrival details:', dataToCreate.flightArrivalDetails);
		console.log(
			'Flight departure details:',
			dataToCreate.flightDepartureDetails
		);
		console.log('Complete payload:', JSON.stringify(dataToCreate, null, 2));
		console.log('=== END DELEGATION PAYLOAD DEBUG ===');

		const newDelegationFromApi = await apiService.createDelegation(
			dataToCreate
		);

		// Format the response to match the expected Delegation interface
		const newDelegation: Delegation = {
			...newDelegationFromApi,
			delegates: Array.isArray(newDelegationFromApi.delegates)
				? newDelegationFromApi.delegates
				: [],
			statusHistory: Array.isArray(newDelegationFromApi.statusHistory)
				? newDelegationFromApi.statusHistory
				: [],
			flightArrivalDetails: newDelegationFromApi.flightArrivalDetails || null,
			flightDepartureDetails:
				newDelegationFromApi.flightDepartureDetails || null,
		};

		return newDelegation;
	} catch (error) {
		console.error('Store: Error adding delegation via API', error);
		// Fallback to localStorage if API fails
		console.warn('Falling back to localStorage for adding delegation');
		return addDelegationToLocalStorage(delegationData);
	}
};

// Update delegation in localStorage (legacy method)
export const updateDelegationInLocalStorage = (
	id: string,
	delegationData: DelegationFormData
): Delegation | undefined => {
	const delegations = getDelegationsFromLocalStorage();
	const index = delegations.findIndex((d) => d.id === id);
	if (index === -1) return undefined;

	const existingDelegation = delegations[index];

	const updatedDelegationData: Partial<Delegation> = {
		...existingDelegation,
		...delegationData,
		durationFrom: new Date(delegationData.durationFrom).toISOString(),
		durationTo: new Date(delegationData.durationTo).toISOString(),
		delegates: delegationData.delegates.map((d) => ({
			...d,
			id: d.id || crypto.randomUUID(),
		})),
		flightArrivalDetails: processFlightDetailsForStorage(
			delegationData.flightArrivalDetails
		),
		flightDepartureDetails: processFlightDetailsForStorage(
			delegationData.flightDepartureDetails
		),
		status: delegationData.status, // Should be from Zod enum (underscore)
	};

	if (
		delegationData.status &&
		delegationData.status !== existingDelegation.status
	) {
		const newStatusEntry: StatusHistoryEntry = {
			id: crypto.randomUUID(),
			status: delegationData.status,
			changedAt: new Date().toISOString(),
			reason: delegationData.statusChangeReason || 'Status updated manually',
		};
		updatedDelegationData.statusHistory = [
			...(existingDelegation.statusHistory || []),
			newStatusEntry,
		];
	} else {
		updatedDelegationData.statusHistory = existingDelegation.statusHistory;
	}

	delegations[index] = updatedDelegationData as Delegation;
	saveDelegationsToLocalStorage(delegations);
	return delegations[index];
};

// Update delegation via API (new method)
export const updateDelegation = async (
	id: string,
	delegationData: DelegationFormData // This comes from the form, status should be underscore version
): Promise<Delegation | undefined> => {
	try {
		// Import the date utility function
		const {formatDateForApi} = require('./utils/dateUtils');

		// Prepare data for API, ensuring status is the correct enum value
		const dataToUpdate = {
			eventName: delegationData.eventName,
			location: delegationData.location,
			durationFrom: formatDateForApi(delegationData.durationFrom),
			durationTo: formatDateForApi(delegationData.durationTo),
			invitationFrom: delegationData.invitationFrom,
			invitationTo: delegationData.invitationTo,
			delegates: delegationData.delegates.map((d) => {
				// For updates, keep existing IDs if present, but don't generate new ones unnecessarily
				const {id, ...delegateWithoutId} = d;
				return d.id ? {id: d.id, ...delegateWithoutId} : delegateWithoutId;
			}),
			notes: delegationData.notes,
			imageUrl: delegationData.imageUrl,
			status: delegationData.status, // This should be the Zod enum value ('No_details')
			statusChangeReason: delegationData.statusChangeReason,
			// Explicitly pass flight details, ensuring null if empty/invalid
			flightArrivalDetails: processFlightDetailsForStorage(
				delegationData.flightArrivalDetails
			),
			flightDepartureDetails: processFlightDetailsForStorage(
				delegationData.flightDepartureDetails
			),
		};

		// Log the data being sent to the API for debugging
		console.debug(
			`Store: Updating delegation ${id} with data:`,
			JSON.stringify(dataToUpdate, null, 2)
		);

		// Update delegation via API
		const updatedDelegationFromApi = await apiService.updateDelegation(
			id,
			dataToUpdate
		);

		if (updatedDelegationFromApi) {
			// Format the response to match the expected Delegation interface
			return {
				...updatedDelegationFromApi,
				delegates: Array.isArray(updatedDelegationFromApi.delegates)
					? updatedDelegationFromApi.delegates
					: [],
				statusHistory: Array.isArray(updatedDelegationFromApi.statusHistory)
					? updatedDelegationFromApi.statusHistory
					: [],
				flightArrivalDetails:
					updatedDelegationFromApi.flightArrivalDetails || null,
				flightDepartureDetails:
					updatedDelegationFromApi.flightDepartureDetails || null,
			};
		}

		return undefined;
	} catch (error) {
		console.error(
			`Store: Error updating delegation with ID ${id} via API`,
			error
		);
		// Fallback to localStorage if API fails
		console.warn(
			`Falling back to localStorage for updating delegation ID ${id}`
		);
		return updateDelegationInLocalStorage(id, delegationData);
	}
};

// Update delegation status in localStorage (legacy method)
export const updateDelegationStatusInLocalStorage = (
	id: string,
	newStatus: DelegationStatus, // Expects 'No_details'
	reason?: string
): Delegation | undefined => {
	const delegations = getDelegationsFromLocalStorage();
	const index = delegations.findIndex((d) => d.id === id);
	if (index === -1) return undefined;

	const existingDelegation = delegations[index];
	const newStatusEntry: StatusHistoryEntry = {
		id: crypto.randomUUID(),
		status: newStatus, // Use the correct enum value
		changedAt: new Date().toISOString(),
		reason: reason || 'Status updated',
	};

	delegations[index] = {
		...existingDelegation,
		status: newStatus, // Use the correct enum value
		statusHistory: [...existingDelegation.statusHistory, newStatusEntry],
	};
	saveDelegationsToLocalStorage(delegations);
	return delegations[index];
};

// Update delegation status via API (new method)
export const updateDelegationStatus = async (
	id: string,
	newStatus: DelegationStatus, // Expects 'No_details'
	reason?: string
): Promise<Delegation | undefined> => {
	try {
		// Prepare data for API
		const dataToUpdate = {
			status: newStatus, // Should be 'No_details'
			statusChangeReason: reason || 'Status updated',
		};

		// Update delegation via API
		const updatedDelegationFromApi = await apiService.updateDelegation(
			id,
			dataToUpdate
		);

		if (updatedDelegationFromApi) {
			// Format the response to match the expected Delegation interface
			return {
				...updatedDelegationFromApi,
				delegates: Array.isArray(updatedDelegationFromApi.delegates)
					? updatedDelegationFromApi.delegates
					: [],
				statusHistory: Array.isArray(updatedDelegationFromApi.statusHistory)
					? updatedDelegationFromApi.statusHistory
					: [],
				flightArrivalDetails:
					updatedDelegationFromApi.flightArrivalDetails || null,
				flightDepartureDetails:
					updatedDelegationFromApi.flightDepartureDetails || null,
			};
		}

		return undefined;
	} catch (error) {
		console.error(
			`Store: Error updating delegation status for ID ${id} via API`,
			error
		);
		// Fallback to localStorage if API fails
		console.warn(
			`Falling back to localStorage for updating delegation status for ID ${id}`
		);
		return updateDelegationStatusInLocalStorage(id, newStatus, reason);
	}
};

// Delete delegation from localStorage (legacy method)
export const deleteDelegationFromLocalStorage = (id: string): void => {
	let delegations = getDelegationsFromLocalStorage();
	delegations = delegations.filter((d) => d.id !== id);
	saveDelegationsToLocalStorage(delegations);
};

// Delete delegation via API (new method)
export const deleteDelegation = async (id: string): Promise<void> => {
	try {
		// Delete delegation via API
		await apiService.deleteDelegation(id);
	} catch (error) {
		console.error(
			`Store: Error deleting delegation with ID ${id} via API`,
			error
		);
		// Fallback to localStorage if API fails
		console.warn(
			`Falling back to localStorage for deleting delegation ID ${id}`
		);
		deleteDelegationFromLocalStorage(id);
	}
};

export const getTasks = async (): Promise<Task[]> => {
	try {
		const tasksFromApi = await apiService.getTasks();
		return tasksFromApi.map((t: any) => ({
			...t,
			statusHistory: Array.isArray(t.statusHistory) ? t.statusHistory : [],
			subTasks: Array.isArray(t.subTasks) ? t.subTasks : [],
			assignedTo: Array.isArray(t.assignedEmployees)
				? t.assignedEmployees.map((e: any) => String(e.id))
				: [],
		}));
	} catch (error) {
		console.error('Store: Error fetching tasks from API', error);
		throw error;
	}
};

// This function is kept for backward compatibility but will be deprecated
const saveTasks = (tasks: Task[]): void => {
	console.warn('saveTasks is deprecated as data is now saved via API calls');
};

export const getTaskById = async (id: string): Promise<Task | undefined> => {
	try {
		const taskFromApi = await apiService.getTaskById(id);
		if (taskFromApi) {
			return {
				...taskFromApi,
				statusHistory: Array.isArray(taskFromApi.statusHistory)
					? taskFromApi.statusHistory
					: [],
				subTasks: Array.isArray(taskFromApi.subTasks)
					? taskFromApi.subTasks
					: [],
				assignedTo: Array.isArray(taskFromApi.assignedEmployees)
					? taskFromApi.assignedEmployees.map((e: any) => String(e.id))
					: [],
			};
		}
		return undefined;
	} catch (error) {
		console.error(`Store: Error fetching task with ID ${id} from API`, error);
		throw error;
	}
};

export const addTask = async (taskData: TaskFormData): Promise<Task> => {
	try {
		// Prepare data for API
		const dataToCreate = {
			description: taskData.description,
			location: taskData.location,
			dateTime: new Date(taskData.dateTime).toISOString(),
			estimatedDuration: taskData.estimatedDuration,
			requiredSkills: taskData.requiredSkills || [],
			priority: taskData.priority || 'Medium',
			deadline: taskData.deadline
				? new Date(taskData.deadline).toISOString()
				: undefined,
			status: taskData.status || 'Pending',
			notes: taskData.notes,
			vehicleId: taskData.vehicleId ? Number(taskData.vehicleId) : undefined,
			// Handle employee assignments if needed
			assignedEmployeeIds: taskData.assignedTo || [],
			subTasks: taskData.subTasks
				? taskData.subTasks.map((st) => ({
						...st,
						id: st.id || undefined, // Let the backend generate IDs
						completed: st.completed || false,
				  }))
				: [],
		};

		// Create task via API
		const newTaskFromApi = await apiService.createTask(dataToCreate);

		// Format the response to match the expected Task interface
		const newTask: Task = {
			...newTaskFromApi,
			statusHistory: Array.isArray(newTaskFromApi.statusHistory)
				? newTaskFromApi.statusHistory
				: [],
			subTasks: Array.isArray(newTaskFromApi.subTasks)
				? newTaskFromApi.subTasks
				: [],
			assignedTo: Array.isArray(newTaskFromApi.assignedEmployees)
				? newTaskFromApi.assignedEmployees.map((e: any) => String(e.id))
				: [],
		};

		return newTask;
	} catch (error) {
		console.error('Store: Error adding task via API', error);
		throw error;
	}
};

export const updateTask = async (
	id: string,
	taskData: Partial<TaskFormData>
): Promise<Task | undefined> => {
	try {
		// Prepare data for API
		const dataToUpdate = {
			description: taskData.description,
			location: taskData.location,
			dateTime: taskData.dateTime
				? new Date(taskData.dateTime).toISOString()
				: undefined,
			estimatedDuration: taskData.estimatedDuration,
			requiredSkills: taskData.requiredSkills,
			priority: taskData.priority,
			deadline: taskData.deadline
				? new Date(taskData.deadline).toISOString()
				: undefined,
			status: taskData.status,
			notes: taskData.notes,
			vehicleId: taskData.vehicleId ? Number(taskData.vehicleId) : undefined,
			// Handle employee assignments if needed
			assignedEmployeeIds: taskData.assignedTo,
			subTasks: taskData.subTasks,
			statusChangeReason: taskData.statusChangeReason,
		};

		// Remove undefined values to avoid overwriting existing data with null
		Object.keys(dataToUpdate).forEach((key) => {
			if (dataToUpdate[key] === undefined) {
				delete dataToUpdate[key];
			}
		});

		// Update task via API
		const updatedTaskFromApi = await apiService.updateTask(id, dataToUpdate);

		if (updatedTaskFromApi) {
			// Format the response to match the expected Task interface
			const updatedTask: Task = {
				...updatedTaskFromApi,
				statusHistory: Array.isArray(updatedTaskFromApi.statusHistory)
					? updatedTaskFromApi.statusHistory
					: [],
				subTasks: Array.isArray(updatedTaskFromApi.subTasks)
					? updatedTaskFromApi.subTasks
					: [],
				assignedTo: Array.isArray(updatedTaskFromApi.assignedEmployees)
					? updatedTaskFromApi.assignedEmployees.map((e: any) => String(e.id))
					: [],
			};

			return updatedTask;
		}

		return undefined;
	} catch (error) {
		console.error(`Store: Error updating task with ID ${id} via API`, error);
		throw error;
	}
};

export const deleteTask = async (id: string): Promise<void> => {
	try {
		// Delete task via API
		await apiService.deleteTask(id);

		// No need to manually update employee assignments as the backend should handle this
	} catch (error) {
		console.error(`Store: Error deleting task with ID ${id} via API`, error);
		throw error;
	}
};

const getEmployeesInternal = (): Employee[] => {
	if (typeof window === 'undefined') return [];
	try {
		const data = localStorage.getItem('carLifeTrackerEmployees_INTERNAL_CACHE');
		return data ? JSON.parse(data) : [];
	} catch (error) {
		console.error(
			'Error reading internal employee cache from localStorage',
			error
		);
		return [];
	}
};

const saveEmployeesInternal = (employees: Employee[]): void => {
	if (typeof window === 'undefined') return;
	try {
		localStorage.setItem(
			'carLifeTrackerEmployees_INTERNAL_CACHE',
			JSON.stringify(employees)
		);
	} catch (error) {
		console.error(
			'Error saving internal employee cache to localStorage',
			error
		);
	}
};

export const getEmployees = async (): Promise<Employee[]> => {
	try {
		const employeesFromApi = await apiService.getEmployees();
		const employees = employeesFromApi.map((e: any) => ({
			...e,
			id: Number(e.id),
			// Map assignedVehicleId from backend to vehicleId for frontend
			vehicleId: e.assignedVehicleId ? String(e.assignedVehicleId) : null,
			assignedTasks: Array.isArray(e.assignedTasks) ? e.assignedTasks : [],
			statusHistory: Array.isArray(e.statusHistory) ? e.statusHistory : [],
			createdAt: e.createdAt || new Date(0).toISOString(),
			updatedAt: e.updatedAt || new Date(0).toISOString(),
		}));
		saveEmployeesInternal(employees);
		return employees;
	} catch (error) {
		console.error('Store: Error fetching employees from API', error);
		throw error;
	}
};

/**
 * Get all employees enriched with vehicle information
 * @returns An array of enriched employees
 */
export const getEnrichedEmployees = async (): Promise<Employee[]> => {
	try {
		// Import the employee service dynamically to avoid circular dependencies
		const {getEnrichedEmployees} = await import('./services/employeeService');
		const enrichedEmployees = await getEnrichedEmployees();

		// Process the enriched employees
		const employees = enrichedEmployees.map((e: any) => ({
			...e,
			id: Number(e.id),
			// Map assignedVehicleId from backend to vehicleId for frontend
			vehicleId: e.assignedVehicleId ? String(e.assignedVehicleId) : null,
			assignedTasks: Array.isArray(e.assignedTasks) ? e.assignedTasks : [],
			statusHistory: Array.isArray(e.statusHistory) ? e.statusHistory : [],
			createdAt: e.createdAt || new Date(0).toISOString(),
			updatedAt: e.updatedAt || new Date(0).toISOString(),
		}));

		// Save to internal cache
		saveEmployeesInternal(employees);
		return employees;
	} catch (error) {
		console.error('Store: Error fetching enriched employees', error);
		// Fall back to regular employees if enriched endpoint fails
		return getEmployees();
	}
};

export const getEmployeeById = async (
	id: number
): Promise<Employee | undefined> => {
	try {
		const employeeFromApi = await apiService.getEmployeeById(id);
		if (employeeFromApi) {
			return {
				...employeeFromApi,
				id: Number(employeeFromApi.id),
				// Map assignedVehicleId from backend to vehicleId for frontend
				vehicleId: employeeFromApi.assignedVehicleId
					? String(employeeFromApi.assignedVehicleId)
					: null,
				assignedTasks: Array.isArray(employeeFromApi.assignedTasks)
					? employeeFromApi.assignedTasks
					: [],
				statusHistory: Array.isArray(employeeFromApi.statusHistory)
					? employeeFromApi.statusHistory
					: [],
				createdAt: employeeFromApi.createdAt || new Date(0).toISOString(),
				updatedAt: employeeFromApi.updatedAt || new Date(0).toISOString(),
			};
		}
		return undefined;
	} catch (error) {
		console.error(
			`Store: Error fetching employee with ID ${id} from API`,
			error
		);
		throw error;
	}
};

export const addEmployee = async (
	employeeData: EmployeeFormData
): Promise<Employee> => {
	// Format the hire date to ISO string format as required by the backend
	let formattedHireDate = employeeData.hireDate;
	if (employeeData.hireDate) {
		// If the date is in YYYY-MM-DD format (from HTML date input), convert to ISO string
		if (employeeData.hireDate.includes('T')) {
			// Already in ISO format
			formattedHireDate = employeeData.hireDate;
		} else {
			// Convert YYYY-MM-DD to ISO format by adding time component
			formattedHireDate = new Date(
				employeeData.hireDate + 'T00:00:00.000Z'
			).toISOString();
		}
	}

	const dataToCreate = {
		name: employeeData.name,
		role: employeeData.role,
		employeeId: employeeData.employeeId,
		contactInfo: employeeData.contactInfo,
		position: employeeData.position,
		department: employeeData.department,
		hireDate: formattedHireDate,
		status: employeeData.status,
		availability: employeeData.availability,
	};

	try {
		const newEmployeeFromApi = await apiService.createEmployee(dataToCreate);
		const feEmployee: Employee = {
			...newEmployeeFromApi,
			id: Number(newEmployeeFromApi.id),
			fullName: employeeData.name,
			assignedTasks: [],
			statusHistory: [
				{
					id: crypto.randomUUID(),
					status: employeeData.status || ('Active' as EmployeeStatus),
					changedAt: new Date().toISOString(),
					reason: 'Employee created',
				},
			],
			contactPhone: '',
			contactMobile: '',
			contactEmail: employeeData.contactInfo || '',
			skills: employeeData.skills || [],
			shiftSchedule: employeeData.shiftSchedule || '',
			generalAssignments: employeeData.generalAssignments || [],
			notes: employeeData.notes,
			profileImageUrl: employeeData.profileImageUrl,
			createdAt: newEmployeeFromApi.createdAt || new Date().toISOString(),
			updatedAt: newEmployeeFromApi.updatedAt || new Date().toISOString(),
		};
		let internalEmployees = getEmployeesInternal();
		internalEmployees.push(feEmployee);
		saveEmployeesInternal(internalEmployees);
		return feEmployee;
	} catch (error) {
		console.error('Store: Error adding employee via API', error);
		throw error;
	}
};

export const updateEmployee = async (
	id: number,
	employeeData: Partial<EmployeeFormData> & {statusChangeReason?: string}
): Promise<Employee | undefined> => {
	const {...dataToUpdate} = employeeData;

	try {
		const updatedEmployeeFromApi = await apiService.updateEmployee(
			id,
			dataToUpdate
		);
		if (updatedEmployeeFromApi) {
			let internalEmployees = getEmployeesInternal();
			const index = internalEmployees.findIndex(
				(e: Employee) => e.id === Number(updatedEmployeeFromApi.id)
			);
			if (index !== -1) {
				const existingLocal = internalEmployees[index];
				const mergedEmployee: Employee = {
					...existingLocal,
					...updatedEmployeeFromApi,
					id: Number(updatedEmployeeFromApi.id),
					status:
						(dataToUpdate.status as EmployeeStatus) || existingLocal.status,
					assignedTasks:
						existingLocal.assignedTasks ||
						updatedEmployeeFromApi.assignedTasks ||
						[],
					statusHistory:
						existingLocal.statusHistory ||
						updatedEmployeeFromApi.statusHistory ||
						[],
					createdAt:
						updatedEmployeeFromApi.createdAt || existingLocal.createdAt,
					updatedAt:
						updatedEmployeeFromApi.updatedAt || new Date().toISOString(),
				};

				if (
					dataToUpdate.status &&
					dataToUpdate.status !== existingLocal.status
				) {
					mergedEmployee.statusHistory = [
						...(existingLocal.statusHistory || []),
						{
							id: crypto.randomUUID(),
							status: dataToUpdate.status as EmployeeStatus,
							changedAt: new Date().toISOString(),
							reason: employeeData.statusChangeReason || 'Status updated',
						},
					];
				}

				internalEmployees[index] = mergedEmployee;
				saveEmployeesInternal(internalEmployees);
				return mergedEmployee;
			}
			return {
				...updatedEmployeeFromApi,
				id: Number(updatedEmployeeFromApi.id),
				fullName: updatedEmployeeFromApi.name || '',
				position: updatedEmployeeFromApi.position || '',
				department: updatedEmployeeFromApi.department || '',
				contactPhone: '',
				contactMobile: '',
				contactEmail: updatedEmployeeFromApi.contactInfo || '',
				hireDate: updatedEmployeeFromApi.hireDate || new Date(0).toISOString(),
				statusHistory: Array.isArray(updatedEmployeeFromApi.statusHistory)
					? updatedEmployeeFromApi.statusHistory
					: [],
				assignedTasks: Array.isArray(updatedEmployeeFromApi.assignedTasks)
					? updatedEmployeeFromApi.assignedTasks
					: [],
				skills: updatedEmployeeFromApi.skills || [],
				shiftSchedule: updatedEmployeeFromApi.shiftSchedule || '',
				generalAssignments: updatedEmployeeFromApi.generalAssignments || [],
				notes: updatedEmployeeFromApi.notes || '',
				profileImageUrl: updatedEmployeeFromApi.profileImageUrl || '',
				createdAt:
					updatedEmployeeFromApi.createdAt || new Date(0).toISOString(),
				updatedAt: updatedEmployeeFromApi.updatedAt || new Date().toISOString(),
			};
		}
		return undefined;
	} catch (error) {
		console.error(
			`Store: Error updating employee with ID ${id} via API`,
			error
		);
		throw error;
	}
};

export const deleteEmployee = async (id: number): Promise<void> => {
	try {
		await apiService.deleteEmployee(id);
		let internalEmployees = getEmployeesInternal();
		internalEmployees = internalEmployees.filter((e: Employee) => e.id !== id);
		saveEmployeesInternal(internalEmployees);

		let tasks = await getTasks(); // Fetch tasks as it's async now
		let tasksModified = false;
		tasks.forEach((task: Task) => {
			if (task.assignedTo && task.assignedTo.includes(String(id))) {
				task.assignedTo = task.assignedTo.filter(
					(empId: string) => empId !== String(id)
				);
				tasksModified = true;
			}
		});
		if (tasksModified) {
			// This saveTasks is deprecated and would need to be an API call
			// For now, it will just log a warning
			saveTasks(tasks);
		}
	} catch (error) {
		console.error(
			`Store: Error deleting employee with ID ${id} via API`,
			error
		);
		throw error;
	}
};

export const assignTaskToEmployee = async (
	taskId: string,
	employeeId: string,
	saveTaskData: boolean = true
): Promise<{task?: Task; employee?: Employee}> => {
	let tasks = await getTasks(); // Fix: await the async getTasks function
	let employees = getEmployeesInternal();

	// Add defensive check to ensure tasks is an array
	if (!Array.isArray(tasks)) {
		console.warn('Tasks is not an array, cannot assign task. Received:', tasks);
		return {};
	}

	const taskIndex = tasks.findIndex((t: Task) => t.id === taskId);
	const employeeIndex = employees.findIndex(
		(e: Employee) => e.id === Number(employeeId)
	);

	if (taskIndex === -1 || employeeIndex === -1) {
		console.warn('Task or Employee not found for assignment');
		return {};
	}

	const task = tasks[taskIndex];
	const employee = employees[employeeIndex];

	// Ensure assignedTo is always an array
	if (!task.assignedTo || !Array.isArray(task.assignedTo)) {
		task.assignedTo = [];
	}

	// Check if already assigned
	if (task.assignedTo.includes(String(employee.id))) {
		console.log('Task already assigned to this employee');
		return {task, employee};
	}

	// Add the assignment
	task.assignedTo.push(String(employee.id));
	task.updatedAt = new Date().toISOString();

	// Ensure assignedTasks is always an array
	if (!employee.assignedTasks || !Array.isArray(employee.assignedTasks)) {
		employee.assignedTasks = [];
	}

	if (!employee.assignedTasks.includes(taskId)) {
		employee.assignedTasks.push(taskId);
	}

	// Save task assignment to backend via API
	if (saveTaskData) {
		try {
			// Call API service directly to update task assignment
			const updatedTask = await apiService.updateTask(taskId, {
				assignedEmployeeIds: task.assignedTo.map((id) => Number(id)),
				status: 'Assigned',
			});

			if (updatedTask) {
				// Update the local task object with the response from API
				Object.assign(task, updatedTask);
			}
		} catch (error) {
			console.error('Error saving task assignment to backend:', error);
			// Revert local changes if API call fails
			task.assignedTo = task.assignedTo.filter(
				(id) => id !== String(employee.id)
			);
			employee.assignedTasks = employee.assignedTasks.filter(
				(id) => id !== taskId
			);
			throw error;
		}
	}

	// Save employee changes to local cache
	saveEmployeesInternal(employees);

	return {task, employee};
};

export const unassignTaskFromEmployee = async (
	taskId: string,
	employeeIdToUnassignFrom?: string,
	saveTaskData: boolean = true
): Promise<{task?: Task; employee?: Employee}> => {
	let tasks = await getTasks(); // Fix: await the async getTasks function
	let employees = getEmployeesInternal();

	// Add defensive check to ensure tasks is an array
	if (!Array.isArray(tasks)) {
		console.warn(
			'Tasks is not an array, cannot unassign task. Received:',
			tasks
		);
		return {};
	}

	const taskIndex = tasks.findIndex((t: Task) => t.id === taskId);

	if (taskIndex === -1) {
		console.warn('Task not found for unassignment');
		return {};
	}
	const task = tasks[taskIndex];

	// Ensure task.assignedTo is always an array
	if (!task.assignedTo || !Array.isArray(task.assignedTo)) {
		task.assignedTo = [];
	}

	if (employeeIdToUnassignFrom) {
		const employeeIndex = employees.findIndex(
			(e: Employee) => e.id === Number(employeeIdToUnassignFrom)
		);
		if (employeeIndex !== -1) {
			// Ensure employee.assignedTasks is always an array
			if (
				!employees[employeeIndex].assignedTasks ||
				!Array.isArray(employees[employeeIndex].assignedTasks)
			) {
				employees[employeeIndex].assignedTasks = [];
			} else {
				employees[employeeIndex].assignedTasks = employees[
					employeeIndex
				].assignedTasks.filter((tId: string) => tId !== taskId);
			}

			task.assignedTo = task.assignedTo.filter(
				(eId: string) => eId !== String(employees[employeeIndex].id)
			);

			task.updatedAt = new Date().toISOString();

			// Save changes to backend and local storage
			if (saveTaskData) {
				try {
					const updatedTask = await apiService.updateTask(taskId, {
						assignedEmployeeIds: task.assignedTo.map((id) => Number(id)),
						status: task.assignedTo.length > 0 ? 'Assigned' : 'Pending',
					});

					if (updatedTask) {
						Object.assign(task, updatedTask);
					}
				} catch (error) {
					console.error('Error saving task unassignment to backend:', error);
					// Revert local changes if API call fails
					task.assignedTo.push(String(employees[employeeIndex].id));
					employees[employeeIndex].assignedTasks.push(taskId);
					throw error;
				}
			}

			saveEmployeesInternal(employees);
			return {task, employee: employees[employeeIndex]};
		}
	} else {
		// Handle all employees assigned to this task
		const originalAssignedTo = [...task.assignedTo]; // Save for potential rollback

		(task.assignedTo || []).forEach((empId: string) => {
			const employeeIndex = employees.findIndex(
				(e: Employee) => e.id === Number(empId)
			);
			if (employeeIndex !== -1) {
				// Ensure employee.assignedTasks is always an array
				if (
					!employees[employeeIndex].assignedTasks ||
					!Array.isArray(employees[employeeIndex].assignedTasks)
				) {
					employees[employeeIndex].assignedTasks = [];
				} else {
					employees[employeeIndex].assignedTasks = employees[
						employeeIndex
					].assignedTasks.filter((tId: string) => tId !== taskId);
				}
			}
		});

		task.assignedTo = [];
		task.updatedAt = new Date().toISOString();

		// Save changes to backend
		if (saveTaskData) {
			try {
				const updatedTask = await apiService.updateTask(taskId, {
					assignedEmployeeIds: [],
					status: 'Pending',
				});

				if (updatedTask) {
					Object.assign(task, updatedTask);
				}
			} catch (error) {
				console.error('Error saving task unassignment to backend:', error);
				// Revert local changes if API call fails
				task.assignedTo = originalAssignedTo;
				originalAssignedTo.forEach((empId: string) => {
					const employeeIndex = employees.findIndex(
						(e: Employee) => e.id === Number(empId)
					);
					if (employeeIndex !== -1) {
						employees[employeeIndex].assignedTasks.push(taskId);
					}
				});
				throw error;
			}
		}

		saveEmployeesInternal(employees);
		return {task};
	}
	return {task};
};
