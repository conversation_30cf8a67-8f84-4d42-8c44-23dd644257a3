import * as serviceRecordService from '../lib/services/serviceRecordService';
import type {ServiceRecord, EnrichedServiceRecord} from '../lib/types';

// Mock the API service
jest.mock('../lib/services/apiService', () => ({
	api: {
		get: jest.fn(),
		post: jest.fn(),
		put: jest.fn(),
		delete: jest.fn(),
	},
	extractApiData: jest.fn((response) => response.data),
}));

describe('Service Record Service', () => {
	const mockServiceRecords: ServiceRecord[] = [
		{
			id: '1',
			vehicleId: 1,
			date: '2023-01-01',
			odometer: 10000,
			servicePerformed: ['Oil Change'],
			notes: 'Regular maintenance',
			cost: 50,
		},
		{
			id: '2',
			vehicleId: 2,
			date: '2023-02-01',
			odometer: 20000,
			servicePerformed: ['Tire Rotation'],
			notes: 'Seasonal maintenance',
			cost: 30,
		},
	];

	const mockEnrichedServiceRecords: EnrichedServiceRecord[] = [
		{
			id: '1',
			vehicleId: '1',
			date: '2023-01-01',
			odometer: 10000,
			servicePerformed: ['Oil Change'],
			notes: 'Regular maintenance',
			cost: 50,
			vehicleMake: 'Toyota',
			vehicleModel: 'Camry',
			vehicleYear: 2020,
			vehiclePlateNumber: 'XYZ-123',
		},
		{
			id: '2',
			vehicleId: '2',
			date: '2023-02-01',
			odometer: 20000,
			servicePerformed: ['Tire Rotation'],
			notes: 'Seasonal maintenance',
			cost: 30,
			vehicleMake: 'Honda',
			vehicleModel: 'Civic',
			vehicleYear: 2019,
			vehiclePlateNumber: 'ABC-789',
		},
	];

	beforeEach(() => {
		jest.resetAllMocks();
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should fetch all service records', async () => {
		// Setup
		const apiModule = require('../lib/services/apiService');
		apiModule.api.get.mockResolvedValue({data: mockServiceRecords});

		// Execute
		const result = await serviceRecordService.getAllServiceRecords();

		// Verify
		expect(apiModule.api.get).toHaveBeenCalledTimes(1);
		expect(apiModule.api.get).toHaveBeenCalledWith(
			'/servicerecords',
			undefined
		);
		expect(result).toEqual(mockServiceRecords);
	});

	it('should handle errors when fetching all service records', async () => {
		// Setup
		const mockError = new Error('API error');
		const apiModule = require('../lib/services/apiService');
		apiModule.api.get.mockRejectedValue(mockError);

		// Execute & Verify
		await expect(serviceRecordService.getAllServiceRecords()).rejects.toThrow(
			mockError
		);
		expect(apiModule.api.get).toHaveBeenCalledTimes(1);
	});

	it('should fetch service records for a specific vehicle', async () => {
		// Setup
		const vehicleId = 1;
		const vehicleServiceRecords = mockServiceRecords.filter(
			(record) => record.vehicleId === vehicleId
		);
		const apiModule = require('../lib/services/apiService');
		apiModule.api.get.mockResolvedValue({data: vehicleServiceRecords});

		// Execute
		const result = await serviceRecordService.getServiceRecords(vehicleId);

		// Verify
		expect(apiModule.api.get).toHaveBeenCalledTimes(1);
		expect(apiModule.api.get).toHaveBeenCalledWith(
			`/vehicles/${vehicleId}/servicerecords`,
			undefined
		);
		expect(result).toEqual(vehicleServiceRecords);
	});

	it('should fetch enriched service records', async () => {
		// Setup
		const apiModule = require('../lib/services/apiService');
		apiModule.api.get.mockResolvedValue({data: mockEnrichedServiceRecords});

		// Execute
		const result = await serviceRecordService.getAllEnrichedServiceRecords();

		// Verify
		expect(apiModule.api.get).toHaveBeenCalledTimes(1);
		expect(apiModule.api.get).toHaveBeenCalledWith(
			'/servicerecords/enriched',
			undefined
		);
		expect(result).toEqual(mockEnrichedServiceRecords);
	});
});
