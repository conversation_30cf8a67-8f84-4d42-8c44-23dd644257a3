'use client';

import React, {useEffect, useState, useRef} from 'react';
import {Card, Card<PERSON>ontent, CardHeader, CardTitle} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import {Button} from '@/components/ui/button';
import {
	MapPin,
	Navigation,
	Clock,
	Car,
	Fuel,
	Activity,
	Phone,
	AlertTriangle,
	CheckCircle2,
} from 'lucide-react';
import {Employee, Vehicle} from '@/lib/types';
import {getVehicleById} from '@/lib/store';
import {useToast} from '@/hooks/use-toast';
import * as L from 'leaflet';

// Types for real-time data
interface DriverLocation {
	latitude: number;
	longitude: number;
	timestamp: Date;
	accuracy?: number;
	speed?: number;
	heading?: number;
}

interface DriverStatus {
	status: 'active' | 'break' | 'offline' | 'emergency';
	lastUpdate: Date;
	currentTask?: string;
	estimatedArrival?: Date;
}

interface DriverInformationProps {
	employee: Employee;
	refreshInterval?: number; // in milliseconds
}

// Extended Vehicle interface with additional properties for real-time tracking
interface ExtendedVehicle extends Vehicle {
	status?: 'Available' | 'In Use' | 'Maintenance' | 'Out of Service';
	mileage?: number;
	fuelLevel?: number;
	currentLocation?: string;
}

const DriverInformation: React.FC<DriverInformationProps> = ({
	employee,
	refreshInterval = 30000, // 30 seconds default
}) => {
	const {toast} = useToast();
	const [vehicle, setVehicle] = useState<ExtendedVehicle | null>(null);
	const [location, setLocation] = useState<DriverLocation | null>(null);
	const [driverStatus, setDriverStatus] = useState<DriverStatus>({
		status: 'active',
		lastUpdate: new Date(),
	});
	const [isTracking, setIsTracking] = useState(false);
	const [lastLocationUpdate, setLastLocationUpdate] = useState<Date | null>(
		null
	);
	const mapContainerRef = useRef<HTMLDivElement>(null);
	const mapRef = useRef<L.Map | null>(null);
	const markerRef = useRef<L.Marker | null>(null);

	// Fetch vehicle information
	useEffect(() => {
		const fetchVehicle = async () => {
			if (employee.vehicleId) {
				try {
					const vehicleData = await getVehicleById(Number(employee.vehicleId));
					if (vehicleData) {
						// Extend vehicle data with mock real-time properties
						const extendedVehicle: ExtendedVehicle = {
							...vehicleData,
							status: 'Available',
							mileage: Math.floor(Math.random() * 100000) + 10000,
							fuelLevel: Math.floor(Math.random() * 100) + 1,
							currentLocation: employee.currentLocation || 'Main Garage',
						};
						setVehicle(extendedVehicle);
					}
				} catch (error) {
					console.error('Failed to fetch vehicle data:', error);
				}
			}
		};
		fetchVehicle();
	}, [employee.vehicleId, employee.currentLocation]);

	// Initialize Leaflet map
	useEffect(() => {
		const initMap = async () => {
			if (!mapContainerRef.current) return;

			try {
				// Create map
				const map = L.map(mapContainerRef.current).setView(
					[40.7128, -74.006],
					13
				); // Default to NYC

				// Add tile layer
				L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
					maxZoom: 19,
					attribution:
						'&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
				}).addTo(map);

				mapRef.current = map;

				// Add driver marker with default icon
				const marker = L.marker([40.7128, -74.006])
					.addTo(map)
					.bindPopup(`${employee.name} - ${employee.role}`);

				markerRef.current = marker;
			} catch (error) {
				console.error('Failed to initialize map:', error);
				toast({
					title: 'Map Error',
					description: 'Failed to load map component.',
					variant: 'destructive',
				});
			}
		};

		initMap();

		return () => {
			if (mapRef.current) {
				mapRef.current.remove();
			}
		};
	}, [employee.name, employee.role, toast]);

	// Simulate real-time location updates
	useEffect(() => {
		if (!isTracking) return;

		const interval = setInterval(() => {
			// Simulate GPS data - in real app, this would come from WebSocket/Socket.IO
			const simulatedLocation: DriverLocation = {
				latitude: 40.7128 + (Math.random() - 0.5) * 0.01,
				longitude: -74.006 + (Math.random() - 0.5) * 0.01,
				timestamp: new Date(),
				accuracy: Math.floor(Math.random() * 10) + 5,
				speed: Math.floor(Math.random() * 60) + 20,
				heading: Math.floor(Math.random() * 360),
			};

			setLocation(simulatedLocation);
			setLastLocationUpdate(new Date());

			// Update map marker position
			if (mapRef.current && markerRef.current) {
				const newLatLng = L.latLng(
					simulatedLocation.latitude,
					simulatedLocation.longitude
				);
				markerRef.current.setLatLng(newLatLng);
				mapRef.current.setView(newLatLng);
			}

			// Simulate status updates
			const statuses: DriverStatus['status'][] = [
				'active',
				'break',
				'active',
				'active',
			];
			const randomStatus =
				statuses[Math.floor(Math.random() * statuses.length)];
			setDriverStatus((prev) => ({
				...prev,
				status: randomStatus,
				lastUpdate: new Date(),
				currentTask:
					randomStatus === 'active' ? 'Delivering to Main Street' : undefined,
			}));
		}, refreshInterval);

		return () => clearInterval(interval);
	}, [isTracking, refreshInterval]);

	const startTracking = () => {
		setIsTracking(true);
		toast({
			title: 'Tracking Started',
			description: `Real-time tracking enabled for ${employee.name}`,
			variant: 'default',
		});
	};

	const stopTracking = () => {
		setIsTracking(false);
		toast({
			title: 'Tracking Stopped',
			description: `Real-time tracking disabled for ${employee.name}`,
			variant: 'default',
		});
	};

	const getStatusColor = (status: DriverStatus['status']) => {
		switch (status) {
			case 'active':
				return 'bg-green-500';
			case 'break':
				return 'bg-yellow-500';
			case 'offline':
				return 'bg-gray-500';
			case 'emergency':
				return 'bg-red-500';
			default:
				return 'bg-gray-500';
		}
	};

	const getStatusIcon = (status: DriverStatus['status']) => {
		switch (status) {
			case 'active':
				return <CheckCircle2 className='h-4 w-4' />;
			case 'break':
				return <Clock className='h-4 w-4' />;
			case 'offline':
				return <Activity className='h-4 w-4' />;
			case 'emergency':
				return <AlertTriangle className='h-4 w-4' />;
			default:
				return <Activity className='h-4 w-4' />;
		}
	};

	return (
		<div className='space-y-6'>
			{/* Status and Controls */}
			<Card>
				<CardHeader>
					<CardTitle className='flex items-center justify-between'>
						<span className='flex items-center gap-2'>
							<Activity className='h-5 w-5' />
							Driver Status
						</span>
						<div className='flex gap-2'>
							{!isTracking ? (
								<Button onClick={startTracking} size='sm'>
									<Navigation className='h-4 w-4 mr-2' />
									Start Tracking
								</Button>
							) : (
								<Button onClick={stopTracking} variant='outline' size='sm'>
									Stop Tracking
								</Button>
							)}
						</div>
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
						<div className='flex items-center gap-3'>
							<div
								className={`w-3 h-3 rounded-full ${getStatusColor(
									driverStatus.status
								)}`}
							/>
							<div>
								<div className='flex items-center gap-2'>
									{getStatusIcon(driverStatus.status)}
									<span className='font-medium capitalize'>
										{driverStatus.status}
									</span>
								</div>
								<p className='text-sm text-muted-foreground'>
									Updated: {driverStatus.lastUpdate.toLocaleTimeString()}
								</p>
							</div>
						</div>

						{driverStatus.currentTask && (
							<div className='flex items-center gap-2'>
								<MapPin className='h-4 w-4 text-blue-500' />
								<div>
									<p className='font-medium'>Current Task</p>
									<p className='text-sm text-muted-foreground'>
										{driverStatus.currentTask}
									</p>
								</div>
							</div>
						)}

						{lastLocationUpdate && (
							<div className='flex items-center gap-2'>
								<Clock className='h-4 w-4 text-green-500' />
								<div>
									<p className='font-medium'>Last Location Update</p>
									<p className='text-sm text-muted-foreground'>
										{lastLocationUpdate.toLocaleTimeString()}
									</p>
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>

			{/* Real-time Location */}
			{location && (
				<Card>
					<CardHeader>
						<CardTitle className='flex items-center gap-2'>
							<MapPin className='h-5 w-5' />
							Live Location
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className='grid grid-cols-2 md:grid-cols-4 gap-4 mb-4'>
							<div>
								<p className='text-sm font-medium'>Coordinates</p>
								<p className='text-sm text-muted-foreground'>
									{location.latitude.toFixed(6)},{' '}
									{location.longitude.toFixed(6)}
								</p>
							</div>
							{location.speed && (
								<div>
									<p className='text-sm font-medium'>Speed</p>
									<p className='text-sm text-muted-foreground'>
										{location.speed} mph
									</p>
								</div>
							)}
							{location.accuracy && (
								<div>
									<p className='text-sm font-medium'>Accuracy</p>
									<p className='text-sm text-muted-foreground'>
										±{location.accuracy}m
									</p>
								</div>
							)}
							{location.heading && (
								<div>
									<p className='text-sm font-medium'>Heading</p>
									<p className='text-sm text-muted-foreground'>
										{location.heading}°
									</p>
								</div>
							)}
						</div>

						{/* Map Container */}
						<div
							ref={mapContainerRef}
							className='h-64 w-full rounded-lg border'
							style={{minHeight: '250px'}}
						/>
					</CardContent>
				</Card>
			)}

			{/* Vehicle Information */}
			{vehicle && (
				<Card>
					<CardHeader>
						<CardTitle className='flex items-center gap-2'>
							<Car className='h-5 w-5' />
							Assigned Vehicle
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<div className='space-y-3'>
								<div>
									<p className='text-sm font-medium'>Vehicle</p>
									<p className='text-lg'>
										{vehicle.make} {vehicle.model} ({vehicle.year})
									</p>
								</div>
								<div>
									<p className='text-sm font-medium'>License Plate</p>
									<p className='text-sm font-mono bg-muted px-2 py-1 rounded w-fit'>
										{vehicle.licensePlate}
									</p>
								</div>
								<div>
									<p className='text-sm font-medium'>Status</p>
									<Badge
										variant={
											vehicle.status === 'Available' ? 'default' : 'secondary'
										}>
										{vehicle.status}
									</Badge>
								</div>
							</div>

							<div className='space-y-3'>
								{vehicle.mileage && (
									<div>
										<p className='text-sm font-medium'>Mileage</p>
										<p className='text-sm text-muted-foreground'>
											{vehicle.mileage.toLocaleString()} miles
										</p>
									</div>
								)}
								{vehicle.fuelLevel && (
									<div>
										<p className='text-sm font-medium'>Fuel Level</p>
										<div className='flex items-center gap-2'>
											<Fuel className='h-4 w-4' />
											<div className='flex-1 bg-muted rounded-full h-2'>
												<div
													className='bg-blue-500 h-2 rounded-full'
													style={{width: `${vehicle.fuelLevel}%`}}
												/>
											</div>
											<span className='text-sm'>{vehicle.fuelLevel}%</span>
										</div>
									</div>
								)}
								<div>
									<p className='text-sm font-medium'>Location</p>
									<p className='text-sm text-muted-foreground'>
										{vehicle.currentLocation || 'Location unknown'}
									</p>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Communication */}
			<Card>
				<CardHeader>
					<CardTitle className='flex items-center gap-2'>
						<Phone className='h-5 w-5' />
						Communication
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
						<Button variant='outline' className='justify-start'>
							<Phone className='h-4 w-4 mr-2' />
							Call Driver
						</Button>
						<Button variant='outline' className='justify-start'>
							<MapPin className='h-4 w-4 mr-2' />
							Send Location
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	);
};

export default DriverInformation;
