/**
 * Vehicle API service
 */
import { api, ApiRequestOptions } from './apiService';
import { Vehicle } from '../types';
import { ApiResponse } from '../types/api';

// Vehicle response types
export interface VehicleResponse extends ApiResponse<Vehicle> {}
export interface VehiclesResponse extends ApiResponse<Vehicle[]> {}

// Vehicle create/update input types
export interface VehicleCreateInput {
  make: string;
  model: string;
  year: number;
  vin: string;
  licensePlate: string;
  ownerName: string;
  ownerContact: string;
  color?: string;
  initialOdometer: number;
  imageUrl?: string;
}

export interface VehicleUpdateInput extends Partial<VehicleCreateInput> {}

/**
 * Get all vehicles
 */
export async function getVehicles(
  options?: ApiRequestOptions
): Promise<Vehicle[]> {
  try {
    const response = await api.get<VehiclesResponse>('/vehicles', options);
    return response.data || [];
  } catch (error) {
    console.error('Error fetching vehicles:', error);
    throw error;
  }
}

/**
 * Get a vehicle by ID
 */
export async function getVehicleById(
  id: number,
  options?: ApiRequestOptions
): Promise<Vehicle> {
  try {
    const response = await api.get<VehicleResponse>(`/vehicles/${id}`, options);
    return response.data;
  } catch (error) {
    console.error(`Error fetching vehicle ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new vehicle
 */
export async function createVehicle(
  data: VehicleCreateInput,
  options?: ApiRequestOptions
): Promise<Vehicle> {
  try {
    const response = await api.post<VehicleResponse>('/vehicles', data, options);
    return response.data;
  } catch (error) {
    console.error('Error creating vehicle:', error);
    throw error;
  }
}

/**
 * Update a vehicle
 */
export async function updateVehicle(
  id: number,
  data: VehicleUpdateInput,
  options?: ApiRequestOptions
): Promise<Vehicle> {
  try {
    const response = await api.put<VehicleResponse>(
      `/vehicles/${id}`,
      data,
      options
    );
    return response.data;
  } catch (error) {
    console.error(`Error updating vehicle ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a vehicle
 */
export async function deleteVehicle(
  id: number,
  options?: ApiRequestOptions
): Promise<void> {
  try {
    await api.delete(`/vehicles/${id}`, options);
  } catch (error) {
    console.error(`Error deleting vehicle ${id}:`, error);
    throw error;
  }
}
