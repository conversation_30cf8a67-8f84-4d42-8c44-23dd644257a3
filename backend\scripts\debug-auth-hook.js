/**
 * Debug Auth Hook Script
 * 
 * This script helps diagnose issues with the custom access token hook
 * that's causing 500 errors during authentication.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function debugAuthHook() {
    console.log('🔍 Debugging Auth Hook Issues');
    console.log('=============================\n');

    try {
        // Step 1: Check if user_profiles table is accessible
        console.log('📋 Step 1: Checking user_profiles table...');
        const { data: profiles, error: profileError } = await supabaseAdmin
            .from('user_profiles')
            .select('*')
            .limit(5);

        if (profileError) {
            console.log('❌ user_profiles table error:', profileError.message);
            return;
        } else {
            console.log(`✅ user_profiles table accessible (${profiles.length} records)`);
            profiles.forEach(profile => {
                console.log(`   - ${profile.id}: ${profile.role} (active: ${profile.is_active})`);
            });
        }

        // Step 2: Test the auth hook function with a real user
        console.log('\n📋 Step 2: Testing auth hook function...');
        
        // Get a real user ID
        const testUserId = profiles[0]?.id;
        if (!testUserId) {
            console.log('❌ No user profiles found to test with');
            return;
        }

        console.log(`🧪 Testing with user ID: ${testUserId}`);

        // Test the auth hook function
        const testEvent = {
            user_id: testUserId,
            claims: { 
                sub: testUserId,
                email: '<EMAIL>',
                aud: 'authenticated',
                role: 'authenticated'
            }
        };

        console.log('📤 Calling custom_access_token_hook...');
        const { data: hookResult, error: hookError } = await supabaseAdmin
            .rpc('custom_access_token_hook', { event: testEvent });

        if (hookError) {
            console.log('❌ Auth hook error:', hookError.message);
            console.log('🔧 This is likely the cause of the 500 error!');
            
            // Provide specific troubleshooting
            if (hookError.message.includes('function') && hookError.message.includes('does not exist')) {
                console.log('\n💡 SOLUTION: The auth hook function is missing!');
                console.log('Execute this SQL in Supabase Dashboard → SQL Editor:');
                console.log(getAuthHookSQL());
            } else if (hookError.message.includes('permission')) {
                console.log('\n💡 SOLUTION: Permission issue with auth hook function');
                console.log('Execute this SQL to fix permissions:');
                console.log(getPermissionSQL());
            } else if (hookError.message.includes('json')) {
                console.log('\n💡 SOLUTION: JSON parsing issue in auth hook');
                console.log('The event parameter format may be incorrect');
            }
        } else {
            console.log('✅ Auth hook function working!');
            console.log('📄 Hook result:', JSON.stringify(hookResult, null, 2));
            
            // Verify the result structure
            if (hookResult && hookResult.claims && hookResult.claims.custom_claims) {
                console.log('✅ Custom claims are being generated correctly');
                console.log('🎯 The 500 error may be caused by something else');
            } else {
                console.log('⚠️  Auth hook not returning expected custom claims structure');
            }
        }

        // Step 3: Check helper functions
        console.log('\n📋 Step 3: Checking helper functions...');
        const helperFunctions = ['get_user_role', 'is_admin', 'is_manager_or_above', 'get_user_employee_id'];
        
        for (const funcName of helperFunctions) {
            try {
                const { error } = await supabaseAdmin.rpc(funcName, { user_id: testUserId });
                if (error) {
                    console.log(`❌ ${funcName}: ${error.message}`);
                } else {
                    console.log(`✅ ${funcName}: Working`);
                }
            } catch (error) {
                console.log(`❌ ${funcName}: ${error.message}`);
            }
        }

        // Step 4: Test authentication without custom hook
        console.log('\n📋 Step 4: Testing basic authentication...');
        console.log('🔧 Try temporarily disabling the auth hook in Supabase Dashboard');
        console.log('   1. Go to Authentication → Hooks');
        console.log('   2. Disable the custom access token hook');
        console.log('   3. Try logging in again');
        console.log('   4. If login works, the issue is definitely in the auth hook');

    } catch (error) {
        console.error('❌ Debug script failed:', error.message);
    }
}

function getAuthHookSQL() {
    return `
-- Create the custom access token hook function
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event JSONB)
RETURNS JSONB
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    claims JSONB;
    user_role TEXT;
    is_active BOOLEAN;
    employee_id INTEGER;
BEGIN
    -- Get the claims from the event
    claims := event->'claims';

    -- Fetch user role, status, and employee_id from user_profiles
    SELECT role, is_active, employee_id 
    INTO user_role, is_active, employee_id
    FROM public.user_profiles 
    WHERE id = (event->>'user_id')::UUID;

    -- Set custom claims in the JWT
    IF user_role IS NOT NULL THEN
        claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
            'user_role', user_role,
            'is_active', COALESCE(is_active, true),
            'employee_id', employee_id
        ));
    ELSE
        -- Default claims for users without a profile
        claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
            'user_role', 'USER',
            'is_active', true,
            'employee_id', null
        ));
    END IF;

    -- Update the event with the new claims
    event := jsonb_set(event, '{claims}', claims);
    
    RETURN event;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO service_role;
`;
}

function getPermissionSQL() {
    return `
-- Fix permissions for auth hook
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO service_role;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO authenticated;

-- Ensure user_profiles table is accessible
GRANT SELECT ON public.user_profiles TO supabase_auth_admin;
GRANT SELECT ON public.user_profiles TO service_role;
`;
}

debugAuthHook().catch(console.error);
