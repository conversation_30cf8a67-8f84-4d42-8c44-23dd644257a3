import prisma from './index.js';
import {Prisma, ServiceRecord} from '../generated/prisma/index.js';
import {PrismaClientKnownRequestError} from '../generated/prisma/runtime/library.js';

// data parameter comes from serviceRecord.controller.ts.
// The controller is responsible for formatting the data correctly for Prisma,
// including using `vehicle: { connect: { id: vehicleId } }` and `employee: { connect: { id: employeeId } }`.
export const createServiceRecord = async (
	data: Prisma.ServiceRecordCreateInput
): Promise<ServiceRecord | null> => {
	try {
		return await prisma.serviceRecord.create({
			data, // Assumes 'data' is correctly structured by the controller
		});
	} catch (error) {
		console.error('Error creating service record:', error);
		if (
			error instanceof PrismaClientKnownRequestError &&
			error.code === 'P2003'
		) {
			// error.meta.field_name typically refers to the FK constraint name (e.g., "ServiceRecord_vehicleId_fkey")
			// Check if the constraint name indicates a vehicle or employee issue.
			const fieldName = error.meta?.field_name as string | undefined;
			if (fieldName?.toLowerCase().includes('vehicleid')) {
				throw new Error(
					`Vehicle not found. Ensure vehicle exists before creating service record.`
				);
			}
			if (fieldName?.toLowerCase().includes('employeeid')) {
				throw new Error(
					`Employee not found. Ensure employee exists before creating service record.`
				);
			}
		}
		return null;
	}
};

export const getAllServiceRecords = async (
	vehicleId?: number
): Promise<ServiceRecord[]> => {
	try {
		const whereCondition = vehicleId ? {vehicleId} : {};

		// Add a timeout to prevent hanging indefinitely
		const timeoutPromise = new Promise<ServiceRecord[]>((_, reject) => {
			setTimeout(() => {
				reject(new Error('Database query timed out after 5 seconds'));
			}, 5000); // 5 second timeout
		});

		// Race between the actual query and the timeout
		const records = await Promise.race([
			prisma.serviceRecord.findMany({
				where: whereCondition,
				orderBy: {date: 'desc'},
			}),
			timeoutPromise,
		]);

		// Ensure we always return an array
		return Array.isArray(records) ? records : [];
	} catch (error) {
		console.error('Error fetching all service records:', error);
		// Always return an empty array on error to prevent frontend from getting stuck
		return [];
	}
};

export const getServiceRecordById = async (
	id: string
): Promise<ServiceRecord | null> => {
	try {
		return await prisma.serviceRecord.findUnique({where: {id}});
	} catch (error) {
		console.error(`Error fetching service record with ID ${id}:`, error);
		return null;
	}
};

// The 'data' parameter (Prisma.ServiceRecordUpdateInput) is prepared by the controller.
// It should correctly structure relational updates like `employee: { connect: { id: ... } }` or `employee: { disconnect: true }`.
export const updateServiceRecord = async (
	id: string,
	data: Prisma.ServiceRecordUpdateInput
): Promise<ServiceRecord | null> => {
	try {
		return await prisma.serviceRecord.update({where: {id}, data});
	} catch (error) {
		console.error(`Error updating service record with ID ${id}:`, error);
		if (
			error instanceof PrismaClientKnownRequestError &&
			error.code === 'P2025'
		) {
			return null;
		}
		if (
			error instanceof PrismaClientKnownRequestError &&
			error.code === 'P2003'
		) {
			const fieldName = error.meta?.field_name as string | undefined;
			if (fieldName?.toLowerCase().includes('vehicleid')) {
				throw new Error(`Vehicle not found for update.`);
			}
			if (fieldName?.toLowerCase().includes('employeeid')) {
				throw new Error(`Employee not found for update.`);
			}
		}
		return null;
	}
};

export const deleteServiceRecord = async (
	id: string
): Promise<ServiceRecord | null> => {
	try {
		return await prisma.serviceRecord.delete({where: {id}});
	} catch (error) {
		console.error(`Error deleting service record with ID ${id}:`, error);
		if (
			error instanceof PrismaClientKnownRequestError &&
			error.code === 'P2025'
		) {
			return null;
		}
		return null;
	}
};
